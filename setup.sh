#!/bin/bash

# Bitmovin DRM Player 项目设置脚本

echo "🎬 Bitmovin DRM Player 项目设置"
echo "================================"

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    echo "请先安装 Node.js (https://nodejs.org/)"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 检查包管理器
if command -v pnpm &> /dev/null; then
    PKG_MANAGER="pnpm"
elif command -v yarn &> /dev/null; then
    PKG_MANAGER="yarn"
elif command -v npm &> /dev/null; then
    PKG_MANAGER="npm"
else
    echo "❌ 未找到包管理器 (npm, yarn, pnpm)"
    exit 1
fi

echo "📦 使用包管理器: $PKG_MANAGER"

# 安装依赖
echo "📥 安装项目依赖..."
$PKG_MANAGER install

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功"
else
    echo "❌ 依赖安装失败"
    exit 1
fi

# 创建环境变量文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "✅ 已创建 .env 文件"
    echo ""
    echo "⚠️  重要提醒:"
    echo "   请编辑 .env 文件，设置你的 Bitmovin Player License Key"
    echo "   获取地址: https://bitmovin.com/"
    echo ""
else
    echo "ℹ️  .env 文件已存在"
fi

# 显示下一步操作
echo ""
echo "🚀 设置完成！下一步操作:"
echo ""
echo "1. 编辑 .env 文件，设置 VITE_BITMOVIN_PLAYER_KEY"
echo "2. 运行开发服务器:"
echo "   $PKG_MANAGER run dev"
echo ""
echo "3. 构建生产版本:"
echo "   $PKG_MANAGER run build"
echo ""
echo "📚 更多信息请查看 README.md"
echo ""
echo "🎉 祝你使用愉快！"
