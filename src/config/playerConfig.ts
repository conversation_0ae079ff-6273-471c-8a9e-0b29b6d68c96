// 播放器配置管理
export interface PlayerConfiguration {
  licenseKey: string
  autoplay: boolean
  muted: boolean
  startupBitrate: {
    desktop: string
    mobile: string
  }
  ui: {
    theme: 'dark' | 'light'
    language: string
  }
}

export interface DRMEndpoints {
  widevine?: string
  playready?: string
  fairplay?: {
    licenseUrl: string
    certificateUrl: string
  }
}

// 默认配置
export const defaultPlayerConfig: PlayerConfiguration = {
  licenseKey: 'AD606391-3952-4756-A3BF-91ED018CC5A8',
  autoplay: false,
  muted: false,
  startupBitrate: {
    desktop: '1000kbps',
    mobile: '600kbps'
  },
  ui: {
    theme: 'dark',
    language: 'zh-CN'
  }
}

// DRM端点配置
export const drmEndpoints: DRMEndpoints = {
  widevine: import.meta.env.VITE_WIDEVINE_LICENSE_URL,
  playready: import.meta.env.VITE_PLAYREADY_LICENSE_URL,
  fairplay: {
    licenseUrl: import.meta.env.VITE_FAIRPLAY_LICENSE_URL,
    certificateUrl: import.meta.env.VITE_FAIRPLAY_CERTIFICATE_URL
  }
}

// 检查配置有效性
export const validateConfig = (config: PlayerConfiguration): boolean => {
  if (!config.licenseKey || config.licenseKey === 'YOUR_BITMOVIN_PLAYER_KEY') {
    console.warn('Bitmovin Player license key not configured')
    return false
  }
  return true
}

// 获取浏览器DRM支持信息
export const getDRMSupport = (): Promise<{
  widevine: boolean
  playready: boolean
  fairplay: boolean
}> => {
  return new Promise((resolve) => {
    const support = {
      widevine: false,
      playready: false,
      fairplay: false
    }

    const checkPromises: Promise<void>[] = []

    // 检查Widevine
    if (navigator.requestMediaKeySystemAccess) {
      const widevinePromise = navigator.requestMediaKeySystemAccess('com.widevine.alpha', [{
        initDataTypes: ['cenc'],
        videoCapabilities: [{ contentType: 'video/mp4; codecs="avc1.42E01E"' }]
      }]).then(() => {
        support.widevine = true
      }).catch(() => {
        // Widevine not supported
      })
      checkPromises.push(widevinePromise)

      // 检查PlayReady
      const playreadyPromise = navigator.requestMediaKeySystemAccess('com.microsoft.playready', [{
        initDataTypes: ['cenc'],
        videoCapabilities: [{ contentType: 'video/mp4; codecs="avc1.42E01E"' }]
      }]).then(() => {
        support.playready = true
      }).catch(() => {
        // PlayReady not supported
      })
      checkPromises.push(playreadyPromise)
    }

    // 检查FairPlay (Safari)
    if (window.WebKitMediaKeys) {
      support.fairplay = true
    }

    Promise.allSettled(checkPromises).then(() => {
      resolve(support)
    })
  })
}

// 应用配置
export const appConfig = {
  title: import.meta.env.VITE_APP_TITLE || 'Bitmovin DRM Player',
  description: import.meta.env.VITE_APP_DESCRIPTION || '支持DRM的流媒体播放器'
}
