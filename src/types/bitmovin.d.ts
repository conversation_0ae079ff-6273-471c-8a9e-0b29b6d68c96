// Bitmovin Player类型定义扩展

declare global {
  interface Window {
    WebKitMediaKeys?: any
    drmSupport?: {
      widevine: boolean
      playready: boolean
      fairplay: boolean
    }
  }
}

// 扩展Bitmovin Player类型
declare module 'bitmovin-player' {
  export interface SourceConfig {
    title?: string
    description?: string
    dash?: string
    hls?: string
    progressive?: string | string[]
    drm?: {
      widevine?: {
        LA_URL: string
        headers?: Record<string, string>
        prepareMessage?: (message: <PERSON><PERSON><PERSON><PERSON>uffer, messageType: string) => ArrayBuffer
        prepareLicense?: (license: ArrayBuffer) => ArrayBuffer
      }
      playready?: {
        LA_URL: string
        headers?: Record<string, string>
        prepareMessage?: (message: A<PERSON><PERSON><PERSON>uffer, messageType: string) => ArrayBuffer
        prepareLicense?: (license: ArrayBuffer) => Array<PERSON>uffer
      }
      fairplay?: {
        LA_URL: string
        certificateURL: string
        headers?: Record<string, string>
        prepareMessage?: (message: A<PERSON><PERSON><PERSON>uffer, messageType: string) => ArrayBuffer
        prepareLicense?: (license: ArrayBuffer) => Arra<PERSON><PERSON>uffer
        prepareCertificate?: (certificate: ArrayBuffer) => Arra<PERSON><PERSON>uffer
      }
    }
    poster?: string
    thumbnails?: {
      url: string
      interval?: number
    }
    metadata?: Record<string, any>
  }

  export interface PlayerConfig {
    key: string
    playback?: {
      autoplay?: boolean
      muted?: boolean
      preload?: boolean
      restoreUserSettings?: boolean
    }
    adaptation?: {
      desktop?: {
        startupBitrate?: string
        maxBitrate?: string
      }
      mobile?: {
        startupBitrate?: string
        maxBitrate?: string
      }
    }
    network?: {
      preprocessHttpRequest?: (type: string, request: any) => any
      preprocessHttpResponse?: (type: string, response: any) => any
    }
    ui?: {
      playbackSpeedSelectionEnabled?: boolean
      hideFirstPlay?: boolean
    }
    logs?: {
      level?: 'debug' | 'info' | 'warn' | 'error'
    }
  }

  export interface PlayerAPI {
    load(source: SourceConfig): Promise<void>
    play(): Promise<void>
    pause(): void
    seek(time: number): boolean
    getCurrentTime(): number
    getDuration(): number
    getVolume(): number
    setVolume(volume: number): void
    isMuted(): boolean
    mute(): void
    unmute(): void
    destroy(): void
    on(event: string, callback: Function): void
    off(event: string, callback?: Function): void
    getConfig(): PlayerConfig
    getSource(): SourceConfig | null
    isPlaying(): boolean
    isPaused(): boolean
    isLive(): boolean
    getAvailableVideoQualities(): any[]
    getVideoQuality(): any
    setVideoQuality(quality: any): void
  }

  export class Player {
    constructor(container: HTMLElement, config: PlayerConfig)
    
    // 继承PlayerAPI的所有方法
    load(source: SourceConfig): Promise<void>
    play(): Promise<void>
    pause(): void
    seek(time: number): boolean
    getCurrentTime(): number
    getDuration(): number
    getVolume(): number
    setVolume(volume: number): void
    isMuted(): boolean
    mute(): void
    unmute(): void
    destroy(): void
    on(event: string, callback: Function): void
    off(event: string, callback?: Function): void
    getConfig(): PlayerConfig
    getSource(): SourceConfig | null
    isPlaying(): boolean
    isPaused(): boolean
    isLive(): boolean
    getAvailableVideoQualities(): any[]
    getVideoQuality(): any
    setVideoQuality(quality: any): void
  }
}

declare module 'bitmovin-player/bitmovinplayer-ui' {
  export class UIFactory {
    static buildDefaultUI(player: any, config?: any): any
    static buildModernUI(player: any, config?: any): any
    static buildModernSmallScreenUI(player: any, config?: any): any
    static buildModernCastReceiverUI(player: any, config?: any): any
  }
}

export {}
