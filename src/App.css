.App {
  text-align: center;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: white;
}

.App-header {
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-bottom: 2rem;
}

.App-header h1 {
  margin: 0 0 1rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.App-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

main {
  padding: 0 2rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .App-header h1 {
    font-size: 2rem;
  }
  
  .App-header p {
    font-size: 1rem;
  }
  
  main {
    padding: 0 1rem 1rem;
  }
}
