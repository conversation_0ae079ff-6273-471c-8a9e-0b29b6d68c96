.bitmovin-player-wrapper {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.player-controls {
  margin-bottom: 2rem;
  text-align: left;
}

.player-controls h3 {
  margin-bottom: 1rem;
  color: #fff;
  font-size: 1.2rem;
}

.source-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.source-buttons button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 200px;
}

.source-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.source-buttons button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.source-buttons button.active {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
}

.current-source {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.current-source p {
  margin: 0;
  color: #fff;
}

.drm-support-status {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.drm-support-status h4 {
  margin: 0 0 1rem 0;
  color: #fff;
  font-size: 1rem;
}

.drm-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.drm-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.drm-status.supported {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.drm-status.not-supported {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #f44336;
}

.drm-name {
  font-weight: 500;
}

.drm-status-indicator {
  font-weight: bold;
  font-size: 1.1rem;
}

.loading {
  padding: 1rem;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.loading p {
  margin: 0;
  color: #667eea;
  font-weight: 500;
}

.error {
  padding: 1rem;
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.error p {
  margin: 0 0 0.5rem 0;
  color: #f44336;
  font-weight: 500;
}

.error small {
  color: #ffab91;
  line-height: 1.4;
}

.bitmovin-player-container {
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.player-info {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  text-align: left;
}

.player-info h4 {
  margin: 0 0 1rem 0;
  color: #fff;
  font-size: 1.1rem;
}

.player-info ul {
  margin: 0 0 1rem 0;
  padding-left: 1.5rem;
}

.player-info li {
  margin-bottom: 0.5rem;
  color: #ccc;
  line-height: 1.4;
}

.player-info li strong {
  color: #667eea;
}

.player-info p {
  margin: 0;
  color: #999;
  line-height: 1.5;
}

.player-info small {
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bitmovin-player-wrapper {
    padding: 1rem;
  }
  
  .source-buttons {
    flex-direction: column;
  }
  
  .source-buttons button {
    min-width: auto;
    width: 100%;
  }
  
  .bitmovin-player-container {
    height: 300px !important;
  }
}

/* Bitmovin Player UI 自定义样式 */
.bmpui-ui-container {
  font-family: 'Inter', sans-serif !important;
}

.bmpui-ui-controlbar {
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%) !important;
}

.bmpui-ui-playbacktimelabel,
.bmpui-ui-volumeslider,
.bmpui-ui-seekbar {
  color: #fff !important;
}

.bmpui-ui-seekbar .bmpui-ui-seekbar-playbackposition {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
}
