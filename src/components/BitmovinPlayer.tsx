import React, { useEffect, useRef, useState } from 'react'
import { Player, PlayerAPI, SourceConfig } from 'bitmovin-player'
import { UIFactory } from 'bitmovin-player/bitmovinplayer-ui'
import { defaultPlayerConfig, validateConfig, getDRMSupport, appConfig } from '../config/playerConfig'
import './BitmovinPlayer.css'

// 播放源配置接口（扩展Bitmovin的SourceConfig）
interface CustomSourceConfig extends SourceConfig {
  // 可以添加自定义属性
}

const BitmovinPlayer: React.FC = () => {
  const playerContainerRef = useRef<HTMLDivElement>(null)
  const playerRef = useRef<PlayerAPI | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentSource, setCurrentSource] = useState<string>('')
  const [drmSupport, setDrmSupport] = useState<{
    widevine: boolean
    playready: boolean
    fairplay: boolean
  } | null>(null)

  // 示例DRM流媒体源
  const drmSources: CustomSourceConfig[] = [
    {
      title: 'Bitmovin Demo - Widevine',
      description: 'DASH stream with Widevine DRM protection',
      dash: 'https://ott-video-cf.formula1.com/v2/pa_cGF0aDolMkZoYWp2azJmZDJ2eHIlMkZvdXQlMkZ2MSUyRk1haW4lMkZXSUYtRjMtUjElMkZTRFItSEQtREFTSC1XVnxraWQ6MTA0MnxleHA6MTc1MzUxMjc2Mnx0dGw6MTQ0MHxnZW86VVN8c3RyZWFtVHlwZTpTRFJfSERfREFTSFdWfHNlc3Npb25JZDoyMjA2NTIxNThfMDFLMTA1TlpDSEU2S01aWVJDRFQzNjhDOFpfUFJPfHN0YXJ0OjE3NTM0MjYyMDZ8ZW5kOjE3NTM0Njk0MDZ8dG9rZW46ZzFGOThBbjkzR1B1Ulg1YWxhUFB3Wkc3Nn5uSkZ4Vkc4OHRvMkZkR00wNF8_/hajvk2fd2vxr/out/v1/Main/WIF-F3-R1/SDR-HD-DASH-WV/index.mpd?end=1753469406&start=1753426206',
      drm: {
        widevine: {
          LA_URL: "https://f1tv.formula1.com/2.0/R/ENG/WEB_HLS/ALL/CONTENT/LA/widevine?contentId=1000009669",
          headers: {
            'sec-fetch-site': 'same-origin',
            'entitlementtoken': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJFeHRlcm5hbEF1dGhvcml6YXRpb25zQ29udGV4dERhdGEiOiJGSU4iLCJTdWJzY3JpcHRpb25TdGF0dXMiOiJhY3RpdmUiLCJTdWJzY3JpYmVySWQiOiIyMjA2NTIxNTgiLCJGaXJzdE5hbWUiOiJLZXkiLCJlbnRzIjpbeyJjb3VudHJ5IjoiRklOIiwiZW50IjoiUkVHIn0seyJjb3VudHJ5IjoiRklOIiwiZW50IjoiUFJPIn1dLCJMYXN0TmFtZSI6IkxvcmQiLCJleHAiOjE3NTM3Njg4MTYsIlNlc3Npb25JZCI6ImV5SmhiR2NpT2lKb2RIUndPaTh2ZDNkM0xuY3pMbTl5Wnk4eU1EQXhMekEwTDNodGJHUnphV2N0Ylc5eVpTTm9iV0ZqTFhOb1lUSTFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUppZFNJNklqRXdNREV4SWl3aWMya2lPaUkyTUdFNVlXUTROQzFsT1ROa0xUUTRNR1l0T0RCa05pMWhaak0zTkRrMFpqSmxNaklpTENKb2RIUndPaTh2YzJOb1pXMWhjeTU0Yld4emIyRndMbTl5Wnk5M2N5OHlNREExTHpBMUwybGtaVzUwYVhSNUwyTnNZV2x0Y3k5dVlXMWxhV1JsYm5ScFptbGxjaUk2SWpJeU1EWTFNakUxT0NJc0ltbGtJam9pTldKak5EVmlORGd0TURFNE1TMDBOMlk1TFRoaU1UY3RNak14TURGaVpqVXpNR1UySWl3aWRDSTZJakVpTENKc0lqb2laVzR0UjBJaUxDSmtZeUk2SWpNMk5EUWlMQ0poWldRaU9pSXlNREkxTFRBNExUQTRWREEyT2pBd09qRTJMalE0TVZvaUxDSmtkQ0k2SWpFaUxDSmxaQ0k2SWpJd01qVXRNRGd0TWpSVU1EWTZNREE2TVRZdU5EZ3hXaUlzSW1ObFpDSTZJakl3TWpVdE1EY3RNalpVTURZNk1EQTZNVFl1TkRneFdpSXNJbWx3SWpvaU9EQXVNakl3TGpJeE5pNHhOVGdpTENKaklqb2lTRVZNVTBsT1Mwa2lMQ0p6ZENJNklqRTRJaXdpY0dNaU9pSXdNRFl5TUNJc0ltTnZJam9pUmtsT0lpd2libUptSWpveE56VXpOREl6TWpFMkxDSmxlSEFpT2pFM05UWXdNVFV5TVRZc0ltbHpjeUk2SW1GelkyVnVaRzl1TG5SMklpd2lZWFZrSWpvaVlYTmpaVzVrYjI0dWRIWWlmUS5IMzgxNnNZajVVcFFnWTByZHhPbmctcVZMZFVNZlFLVVV5c0xnQXBSakNjIiwiU3Vic2NyaWJlZFByb2R1Y3QiOiJGMSBUViBQcm8gTW9udGhseSIsImp0aSI6IjE1NThmMmQwLWQzZjktNGI3Zi1iZjYwLTc1MDI5NzZlYjQ5MiIsImhhc2hlZFN1YnNjcmliZXJJZCI6IkV6S2lKcFVObUJ0QlJQd05xNlB1WTZzbVpTa2FxSTdjbm5lM083LzhTSkE9IiwiU3Vic2NyaXB0aW9uIjoiUFJPIiwiaWF0IjoxNzUzNDI2MzYxLCJpc3MiOiJGMVRWIn0.gmkmFGzSCkxAd2bP3gDvSSU0SoKIncRlUieN0qR7UHk'
          }
        }
      }
    },
    {
      title: 'Bitmovin Demo - Multi-DRM',
      description: 'Multi-DRM protected content (Widevine + PlayReady)',
      dash: 'https://bitmovin-a.akamaihd.net/content/art-of-motion_drm/mpds/11331.mpd',
      hls: 'https://bitmovin-a.akamaihd.net/content/art-of-motion_drm/m3u8s/11331.m3u8',
      drm: {
        widevine: {
          LA_URL: 'https://widevine-proxy.appspot.com/proxy'
        },
        playready: {
          LA_URL: 'https://playready.directtaps.net/pr/svc/rightsmanager.asmx'
        }
      }
    }
  ]

  // 检查DRM支持
  useEffect(() => {
    getDRMSupport().then(setDrmSupport)
  }, [])

  // 初始化播放器
  const initializePlayer = async (source: CustomSourceConfig) => {
    if (!playerContainerRef.current) return

    setIsLoading(true)
    setError(null)

    try {
      // 如果已有播放器实例，先销毁
      if (playerRef.current) {
        playerRef.current.destroy()
        playerRef.current = null
      }

      // 验证配置
      if (!validateConfig(defaultPlayerConfig)) {
        throw new Error('Bitmovin Player配置无效，请检查license key')
      }

      // 播放器配置
      const playerConfig = {
        key: defaultPlayerConfig.licenseKey,
        playback: {
          autoplay: defaultPlayerConfig.autoplay,
          muted: defaultPlayerConfig.muted
        },
        adaptation: {
          desktop: {
            startupBitrate: defaultPlayerConfig.startupBitrate.desktop
          },
          mobile: {
            startupBitrate: defaultPlayerConfig.startupBitrate.mobile
          }
        },
        network: {
          preprocessHttpRequest: (type: string, request: any) => {
            // 可以在这里添加自定义请求头
            console.log('HTTP Request:', type, request)
            return request
          }
        },
        logs: {
          level: 'info' as const
        }
      }

      // 创建播放器实例
      const player = new Player(playerContainerRef.current, playerConfig)
      
      // 创建UI
      UIFactory.buildDefaultUI(player)
      
      playerRef.current = player

      // 准备播放源
      const sourceConfig: any = {
        title: source.title,
        description: source.description
      }

      // 添加流媒体源
      if (source.dash) {
        sourceConfig.dash = source.dash
      }
      if (source.hls) {
        sourceConfig.hls = source.hls
      }

      // 添加DRM配置
      if (source.drm) {
        sourceConfig.drm = source.drm
      }

      // 加载源
      await player.load(sourceConfig)
      
      setCurrentSource(source.title)
      console.log('Player initialized successfully with source:', source.title)

    } catch (err) {
      console.error('Failed to initialize player:', err)
      setError(`播放器初始化失败: ${err instanceof Error ? err.message : '未知错误'}`)
    } finally {
      setIsLoading(false)
    }
  }

  // 组件卸载时清理播放器
  useEffect(() => {
    return () => {
      if (playerRef.current) {
        playerRef.current.destroy()
        playerRef.current = null
      }
    }
  }, [])

  return (
    <div className="bitmovin-player-wrapper">
      <div className="player-controls">
        <h3>选择DRM流媒体源:</h3>
        <div className="source-buttons">
          {drmSources.map((source, index) => (
            <button
              key={index}
              onClick={() => initializePlayer(source)}
              disabled={isLoading}
              className={currentSource === source.title ? 'active' : ''}
            >
              {source.title}
            </button>
          ))}
        </div>
        
        {currentSource && (
          <div className="current-source">
            <p><strong>当前播放:</strong> {currentSource}</p>
          </div>
        )}
        
        {isLoading && (
          <div className="loading">
            <p>正在加载播放器...</p>
          </div>
        )}
        
        {drmSupport && (
          <div className="drm-support-status">
            <h4>浏览器DRM支持状态:</h4>
            <div className="drm-status-grid">
              <div className={`drm-status ${drmSupport.widevine ? 'supported' : 'not-supported'}`}>
                <span className="drm-name">Widevine</span>
                <span className="drm-status-indicator">{drmSupport.widevine ? '✓' : '✗'}</span>
              </div>
              <div className={`drm-status ${drmSupport.playready ? 'supported' : 'not-supported'}`}>
                <span className="drm-name">PlayReady</span>
                <span className="drm-status-indicator">{drmSupport.playready ? '✓' : '✗'}</span>
              </div>
              <div className={`drm-status ${drmSupport.fairplay ? 'supported' : 'not-supported'}`}>
                <span className="drm-name">FairPlay</span>
                <span className="drm-status-indicator">{drmSupport.fairplay ? '✓' : '✗'}</span>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="error">
            <p>{error}</p>
            <small>
              注意: 需要有效的Bitmovin Player License Key才能正常播放。
              请在.env文件中设置VITE_BITMOVIN_PLAYER_KEY环境变量。
            </small>
          </div>
        )}
      </div>
      
      <div 
        ref={playerContainerRef} 
        className="bitmovin-player-container"
        style={{ 
          width: '100%', 
          height: '500px',
          backgroundColor: '#000',
          borderRadius: '8px',
          overflow: 'hidden'
        }}
      />
      
      <div className="player-info">
        <h4>DRM支持说明:</h4>
        <ul>
          <li><strong>Widevine:</strong> Chrome, Firefox, Edge等现代浏览器</li>
          <li><strong>PlayReady:</strong> Edge, Internet Explorer</li>
          <li><strong>FairPlay:</strong> Safari (需要额外配置)</li>
        </ul>
        <p>
          <small>
            此演示使用Bitmovin提供的测试DRM内容。
            在生产环境中，您需要配置自己的DRM许可证服务器。
          </small>
        </p>
      </div>
    </div>
  )
}

export default BitmovinPlayer
