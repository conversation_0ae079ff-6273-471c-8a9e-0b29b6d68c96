@echo off
chcp 65001 >nul

echo 🎬 Bitmovin DRM Player 项目设置
echo ================================

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装
    echo 请先安装 Node.js ^(https://nodejs.org/^)
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
node --version

REM 检查包管理器
set PKG_MANAGER=

pnpm --version >nul 2>&1
if %errorlevel% equ 0 (
    set PKG_MANAGER=pnpm
    goto :install
)

yarn --version >nul 2>&1
if %errorlevel% equ 0 (
    set PKG_MANAGER=yarn
    goto :install
)

npm --version >nul 2>&1
if %errorlevel% equ 0 (
    set PKG_MANAGER=npm
    goto :install
)

echo ❌ 未找到包管理器 ^(npm, yarn, pnpm^)
pause
exit /b 1

:install
echo 📦 使用包管理器: %PKG_MANAGER%

REM 安装依赖
echo 📥 安装项目依赖...
%PKG_MANAGER% install

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装成功

REM 创建环境变量文件
if not exist ".env" (
    echo 📝 创建环境变量文件...
    copy .env.example .env >nul
    echo ✅ 已创建 .env 文件
    echo.
    echo ⚠️  重要提醒:
    echo    请编辑 .env 文件，设置你的 Bitmovin Player License Key
    echo    获取地址: https://bitmovin.com/
    echo.
) else (
    echo ℹ️  .env 文件已存在
)

REM 显示下一步操作
echo.
echo 🚀 设置完成！下一步操作:
echo.
echo 1. 编辑 .env 文件，设置 VITE_BITMOVIN_PLAYER_KEY
echo 2. 运行开发服务器:
echo    %PKG_MANAGER% run dev
echo.
echo 3. 构建生产版本:
echo    %PKG_MANAGER% run build
echo.
echo 📚 更多信息请查看 README.md
echo.
echo 🎉 祝你使用愉快！
echo.
pause
