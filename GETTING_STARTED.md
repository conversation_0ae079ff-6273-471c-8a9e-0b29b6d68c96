# 快速开始指南

## 🚀 一键设置

### 方法1: 使用设置脚本（推荐）

**Linux/macOS:**
```bash
./setup.sh
```

**Windows:**
```cmd
setup.bat
```

### 方法2: 手动设置

1. **安装依赖**
   ```bash
   npm install
   # 或
   yarn install
   # 或
   pnpm install
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env
   ```
   
   编辑 `.env` 文件，设置你的Bitmovin Player License Key：
   ```env
   VITE_BITMOVIN_PLAYER_KEY=your_actual_license_key_here
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

## 📋 获取Bitmovin License Key

1. 访问 [Bitmovin官网](https://bitmovin.com/)
2. 注册账户
3. 创建新的Player License
4. 复制License Key到 `.env` 文件中

## 🎯 功能演示

启动项目后，你可以：

1. **查看DRM支持状态** - 页面会自动检测浏览器支持的DRM技术
2. **测试DRM播放** - 点击不同的源按钮测试各种DRM保护的内容
3. **响应式体验** - 在不同设备上测试播放器的响应式设计

## 🔧 自定义配置

### 添加自定义DRM源

编辑 `src/components/BitmovinPlayer.tsx`，在 `drmSources` 数组中添加：

```typescript
{
  title: '你的自定义源',
  description: '描述信息',
  dash: 'https://your-dash-stream.mpd',
  hls: 'https://your-hls-stream.m3u8',
  drm: {
    widevine: {
      LA_URL: 'https://your-widevine-license-server'
    }
  }
}
```

### 修改播放器配置

编辑 `src/config/playerConfig.ts` 来调整：
- 自动播放设置
- 启动码率
- UI主题
- 其他播放器选项

## 🌐 浏览器兼容性

| 浏览器 | Widevine | PlayReady | FairPlay |
|--------|----------|-----------|----------|
| Chrome | ✅ | ❌ | ❌ |
| Firefox | ✅ | ❌ | ❌ |
| Safari | ❌ | ❌ | ✅ |
| Edge | ✅ | ✅ | ❌ |

## 🐛 常见问题

### Q: 播放器显示"License Key无效"
**A:** 检查 `.env` 文件中的 `VITE_BITMOVIN_PLAYER_KEY` 是否正确设置

### Q: DRM内容无法播放
**A:** 
- 确认浏览器支持相应的DRM技术
- 检查DRM许可证服务器配置
- 查看浏览器控制台的错误信息

### Q: 移动端播放问题
**A:** 
- iOS设备需要使用FairPlay DRM
- Android设备通常支持Widevine
- 确保移动浏览器支持EME (Encrypted Media Extensions)

### Q: 开发服务器启动失败
**A:** 
- 确认Node.js版本 >= 16
- 删除 `node_modules` 文件夹后重新安装依赖
- 检查端口3000是否被占用

## 📚 更多资源

- [Bitmovin Player文档](https://bitmovin.com/docs/player)
- [DRM技术介绍](https://bitmovin.com/digital-rights-management/)
- [Vite文档](https://vitejs.dev/)
- [React文档](https://react.dev/)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目仅用于演示目的。Bitmovin Player需要商业许可证才能在生产环境中使用。
