/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
!function(e){var t;"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):((t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).bitmovin||(t.bitmovin={})).playerui=e()}(function(){return function o(i,r,s){function a(n,e){if(!r[n]){if(!i[n]){var t="function"==typeof require&&require;if(!e&&t)return t(n,!0);if(l)return l(n,!0);e=new Error("Cannot find module '"+n+"'");throw e.code="MODULE_NOT_FOUND",e}t=r[n]={exports:{}};i[n][0].call(t.exports,function(e){var t=i[n][1][e];return a(t||e)},t,t.exports,o,i,r,s)}return r[n].exports}for(var l="function"==typeof require&&require,e=0;e<s.length;e++)a(s[e]);return a}({1:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ArrayUtils=void 0,(n.ArrayUtils||(n.ArrayUtils={})).remove=function(e,t){return-1<(t=e.indexOf(t))?e.splice(t,1)[0]:null}},{}],2:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.AudioTrackSwitchHandler=void 0;var i=e("./localization/i18n");function o(e,t,n){var o=this;this.addAudioTrack=function(e){e=e.track;o.listElement.hasItem(e.id)||o.listElement.addItem(e.id,i.i18n.getLocalizer(e.label),!0)},this.removeAudioTrack=function(e){e=e.track;o.listElement.hasItem(e.id)&&o.listElement.removeItem(e.id)},this.selectCurrentAudioTrack=function(){var e=o.player.getAudio();e&&o.listElement.selectItem(e.id)},this.refreshAudioTracks=function(){var e=o.player.getAvailableAudio();o.listElement.synchronizeItems(e.map(function(e){return{key:e.id,label:e.label}})),o.selectCurrentAudioTrack()},this.player=e,this.listElement=t,this.uimanager=n,this.bindSelectionEvent(),this.bindPlayerEvents(),this.refreshAudioTracks()}o.prototype.bindSelectionEvent=function(){var n=this;this.listElement.onItemSelected.subscribe(function(e,t){n.player.setAudio(t)})},o.prototype.bindPlayerEvents=function(){this.player.on(this.player.exports.PlayerEvent.AudioChanged,this.selectCurrentAudioTrack),this.player.on(this.player.exports.PlayerEvent.SourceUnloaded,this.refreshAudioTracks),this.player.on(this.player.exports.PlayerEvent.PeriodSwitched,this.refreshAudioTracks),this.player.on(this.player.exports.PlayerEvent.AudioAdded,this.addAudioTrack),this.player.on(this.player.exports.PlayerEvent.AudioRemoved,this.removeAudioTrack),this.uimanager.getConfig().events.onUpdated.subscribe(this.refreshAudioTracks)},n.AudioTrackSwitchHandler=o},{"./localization/i18n":91}],3:[function(e,t,n){"use strict";function o(){}Object.defineProperty(n,"__esModule",{value:!0}),n.BrowserUtils=void 0,Object.defineProperty(o,"isMobile",{get:function(){return!!this.windowExists()&&navigator&&navigator.userAgent&&/Mobi/.test(navigator.userAgent)},enumerable:!1,configurable:!0}),Object.defineProperty(o,"isChrome",{get:function(){return!!this.windowExists()&&navigator&&navigator.userAgent&&/Chrome/.test(navigator.userAgent)},enumerable:!1,configurable:!0}),Object.defineProperty(o,"isAndroid",{get:function(){return!!this.windowExists()&&navigator&&navigator.userAgent&&/Android/.test(navigator.userAgent)&&!this.isHisense},enumerable:!1,configurable:!0}),Object.defineProperty(o,"isIOS",{get:function(){return!!this.windowExists()&&navigator&&navigator.userAgent&&/iPad|iPhone|iPod/.test(navigator.userAgent)},enumerable:!1,configurable:!0}),Object.defineProperty(o,"isMacIntel",{get:function(){return!!this.windowExists()&&navigator&&navigator.userAgent&&"MacIntel"===navigator.platform},enumerable:!1,configurable:!0}),Object.defineProperty(o,"isHisense",{get:function(){return!!this.windowExists()&&navigator&&navigator.userAgent&&/Hisense/.test(navigator.userAgent)},enumerable:!1,configurable:!0}),Object.defineProperty(o,"isPlayStation",{get:function(){return!!this.windowExists()&&navigator&&navigator.userAgent&&/PlayStation/i.test(navigator.userAgent)},enumerable:!1,configurable:!0}),Object.defineProperty(o,"isWebOs",{get:function(){return!!this.windowExists()&&navigator&&navigator.userAgent&&(navigator.userAgent.includes("Web0S")||navigator.userAgent.includes("NetCast"))},enumerable:!1,configurable:!0}),Object.defineProperty(o,"isTizen",{get:function(){return!!this.windowExists()&&navigator&&navigator.userAgent&&/Tizen/.test(navigator.userAgent)},enumerable:!1,configurable:!0}),Object.defineProperty(o,"isTouchSupported",{get:function(){return!!this.windowExists()&&("ontouchstart"in window||navigator&&navigator.userAgent&&(0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints))},enumerable:!1,configurable:!0}),o.windowExists=function(){return"undefined"!=typeof window},n.BrowserUtils=o},{}],4:[function(e,t,n){"use strict";var o,r,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),e=(Object.defineProperty(n,"__esModule",{value:!0}),n.AdClickOverlay=void 0,e("./clickoverlay")),e=(r=e.ClickOverlay,i(s,r),s.prototype.configure=function(e,t){function n(){o.setUrl(null)}var o=this,i=(r.prototype.configure.call(this,e,t),null);e.on(e.exports.PlayerEvent.AdStarted,function(e){e=e.ad;o.setUrl(e.clickThroughUrl),i=e.clickThroughUrlOpened});e.on(e.exports.PlayerEvent.AdFinished,n),e.on(e.exports.PlayerEvent.AdSkipped,n),e.on(e.exports.PlayerEvent.AdError,n),this.onClick.subscribe(function(){e.pause("ui-ad-click-overlay"),i&&i()})},s);function s(e){var t=r.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{acceptsTouchWithUiHidden:!0},t.config),t}n.AdClickOverlay=e},{"./clickoverlay":16}],5:[function(e,t,n){"use strict";var o,a,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.AdMessageLabel=void 0,e("./label")),l=e("../stringutils"),c=e("../localization/i18n"),e=(a=r.Label,i(s,a),s.prototype.configure=function(t,e){function n(){t.off(t.exports.PlayerEvent.TimeChanged,s)}var o=this,i=(a.prototype.configure.call(this,t,e),this.getConfig()),r=i.text,s=function(){o.setText(l.StringUtils.replaceAdMessagePlaceholders(c.i18n.performLocalization(r),null,t))};t.on(t.exports.PlayerEvent.AdStarted,function(e){e=e.ad.uiConfig;r=e&&e.message||i.text,s(),t.on(t.exports.PlayerEvent.TimeChanged,s)}),t.on(t.exports.PlayerEvent.AdSkipped,n),t.on(t.exports.PlayerEvent.AdError,n),t.on(t.exports.PlayerEvent.AdFinished,n)},s);function s(e){var t=a.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-label-ad-message",text:c.i18n.getLocalizer("ads.remainingTime")},t.config),t}n.AdMessageLabel=e},{"../localization/i18n":91,"../stringutils":111,"./label":28}],6:[function(e,t,n){"use strict";var o,c,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.AdSkipButton=void 0,e("./button")),u=e("../stringutils"),e=(c=r.Button,i(s,c),s.prototype.configure=function(t,e){function n(){t.off(t.exports.PlayerEvent.TimeChanged,l)}var o=this,i=(c.prototype.configure.call(this,t,e),this.getConfig()),r=i.untilSkippableMessage,s=i.skippableMessage,a=-1,l=function(){o.show(),t.getCurrentTime()<a?(o.setText(u.StringUtils.replaceAdMessagePlaceholders(r,a,t)),o.disable()):(o.setText(s),o.enable())};t.on(t.exports.PlayerEvent.AdStarted,function(e){e=e.ad;a=e.skippableAfter,r=e.uiConfig&&e.uiConfig.untilSkippableMessage||i.untilSkippableMessage,s=e.uiConfig&&e.uiConfig.skippableMessage||i.skippableMessage,"number"==typeof a&&0<=a?(l(),t.on(t.exports.PlayerEvent.TimeChanged,l)):o.hide()}),t.on(t.exports.PlayerEvent.AdSkipped,n),t.on(t.exports.PlayerEvent.AdError,n),t.on(t.exports.PlayerEvent.AdFinished,n),this.onClick.subscribe(function(){t.ads.skip()})},s);function s(e){var t=c.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-button-ad-skip",untilSkippableMessage:"Skip ad in {remainingTime}",skippableMessage:"Skip ad",acceptsTouchWithUiHidden:!0},t.config),t}n.AdSkipButton=e},{"../stringutils":111,"./button":12}],7:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.AirPlayToggleButton=void 0,e("./togglebutton")),a=e("../localization/i18n"),e=(i=s.ToggleButton,r(l,i),l.prototype.configure=function(e,t){var n,o=this;i.prototype.configure.call(this,e,t),e.isAirplayAvailable?(this.onClick.subscribe(function(){e.isAirplayAvailable()?e.showAirplayTargetPicker():console&&console.log("AirPlay unavailable")}),t=function(){e.isAirplayActive()?o.on():o.off()},e.on(e.exports.PlayerEvent.AirplayAvailable,n=function(){e.isAirplayAvailable()?o.show():o.hide()}),e.on(e.exports.PlayerEvent.AirplayChanged,t),n(),t()):this.hide()},l);function l(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-airplaytogglebutton",text:a.i18n.getLocalizer("appleAirplay")},t.config),t}n.AirPlayToggleButton=e},{"../localization/i18n":91,"./togglebutton":74}],8:[function(e,t,n){"use strict";var o,a,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.AudioQualitySelectBox=void 0,e("./selectbox")),l=e("../localization/i18n"),e=(a=r.SelectBox,i(s,a),s.prototype.configure=function(i,e){function t(){var e=i.getAvailableAudioQualities();r.clearItems(),r.addItem("auto",l.i18n.getLocalizer("auto"));for(var t=0,n=e;t<n.length;t++){var o=n[t];r.addItem(o.id,o.label)}s()}var r=this,s=(a.prototype.configure.call(this,i,e),function(){r.selectItem(i.getAudioQuality().id)});this.onItemSelected.subscribe(function(e,t){i.setAudioQuality(t)}),i.on(i.exports.PlayerEvent.AudioChanged,t),i.on(i.exports.PlayerEvent.SourceUnloaded,t),i.on(i.exports.PlayerEvent.PeriodSwitched,t),i.on(i.exports.PlayerEvent.AudioQualityChanged,s),i.exports.PlayerEvent.AudioQualityAdded&&(i.on(i.exports.PlayerEvent.AudioQualityAdded,t),i.on(i.exports.PlayerEvent.AudioQualityRemoved,t)),e.getConfig().events.onUpdated.subscribe(t)},s);function s(e){var t=a.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-audioqualityselectbox"]},t.config),t}n.AudioQualitySelectBox=e},{"../localization/i18n":91,"./selectbox":44}],9:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.AudioTrackListBox=void 0,e("./listbox")),a=e("../audiotrackutils"),e=(i=s.ListBox,r(l,i),l.prototype.configure=function(e,t){i.prototype.configure.call(this,e,t),new a.AudioTrackSwitchHandler(e,this,t)},l);function l(){return null!==i&&i.apply(this,arguments)||this}n.AudioTrackListBox=e},{"../audiotrackutils":2,"./listbox":29}],10:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.AudioTrackSelectBox=void 0,e("./selectbox")),a=e("../audiotrackutils"),e=(i=s.SelectBox,r(l,i),l.prototype.configure=function(e,t){i.prototype.configure.call(this,e,t),new a.AudioTrackSwitchHandler(e,this,t)},l);function l(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-audiotrackselectbox"]},t.config),t}n.AudioTrackSelectBox=e},{"../audiotrackutils":2,"./selectbox":44}],11:[function(e,t,n){"use strict";var o,s,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.BufferingOverlay=void 0,e("./container")),a=e("./component"),l=e("../timeout"),e=(s=r.Container,i(c,s),c.prototype.configure=function(e,t){function n(){r.start()}function o(){r.clear(),i.hide()}var i=this,t=(s.prototype.configure.call(this,e,t),this.getConfig()),r=new l.Timeout(t.showDelayMs,function(){i.show()});e.on(e.exports.PlayerEvent.StallStarted,n),e.on(e.exports.PlayerEvent.StallEnded,o),e.on(e.exports.PlayerEvent.Play,n),e.on(e.exports.PlayerEvent.Playing,o),e.on(e.exports.PlayerEvent.Paused,o),e.on(e.exports.PlayerEvent.Seek,n),e.on(e.exports.PlayerEvent.Seeked,o),e.on(e.exports.PlayerEvent.TimeShift,n),e.on(e.exports.PlayerEvent.TimeShifted,o),e.on(e.exports.PlayerEvent.SourceUnloaded,o),e.isStalled()&&this.show()},c);function c(e){var t=s.call(this,e=void 0===e?{}:e)||this;return t.indicators=[new a.Component({tag:"div",cssClass:"ui-buffering-overlay-indicator",role:"img"}),new a.Component({tag:"div",cssClass:"ui-buffering-overlay-indicator",role:"img"}),new a.Component({tag:"div",cssClass:"ui-buffering-overlay-indicator",role:"img"})],t.config=t.mergeConfig(e,{cssClass:"ui-buffering-overlay",hidden:!0,components:t.indicators,showDelayMs:1e3},t.config),t}n.BufferingOverlay=e},{"../timeout":113,"./component":18,"./container":19}],12:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.Button=void 0,e("./component")),a=e("../dom"),l=e("../eventdispatcher"),c=e("../localization/i18n"),e=(i=s.Component,r(u,i),u.prototype.toDomElement=function(){var e=this,t={id:this.config.id,"aria-label":c.i18n.performLocalization(this.config.ariaLabel||this.config.text),class:this.getCssClasses(),type:"button",tabindex:this.config.tabIndex.toString()},t=(null!=this.config.role&&(t.role=this.config.role),new a.DOM("button",t,this).append(new a.DOM("span",{class:this.prefixCss("label")}).html(c.i18n.performLocalization(this.config.text))));return t.on("click",function(){e.onClickEvent()}),t},u.prototype.setText=function(e){this.getDomElement().find("."+this.prefixCss("label")).html(c.i18n.performLocalization(e))},u.prototype.onClickEvent=function(){this.buttonEvents.onClick.dispatch(this)},Object.defineProperty(u.prototype,"onClick",{get:function(){return this.buttonEvents.onClick.getEvent()},enumerable:!1,configurable:!0}),u);function u(e){var t=i.call(this,e)||this;return t.buttonEvents={onClick:new l.EventDispatcher},t.config=t.mergeConfig(e,{cssClass:"ui-button",role:"button",tabIndex:0,acceptsTouchWithUiHidden:!1},t.config),t}n.Button=e},{"../dom":84,"../eventdispatcher":86,"../localization/i18n":91,"./component":18}],13:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.CastStatusOverlay=void 0,e("./container")),a=e("./label"),l=e("../localization/i18n"),e=(i=s.Container,r(c,i),c.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),e.on(e.exports.PlayerEvent.CastWaitingForDevice,function(e){n.show();e=e.castPayload.deviceName;n.statusLabel.setText(l.i18n.getLocalizer("connectingTo",{castDeviceName:e}))}),e.on(e.exports.PlayerEvent.CastStarted,function(e){n.show();e=e.deviceName;n.statusLabel.setText(l.i18n.getLocalizer("playingOn",{castDeviceName:e}))}),e.on(e.exports.PlayerEvent.CastStopped,function(e){n.hide()})},c);function c(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.statusLabel=new a.Label({cssClass:"ui-cast-status-label"}),t.config=t.mergeConfig(e,{cssClass:"ui-cast-status-overlay",components:[t.statusLabel],hidden:!0},t.config),t}n.CastStatusOverlay=e},{"../localization/i18n":91,"./container":19,"./label":28}],14:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.CastToggleButton=void 0,e("./togglebutton")),a=e("../localization/i18n"),e=(i=s.ToggleButton,r(l,i),l.prototype.configure=function(e,t){function n(){e.isCastAvailable()?o.show():o.hide()}var o=this;i.prototype.configure.call(this,e,t),this.onClick.subscribe(function(){e.isCastAvailable()?e.isCasting()?e.castStop():e.castVideo():console&&console.log("Cast unavailable")});e.on(e.exports.PlayerEvent.CastAvailable,n),e.on(e.exports.PlayerEvent.CastWaitingForDevice,function(){o.on()}),e.on(e.exports.PlayerEvent.CastStarted,function(){o.on()}),e.on(e.exports.PlayerEvent.CastStopped,function(){o.off()}),n(),e.isCasting()&&this.on()},l);function l(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-casttogglebutton",text:a.i18n.getLocalizer("googleCast")},t.config),t}n.CastToggleButton=e},{"../localization/i18n":91,"./togglebutton":74}],15:[function(e,t,n){"use strict";var o,l,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.CastUIContainer=void 0,e("./uicontainer")),c=e("../timeout"),e=(l=r.UIContainer,i(s,l),s.prototype.configure=function(e,t){var n=this,o=(l.prototype.configure.call(this,e,t),this.getConfig()),i=!1,r=(this.castUiHideTimeout=new c.Timeout(o.hideDelay,function(){t.onControlsHide.dispatch(n),i=!1}),function(){i||(t.onControlsShow.dispatch(n),i=!0)}),s=function(){r(),n.castUiHideTimeout.clear()},a=function(){r(),n.castUiHideTimeout.start()};e.on(e.exports.PlayerEvent.Play,a),e.on(e.exports.PlayerEvent.Paused,s),e.on(e.exports.PlayerEvent.Seek,s),e.on(e.exports.PlayerEvent.Seeked,function(){(e.isPlaying()?a:s)()}),t.getConfig().events.onUpdated.subscribe(a)},s.prototype.release=function(){l.prototype.release.call(this),this.castUiHideTimeout.clear()},s);function s(e){return l.call(this,e)||this}n.CastUIContainer=e},{"../timeout":113,"./uicontainer":76}],16:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),e=(Object.defineProperty(n,"__esModule",{value:!0}),n.ClickOverlay=void 0,e("./button")),e=(i=e.Button,r(s,i),s.prototype.initialize=function(){i.prototype.initialize.call(this),this.setUrl(this.config.url);var e=this.getDomElement();e.on("click",function(){e.data("url")&&window.open(e.data("url"),"_blank")})},s.prototype.getUrl=function(){return this.getDomElement().data("url")},s.prototype.setUrl=function(e){void 0!==e&&null!=e||(e=""),this.getDomElement().data("url",e)},s);function s(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-clickoverlay",role:t.config.role},t.config),t}n.ClickOverlay=e},{"./button":12}],17:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.CloseButton=void 0,e("./button")),a=e("../localization/i18n"),e=(i=s.Button,r(l,i),l.prototype.configure=function(e,t){i.prototype.configure.call(this,e,t);var n=this.getConfig();this.onClick.subscribe(function(){n.target.hide()})},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-closebutton",text:a.i18n.getLocalizer("close")},t.config),t}n.CloseButton=e},{"../localization/i18n":91,"./button":12}],18:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Component=n.ViewMode=void 0;var o,i=e("../guid"),r=e("../dom"),s=e("../eventdispatcher"),a=e("../localization/i18n");(e=o=n.ViewMode||(n.ViewMode={})).Persistent="persistent",e.Temporary="temporary",n.Component=(l.prototype.initialize=function(){this.hidden=this.config.hidden,this.disabled=this.config.disabled,this.isHidden()&&(this.hidden=!1,this.hide()),this.isDisabled()&&(this.disabled=!1,this.disable())},l.prototype.configure=function(e,n){var o=this;this.onShow.subscribe(function(){return n.onComponentShow.dispatch(o)}),this.onHide.subscribe(function(){return n.onComponentHide.dispatch(o)}),this.onViewModeChanged.subscribe(function(e,t){return n.onComponentViewModeChanged.dispatch(o,t)}),this.getDomElement().on("mouseenter",function(){return o.onHoverChangedEvent(!0)}),this.getDomElement().on("mouseleave",function(){return o.onHoverChangedEvent(!1)})},l.prototype.release=function(){},l.prototype.toDomElement=function(){return new r.DOM(this.config.tag,{id:this.config.id,class:this.getCssClasses(),role:this.config.role},this)},l.prototype.getDomElement=function(){return this.element||(this.element=this.toDomElement()),this.element},l.prototype.hasDomElement=function(){return Boolean(this.element)},l.prototype.setAriaLabel=function(e){this.setAriaAttr("label",a.i18n.performLocalization(e))},l.prototype.setAriaAttr=function(e,t){this.getDomElement().attr("aria-".concat(e),t)},l.prototype.mergeConfig=function(e,t,n){return Object.assign({},n,t,e)},l.prototype.getCssClasses=function(){var t=this;return[this.config.cssClass].concat(this.config.cssClasses).map(function(e){return t.prefixCss(e)}).join(" ").trim()},l.prototype.prefixCss=function(e){return this.config.cssPrefix+"-"+e},l.prototype.getConfig=function(){return this.config},l.prototype.hide=function(){this.hidden||(this.hidden=!0,this.getDomElement().addClass(this.prefixCss(l.CLASS_HIDDEN)),this.onHideEvent())},l.prototype.show=function(){this.hidden&&(this.getDomElement().removeClass(this.prefixCss(l.CLASS_HIDDEN)),this.hidden=!1,this.onShowEvent())},l.prototype.isHidden=function(){return this.hidden},l.prototype.isShown=function(){return!this.isHidden()},l.prototype.toggleHidden=function(){this.isHidden()?this.show():this.hide()},l.prototype.disable=function(){this.disabled||(this.disabled=!0,this.getDomElement().addClass(this.prefixCss(l.CLASS_DISABLED)),this.onDisabledEvent())},l.prototype.enable=function(){this.disabled&&(this.getDomElement().removeClass(this.prefixCss(l.CLASS_DISABLED)),this.disabled=!1,this.onEnabledEvent())},l.prototype.isDisabled=function(){return this.disabled},l.prototype.isEnabled=function(){return!this.isDisabled()},l.prototype.isHovered=function(){return this.hovered},l.prototype.onShowEvent=function(){this.componentEvents.onShow.dispatch(this)},l.prototype.onHideEvent=function(){this.componentEvents.onHide.dispatch(this)},l.prototype.onEnabledEvent=function(){this.componentEvents.onEnabled.dispatch(this)},l.prototype.onDisabledEvent=function(){this.componentEvents.onDisabled.dispatch(this)},l.prototype.onViewModeChangedEvent=function(e){this.viewMode!==e&&(this.viewMode=e,this.componentEvents.onViewModeChanged.dispatch(this,{mode:e}))},l.prototype.onHoverChangedEvent=function(e){this.hovered=e,this.componentEvents.onHoverChanged.dispatch(this,{hovered:e})},Object.defineProperty(l.prototype,"onShow",{get:function(){return this.componentEvents.onShow.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onHide",{get:function(){return this.componentEvents.onHide.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onEnabled",{get:function(){return this.componentEvents.onEnabled.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onDisabled",{get:function(){return this.componentEvents.onDisabled.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onHoverChanged",{get:function(){return this.componentEvents.onHoverChanged.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onViewModeChanged",{get:function(){return this.componentEvents.onViewModeChanged.getEvent()},enumerable:!1,configurable:!0}),l.CLASS_HIDDEN="hidden",l.CLASS_DISABLED="disabled",l);function l(e){void 0===e&&(e={}),this.componentEvents={onShow:new s.EventDispatcher,onHide:new s.EventDispatcher,onViewModeChanged:new s.EventDispatcher,onHoverChanged:new s.EventDispatcher,onEnabled:new s.EventDispatcher,onDisabled:new s.EventDispatcher},this.config=this.mergeConfig(e,{tag:"div",id:"bmpui-id-"+i.Guid.next(),cssPrefix:"bmpui",cssClass:"ui-component",cssClasses:[],hidden:!1,disabled:!1},{}),this.viewMode=o.Temporary}},{"../dom":84,"../eventdispatcher":86,"../guid":89,"../localization/i18n":91}],19:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.Container=void 0,e("./component")),a=e("../dom"),l=e("../arrayutils"),c=e("../localization/i18n"),e=(i=s.Component,r(u,i),u.prototype.addComponent=function(e){this.config.components.push(e),this.componentsToAdd.push(e)},u.prototype.removeComponent=function(e){return null!=l.ArrayUtils.remove(this.config.components,e)&&(this.componentsToRemove.push(e),!0)},u.prototype.getComponents=function(){return this.config.components},u.prototype.removeComponents=function(){for(var e=0,t=this.getComponents().slice();e<t.length;e++){var n=t[e];this.removeComponent(n)}},u.prototype.updateComponents=function(){for(var e;void 0!==(e=this.componentsToRemove.shift());)e.getDomElement().remove();for(;void 0!==(e=this.componentsToAdd.shift());)this.innerContainerElement.append(e.getDomElement())},u.prototype.toDomElement=function(){var e=new a.DOM(this.config.tag,{id:this.config.id,class:this.getCssClasses(),role:this.config.role,"aria-label":c.i18n.performLocalization(this.config.ariaLabel)},this),t=new a.DOM(this.config.tag,{class:this.prefixCss("container-wrapper")});this.innerContainerElement=t;for(var n=0,o=this.config.components;n<o.length;n++){var i=o[n];this.componentsToAdd.push(i)}return this.updateComponents(),e.append(t),e},u.prototype.suspendHideTimeout=function(){},u.prototype.resumeHideTimeout=function(){},u.prototype.trackComponentViewMode=function(e){e===s.ViewMode.Persistent?this.componentsInPersistentViewMode++:e===s.ViewMode.Temporary&&(this.componentsInPersistentViewMode=Math.max(this.componentsInPersistentViewMode-1,0)),0<this.componentsInPersistentViewMode?this.suspendHideTimeout():this.resumeHideTimeout()},u);function u(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-container",components:[]},t.config),t.componentsToAdd=[],t.componentsToRemove=[],t.componentsInPersistentViewMode=0,t}n.Container=e},{"../arrayutils":1,"../dom":84,"../localization/i18n":91,"./component":18}],20:[function(e,t,n){"use strict";var o,r,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.ControlBar=void 0,e("./container")),a=e("../uiutils"),l=e("./spacer"),c=e("../localization/i18n"),u=e("../browserutils"),p=e("./settingspanel"),e=(r=s.Container,i(g,r),g.prototype.configure=function(e,t){var n=this,o=(r.prototype.configure.call(this,e,t),0),i=!1;t.getConfig().disableAutoHideWhenHovered&&!u.BrowserUtils.isTouchSupported&&a.UIUtils.traverseTree(this,function(e){e instanceof s.Container||e instanceof l.Spacer||e.onHoverChanged.subscribe(function(e,t){t.hovered?o++:o--})}),u.BrowserUtils.isMobile&&(t.onComponentShow.subscribe(function(e){e instanceof p.SettingsPanel&&(i=!0)}),t.onComponentHide.subscribe(function(e){e instanceof p.SettingsPanel&&(i=!1)})),t.onControlsShow.subscribe(function(){n.show()}),t.onPreviewControlsHide.subscribe(function(e,t){t.cancel=t.cancel||0<o||i}),t.onControlsHide.subscribe(function(){n.hide()})},g);function g(e){var t=r.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-controlbar",hidden:!0,role:"region",ariaLabel:c.i18n.getLocalizer("controlBar")},t.config),t}n.ControlBar=e},{"../browserutils":3,"../localization/i18n":91,"../uiutils":117,"./container":19,"./settingspanel":45,"./spacer":52}],21:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.EcoModeContainer=void 0,e("../localization/i18n")),a=e("./container"),l=e("./ecomodetogglebutton"),c=e("./label"),u=e("./settingspanelitem"),e=(i=a.Container,r(p,i),p.prototype.setOnToggleCallback=function(e){this.onToggleCallback=e},p.prototype.configure=function(l){var c=this;l.on(l.exports.PlayerEvent.SegmentPlayback,function(e){var t,n,o,i,r,s,a;e.mimeType.includes("video")&&(s=(n=e.mediaInfo).height,a=n.width,t=n.bitrate,n=n.frameRate,o=(r=c.getMaxQualityAvailable(l.getAvailableVideoQualities())).height,i=r.bitrate,r=r.width,s=c.calculateEnergyConsumption(n,s,a,t,e.duration),a=c.calculateEnergyConsumption(n,o,r,i,e.duration),c.ecoModeSavedEmissionsItem.isShown())&&c.updateSavedEmissions(s,a,c.emissionsSavedLabel)})},p.prototype.updateSavedEmissions=function(e,t,n){this.currentEnergyEmission=475*e;e=475*t;this.savedEmissons+=e-this.currentEnergyEmission,n.setText(this.savedEmissons.toFixed(4)+" gCO2")},p.prototype.calculateEnergyConsumption=function(e,t,n,o,i){return(.035*e+5.76e-9*t*n+o/1e3*(697e-8+324e-7)+4.16+8.52+1.15)*i/36e5},p.prototype.getMaxQualityAvailable=function(e){e=e.sort(function(e,t){return e.bitrate-t.bitrate});return e[e.length-1]},p);function p(e){var t=i.call(this,e=void 0===e?{}:e)||this,e=(t.savedEmissons=0,new l.EcoModeToggleButton),n=new c.Label({text:s.i18n.getLocalizer("ecoMode.title"),for:e.getConfig().id,id:"ecomodelabel"});return t.emissionsSavedLabel=new c.Label({text:"".concat(t.savedEmissons.toFixed(4)," gCO2"),cssClass:"ui-label-savedEnergy"}),t.ecoModeToggleButtonItem=new u.SettingsPanelItem(n,e),t.ecoModeSavedEmissionsItem=new u.SettingsPanelItem("Saved Emissions",t.emissionsSavedLabel,{hidden:!0}),t.addComponent(t.ecoModeToggleButtonItem),t.addComponent(t.ecoModeSavedEmissionsItem),e.onToggleOn.subscribe(function(){t.ecoModeSavedEmissionsItem.show(),t.onToggleCallback()}),e.onToggleOff.subscribe(function(){t.ecoModeSavedEmissionsItem.hide(),t.onToggleCallback()}),t}n.EcoModeContainer=e},{"../localization/i18n":91,"./container":19,"./ecomodetogglebutton":22,"./label":28,"./settingspanelitem":46}],22:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.EcoModeToggleButton=void 0,e("./togglebutton")),a=e("../localization/i18n"),e=(i=s.ToggleButton,r(l,i),l.prototype.configure=function(t,e){var n=this;i.prototype.configure.call(this,t,e),this.areAdaptationApisAvailable(t)?(this.onClick.subscribe(function(){n.toggle()}),this.onToggleOn.subscribe(function(){n.enableEcoMode(t),t.setVideoQuality("auto")}),this.onToggleOff.subscribe(function(){n.disableEcoMode(t)}),t.on(t.exports.PlayerEvent.VideoQualityChanged,function(e){"auto"!==e.targetQuality.id&&(n.off(),n.disableEcoMode(t))})):i.prototype.disable.call(this)},l.prototype.areAdaptationApisAvailable=function(e){var t=Boolean(e.adaptation.getConfig&&"function"==typeof e.adaptation.getConfig),n=Boolean(e.adaptation.setConfig&&"function"==typeof e.adaptation.setConfig);return Boolean(e.adaptation&&t&&n)},l.prototype.enableEcoMode=function(e){this.adaptationConfig=e.adaptation.getConfig();var t=e.getAvailableVideoQualities()[0].codec;t.includes("avc")&&e.adaptation.setConfig({resolution:{maxSelectableVideoHeight:720},limitToPlayerSize:!0}),(t.includes("hvc")||t.includes("hev"))&&e.adaptation.setConfig({resolution:{maxSelectableVideoHeight:1080},limitToPlayerSize:!0}),(t.includes("av1")||t.includes("av01"))&&e.adaptation.setConfig({resolution:{maxSelectableVideoHeight:1440},limitToPlayerSize:!0})},l.prototype.disableEcoMode=function(e){e.adaptation.setConfig(this.adaptationConfig)},l);function l(e){var t=i.call(this,e=void 0===e?{}:e)||this,n={text:a.i18n.getLocalizer("ecoMode"),cssClass:"ui-ecomodetogglebutton",onClass:"on",offClass:"off",ariaLabel:a.i18n.getLocalizer("ecoMode")};return t.config=t.mergeConfig(e,n,t.config),t}n.EcoModeToggleButton=e},{"../localization/i18n":91,"./togglebutton":74}],23:[function(e,t,n){"use strict";var o,s,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.ErrorMessageOverlay=void 0,e("./container")),a=e("./label"),l=e("./tvnoisecanvas"),c=e("../errorutils"),u=e("../mobilev3playerapi"),e=(s=r.Container,i(p,s),p.prototype.configure=function(e,n){var t,o=this,i=(s.prototype.configure.call(this,e,n),this.getConfig()),r=function(e,t){e=function(e,t){if(!e)return;if("function"==typeof e)return e(t);if(e[t.code])return"string"==typeof(e=e[t.code])?e:e(t)}(n.getConfig().errorMessages||i.messages,e);o.display(t=e?e:t)};(0,u.isMobileV3PlayerAPI)(e)?(e.on(u.MobileV3PlayerEvent.PlayerError,t=function(e){var t=c.ErrorUtils.defaultMobileV3ErrorMessageTranslator(e);r(e,t)}),e.on(u.MobileV3PlayerEvent.SourceError,t)):e.on(e.exports.PlayerEvent.Error,function(e){var t=c.ErrorUtils.defaultWebErrorMessageTranslator(e);r(e,t)}),e.on(e.exports.PlayerEvent.SourceLoaded,function(e){o.isShown()&&o.clear()})},p.prototype.display=function(e){this.errorLabel.setText(e),this.tvNoiseBackground.start(),this.show()},p.prototype.clear=function(){this.errorLabel.setText(""),this.tvNoiseBackground.stop(),this.hide()},p.prototype.release=function(){s.prototype.release.call(this),this.clear()},p);function p(e){var t=s.call(this,e=void 0===e?{}:e)||this;return t.errorLabel=new a.Label({cssClass:"ui-errormessage-label"}),t.tvNoiseBackground=new l.TvNoiseCanvas,t.config=t.mergeConfig(e,{cssClass:"ui-errormessage-overlay",components:[t.tvNoiseBackground,t.errorLabel],hidden:!0,role:"status"},t.config),t}n.ErrorMessageOverlay=e},{"../errorutils":85,"../mobilev3playerapi":97,"./container":19,"./label":28,"./tvnoisecanvas":75}],24:[function(e,t,n){"use strict";var o,s,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.FullscreenToggleButton=void 0,e("./togglebutton")),a=e("../localization/i18n"),e=(s=r.ToggleButton,i(l,s),l.prototype.configure=function(t,e){function n(){t.getViewMode()===t.exports.ViewMode.Fullscreen?i.on():i.off()}function o(){r()?i.show():i.hide()}var i=this,r=(s.prototype.configure.call(this,t,e),function(){return t.isViewModeAvailable(t.exports.ViewMode.Fullscreen)});t.on(t.exports.PlayerEvent.ViewModeChanged,n),t.exports.PlayerEvent.ViewModeAvailabilityChanged&&t.on(t.exports.PlayerEvent.ViewModeAvailabilityChanged,o),e.getConfig().events.onUpdated.subscribe(o),this.onClick.subscribe(function(){var e;r()?(e=t.getViewMode()===t.exports.ViewMode.Fullscreen?t.exports.ViewMode.Inline:t.exports.ViewMode.Fullscreen,t.setViewMode(e)):console&&console.log("Fullscreen unavailable")}),o(),n()},l);function l(e){var t=s.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-fullscreentogglebutton",text:a.i18n.getLocalizer("fullscreen")},t.config),t}n.FullscreenToggleButton=e},{"../localization/i18n":91,"./togglebutton":74}],25:[function(e,t,n){"use strict";var o,c,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.HugePlaybackToggleButton=void 0,e("./playbacktogglebutton")),s=e("../dom"),a=e("../localization/i18n"),e=(c=r.PlaybackToggleButton,i(l,c),l.prototype.configure=function(t,e){function n(){t.isPlaying()||r.isPlayInitiated?t.pause("ui"):t.play("ui")}function o(){t.getViewMode()===t.exports.ViewMode.Fullscreen?t.setViewMode(t.exports.ViewMode.Inline):t.setViewMode(t.exports.ViewMode.Fullscreen)}function i(){r.setTransitionAnimationsEnabled(!1),r.onToggle.subscribeOnce(function(){r.setTransitionAnimationsEnabled(!0)})}var r=this,s=(c.prototype.configure.call(this,t,e,!1),"boolean"==typeof e.getConfig().enterFullscreenOnInitialPlayback&&(this.config.enterFullscreenOnInitialPlayback=e.getConfig().enterFullscreenOnInitialPlayback),!0),a=0,l=0,e=(this.onClick.subscribe(function(){var e;s?(n(),r.config.enterFullscreenOnInitialPlayback&&t.setViewMode(t.exports.ViewMode.Fullscreen)):(e=Date.now())-a<200?(o(),l=e):e-a<500?(o(),n(),l=e):(a=e,setTimeout(function(){200<Date.now()-l&&n()},200))}),t.on(t.exports.PlayerEvent.Play,function(){s=!1}),t.on(t.exports.PlayerEvent.Warning,function(e){e.code===t.exports.WarningCode.PLAYBACK_COULD_NOT_BE_STARTED&&(s=!0)}),i(),t.getConfig().playback&&Boolean(t.getConfig().playback.autoplay)),e=!t.getSource()&&e;(t.isPlaying()||e)&&(this.on(),i(),t.on(t.exports.PlayerEvent.Warning,function(e){e.code===t.exports.WarningCode.PLAYBACK_COULD_NOT_BE_STARTED&&i()}))},l.prototype.toDomElement=function(){var e=c.prototype.toDomElement.call(this);return e.append(new s.DOM("div",{class:this.prefixCss("image")})),e},l.prototype.setTransitionAnimationsEnabled=function(e){var t=this.prefixCss("no-transition-animations");e?this.getDomElement().removeClass(t):this.getDomElement().hasClass(t)||this.getDomElement().addClass(t)},l);function l(e){var t=c.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-hugeplaybacktogglebutton",text:a.i18n.getLocalizer("playPause"),role:"button"},t.config),t}n.HugePlaybackToggleButton=e},{"../dom":84,"../localization/i18n":91,"./playbacktogglebutton":35}],26:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.HugeReplayButton=void 0,e("./button")),a=e("../dom"),l=e("../localization/i18n"),e=(i=s.Button,r(c,i),c.prototype.configure=function(e,t){i.prototype.configure.call(this,e,t),this.onClick.subscribe(function(){e.play("ui")})},c.prototype.toDomElement=function(){var e=i.prototype.toDomElement.call(this);return e.append(new a.DOM("div",{class:this.prefixCss("image")})),e},c);function c(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-hugereplaybutton",text:l.i18n.getLocalizer("replay")},t.config),t}n.HugeReplayButton=e},{"../dom":84,"../localization/i18n":91,"./button":12}],27:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.ItemSelectionList=void 0,e("./listselector")),l=e("../dom"),c=e("../localization/i18n");n.ItemSelectionList=(i=s.ListSelector,r(u,i),u.prototype.isActive=function(){return 1<this.items.length},u.prototype.toDomElement=function(){var e=new l.DOM("ul",{id:this.config.id,class:this.getCssClasses()},this);return this.listElement=e,this.updateDomItems(),e},u.prototype.updateDomItems=function(n){for(var o=this,i=(void 0===n&&(n=null),this.listElement.empty(),null),r=function(e){e.addClass(o.prefixCss(u.CLASS_SELECTED))},s=function(e){e.removeClass(o.prefixCss(u.CLASS_SELECTED))},a=this,e=0,t=this.items;e<t.length;e++)!function(e){var t=new l.DOM("li",{type:"li",class:a.prefixCss("ui-selectionlistitem")}).append(new l.DOM("a",{}).html(c.i18n.performLocalization(e.label)));i||null!=n&&String(n)!==e.key||(i=t),t.on("click",function(){i&&s(i),r(i=t),o.onItemSelectedEvent(e.key,!1)}),i&&r(i),a.listElement.append(t)}(t[e])},u.prototype.onItemAddedEvent=function(e){i.prototype.onItemAddedEvent.call(this,e),this.updateDomItems(this.selectedItem)},u.prototype.onItemRemovedEvent=function(e){i.prototype.onItemRemovedEvent.call(this,e),this.updateDomItems(this.selectedItem)},u.prototype.onItemSelectedEvent=function(e,t){void 0===t&&(t=!0),i.prototype.onItemSelectedEvent.call(this,e),t&&this.updateDomItems(e)},u.CLASS_SELECTED="selected",u);function u(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{tag:"ul",cssClass:"ui-itemselectionlist"},t.config),t}},{"../dom":84,"../localization/i18n":91,"./listselector":30}],28:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.Label=void 0,e("./component")),a=e("../dom"),l=e("../eventdispatcher"),c=e("../localization/i18n"),e=(i=s.Component,r(u,i),u.prototype.toDomElement=function(){var e=this,t=null!=this.config.for?"label":"span",t=new a.DOM(t,{id:this.config.id,for:this.config.for,class:this.getCssClasses()},this).html(c.i18n.performLocalization(this.text));return t.on("click",function(){e.onClickEvent()}),t},u.prototype.setText=function(e){e!==this.text&&(this.text=e,e=c.i18n.performLocalization(e),this.getDomElement().html(e),this.onTextChangedEvent(e))},u.prototype.getText=function(){return c.i18n.performLocalization(this.text)},u.prototype.clearText=function(){this.getDomElement().html(""),this.onTextChangedEvent(null)},u.prototype.isEmpty=function(){return!this.text},u.prototype.onClickEvent=function(){this.labelEvents.onClick.dispatch(this)},u.prototype.onTextChangedEvent=function(e){this.labelEvents.onTextChanged.dispatch(this,e)},Object.defineProperty(u.prototype,"onClick",{get:function(){return this.labelEvents.onClick.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onTextChanged",{get:function(){return this.labelEvents.onTextChanged.getEvent()},enumerable:!1,configurable:!0}),u);function u(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.labelEvents={onClick:new l.EventDispatcher,onTextChanged:new l.EventDispatcher},t.config=t.mergeConfig(e,{cssClass:"ui-label"},t.config),t.text=t.config.text,t}n.Label=e},{"../dom":84,"../eventdispatcher":86,"../localization/i18n":91,"./component":18}],29:[function(e,t,n){"use strict";var o,r,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.ListBox=void 0,e("./togglebutton")),a=e("./listselector"),l=e("../dom"),c=e("../arrayutils"),e=(r=a.ListSelector,i(u,r),u.prototype.configure=function(e,t){this.onItemAdded.subscribe(this.addListBoxDomItem),this.onItemRemoved.subscribe(this.removeListBoxDomItem),this.onItemSelected.subscribe(this.refreshSelectedItem),r.prototype.configure.call(this,e,t)},u.prototype.toDomElement=function(){var e=new l.DOM("div",{id:this.config.id,class:this.getCssClasses()},this);return this.listBoxElement=e,this.createListBoxDomItems(),this.refreshSelectedItem(),e},u.prototype.createListBoxDomItems=function(){this.listBoxElement.empty(),this.components=[];for(var e=0,t=this.items;e<t.length;e++){var n=t[e];this.addListBoxDomItem(this,n.key)}},u.prototype.buildListBoxItemButton=function(e){return new g({key:e.key,text:e.label,ariaLabel:e.ariaLabel})},u.prototype.getComponentForKey=function(t){return this.components.find(function(e){return t===e.key})},u);function u(e){var i=r.call(this,e=void 0===e?{}:e)||this;return i.components=[],i.removeListBoxDomItem=function(e,t){t=i.getComponentForKey(t);t&&(t.getDomElement().remove(),c.ArrayUtils.remove(i.components,t))},i.addListBoxDomItem=function(e,t){var n,o=i.getComponentForKey(t),t=i.getItemForKey(t);o?o.setText(t.label):((n=i.buildListBoxItemButton(t)).onClick.subscribe(function(){i.handleSelectionChange(n)}),i.components.push(n),i.listBoxElement.append(n.getDomElement()))},i.refreshSelectedItem=function(){for(var e=0,t=i.items;e<t.length;e++){var n=t[e],n=i.getComponentForKey(n.key);n&&(String(n.key)===String(i.selectedItem)?n.on():n.off())}},i.handleSelectionChange=function(e){i.onItemSelectedEvent(e.key)},i.config=i.mergeConfig(e,{cssClass:"ui-listbox"},i.config),i}n.ListBox=e;p=s.ToggleButton,i(f,p),Object.defineProperty(f.prototype,"key",{get:function(){return this.config.key},enumerable:!1,configurable:!0});var p,g=f;function f(e){var t=p.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-listbox-button",onClass:"selected",offClass:""},t.config),t}},{"../arrayutils":1,"../dom":84,"./listselector":30,"./togglebutton":74}],30:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=this&&this.__assign||function(){return(s=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},a=(Object.defineProperty(n,"__esModule",{value:!0}),n.ListSelector=void 0,e("./component")),l=e("../eventdispatcher"),c=e("../arrayutils"),u=e("../localization/i18n"),e=(i=a.Component,r(p,i),p.prototype.getItemIndex=function(e){for(var t=0;t<this.items.length;t++)if(this.items[t].key===e)return t;return-1},p.prototype.getItems=function(){return this.items},p.prototype.hasItem=function(e){return-1<this.getItemIndex(e)},p.prototype.addItem=function(t,e,n,o){void 0===n&&(n=!1),void 0===o&&(o="");e=s({key:t,label:u.i18n.performLocalization(e)},o&&{ariaLabel:o});this.config.filter&&!this.config.filter(e)||(this.config.translator&&(e.label=this.config.translator(e)),this.removeItem(t),!n||(o=this.items.findIndex(function(e){return e.key>t}))<0?this.items.push(e):this.items.splice(o,0,e),this.onItemAddedEvent(t))},p.prototype.removeItem=function(e){var t=this.getItemIndex(e);return-1<t&&(c.ArrayUtils.remove(this.items,this.items[t]),this.onItemRemovedEvent(e),!0)},p.prototype.selectItem=function(e){return e===this.selectedItem||-1<this.getItemIndex(e)&&(this.selectedItem=e,this.onItemSelectedEvent(e),!0)},p.prototype.getSelectedItem=function(){return this.selectedItem},p.prototype.getItemForKey=function(t){return this.items.find(function(e){return e.key===t})},p.prototype.synchronizeItems=function(e){var t=this;e.filter(function(e){return!t.hasItem(e.key)}).forEach(function(e){return t.addItem(e.key,e.label,e.sortedInsert,e.ariaLabel)}),this.items.filter(function(t){return 0===e.filter(function(e){return e.key===t.key}).length}).forEach(function(e){return t.removeItem(e.key)})},p.prototype.clearItems=function(){var e=this.items;this.items=[],this.selectedItem=null;for(var t=0,n=e;t<n.length;t++){var o=n[t];this.onItemRemovedEvent(o.key)}},p.prototype.itemCount=function(){return Object.keys(this.items).length},p.prototype.onItemAddedEvent=function(e){this.listSelectorEvents.onItemAdded.dispatch(this,e)},p.prototype.onItemRemovedEvent=function(e){this.listSelectorEvents.onItemRemoved.dispatch(this,e)},p.prototype.onItemSelectedEvent=function(e){this.listSelectorEvents.onItemSelected.dispatch(this,e)},Object.defineProperty(p.prototype,"onItemAdded",{get:function(){return this.listSelectorEvents.onItemAdded.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"onItemRemoved",{get:function(){return this.listSelectorEvents.onItemRemoved.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"onItemSelected",{get:function(){return this.listSelectorEvents.onItemSelected.getEvent()},enumerable:!1,configurable:!0}),p);function p(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.listSelectorEvents={onItemAdded:new l.EventDispatcher,onItemRemoved:new l.EventDispatcher,onItemSelected:new l.EventDispatcher},t.config=t.mergeConfig(e,{items:[],cssClass:"ui-listselector"},t.config),t.items=t.config.items,t}n.ListSelector=e},{"../arrayutils":1,"../eventdispatcher":86,"../localization/i18n":91,"./component":18}],31:[function(e,t,n){"use strict";var o,s,a,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),e=(Object.defineProperty(n,"__esModule",{value:!0}),n.MetadataLabel=n.MetadataLabelContent=void 0,e("./label")),r=((r=s=n.MetadataLabelContent||(n.MetadataLabelContent={}))[r.Title=0]="Title",r[r.Description=1]="Description",a=e.Label,i(l,a),l.prototype.configure=function(e,t){function n(){switch(i.content){case s.Title:o.setText(r.metadata.title);break;case s.Description:o.setText(r.metadata.description)}}var o=this,i=(a.prototype.configure.call(this,e,t),this.getConfig()),r=t.getConfig();n(),e.on(e.exports.PlayerEvent.SourceUnloaded,function(){o.setText(null)}),t.getConfig().events.onUpdated.subscribe(n)},l);function l(e){var t=a.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["label-metadata","label-metadata-"+s[e.content].toLowerCase()]},t.config),t}n.MetadataLabel=r},{"./label":28}],32:[function(e,t,n){"use strict";var o,s,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.PictureInPictureToggleButton=void 0,e("./togglebutton")),a=e("../localization/i18n"),e=(s=r.ToggleButton,i(l,s),l.prototype.configure=function(t,e){function n(){t.getViewMode()===t.exports.ViewMode.PictureInPicture?i.on():i.off()}function o(){r()?i.show():i.hide()}var i=this,r=(s.prototype.configure.call(this,t,e),function(){return t.isViewModeAvailable(t.exports.ViewMode.PictureInPicture)});t.on(t.exports.PlayerEvent.ViewModeChanged,n),t.exports.PlayerEvent.ViewModeAvailabilityChanged&&t.on(t.exports.PlayerEvent.ViewModeAvailabilityChanged,o),e.getConfig().events.onUpdated.subscribe(o),this.onClick.subscribe(function(){var e;r()?(e=t.getViewMode()===t.exports.ViewMode.PictureInPicture?t.exports.ViewMode.Inline:t.exports.ViewMode.PictureInPicture,t.setViewMode(e)):console&&console.log("PIP unavailable")}),o(),n()},l);function l(e){var t=s.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-piptogglebutton",text:a.i18n.getLocalizer("pictureInPicture")},t.config),t}n.PictureInPictureToggleButton=e},{"../localization/i18n":91,"./togglebutton":74}],33:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.PlaybackSpeedSelectBox=void 0,e("./selectbox")),a=e("../localization/i18n"),e=(i=s.SelectBox,r(l,i),l.prototype.configure=function(n,e){function t(){var e=n.getPlaybackSpeed();o.setSpeed(e)}var o=this;i.prototype.configure.call(this,n,e),this.addDefaultItems(),this.onItemSelected.subscribe(function(e,t){n.setPlaybackSpeed(parseFloat(t)),o.selectItem(t)});n.on(n.exports.PlayerEvent.PlaybackSpeedChanged,t),e.getConfig().events.onUpdated.subscribe(t)},l.prototype.setSpeed=function(e){this.selectItem(String(e))||(this.clearItems(),this.addDefaultItems([e]),this.selectItem(String(e)))},l.prototype.addDefaultItems=function(e){var t=this;this.defaultPlaybackSpeeds.concat(e=void 0===e?[]:e).sort().forEach(function(e){1!==e?t.addItem(String(e),"".concat(e,"x")):t.addItem(String(e),a.i18n.getLocalizer("normal"))})},l.prototype.clearItems=function(){this.items=[],this.selectedItem=null},l);function l(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.defaultPlaybackSpeeds=[.25,.5,1,1.5,2],t.config=t.mergeConfig(e,{cssClasses:["ui-playbackspeedselectbox"]},t.config),t}n.PlaybackSpeedSelectBox=e},{"../localization/i18n":91,"./selectbox":44}],34:[function(e,t,n){"use strict";var o,i,d,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.PlaybackTimeLabel=n.PlaybackTimeLabelMode=void 0,e("./label")),h=e("../playerutils"),y=e("../stringutils"),m=e("../localization/i18n"),e=((e=i=n.PlaybackTimeLabelMode||(n.PlaybackTimeLabelMode={}))[e.CurrentTime=0]="CurrentTime",e[e.TotalTime=1]="TotalTime",e[e.CurrentAndTotalTime=2]="CurrentAndTotalTime",e[e.RemainingTime=3]="RemainingTime",d=s.Label,r(a,d),a.prototype.configure=function(n,e){function t(){l=0,o.getDomElement().css({"min-width":null}),g()}var o=this,i=(d.prototype.configure.call(this,n,e),this.getConfig()),r=!1,s=this.prefixCss("ui-playbacktimelabel-live"),a=this.prefixCss("ui-playbacktimelabel-live-edge"),l=0,c=function(){n.timeShift(0)},u=function(){var e,t;r&&(e=n.getTimeShift()<0,t=n.getMaxTimeShift()<0,e||n.isPaused()&&t?o.getDomElement().removeClass(a):o.getDomElement().addClass(a))},p=function(){r||n.getDuration()===1/0||o.setTime(h.PlayerUtils.getCurrentTimeRelativeToSeekableRange(n),n.getDuration());var e=o.getDomElement().width();l<e&&(l=e,o.getDomElement().css({"min-width":l+"px"}))},g=function(){o.timeFormat=3600<=Math.abs(n.isLive()?n.getMaxTimeShift():n.getDuration())?y.StringUtils.FORMAT_HHMMSS:y.StringUtils.FORMAT_MMSS,p()},f=new h.PlayerUtils.LiveStreamDetector(n,e);f.onLiveChanged.subscribe(function(e,t){r=t.live,p(),g(),(r=n.isLive())?(o.getDomElement().addClass(s),o.setText(m.i18n.getLocalizer("live")),i.hideInLivePlayback&&o.hide(),o.onClick.subscribe(c),u()):(o.getDomElement().removeClass(s),o.getDomElement().removeClass(a),o.show(),o.onClick.unsubscribe(c))}),f.detect(),n.on(n.exports.PlayerEvent.TimeChanged,p),n.on(n.exports.PlayerEvent.Ready,g),n.on(n.exports.PlayerEvent.Seeked,p),n.on(n.exports.PlayerEvent.TimeShift,u),n.on(n.exports.PlayerEvent.TimeShifted,u),n.on(n.exports.PlayerEvent.Playing,u),n.on(n.exports.PlayerEvent.Paused,u),n.on(n.exports.PlayerEvent.StallStarted,u),n.on(n.exports.PlayerEvent.StallEnded,u);e.getConfig().events.onUpdated.subscribe(t),t()},a.prototype.setTime=function(e,t){var n=y.StringUtils.secondsToTime(e,this.timeFormat),o=y.StringUtils.secondsToTime(t,this.timeFormat);switch(this.config.timeLabelMode){case i.CurrentTime:this.setText("".concat(n));break;case i.TotalTime:this.setText("".concat(o));break;case i.CurrentAndTotalTime:this.setText("".concat(n," / ").concat(o));break;case i.RemainingTime:this.setText("".concat(y.StringUtils.secondsToTime(t-e,this.timeFormat)))}},a.prototype.setTimeFormat=function(e){this.timeFormat=e},a);function a(e){var t=d.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-playbacktimelabel",timeLabelMode:i.CurrentAndTotalTime,hideInLivePlayback:!1},t.config),t}n.PlaybackTimeLabel=e},{"../localization/i18n":91,"../playerutils":98,"../stringutils":111,"./label":28}],35:[function(e,t,n){"use strict";var o,u,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.PlaybackToggleButton=void 0,e("./togglebutton")),p=e("../playerutils"),s=e("../localization/i18n");n.PlaybackToggleButton=(u=r.ToggleButton,i(g,u),g.prototype.configure=function(t,e,n){function o(){s||(t.isPlaying()||r.isPlayInitiated?r.on():r.off())}function i(){t.isLive()&&!p.PlayerUtils.isTimeShiftAvailable(t)?r.getDomElement().addClass(r.prefixCss(g.CLASS_STOPTOGGLE)):r.getDomElement().removeClass(r.prefixCss(g.CLASS_STOPTOGGLE))}var r=this,s=(void 0===n&&(n=!0),u.prototype.configure.call(this,t,e),"boolean"==typeof e.getConfig().enterFullscreenOnInitialPlayback&&(this.config.enterFullscreenOnInitialPlayback=e.getConfig().enterFullscreenOnInitialPlayback),!1),a=!0,l=(t.on(t.exports.PlayerEvent.Play,function(e){r.isPlayInitiated=!0,a=!1,o()}),t.on(t.exports.PlayerEvent.Paused,function(e){r.isPlayInitiated=!1,o()}),t.on(t.exports.PlayerEvent.Playing,function(e){r.isPlayInitiated=!1,o()}),t.on(t.exports.PlayerEvent.SourceLoaded,o),e.getConfig().events.onUpdated.subscribe(o),t.on(t.exports.PlayerEvent.SourceUnloaded,o),t.on(t.exports.PlayerEvent.PlaybackFinished,o),t.on(t.exports.PlayerEvent.CastStarted,o),t.on(t.exports.PlayerEvent.Warning,function(e){e.code===t.exports.WarningCode.PLAYBACK_COULD_NOT_BE_STARTED&&(r.isPlayInitiated=!1,a=!0,r.off())}),new p.PlayerUtils.TimeShiftAvailabilityDetector(t)),c=new p.PlayerUtils.LiveStreamDetector(t,e);l.onTimeShiftAvailabilityChanged.subscribe(i),c.onLiveChanged.subscribe(i),l.detect(),c.detect(),n&&this.onClick.subscribe(function(){t.isPlaying()||r.isPlayInitiated?t.pause("ui"):(t.play("ui"),a&&r.config.enterFullscreenOnInitialPlayback&&t.setViewMode(t.exports.ViewMode.Fullscreen))}),e.onSeek.subscribe(function(){s=!0}),e.onSeeked.subscribe(function(){s=!1}),o()},g.CLASS_STOPTOGGLE="stoptoggle",g);function g(e){var t=u.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-playbacktogglebutton",text:s.i18n.getLocalizer("play"),onAriaLabel:s.i18n.getLocalizer("pause"),offAriaLabel:s.i18n.getLocalizer("play")},t.config),t.isPlayInitiated=!1,t}},{"../localization/i18n":91,"../playerutils":98,"./togglebutton":74}],36:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.PlaybackToggleOverlay=void 0,e("./container")),a=e("./hugeplaybacktogglebutton"),e=(i=s.Container,r(l,i),l);function l(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.playbackToggleButton=new a.HugePlaybackToggleButton({enterFullscreenOnInitialPlayback:Boolean(e.enterFullscreenOnInitialPlayback)}),t.config=t.mergeConfig(e,{cssClass:"ui-playbacktoggle-overlay",components:[t.playbackToggleButton]},t.config),t}n.PlaybackToggleOverlay=e},{"./container":19,"./hugeplaybacktogglebutton":25}],37:[function(e,t,n){"use strict";var o,a,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.QuickSeekButton=void 0,e("./button")),s=e("../localization/i18n"),l=e("../playerutils"),e=(a=r.Button,i(c,a),c.prototype.configure=function(n,e){function o(e,t){e&&!t?s.hide():s.show()}var i,r,s=this,t=(a.prototype.configure.call(this,n,e),this.player=n,new l.PlayerUtils.TimeShiftAvailabilityDetector(n)),e=(t.onTimeShiftAvailabilityChanged.subscribe(function(e,t){r=t.timeShiftAvailable,o(i,r)}),new l.PlayerUtils.LiveStreamDetector(n,e));e.onLiveChanged.subscribe(function(e,t){i=t.live,o(i,r)}),t.detect(),e.detect(),this.onClick.subscribe(function(){var e,t;i&&!r||i&&0<s.config.seekSeconds&&0===n.getTimeShift()||(e=(null!==s.currentSeekTarget?s.currentSeekTarget:i?n.getTimeShift():n.getCurrentTime())+s.config.seekSeconds,i?(t=l.PlayerUtils.clampValueToRange(e,n.getMaxTimeShift(),0),n.timeShift(t)):(t=l.PlayerUtils.clampValueToRange(e,0,n.getDuration()),n.seek(t)))}),this.player.on(this.player.exports.PlayerEvent.Seek,this.onSeek),this.player.on(this.player.exports.PlayerEvent.Seeked,this.onSeekedOrTimeShifted),this.player.on(this.player.exports.PlayerEvent.TimeShift,this.onTimeShift),this.player.on(this.player.exports.PlayerEvent.TimeShifted,this.onSeekedOrTimeShifted)},c.prototype.release=function(){this.player.off(this.player.exports.PlayerEvent.Seek,this.onSeek),this.player.off(this.player.exports.PlayerEvent.Seeked,this.onSeekedOrTimeShifted),this.player.off(this.player.exports.PlayerEvent.TimeShift,this.onTimeShift),this.player.off(this.player.exports.PlayerEvent.TimeShifted,this.onSeekedOrTimeShifted),this.currentSeekTarget=null,this.player=null},c);function c(e){var t=a.call(this,e=void 0===e?{}:e)||this,e=(t.onSeek=function(e){t.currentSeekTarget=e.seekTarget},t.onSeekedOrTimeShifted=function(){t.currentSeekTarget=null},t.onTimeShift=function(e){t.currentSeekTarget=t.player.getTimeShift()+(e.target-e.position)},t.currentSeekTarget=null,t.config=t.mergeConfig(e,{seekSeconds:-10,cssClass:"ui-quickseekbutton"},t.config),t.config.seekSeconds<0?"rewind":"forward");return t.config.text=t.config.text||s.i18n.getLocalizer("quickseek.".concat(e)),t.config.ariaLabel=t.config.ariaLabel||s.i18n.getLocalizer("quickseek.".concat(e),{seekSeconds:Math.abs(t.config.seekSeconds)}),t.getDomElement().data(t.prefixCss("seek-direction"),e),t}n.QuickSeekButton=e},{"../localization/i18n":91,"../playerutils":98,"./button":12}],38:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.RecommendationOverlay=void 0,e("./container")),a=e("./component"),l=e("../dom"),c=e("../stringutils"),u=e("./hugereplaybutton"),e=(i=s.Container,r(p,i),p.prototype.configure=function(e,r){function t(){a();var e=r.getConfig().recommendations;if(0<e.length){for(var t=1,n=0,o=e;n<o.length;n++){var i=o[n];s.addComponent(new f({itemConfig:i,cssClasses:["recommendation-item-"+t++]}))}s.updateComponents(),s.getDomElement().addClass(s.prefixCss("recommendations"))}}var s=this,a=(i.prototype.configure.call(this,e,r),function(){for(var e=0,t=s.getComponents().slice();e<t.length;e++){var n=t[e];n instanceof f&&s.removeComponent(n)}s.updateComponents(),s.getDomElement().removeClass(s.prefixCss("recommendations"))});r.getConfig().events.onUpdated.subscribe(t),e.on(e.exports.PlayerEvent.SourceUnloaded,function(){a(),s.hide()}),e.on(e.exports.PlayerEvent.PlaybackFinished,function(){s.show()}),e.on(e.exports.PlayerEvent.Play,function(){s.hide()}),t()},p);function p(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.replayButton=new u.HugeReplayButton,t.config=t.mergeConfig(e,{cssClass:"ui-recommendation-overlay",hidden:!0,components:[t.replayButton]},t.config),t}n.RecommendationOverlay=e;g=a.Component,r(d,g),d.prototype.toDomElement=function(){var e=this.config.itemConfig,t=new l.DOM("a",{id:this.config.id,class:this.getCssClasses(),href:e.url},this).css({"background-image":"url(".concat(e.thumbnail,")")}),n=new l.DOM("div",{class:this.prefixCss("background")}),n=(t.append(n),new l.DOM("span",{class:this.prefixCss("title")}).append(new l.DOM("span",{class:this.prefixCss("innertitle")}).html(e.title))),n=(t.append(n),new l.DOM("span",{class:this.prefixCss("duration")}).append(new l.DOM("span",{class:this.prefixCss("innerduration")}).html(e.duration?c.StringUtils.secondsToTime(e.duration):"")));return t.append(n),t};var g,f=d;function d(e){var t=g.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-recommendation-item",itemConfig:null},t.config),t}},{"../dom":84,"../stringutils":111,"./component":18,"./container":19,"./hugereplaybutton":26}],39:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.ReplayButton=void 0,e("./button")),a=e("../localization/i18n"),l=e("../playerutils"),e=(i=s.Button,r(c,i),c.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),e.isLive()&&this.hide(),new l.PlayerUtils.LiveStreamDetector(e,t).onLiveChanged.subscribe(function(e,t){t.live?n.hide():n.show()}),this.onClick.subscribe(function(){e.hasEnded()?e.play("ui"):e.seek(0)})},c);function c(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-replaybutton",text:a.i18n.getLocalizer("replay"),ariaLabel:a.i18n.getLocalizer("replay")},t.config),t}n.ReplayButton=e},{"../localization/i18n":91,"../playerutils":98,"./button":12}],40:[function(e,t,n){"use strict";var o,d,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),h=(Object.defineProperty(n,"__esModule",{value:!0}),n.SeekBar=void 0,e("./../groupplaybackapi")),r=e("./component"),a=e("../dom"),s=e("../eventdispatcher"),l=e("../timeout"),y=e("../playerutils"),c=e("../stringutils"),m=e("./seekbarcontroller"),u=e("../localization/i18n"),p=e("../browserutils"),g=e("./timelinemarkershandler"),f=e("./seekbarbufferlevel");n.SeekBar=(d=r.Component,i(b,d),b.prototype.initialize=function(){d.prototype.initialize.call(this),this.hasLabel()&&this.getLabel().initialize()},b.prototype.setAriaSliderMinMax=function(e,t){this.getDomElement().attr("aria-valuemin",e),this.getDomElement().attr("aria-valuemax",t)},b.prototype.setAriaSliderValues=function(){var e,t;this.seekBarType===m.SeekBarType.Live?(t=Math.ceil(this.player.getTimeShift()).toString(),e="".concat(u.i18n.performLocalization(u.i18n.getLocalizer("seekBar.timeshift"))," ").concat(u.i18n.performLocalization(u.i18n.getLocalizer("seekBar.value")),": ").concat(t),this.getDomElement().attr("aria-valuenow",t),this.getDomElement().attr("aria-valuetext",e),this.config.addCurrentTimeToAriaLabel&&this.getDomElement().attr("aria-label","".concat(u.i18n.performLocalization(this.config.ariaLabel),": ").concat(e))):this.seekBarType===m.SeekBarType.Vod&&(t="".concat(c.StringUtils.secondsToText(this.player.getCurrentTime())," ").concat(u.i18n.performLocalization(u.i18n.getLocalizer("seekBar.durationText"))," ").concat(c.StringUtils.secondsToText(this.player.getDuration())),this.getDomElement().attr("aria-valuenow",Math.floor(this.player.getCurrentTime()).toString()),this.getDomElement().attr("aria-valuetext",t),this.config.addCurrentTimeToAriaLabel)&&this.getDomElement().attr("aria-label","".concat(u.i18n.performLocalization(this.config.ariaLabel),": ").concat(c.StringUtils.secondsToText(this.player.getCurrentTime())))},b.prototype.getPlaybackPositionPercentage=function(){return this.player.isLive()?100-100/this.player.getMaxTimeShift()*this.player.getTimeShift():100/this.player.getDuration()*this.getRelativeCurrentTime()},b.prototype.updateBufferLevel=function(e){e=this.player.isLive()?100:e+(0,f.getMinBufferLevel)(this.player);this.setBufferPosition(e)},b.prototype.configure=function(o,n,e){var i,r,t,s,a,l,c,u,p,g,f=this;void 0===e&&(e=!0),d.prototype.configure.call(this,o,n),this.player=o,this.uiManager=n,this.setPosition(this.seekBarBackdrop,100),new m.SeekBarController(this.config.keyStepIncrements,o,n.getConfig().volumeController).setSeekBarControls(this.getDomElement(),function(){return f.seekBarType}),e?(n.onControlsShow.subscribe(function(){f.isUiShown=!0,!f.smoothPlaybackPositionUpdater||o.isLive()||f.smoothPlaybackPositionUpdater.isActive()||(a(null,!0),f.smoothPlaybackPositionUpdater.start())}),n.onControlsHide.subscribe(function(){f.isUiShown=!1,f.smoothPlaybackPositionUpdater&&f.smoothPlaybackPositionUpdater.isActive()&&f.smoothPlaybackPositionUpdater.clear()}),t=r=i=!1,a=function(e,t){var n;void 0===e&&(e=null),void 0===t&&(t=!1),f.isUserSeeking||(n=f.getPlaybackPositionPercentage(),f.updateBufferLevel(n),r&&e&&e.type===o.exports.PlayerEvent.SegmentRequestFinished&&n!==f.playbackPositionPercentage&&(n=f.playbackPositionPercentage),o.isLive()?0===o.getMaxTimeShift()?f.setPlaybackPosition(100):(f.isSeeking()||f.setPlaybackPosition(n),f.setAriaSliderMinMax(o.getMaxTimeShift().toString(),"0")):(e=f.config.smoothPlaybackPositionUpdateIntervalMs===b.SMOOTH_PLAYBACK_POSITION_UPDATE_DISABLED||t||o.isPaused(),t=o.isPaused()===o.isPlaying(),!e&&!t||f.isSeeking()||f.setPlaybackPosition(n),f.setAriaSliderMinMax("0",o.getDuration().toString())),f.isUiShown&&f.setAriaSliderValues())},o.on(o.exports.PlayerEvent.Ready,a),o.on(o.exports.PlayerEvent.TimeChanged,a),o.on(o.exports.PlayerEvent.StallEnded,a),o.on(o.exports.PlayerEvent.TimeShifted,a),o.on(o.exports.PlayerEvent.SegmentRequestFinished,a),this.configureLivePausedTimeshiftUpdater(o,n,a),e=function(){t=!0,f.setSeeking(!0),r=!1},g=function(e){void 0===e&&(e=null),t=!1,f.setSeeking(!1),a(e,!0)},o.on(o.exports.PlayerEvent.Seek,e),o.on(o.exports.PlayerEvent.Seeked,g),o.on(o.exports.PlayerEvent.TimeShift,e),o.on(o.exports.PlayerEvent.TimeShifted,g),l=function(e){return!!e.groupPlayback},this.onSeek.subscribe(function(e){f.isUserSeeking=!0,n.onSeek.dispatch(e),l(o)&&o.groupPlayback.hasJoined()&&!s&&(s=o.groupPlayback.beginSuspension(h.GroupPlaybackSuspensionReason.UserIsScrubbing)),t||(i=o.isPlaying())&&o.pause("ui-seek")}),this.onSeekPreview.subscribe(function(e,t){n.onSeekPreview.dispatch(e,t),r=t.scrubbing}),"boolean"==typeof n.getConfig().enableSeekPreview&&(this.config.enableSeekPreview=n.getConfig().enableSeekPreview),this.config.enableSeekPreview&&this.onSeekPreview.subscribeRateLimited(this.seekWhileScrubbing,200),this.onSeeked.subscribe(function(e,t){f.isUserSeeking=!1,f.seek(t),n.onSeeked.dispatch(e),i&&o.play("ui-seek"),l(o)&&o.groupPlayback.hasJoined()&&s&&(e=f.getTargetSeekPosition(t),o.groupPlayback.endSuspension(s,{proposedPlaybackTime:e}),s=void 0)}),this.hasLabel()&&this.getLabel().configure(o,n),c=!1,u=!1,p=function(e,t){e&&!t?f.hide():f.show(),a(null,!0),f.refreshPlaybackPosition()},(e=new y.PlayerUtils.LiveStreamDetector(o,n)).onLiveChanged.subscribe(function(e,t){(c=t.live)&&null!=f.smoothPlaybackPositionUpdater?(f.smoothPlaybackPositionUpdater.clear(),f.seekBarType=m.SeekBarType.Live):f.seekBarType=m.SeekBarType.Vod,p(c,u)}),(g=new y.PlayerUtils.TimeShiftAvailabilityDetector(o)).onTimeShiftAvailabilityChanged.subscribe(function(e,t){u=t.timeShiftAvailable,p(c,u)}),e.detect(),g.detect(),o.on(o.exports.PlayerEvent.PlayerResized,function(){f.refreshPlaybackPosition(),f.uiBoundingRect=f.uiManager.getUI().getDomElement().get(0).getBoundingClientRect()}),n.onConfigured.subscribe(function(){f.refreshPlaybackPosition()}),o.on(o.exports.PlayerEvent.SourceLoaded,function(){f.refreshPlaybackPosition()}),n.getConfig().events.onUpdated.subscribe(function(){a()}),"number"==typeof n.getConfig().seekbarSnappingRange&&(this.config.snappingRange=n.getConfig().seekbarSnappingRange),"boolean"==typeof n.getConfig().seekbarSnappingEnabled&&(this.config.snappingEnabled=n.getConfig().seekbarSnappingEnabled),a(),this.setBufferPosition(0),this.setSeekPosition(0),this.config.smoothPlaybackPositionUpdateIntervalMs!==b.SMOOTH_PLAYBACK_POSITION_UPDATE_DISABLED&&this.configureSmoothPlaybackPositionUpdater(o,n),this.initializeTimelineMarkers(o,n)):this.seekBarType=m.SeekBarType.Volume},b.prototype.initializeTimelineMarkers=function(e,t){var n=this,o={cssPrefix:this.config.cssPrefix,snappingRange:this.config.snappingRange};this.timelineMarkersHandler=new g.TimelineMarkersHandler(o,function(){return n.seekBar.width()},this.seekBarMarkersContainer),this.timelineMarkersHandler.initialize(e,t)},b.prototype.configureLivePausedTimeshiftUpdater=function(e,t,n){var o=this;this.pausedTimeshiftUpdater=new l.Timeout(1e3,n,!0),e.on(e.exports.PlayerEvent.Paused,function(){e.isLive()&&e.getMaxTimeShift()<0&&o.pausedTimeshiftUpdater.start()}),e.on(e.exports.PlayerEvent.Play,function(){return o.pausedTimeshiftUpdater.clear()})},b.prototype.configureSmoothPlaybackPositionUpdater=function(t,e){function n(){t.isLive()||(r=i.getRelativeCurrentTime(),i.smoothPlaybackPositionUpdater.start())}function o(){i.smoothPlaybackPositionUpdater.clear()}var i=this,r=0,s=0;this.smoothPlaybackPositionUpdater=new l.Timeout(50,function(){if(!i.isSeeking()){r+=.05;try{s=i.getRelativeCurrentTime()}catch(e){return void(e instanceof t.exports.PlayerAPINotAvailableError&&i.smoothPlaybackPositionUpdater.clear())}var e=r-s,e=(2<Math.abs(e)?r=s:e<=-.05?r+=.05:.05<=e&&(r-=.05),100/t.getDuration()*r);i.setPlaybackPosition(e)}},!0);t.on(t.exports.PlayerEvent.Play,n),t.on(t.exports.PlayerEvent.Playing,n),t.on(t.exports.PlayerEvent.Paused,o),t.on(t.exports.PlayerEvent.PlaybackFinished,o),t.on(t.exports.PlayerEvent.Seeked,function(){r=i.getRelativeCurrentTime()}),t.on(t.exports.PlayerEvent.SourceUnloaded,o),t.isPlaying()&&n()},b.prototype.getRelativeCurrentTime=function(){return y.PlayerUtils.getCurrentTimeRelativeToSeekableRange(this.player)},b.prototype.release=function(){d.prototype.release.call(this),this.smoothPlaybackPositionUpdater&&this.smoothPlaybackPositionUpdater.clear(),this.pausedTimeshiftUpdater&&this.pausedTimeshiftUpdater.clear(),this.config.enableSeekPreview&&this.onSeekPreview.unsubscribe(this.seekWhileScrubbing)},b.prototype.toDomElement=function(){var n=this,e=(this.config.vertical&&this.config.cssClasses.push("vertical"),new a.DOM("div",{id:this.config.id,class:this.getCssClasses(),role:"slider","aria-label":u.i18n.performLocalization(this.config.ariaLabel),tabindex:this.config.tabIndex.toString()},this)),t=new a.DOM("div",{class:this.prefixCss("seekbar")}),o=(this.seekBar=t,new a.DOM("div",{class:this.prefixCss("seekbar-bufferlevel")})),o=(this.seekBarBufferPosition=o,new a.DOM("div",{class:this.prefixCss("seekbar-playbackposition")})),o=(this.seekBarPlaybackPosition=o,new a.DOM("div",{class:this.prefixCss("seekbar-playbackposition-marker")})),o=(this.seekBarPlaybackPositionMarker=o,new a.DOM("div",{class:this.prefixCss("seekbar-seekposition")})),o=(this.seekBarSeekPosition=o,new a.DOM("div",{class:this.prefixCss("seekbar-backdrop")})),o=(this.seekBarBackdrop=o,new a.DOM("div",{class:this.prefixCss("seekbar-markers")})),i=(this.seekBarMarkersContainer=o,t.append(this.seekBarBackdrop,this.seekBarBufferPosition,this.seekBarSeekPosition,this.seekBarPlaybackPosition,this.seekBarMarkersContainer,this.seekBarPlaybackPositionMarker),!1),r=function(e){e.preventDefault(),null!=n.player.vr&&e.stopPropagation();var e=n.getOffset(e),t=100*e,e=e*n.seekBar.width();n.setSeekPosition(t),n.setPlaybackPosition(t),n.onSeekPreviewEvent(t,e,!0)},s=function(e){e.preventDefault(),new a.DOM(document).off("touchmove mousemove",r),new a.DOM(document).off("touchend mouseup",s);var t,e=100*n.getOffset(e);n.config.snappingEnabled&&(e=(t=null==(t=n.timelineMarkersHandler)?void 0:t.getMarkerAtPosition(e))?t.position:e),n.setSeeking(!1),i=!1,n.onSeekedEvent(e)};return t.on("touchstart mousedown",function(e){var t=p.BrowserUtils.isTouchSupported&&n.isTouchEvent(e);e.preventDefault(),null!=n.player.vr&&e.stopPropagation(),n.setSeeking(!0),i=!0,n.onSeekEvent(),new a.DOM(document).on(t?"touchmove":"mousemove",r),new a.DOM(document).on(t?"touchend":"mouseup",s)}),t.on("touchmove mousemove",function(e){e.preventDefault(),i&&r(e);var e=n.getOffset(e),t=100*e,e=e*n.seekBar.width();n.setSeekPosition(t),n.onSeekPreviewEvent(t,e,!1),n.hasLabel()&&n.getLabel().isHidden()&&n.getLabel().show()}),t.on("touchend mouseleave",function(e){e.preventDefault(),n.setSeekPosition(0),n.hasLabel()&&n.getLabel().hide()}),e.append(t),this.label&&e.append(this.label.getDomElement()),e},b.prototype.getHorizontalOffset=function(e){var t=this.seekBar.offset().left,n=this.seekBar.width();return this.sanitizeOffset(1/n*(e-t))},b.prototype.getVerticalOffset=function(e){var t=this.seekBar.offset().top,n=this.seekBar.height();return 1-this.sanitizeOffset(1/n*(e-t))},b.prototype.getOffset=function(e){return p.BrowserUtils.isTouchSupported&&this.isTouchEvent(e)?this.config.vertical?this.getVerticalOffset(("touchend"===e.type?e.changedTouches:e.touches)[0].pageY):this.getHorizontalOffset(("touchend"===e.type?e.changedTouches:e.touches)[0].pageX):e instanceof MouseEvent?this.config.vertical?this.getVerticalOffset(e.pageY):this.getHorizontalOffset(e.pageX):(console&&console.warn("invalid event"),0)},b.prototype.sanitizeOffset=function(e){return e<0?e=0:1<e&&(e=1),e},b.prototype.setPlaybackPosition=function(e){this.playbackPositionPercentage=e,this.setPosition(this.seekBarPlaybackPosition,e);e=(this.config.vertical?this.seekBar.height()-this.seekBarPlaybackPositionMarker.height():this.seekBar.width())/100*e,this.config.vertical&&(e=this.seekBar.height()-e-this.seekBarPlaybackPositionMarker.height()),e=this.config.vertical?{transform:"translateY("+e+"px)","-ms-transform":"translateY("+e+"px)","-webkit-transform":"translateY("+e+"px)"}:{transform:"translateX("+e+"px)","-ms-transform":"translateX("+e+"px)","-webkit-transform":"translateX("+e+"px)"};this.seekBarPlaybackPositionMarker.css(e)},b.prototype.refreshPlaybackPosition=function(){this.setPlaybackPosition(this.playbackPositionPercentage)},b.prototype.setBufferPosition=function(e){this.setPosition(this.seekBarBufferPosition,e)},b.prototype.setSeekPosition=function(e){this.setPosition(this.seekBarSeekPosition,e)},b.prototype.setPosition=function(e,t){t/=100,.99999<=t&&t<=1.00001&&(t=.99999),t=this.config.vertical?{transform:"scaleY("+t+")","-ms-transform":"scaleY("+t+")","-webkit-transform":"scaleY("+t+")"}:{transform:"scaleX("+t+")","-ms-transform":"scaleX("+t+")","-webkit-transform":"scaleX("+t+")"};e.css(t)},b.prototype.setSeeking=function(e){e?this.getDomElement().addClass(this.prefixCss(b.CLASS_SEEKING)):this.getDomElement().removeClass(this.prefixCss(b.CLASS_SEEKING))},b.prototype.isSeeking=function(){return this.getDomElement().hasClass(this.prefixCss(b.CLASS_SEEKING))},b.prototype.hasLabel=function(){return null!=this.label},b.prototype.getLabel=function(){return this.label},b.prototype.onSeekEvent=function(){this.seekBarEvents.onSeek.dispatch(this)},b.prototype.onSeekPreviewEvent=function(e,t,n){var o=this.timelineMarkersHandler&&this.timelineMarkersHandler.getMarkerAtPosition(e),i=e;o&&(!(0<o.duration)||e<o.position?i=o.position:e>o.position+o.duration&&(i=o.position+o.duration)),this.label&&this.updateLabelPosition(t),this.seekBarEvents.onSeekPreview.dispatch(this,{scrubbing:n,position:i,marker:o})},b.prototype.onSeekedEvent=function(e){this.seekBarEvents.onSeeked.dispatch(this,e)},Object.defineProperty(b.prototype,"onSeek",{get:function(){return this.seekBarEvents.onSeek.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(b.prototype,"onSeekPreview",{get:function(){return this.seekBarEvents.onSeekPreview.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(b.prototype,"onSeeked",{get:function(){return this.seekBarEvents.onSeeked.getEvent()},enumerable:!1,configurable:!0}),b.prototype.onShowEvent=function(){d.prototype.onShowEvent.call(this),this.refreshPlaybackPosition()},b.prototype.isTouchEvent=function(e){return window.TouchEvent&&e instanceof TouchEvent},b.SMOOTH_PLAYBACK_POSITION_UPDATE_DISABLED=-1,b.CLASS_SEEKING="seeking",b);function b(e){var n=d.call(this,e=void 0===e?{}:e)||this,t=(n.playbackPositionPercentage=0,n.isUserSeeking=!1,n.seekBarEvents={onSeek:new s.EventDispatcher,onSeekPreview:new s.EventDispatcher,onSeeked:new s.EventDispatcher},n.seekWhileScrubbing=function(e,t){t.scrubbing&&n.seek(t.position)},n.getTargetSeekPosition=function(e){var t;return n.player.isLive()?(t=n.player.getMaxTimeShift())-t*(e/100):(t=y.PlayerUtils.getSeekableRangeStart(n.player,0),n.player.getDuration()*(e/100)+t)},n.seek=function(e){e=n.getTargetSeekPosition(e);n.player.isLive()?n.player.timeShift(e,"ui"):n.player.seek(e,"ui")},n.updateLabelPosition=function(e){n.uiBoundingRect||(n.uiBoundingRect=n.uiManager.getUI().getDomElement().get(0).getBoundingClientRect()),n.label.setPositionInBounds(e,n.uiBoundingRect)},n.config.keyStepIncrements||{leftRight:1,upDown:5});return n.config=n.mergeConfig(e,{cssClass:"ui-seekbar",vertical:!1,smoothPlaybackPositionUpdateIntervalMs:50,keyStepIncrements:t,ariaLabel:u.i18n.getLocalizer("seekBar"),tabIndex:0,snappingRange:1,enableSeekPreview:!0,snappingEnabled:!0},n.config),n.label=n.config.label,n}},{"../browserutils":3,"../dom":84,"../eventdispatcher":86,"../localization/i18n":91,"../playerutils":98,"../stringutils":111,"../timeout":113,"./../groupplaybackapi":88,"./component":18,"./seekbarbufferlevel":41,"./seekbarcontroller":42,"./timelinemarkershandler":72}],41:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getMinBufferLevel=void 0,n.getMinBufferLevel=function(e){var t=e.getDuration(),n=e.getVideoBufferLength(),e=e.getAudioBufferLength(),n=Math.min(null!=n?n:Number.MAX_VALUE,null!=e?e:Number.MAX_VALUE);return 100/t*(n=n===Number.MAX_VALUE?0:n)}},{}],42:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.SeekBarController=n.SeekBarType=void 0;function r(e,t,n){e<t.min?n(t.min):e>t.max?n(t.max):n(e)}var o,i=e("../uiutils");(e=o=n.SeekBarType||(n.SeekBarType={}))[e.Vod=0]="Vod",e[e.Live=1]="Live",e[e.Volume=2]="Volume";function s(e,t,n){this.keyStepIncrements=e,this.player=t,this.volumeController=n}s.prototype.arrowKeyControls=function(e,t,n){var o=this,i=Math.floor(e);return{left:function(){return r(i-o.keyStepIncrements.leftRight,t,n)},right:function(){return r(i+o.keyStepIncrements.leftRight,t,n)},up:function(){return r(i+o.keyStepIncrements.upDown,t,n)},down:function(){return r(i-o.keyStepIncrements.upDown,t,n)},home:function(){return r(t.min,t,n)},end:function(){return r(t.max,t,n)}}},s.prototype.seekBarControls=function(e){return e===o.Live?this.arrowKeyControls(this.player.getTimeShift(),{min:this.player.getMaxTimeShift(),max:0},this.player.timeShift):e===o.Vod?this.arrowKeyControls(this.player.getCurrentTime(),{min:0,max:this.player.getDuration()},this.player.seek):e===o.Volume&&null!=this.volumeController?(e=this.volumeController.startTransition(),this.arrowKeyControls(this.player.getVolume(),{min:0,max:100},e.finish.bind(e))):void 0},s.prototype.setSeekBarControls=function(e,n){var o=this;e.on("keydown",function(e){var t=o.seekBarControls(n());switch(e.keyCode){case i.UIUtils.KeyCode.LeftArrow:t.left(),e.preventDefault();break;case i.UIUtils.KeyCode.RightArrow:t.right(),e.preventDefault();break;case i.UIUtils.KeyCode.UpArrow:t.up(),e.preventDefault();break;case i.UIUtils.KeyCode.DownArrow:t.down(),e.preventDefault();break;case i.UIUtils.KeyCode.Home:t.home(),e.preventDefault();break;case i.UIUtils.KeyCode.End:t.end(),e.preventDefault();break;case i.UIUtils.KeyCode.Space:o.player.isPlaying()?o.player.pause():o.player.play(),e.preventDefault()}})},n.SeekBarController=s},{"../uiutils":117}],43:[function(e,t,n){"use strict";var o,r,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SeekBarLabel=void 0,e("./container")),a=e("./label"),l=e("./component"),c=e("../stringutils"),u=e("../imageloader"),p=e("../playerutils"),e=(r=s.Container,i(g,r),g.prototype.configure=function(e,t){function n(){o.timeFormat=3600<=Math.abs(e.isLive()?e.getMaxTimeShift():e.getDuration())?c.StringUtils.FORMAT_HHMMSS:c.StringUtils.FORMAT_MMSS,o.setTitleText(null),o.setThumbnail(null)}var o=this;r.prototype.configure.call(this,e,t),this.player=e,(this.uiManager=t).onSeekPreview.subscribeRateLimited(this.handleSeekPreview,100);t.getConfig().events.onUpdated.subscribe(n),n()},g.prototype.setPositionInBounds=function(e,t){this.getDomElement().css("left",e+"px");var n=this.container.getDomElement().get(0).parentElement.getBoundingClientRect(),o=0;n.right>t.right?o=n.right-t.right:n.left<t.left&&(o=n.left-t.left),0!==o?(this.getDomElement().css("left",e-o+"px"),this.caret.getDomElement().css("transform","translateX(".concat(o,"px)"))):this.caret.getDomElement().css("transform",null)},g.prototype.setText=function(e){this.timeLabel.setText(e)},g.prototype.setTime=function(e){this.setText(c.StringUtils.secondsToTime(e,this.timeFormat))},g.prototype.setTitleText=function(e){this.titleLabel.setText(e=void 0===e?"":e)},g.prototype.setThumbnail=function(o){var i=this,r=(void 0===o&&(o=null),this.thumbnail.getDomElement());null==o?r.css({"background-image":null,display:null,width:null,height:null}):this.thumbnailImageLoader.load(o.url,function(e,t,n){void 0!==o.x?r.css(i.thumbnailCssSprite(o,t,n)):r.css(i.thumbnailCssSingleImage(o,t,n))})},g.prototype.thumbnailCssSprite=function(e,t,n){var t=100*(t/e.width),n=100*(n/e.height),o=100*(e.x/e.width),i=100*(e.y/e.height),r=1/e.width*e.height;return{display:"inherit","background-image":"url(".concat(e.url,")"),"padding-bottom":"".concat(100*r,"%"),"background-size":"".concat(t,"% ").concat(n,"%"),"background-position":"-".concat(o,"% -").concat(i,"%")}},g.prototype.thumbnailCssSingleImage=function(e,t,n){t=1/t*n;return{display:"inherit","background-image":"url(".concat(e.url,")"),"padding-bottom":"".concat(100*t,"%"),"background-size":"100% 100%","background-position":"0 0"}},g.prototype.release=function(){r.prototype.release.call(this),this.uiManager.onSeekPreview.unsubscribe(this.handleSeekPreview)},g);function g(e){var i=r.call(this,e=void 0===e?{}:e)||this;return i.appliedMarkerCssClasses=[],i.handleSeekPreview=function(e,t){var n,o;i.player.isLive()?(n=(n=i.player.getMaxTimeShift())-n*(t.position/100),i.setTime(n),n=n,o=i.player.getTimeShift(),o=i.player.getCurrentTime()-o+n,i.setThumbnail(i.player.getThumbnail(o))):(n=i.player.getDuration()*(t.position/100),i.setTime(n),o=p.PlayerUtils.getSeekableRangeStart(i.player,0),i.setThumbnail(i.player.getThumbnail(n+o))),t.marker?i.setTitleText(t.marker.marker.title):i.setTitleText(null),0<i.appliedMarkerCssClasses.length&&(i.getDomElement().removeClass(i.appliedMarkerCssClasses.join(" ")),i.appliedMarkerCssClasses=[]),t.marker&&(t=(t.marker.marker.cssClasses||[]).map(function(e){return i.prefixCss(e)}),i.getDomElement().addClass(t.join(" ")),i.appliedMarkerCssClasses=t)},i.timeLabel=new a.Label({cssClasses:["seekbar-label-time"]}),i.titleLabel=new a.Label({cssClasses:["seekbar-label-title"]}),i.thumbnail=new l.Component({cssClasses:["seekbar-thumbnail"],role:"img"}),i.thumbnailImageLoader=new u.ImageLoader,i.container=new s.Container({components:[i.thumbnail,new s.Container({components:[i.titleLabel,i.timeLabel],cssClass:"seekbar-label-metadata"})],cssClass:"seekbar-label-inner"}),i.caret=new a.Label({cssClasses:["seekbar-label-caret"]}),i.config=i.mergeConfig(e,{cssClass:"ui-seekbar-label",components:[i.container,i.caret],hidden:!0},i.config),i}n.SeekBarLabel=e},{"../imageloader":90,"../playerutils":98,"../stringutils":111,"./component":18,"./container":19,"./label":28}],44:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SelectBox=void 0,e("./listselector")),a=e("../dom"),l=e("../localization/i18n"),c=e("../playerutils"),u=e("./component"),p=["mousemove","mouseenter","mouseleave","touchstart","touchmove","touchend","pointermove","click","keydown","keypress","keyup","blur"],g=["change","keyup","mouseup"],f=[["click",function(){return!0}],["keydown",function(e){return[" ","ArrowUp","ArrowDown"].includes(e.key)}],["mousedown",function(){return!0}]],e=(i=s.ListSelector,r(d,i),d.prototype.toDomElement=function(){return this.selectElement=new a.DOM("select",{id:this.config.id,class:this.getCssClasses(),"aria-label":l.i18n.performLocalization(this.config.ariaLabel)},this),this.onDisabled.subscribe(this.closeDropdown),this.onHide.subscribe(this.closeDropdown),this.addDropdownOpenedListeners(),this.updateDomItems(),this.selectElement.on("change",this.onChange),this.selectElement},d.prototype.configure=function(e,t){i.prototype.configure.call(this,e,t),this.uiContainer=t.getUI(),null!=(e=this.uiContainer)&&e.onPlayerStateChange().subscribe(this.onPlayerStateChange)},d.prototype.getSelectElement=function(){var e;return null==(e=null==(e=this.selectElement)?void 0:e.get())?void 0:e[0]},d.prototype.updateDomItems=function(e){if(void 0===e&&(e=null),void 0!==this.selectElement){this.selectElement.empty();for(var t=0,n=this.items;t<n.length;t++){var o=n[t],i=new a.DOM("option",{value:String(o.key)}).html(l.i18n.performLocalization(o.label));o.key===String(e)&&i.attr("selected","selected"),this.selectElement.append(i)}}},d.prototype.onItemAddedEvent=function(e){i.prototype.onItemAddedEvent.call(this,e),this.updateDomItems(this.selectedItem)},d.prototype.onItemRemovedEvent=function(e){i.prototype.onItemRemovedEvent.call(this,e),this.updateDomItems(this.selectedItem)},d.prototype.onItemSelectedEvent=function(e,t){void 0===t&&(t=!0),i.prototype.onItemSelectedEvent.call(this,e),t&&this.updateDomItems(e)},d.prototype.addDropdownCloseListeners=function(){var t=this;this.removeDropdownCloseListeners(),clearTimeout(this.dropdownCloseListenerTimeoutId),p.forEach(function(e){return document.addEventListener(e,t.onDropdownClosed,!0)}),g.forEach(function(e){return t.selectElement.on(e,t.onDropdownClosed,!0)}),this.removeDropdownCloseListeners=function(){p.forEach(function(e){return document.removeEventListener(e,t.onDropdownClosed,!0)}),g.forEach(function(e){return t.selectElement.off(e,t.onDropdownClosed,!0)})}},d.prototype.addDropdownOpenedListeners=function(){for(var o=this,i=[],e=(this.removeDropdownOpenedListeners(),function(e,t){function n(e){t(e)&&o.onDropdownOpened()}i.push(function(){return o.selectElement.off(e,n,!0)}),r.selectElement.on(e,n,!0)}),r=this,t=0,n=f;t<n.length;t++){var s=n[t];e(s[0],s[1])}this.removeDropdownOpenedListeners=function(){for(var e=0,t=i;e<t.length;e++)(0,t[e])()}},d.prototype.release=function(){i.prototype.release.call(this),this.removeDropdownCloseListeners(),this.removeDropdownOpenedListeners(),clearTimeout(this.dropdownCloseListenerTimeoutId)},d);function d(e){var n=i.call(this,e=void 0===e?{}:e)||this;return n.dropdownCloseListenerTimeoutId=0,n.removeDropdownCloseListeners=function(){},n.removeDropdownOpenedListeners=function(){},n.onChange=function(){var e=n.selectElement.val();n.onItemSelectedEvent(e,!1)},n.closeDropdown=function(){var e=n.getSelectElement();void 0!==e&&e.blur()},n.onPlayerStateChange=function(e,t){[c.PlayerUtils.PlayerState.Idle,c.PlayerUtils.PlayerState.Finished].includes(t)&&n.closeDropdown()},n.onDropdownOpened=function(){clearTimeout(n.dropdownCloseListenerTimeoutId),n.dropdownCloseListenerTimeoutId=window.setTimeout(function(){return n.addDropdownCloseListeners()},100),n.onViewModeChangedEvent(u.ViewMode.Persistent)},n.onDropdownClosed=function(){clearTimeout(n.dropdownCloseListenerTimeoutId),n.removeDropdownCloseListeners(),n.onViewModeChangedEvent(u.ViewMode.Temporary)},n.config=n.mergeConfig(e,{cssClass:"ui-selectbox"},n.config),n}n.SelectBox=e},{"../dom":84,"../localization/i18n":91,"../playerutils":98,"./component":18,"./listselector":30}],45:[function(e,t,n){"use strict";var o,i,r,s=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),a=(Object.defineProperty(n,"__esModule",{value:!0}),n.SettingsPanel=void 0,e("./container")),l=e("./selectbox"),c=e("../timeout"),u=e("../eventdispatcher"),p=e("./settingspanelpage");(e=i=i||{})[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",n.SettingsPanel=(r=a.Container,s(g,r),g.prototype.configure=function(e,t){var n=this,o=(r.prototype.configure.call(this,e,t),this.getConfig());t.onControlsHide.subscribe(function(){return n.hideHoveredSelectBoxes()}),t.onComponentViewModeChanged.subscribe(function(e,t){t=t.mode;return n.trackComponentViewMode(t)}),-1<o.hideDelay&&(this.hideTimeout=new c.Timeout(o.hideDelay,function(){n.hide(),n.hideHoveredSelectBoxes()}),this.getDomElement().on("mouseenter",function(){n.hideTimeout.clear()}),this.getDomElement().on("mouseleave",function(){n.hideTimeout.reset()}),this.getDomElement().on("focusin",function(){n.hideTimeout.clear()}),this.getDomElement().on("focusout",function(){n.hideTimeout.reset()})),this.onHide.subscribe(function(){-1<o.hideDelay&&n.hideTimeout.clear(),n.activePage.onInactiveEvent()}),this.onShow.subscribe(function(){n.resetNavigation(!0),n.activePage.onActiveEvent(),-1<o.hideDelay&&n.hideTimeout.start()}),this.getRootPage().onSettingsStateChanged.subscribe(function(){n.onSettingsStateChangedEvent()}),this.updateActivePageClass()},g.prototype.getActivePage=function(){return this.activePage},g.prototype.setActivePageIndex=function(e){this.setActivePage(this.getPages()[e])},g.prototype.setActivePage=function(e){e===this.getActivePage()?console.warn("Page is already the current one ... skipping navigation"):this.navigateToPage(e,this.getActivePage(),i.Forwards,!this.config.pageTransitionAnimation)},g.prototype.popToRootSettingsPanelPage=function(){this.resetNavigation(this.config.pageTransitionAnimation)},g.prototype.popSettingsPanelPage=function(){var e;0===this.navigationStack.length?console.warn("Already on the root page ... skipping navigation"):(e=(e=this.navigationStack[this.navigationStack.length-2])||this.getRootPage(),this.navigateToPage(e,this.activePage,i.Backwards,!this.config.pageTransitionAnimation))},g.prototype.rootPageHasActiveSettings=function(){return this.getRootPage().hasActiveSettings()},g.prototype.getPages=function(){return this.config.components.filter(function(e){return e instanceof p.SettingsPanelPage})},Object.defineProperty(g.prototype,"onSettingsStateChanged",{get:function(){return this.settingsPanelEvents.onSettingsStateChanged.getEvent()},enumerable:!1,configurable:!0}),g.prototype.release=function(){r.prototype.release.call(this),this.hideTimeout&&this.hideTimeout.clear()},g.prototype.addComponent=function(e){0===this.getPages().length&&e instanceof p.SettingsPanelPage&&(this.activePage=e),r.prototype.addComponent.call(this,e)},g.prototype.suspendHideTimeout=function(){this.hideTimeout.suspend()},g.prototype.resumeHideTimeout=function(){this.hideTimeout.resume(!0)},g.prototype.updateActivePageClass=function(){var t=this;this.getPages().forEach(function(e){e===t.activePage?e.getDomElement().addClass(t.prefixCss(g.CLASS_ACTIVE_PAGE)):e.getDomElement().removeClass(t.prefixCss(g.CLASS_ACTIVE_PAGE))})},g.prototype.resetNavigation=function(e){var t=this.getActivePage(),n=this.getRootPage();t&&!e&&t.onInactiveEvent(),this.navigationStack=[],this.animateNavigation(n,t,e),this.activePage=n,this.updateActivePageClass()},g.prototype.navigateToPage=function(e,t,n,o){this.activePage=e,n===i.Forwards?this.navigationStack.push(e):this.navigationStack.pop(),this.animateNavigation(e,t,o),this.updateActivePageClass(),e.onActiveEvent(),t.onInactiveEvent()},g.prototype.animateNavigation=function(e,t,n){var o,i,r,s,a;this.config.pageTransitionAnimation&&(o=this.getDomElement(),i=(a=this.getDomElement().get(0)).scrollWidth,r=a.scrollHeight,t.getDomElement().css("display","none"),this.getDomElement().css({width:"",height:""}),s=(e=e.getDomElement().get(0)).cloneNode(!0),e.parentNode.appendChild(s),s.style.display="block",e=a.scrollWidth,a=a.scrollHeight,s.parentElement.removeChild(s),t.getDomElement().css("display",""),o.css({width:i+"px",height:r+"px"}),n||this.forceBrowserReflow(),o.css({width:e+"px",height:a+"px"}))},g.prototype.forceBrowserReflow=function(){this.getDomElement().get(0).offsetLeft},g.prototype.hideHoveredSelectBoxes=function(){this.getComputedItems().map(function(e){return e.setting}).filter(function(e){return e instanceof l.SelectBox}).forEach(function(e){return e.closeDropdown()})},g.prototype.getComputedItems=function(){for(var e=[],t=0,n=this.getPages();t<n.length;t++){var o=n[t];e.push.apply(e,o.getItems())}return e},g.prototype.getRootPage=function(){return this.getPages()[0]},g.prototype.onSettingsStateChangedEvent=function(){this.settingsPanelEvents.onSettingsStateChanged.dispatch(this)},g.CLASS_ACTIVE_PAGE="active",g);function g(e){var t=r.call(this,e)||this;return t.navigationStack=[],t.settingsPanelEvents={onSettingsStateChanged:new u.EventDispatcher},t.config=t.mergeConfig(e,{cssClass:"ui-settings-panel",hideDelay:3e3,pageTransitionAnimation:!0},t.config),t.activePage=t.getRootPage(),t}},{"../eventdispatcher":86,"../timeout":113,"./container":19,"./selectbox":44,"./settingspanelpage":47}],46:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SettingsPanelItem=void 0,e("./container")),a=e("./component"),l=e("../eventdispatcher"),c=e("./label"),u=e("./selectbox"),p=e("./listbox"),g=e("./videoqualityselectbox"),f=e("./audioqualityselectbox"),d=e("./playbackspeedselectbox"),e=(i=s.Container,r(h,i),h.prototype.configure=function(e,t){var n,o=this;(this.setting instanceof u.SelectBox||this.setting instanceof p.ListBox)&&(this.setting.onItemAdded.subscribe(n=function(){var e;(o.setting instanceof u.SelectBox||o.setting instanceof p.ListBox)&&(e=2,(o.setting instanceof g.VideoQualitySelectBox&&o.setting.hasAutoItem()||o.setting instanceof f.AudioQualitySelectBox)&&(e=3),o.setting.itemCount()<e||o.setting instanceof d.PlaybackSpeedSelectBox&&!t.getConfig().playbackSpeedSelectionEnabled?o.hide():o.show(),o.onActiveChangedEvent(),o.getDomElement().attr("aria-haspopup","true"))}),this.setting.onItemRemoved.subscribe(n),n())},h.prototype.isActive=function(){return this.isShown()},h.prototype.onActiveChangedEvent=function(){this.settingsPanelItemEvents.onActiveChanged.dispatch(this)},Object.defineProperty(h.prototype,"onActiveChanged",{get:function(){return this.settingsPanelItemEvents.onActiveChanged.getEvent()},enumerable:!1,configurable:!0}),h);function h(e,t,n){var o=i.call(this,n=void 0===n?{}:n)||this;return o.settingsPanelItemEvents={onActiveChanged:new l.EventDispatcher},o.setting=t,o.config=o.mergeConfig(n,{cssClass:"ui-settings-panel-item",role:"menuitem"},o.config),null!==e&&(e instanceof a.Component?o.label=e:o.label=new c.Label({text:e,for:o.setting.getConfig().id}),o.addComponent(o.label)),o.addComponent(o.setting),o}n.SettingsPanelItem=e},{"../eventdispatcher":86,"./audioqualityselectbox":8,"./component":18,"./container":19,"./label":28,"./listbox":29,"./playbackspeedselectbox":33,"./selectbox":44,"./videoqualityselectbox":77}],47:[function(e,t,n){"use strict";var o,s,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.SettingsPanelPage=void 0,e("./container")),a=e("./settingspanelitem"),l=e("../eventdispatcher"),c=e("../browserutils");n.SettingsPanelPage=(s=r.Container,i(u,s),u.prototype.configure=function(e,t){for(var i=this,n=(s.prototype.configure.call(this,e,t),function(){i.onSettingsStateChangedEvent();for(var e=null,t=0,n=i.getItems();t<n.length;t++){var o=n[t];o.getDomElement().removeClass(i.prefixCss(u.CLASS_LAST)),o.isShown()&&(e=o)}e&&e.getDomElement().addClass(i.prefixCss(u.CLASS_LAST))}),o=0,r=this.getItems();o<r.length;o++)r[o].onActiveChanged.subscribe(n)},u.prototype.hasActiveSettings=function(){for(var e=0,t=this.getItems();e<t.length;e++)if(t[e].isActive())return!0;return!1},u.prototype.getItems=function(){return this.config.components.filter(function(e){return e instanceof a.SettingsPanelItem})},u.prototype.onSettingsStateChangedEvent=function(){this.settingsPanelPageEvents.onSettingsStateChanged.dispatch(this)},Object.defineProperty(u.prototype,"onSettingsStateChanged",{get:function(){return this.settingsPanelPageEvents.onSettingsStateChanged.getEvent()},enumerable:!1,configurable:!0}),u.prototype.onActiveEvent=function(){var e=this.getItems().filter(function(e){return e.isActive()});this.settingsPanelPageEvents.onActive.dispatch(this),!(0<e.length)||c.BrowserUtils.isIOS||c.BrowserUtils.isMacIntel&&c.BrowserUtils.isTouchSupported||e[0].getDomElement().focusToFirstInput()},Object.defineProperty(u.prototype,"onActive",{get:function(){return this.settingsPanelPageEvents.onActive.getEvent()},enumerable:!1,configurable:!0}),u.prototype.onInactiveEvent=function(){this.settingsPanelPageEvents.onInactive.dispatch(this)},Object.defineProperty(u.prototype,"onInactive",{get:function(){return this.settingsPanelPageEvents.onInactive.getEvent()},enumerable:!1,configurable:!0}),u.CLASS_LAST="last",u);function u(e){var t=s.call(this,e)||this;return t.settingsPanelPageEvents={onSettingsStateChanged:new l.EventDispatcher,onActive:new l.EventDispatcher,onInactive:new l.EventDispatcher},t.config=t.mergeConfig(e,{cssClass:"ui-settings-panel-page",role:"menu"},t.config),t}},{"../browserutils":3,"../eventdispatcher":86,"./container":19,"./settingspanelitem":46}],48:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),e=(Object.defineProperty(n,"__esModule",{value:!0}),n.SettingsPanelPageBackButton=void 0,e("./settingspanelpagenavigatorbutton")),e=(i=e.SettingsPanelPageNavigatorButton,r(s,i),s.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.onClick.subscribe(function(){n.popPage()})},s);function s(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-settingspanelpagebackbutton",text:"back"},t.config),t}n.SettingsPanelPageBackButton=e},{"./settingspanelpagenavigatorbutton":49}],49:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),e=(Object.defineProperty(n,"__esModule",{value:!0}),n.SettingsPanelPageNavigatorButton=void 0,e("./button")),e=(i=e.Button,r(s,i),s.prototype.popPage=function(){this.container.popSettingsPanelPage()},s.prototype.pushTargetPage=function(){this.container.setActivePage(this.targetPage)},s);function s(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{},t.config),t.container=t.config.container,t.targetPage=t.config.targetPage,t}n.SettingsPanelPageNavigatorButton=e},{"./button":12}],50:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SettingsPanelPageOpenButton=void 0,e("./settingspanelpagenavigatorbutton")),a=e("../localization/i18n"),e=(i=s.SettingsPanelPageNavigatorButton,r(l,i),l.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.getDomElement().attr("aria-haspopup","true"),this.getDomElement().attr("aria-owns",this.config.targetPage.getConfig().id),this.onClick.subscribe(function(){n.pushTargetPage()})},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-settingspanelpageopenbutton",text:a.i18n.getLocalizer("open"),role:"menuitem"},t.config),t}n.SettingsPanelPageOpenButton=e},{"../localization/i18n":91,"./settingspanelpagenavigatorbutton":49}],51:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SettingsToggleButton=void 0,e("./togglebutton")),a=e("./settingspanel"),l=e("../arrayutils"),c=e("../localization/i18n"),e=(i=s.ToggleButton,r(u,i),u.prototype.configure=function(e,t){var n=this,e=(i.prototype.configure.call(this,e,t),this.getConfig()),o=e.settingsPanel;this.onClick.subscribe(function(){o.isShown()||n.visibleSettingsPanels.slice().forEach(function(e){return e.hide()}),o.toggleHidden()}),o.onShow.subscribe(function(){n.on()}),o.onHide.subscribe(function(){n.off()}),t.onComponentShow.subscribe(function(e){e instanceof a.SettingsPanel&&(n.visibleSettingsPanels.push(e),e.onHide.subscribeOnce(function(){return l.ArrayUtils.remove(n.visibleSettingsPanels,e)}))}),e.autoHideWhenNoActiveSettings&&(o.onSettingsStateChanged.subscribe(t=function(){o.rootPageHasActiveSettings()?n.isHidden()&&n.show():n.isShown()&&n.hide()}),t())},u);function u(e){var t=i.call(this,e)||this;if(t.visibleSettingsPanels=[],e.settingsPanel)return t.config=t.mergeConfig(e,{cssClass:"ui-settingstogglebutton",text:c.i18n.getLocalizer("settings"),settingsPanel:null,autoHideWhenNoActiveSettings:!0,role:"pop-up button"},t.config),t.getDomElement().attr("aria-owns",e.settingsPanel.getActivePage().getConfig().id),t.getDomElement().attr("aria-haspopup","true"),t;throw new Error("Required SettingsPanel is missing")}n.SettingsToggleButton=e},{"../arrayutils":1,"../localization/i18n":91,"./settingspanel":45,"./togglebutton":74}],52:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),e=(Object.defineProperty(n,"__esModule",{value:!0}),n.Spacer=void 0,e("./component")),e=(i=e.Component,r(s,i),s.prototype.onShowEvent=function(){},s.prototype.onHideEvent=function(){},s.prototype.onHoverChangedEvent=function(e){},s);function s(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-spacer"},t.config),t}n.Spacer=e},{"./component":18}],53:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SubtitleListBox=void 0,e("./listbox")),a=e("../subtitleutils"),e=(i=s.ListBox,r(l,i),l.prototype.configure=function(e,t){i.prototype.configure.call(this,e,t),new a.SubtitleSwitchHandler(e,this,t)},l);function l(){return null!==i&&i.apply(this,arguments)||this}n.SubtitleListBox=e},{"../subtitleutils":112,"./listbox":29}],54:[function(e,t,n){"use strict";var o,s,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.SubtitleRegionContainer=n.SubtitleRegionContainerManager=n.SubtitleLabel=n.SubtitleOverlay=void 0,e("./container")),a=e("./label"),l=e("./controlbar"),c=e("../eventdispatcher"),u=e("../dom"),p=e("../localization/i18n"),g=e("../vttutils");n.SubtitleOverlay=(s=r.Container,i(y,s),y.prototype.configure=function(e,o){function t(){i.hide(),i.subtitleContainerManager.clear(),r.clear(),i.removeComponents(),i.updateComponents()}function n(){r.clearInactiveCues(e.getCurrentTime()).forEach(function(e){i.subtitleContainerManager.removeLabel(e.label)}),i.updateComponents()}var i=this,r=(s.prototype.configure.call(this,e,o),new h);this.subtitleManager=r,this.subtitleContainerManager=new v(this),e.on(e.exports.PlayerEvent.CueEnter,function(e){var t=i.generateLabel(e);r.cueEnter(e,t),i.preprocessLabelEventCallback.dispatch(e,t),i.previewSubtitleActive&&i.subtitleContainerManager.removeLabel(i.previewSubtitle),i.show(),i.subtitleContainerManager.addLabel(t,i.getDomElement().size()),i.updateComponents(),o.getConfig().forceSubtitlesIntoViewContainer&&i.handleSubtitleCropping(t)}),e.on(e.exports.PlayerEvent.CueUpdate,function(e){var t=i.generateLabel(e),n=r.cueUpdate(e,t);i.preprocessLabelEventCallback.dispatch(e,t),n&&i.subtitleContainerManager.replaceLabel(n,t),o.getConfig().forceSubtitlesIntoViewContainer&&i.handleSubtitleCropping(t)}),e.on(e.exports.PlayerEvent.CueExit,function(e){e=r.cueExit(e);e&&(i.subtitleContainerManager.removeLabel(e),i.updateComponents()),r.hasCues||(i.previewSubtitleActive?(i.subtitleContainerManager.addLabel(i.previewSubtitle),i.updateComponents()):i.hide())});e.on(e.exports.PlayerEvent.AudioChanged,t),e.on(e.exports.PlayerEvent.SubtitleDisabled,t),e.on(e.exports.PlayerEvent.Seeked,n),e.on(e.exports.PlayerEvent.TimeShifted,n),e.on(e.exports.PlayerEvent.PlaybackFinished,t),e.on(e.exports.PlayerEvent.SourceUnloaded,t),o.onComponentShow.subscribe(function(e){e instanceof l.ControlBar&&i.getDomElement().addClass(i.prefixCss(y.CLASS_CONTROLBAR_VISIBLE))}),o.onComponentHide.subscribe(function(e){e instanceof l.ControlBar&&i.getDomElement().removeClass(i.prefixCss(y.CLASS_CONTROLBAR_VISIBLE))}),this.configureCea608Captions(e,o),t()},y.prototype.setFontSizeFactor=function(e){this.FONT_SIZE_FACTOR=Math.max(.5,Math.min(2,e)),this.recalculateCEAGrid()},y.prototype.recalculateCEAGrid=function(){this.CEA608_NUM_ROWS=Math.floor(y.DEFAULT_CEA608_NUM_ROWS/Math.max(this.FONT_SIZE_FACTOR,1)),this.CEA608_NUM_COLUMNS=Math.floor(y.DEFAULT_CEA608_NUM_COLUMNS/this.FONT_SIZE_FACTOR),this.CEA608_COLUMN_OFFSET=100/this.CEA608_NUM_COLUMNS},y.prototype.detectCroppedSubtitleLabel=function(e){var t=this.getDomElement().get(0),e=e.getBoundingClientRect(),t=t.getBoundingClientRect();return{top:e.top<t.top,right:e.right>t.right,bottom:e.bottom>t.bottom,left:e.left<t.left}},y.prototype.handleSubtitleCropping=function(e){var e=e.getDomElement(),t=this.detectCroppedSubtitleLabel(e.get(0));t.top&&(e.css("top","0"),e.removeCss("bottom")),t.right&&(e.css("right","0"),e.removeCss("left")),t.bottom&&(e.css("bottom","0"),e.removeCss("top")),t.left&&(e.css("left","0"),e.removeCss("right"))},y.prototype.resolveRowNumber=function(e){return 1<this.FONT_SIZE_FACTOR&&e>this.CEA608_NUM_ROWS?e-(y.DEFAULT_CEA608_NUM_ROWS-this.CEA608_NUM_ROWS):e},y.prototype.generateLabel=function(e){var t=e.region,n=(null==(n=e.position)?void 0:n.row)||0;return e.position&&(e.position.row=this.resolveRowNumber(e.position.row)||0,e.position.column=e.position.column||0,t=t||"cea608-row-".concat(e.position.row)),new m({text:e.html||h.generateImageTagText(e.image)||e.text,vtt:e.vtt,region:t,regionStyle:e.regionStyle,originalRowPosition:n})},y.prototype.resolveFontSizeFactor=function(e){return parseInt(e)/100},y.prototype.updateRegionRowPosition=function(e){var t,n,o,i=e.getDomElement().get()[0],e=e.getComponents()[0];i&&e&&(i=i.classList,e=null==(e=e.getConfig())?void 0:e.originalRowPosition,t=/subtitle-position-cea608-row-(\d+)/,n=Array.from(i).find(function(e){return t.test(e)}))&&(o=(o=t.exec(n))?parseInt(o[1],10):null,e=this.resolveRowNumber(null!=e?e:o),o=n.replace(t,"subtitle-position-cea608-row-".concat(e)),i.replace(n,o))},y.prototype.configureCea608Captions=function(e,t){function n(){g.getDomElement().removeClass(g.prefixCss(y.CLASS_CEA_608)),g.cea608Enabled=!1}var p,o,g=this,f=0,d=0,i=!0,h=.2,t=(this.cea608Enabled=!1,t.getSubtitleSettingsManager()),r=(null!=t.fontSize.value&&(o=this.resolveFontSizeFactor(t.fontSize.value),this.setFontSizeFactor(o)),t.fontSize.onChanged.subscribe(function(e,t){t.isSet()?(t=g.resolveFontSizeFactor(t.value),g.setFontSizeFactor(t)):g.setFontSizeFactor(1),r()}),function(){var e=new m({text:"X"}),t=(e.getDomElement().css({"font-size":"200px","line-height":"200px",visibility:"hidden"}),g.addComponent(e),g.updateComponents(),g.show(),e.getDomElement().width()*g.FONT_SIZE_FACTOR),n=e.getDomElement().height()*g.FONT_SIZE_FACTOR,o=t/n,e=(g.removeComponent(e),g.updateComponents(),g.subtitleManager.hasCues||g.hide(),g.getDomElement()),i=e.width()-10,r=e.height(),t=t*g.CEA608_NUM_COLUMNS/(n*g.CEA608_NUM_ROWS),s=0;d=t<i/r?(s=r/g.CEA608_NUM_ROWS,f=s*(1-h),n=i/g.CEA608_NUM_COLUMNS,t=f*o,Math.max(n-t,0)):(s=i/g.CEA608_NUM_COLUMNS/o,f=s*(1-h),0),p=s*h;g.getComponents().forEach(function(e){e instanceof P&&g.updateRegionRowPosition(e)}),e.get().forEach(function(e){e.style.setProperty("--cea608-row-height","".concat(s,"px"))});for(var a=function(e){var t=1<g.FONT_SIZE_FACTOR;e.getDomElement().css({"font-size":"".concat(f,"px"),"line-height":"".concat(f,"px"),"letter-spacing":"".concat(t?0:d,"px"),"white-space":"".concat(t?"nowrap":"normal"),left:t&&y.DEFAULT_CAPTION_LEFT_OFFSET}),e.regionStyle="line-height: ".concat(f,"px; padding: ").concat(p/2,"px; height: ").concat(f,"px")},l=0,c=g.getComponents();l<c.length;l++){var u=c[l];u instanceof P&&(u.getDomElement().css({"line-height":"".concat(f,"px"),padding:"".concat(p/2,"px"),height:"".concat(f,"px")}),u.getComponents().forEach(function(e){a(e)})),u instanceof m&&a(u)}});e.on(e.exports.PlayerEvent.PlayerResized,function(){g.cea608Enabled?r():i=!0}),this.preprocessLabelEventCallback.subscribe(function(e,t){var n;null!=e.position&&(g.cea608Enabled||(g.cea608Enabled=!0,g.getDomElement().addClass(g.prefixCss(y.CLASS_CEA_608)),i&&(r(),i=!1)),n=1<g.FONT_SIZE_FACTOR,"0%"!==(e=e.position.column*g.CEA608_COLUMN_OFFSET+"%")&&!n||(e=y.DEFAULT_CAPTION_LEFT_OFFSET),t.getDomElement().css({left:e,"font-size":"".concat(f,"px"),"letter-spacing":"".concat(n?0:d,"px"),"white-space":"".concat(n?"nowrap":"normal")}),t.regionStyle="line-height: ".concat(f,"px; padding: ").concat(p/2,"px; height: ").concat(f,"px"))});e.on(e.exports.PlayerEvent.CueExit,function(){g.subtitleManager.hasCues||n()}),e.on(e.exports.PlayerEvent.SourceUnloaded,n),e.on(e.exports.PlayerEvent.SubtitleEnable,n),e.on(e.exports.PlayerEvent.SubtitleDisabled,n)},y.prototype.enablePreviewSubtitleLabel=function(){this.subtitleManager.hasCues||(this.previewSubtitleActive=!0,this.subtitleContainerManager.addLabel(this.previewSubtitle),this.updateComponents(),this.show())},y.prototype.removePreviewSubtitleLabel=function(){this.previewSubtitleActive&&(this.previewSubtitleActive=!1,this.subtitleContainerManager.removeLabel(this.previewSubtitle),this.updateComponents())},y.CLASS_CONTROLBAR_VISIBLE="controlbar-visible",y.CLASS_CEA_608="cea608",y.DEFAULT_CEA608_NUM_ROWS=15,y.DEFAULT_CEA608_NUM_COLUMNS=32,y.DEFAULT_CAPTION_LEFT_OFFSET="0.5%",y);function y(e){var t=s.call(this,e=void 0===e?{}:e)||this;return t.preprocessLabelEventCallback=new c.EventDispatcher,t.FONT_SIZE_FACTOR=1,t.CEA608_NUM_ROWS=y.DEFAULT_CEA608_NUM_ROWS,t.CEA608_NUM_COLUMNS=y.DEFAULT_CEA608_NUM_COLUMNS,t.CEA608_COLUMN_OFFSET=100/t.CEA608_NUM_COLUMNS,t.cea608Enabled=!1,t.filterFontSizeOptions=function(e){return!t.cea608Enabled||null===e.key||(e=parseInt(e.key,10),!isNaN(e)&&e<=200)},t.recalculateCEAGrid(),t.previewSubtitleActive=!1,t.previewSubtitle=new m({text:p.i18n.getLocalizer("subtitle.example")}),t.config=t.mergeConfig(e,{cssClass:"ui-subtitle-overlay"},t.config),t}f=a.Label,i(d,f),Object.defineProperty(d.prototype,"vtt",{get:function(){return this.config.vtt},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"region",{get:function(){return this.config.region},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"regionStyle",{get:function(){return this.config.regionStyle},set:function(e){this.config.regionStyle=e},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"originalRowPosition",{get:function(){return this.config.originalRowPosition},set:function(e){this.config.originalRowPosition=e},enumerable:!1,configurable:!0});var f,m=d;function d(e){var t=f.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-subtitle-label"},t.config),t}n.SubtitleLabel=m;b.calculateId=function(e){var t=e.start+"-"+e.text;return e.position&&(t+="-"+e.position.row+"-"+e.position.column),t},b.prototype.cueEnter=function(e,t){this.addCueToMap(e,t)},b.prototype.cueUpdate=function(e,t){var n=this.popCueFromMap(e);if(n)return this.addCueToMap(e,t),n},b.prototype.addCueToMap=function(e,t){var n=b.calculateId(e);this.activeSubtitleCueMap[n]=this.activeSubtitleCueMap[n]||[],this.activeSubtitleCueMap[n].push({event:e,label:t}),this.activeSubtitleCueCount++},b.prototype.popCueFromMap=function(e){var e=b.calculateId(e),e=this.activeSubtitleCueMap[e];if(e&&0<e.length)return e=e.shift(),this.activeSubtitleCueCount--,e.label},b.prototype.clearInactiveCues=function(t){var n=this,o=[];return Object.keys(this.activeSubtitleCueMap).forEach(function(e){n.activeSubtitleCueMap[e].forEach(function(e){(t<e.event.start||t>e.event.end)&&(n.popCueFromMap(e.event),o.push(e))})}),o},b.generateImageTagText=function(e){if(e)return(e=new u.DOM("img",{src:e})).css("width","100%"),e.get(0).outerHTML},b.prototype.getCues=function(e){e=b.calculateId(e),e=this.activeSubtitleCueMap[e];if(e&&0<e.length)return e.map(function(e){return e.label})},b.prototype.cueExit=function(e){return this.popCueFromMap(e)},Object.defineProperty(b.prototype,"cueCount",{get:function(){return this.activeSubtitleCueCount},enumerable:!1,configurable:!0}),Object.defineProperty(b.prototype,"hasCues",{get:function(){return 0<this.cueCount},enumerable:!1,configurable:!0}),b.prototype.clear=function(){this.activeSubtitleCueMap={},this.activeSubtitleCueCount=0};var h=b;function b(){this.activeSubtitleCueMap={},this.activeSubtitleCueCount=0}C.prototype.getRegion=function(e){return e.vtt?{regionContainerId:e.vtt.region&&e.vtt.region.id?e.vtt.region.id:"vtt",regionName:"vtt"}:{regionContainerId:e.region||"default",regionName:e.region||"default"}},C.prototype.addLabel=function(e,t){var n=this.getRegion(e),o=n.regionContainerId,n=n.regionName,n=["subtitle-position-".concat(n)];if(e.vtt&&e.vtt.region&&n.push("vtt-region-".concat(e.vtt.region.id)),!this.subtitleRegionContainers[o]){var i,n=new P({cssClasses:n});for(i in this.subtitleRegionContainers[o]=n,e.regionStyle&&n.getDomElement().attr("style",e.regionStyle),e.vtt&&n.getDomElement().css("position","static"),n.getDomElement(),this.subtitleRegionContainers)this.subtitleOverlay.addComponent(this.subtitleRegionContainers[i])}this.subtitleRegionContainers[o].addLabel(e,t)},C.prototype.replaceLabel=function(e,t){var n=this.getRegion(e).regionContainerId;this.subtitleRegionContainers[n].removeLabel(e),this.subtitleRegionContainers[n].addLabel(t)},C.prototype.removeLabel=function(e){var t=e.vtt?e.vtt.region&&e.vtt.region.id?e.vtt.region.id:"vtt":e.region||"default";this.subtitleRegionContainers[t].removeLabel(e),this.subtitleRegionContainers[t].isEmpty()&&(this.subtitleOverlay.removeComponent(this.subtitleRegionContainers[t]),delete this.subtitleRegionContainers[t])},C.prototype.clear=function(){for(var e in this.subtitleRegionContainers)this.subtitleOverlay.removeComponent(this.subtitleRegionContainers[e]);this.subtitleRegionContainers={}};var v=C;function C(e){this.subtitleOverlay=e,this.subtitleRegionContainers={},this.subtitleOverlay=e}n.SubtitleRegionContainerManager=v;S=r.Container,i(w,S),w.prototype.addLabel=function(e,t){this.labelCount++,e.vtt&&(e.vtt.region&&t&&g.VttUtils.setVttRegionStyles(this,e.vtt.region,t),g.VttUtils.setVttCueBoxStyles(e,t)),this.addComponent(e),this.updateComponents()},w.prototype.removeLabel=function(e){this.labelCount--,this.removeComponent(e),this.updateComponents()},w.prototype.isEmpty=function(){return 0===this.labelCount};var S,P=w;function w(e){var t=S.call(this,e=void 0===e?{}:e)||this;return t.labelCount=0,t.config=t.mergeConfig(e,{cssClass:"subtitle-region-container"},t.config),t}n.SubtitleRegionContainer=P},{"../dom":84,"../eventdispatcher":86,"../localization/i18n":91,"../vttutils":119,"./container":19,"./controlbar":20,"./label":28}],55:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SubtitleSelectBox=void 0,e("./selectbox")),a=e("../subtitleutils"),l=e("../localization/i18n"),e=(i=s.SelectBox,r(c,i),c.prototype.configure=function(e,t){i.prototype.configure.call(this,e,t),new a.SubtitleSwitchHandler(e,this,t)},c);function c(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitleselectbox"],ariaLabel:l.i18n.getLocalizer("subtitle.select")},t.config),t}n.SubtitleSelectBox=e},{"../localization/i18n":91,"../subtitleutils":112,"./selectbox":44}],56:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.BackgroundColorSelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){function n(){o.settingsManager.backgroundColor.isSet()&&o.settingsManager.backgroundOpacity.isSet()?o.toggleOverlayClass("bgcolor-"+o.settingsManager.backgroundColor.value+o.settingsManager.backgroundOpacity.value):o.toggleOverlayClass(null)}var o=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("white",a.i18n.getLocalizer("colors.white")),this.addItem("black",a.i18n.getLocalizer("colors.black")),this.addItem("red",a.i18n.getLocalizer("colors.red")),this.addItem("green",a.i18n.getLocalizer("colors.green")),this.addItem("blue",a.i18n.getLocalizer("colors.blue")),this.addItem("cyan",a.i18n.getLocalizer("colors.cyan")),this.addItem("yellow",a.i18n.getLocalizer("colors.yellow")),this.addItem("magenta",a.i18n.getLocalizer("colors.magenta"));this.onItemSelected.subscribe(function(e,t){o.settingsManager.backgroundColor.value=t}),this.settingsManager.backgroundColor.onChanged.subscribe(function(e,t){o.settingsManager.backgroundColor.isSet()?o.settingsManager.backgroundOpacity.isSet()||(o.settingsManager.backgroundOpacity.value="100"):o.settingsManager.backgroundOpacity.clear(),o.selectItem(t.value),n()}),this.settingsManager.backgroundOpacity.onChanged.subscribe(function(){n()}),this.settingsManager.backgroundColor.isSet()&&this.selectItem(this.settingsManager.backgroundColor.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitlesettingsbackgroundcolorselectbox"]},t.config),t}n.BackgroundColorSelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],57:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.BackgroundOpacitySelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("100",a.i18n.getLocalizer("percent",{value:100})),this.addItem("75",a.i18n.getLocalizer("percent",{value:75})),this.addItem("50",a.i18n.getLocalizer("percent",{value:50})),this.addItem("25",a.i18n.getLocalizer("percent",{value:25})),this.addItem("0",a.i18n.getLocalizer("percent",{value:0})),this.onItemSelected.subscribe(function(e,t){n.settingsManager.backgroundOpacity.value=t,n.settingsManager.backgroundOpacity.isSet()?n.settingsManager.backgroundColor.isSet()||(n.settingsManager.backgroundColor.value="black"):n.settingsManager.backgroundColor.clear()}),this.settingsManager.backgroundOpacity.onChanged.subscribe(function(e,t){n.selectItem(t.value)}),this.settingsManager.backgroundOpacity.isSet()&&this.selectItem(this.settingsManager.backgroundOpacity.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitlesettingsbackgroundopacityselectbox"]},t.config),t}n.BackgroundOpacitySelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],58:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.CharacterEdgeColorSelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("white",a.i18n.getLocalizer("colors.white")),this.addItem("black",a.i18n.getLocalizer("colors.black")),this.addItem("red",a.i18n.getLocalizer("colors.red")),this.addItem("green",a.i18n.getLocalizer("colors.green")),this.addItem("blue",a.i18n.getLocalizer("colors.blue")),this.addItem("cyan",a.i18n.getLocalizer("colors.cyan")),this.addItem("yellow",a.i18n.getLocalizer("colors.yellow")),this.addItem("magenta",a.i18n.getLocalizer("colors.magenta")),this.onItemSelected.subscribe(function(e,t){n.settingsManager.characterEdgeColor.value=t,n.settingsManager.characterEdgeColor.isSet()?n.settingsManager.characterEdge.isSet()||(n.settingsManager.characterEdge.value="uniform"):n.settingsManager.characterEdge.clear()}),this.settingsManager.characterEdgeColor.onChanged.subscribe(function(e,t){n.selectItem(t.value)}),this.settingsManager.characterEdgeColor.isSet()&&this.selectItem(this.settingsManager.characterEdgeColor.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitle-settings-character-edge-color-select-box"]},t.config),t}n.CharacterEdgeColorSelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],59:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.CharacterEdgeSelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){function n(){o.settingsManager.characterEdge.isSet()&&o.settingsManager.characterEdgeColor.isSet()?o.toggleOverlayClass("characteredge-"+o.settingsManager.characterEdge.value+"-"+o.settingsManager.characterEdgeColor.value):o.toggleOverlayClass(null)}var o=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("raised",a.i18n.getLocalizer("settings.subtitles.characterEdge.raised")),this.addItem("depressed",a.i18n.getLocalizer("settings.subtitles.characterEdge.depressed")),this.addItem("uniform",a.i18n.getLocalizer("settings.subtitles.characterEdge.uniform")),this.addItem("dropshadowed",a.i18n.getLocalizer("settings.subtitles.characterEdge.dropshadowed"));this.onItemSelected.subscribe(function(e,t){o.settingsManager.characterEdge.value=t}),this.settingsManager.characterEdge.onChanged.subscribe(function(e,t){o.settingsManager.characterEdge.isSet()?o.settingsManager.characterEdgeColor.isSet()||(o.settingsManager.characterEdgeColor.value="black"):o.settingsManager.characterEdgeColor.clear(),o.selectItem(t.value),n()}),this.settingsManager.characterEdgeColor.onChanged.subscribe(function(){n()}),this.settingsManager.characterEdge.isSet()&&this.selectItem(this.settingsManager.characterEdge.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitlesettingscharacteredgeselectbox"]},t.config),t}n.CharacterEdgeSelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],60:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.FontColorSelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){function n(){o.settingsManager.fontColor.isSet()&&o.settingsManager.fontOpacity.isSet()?o.toggleOverlayClass("fontcolor-"+o.settingsManager.fontColor.value+o.settingsManager.fontOpacity.value):o.toggleOverlayClass(null)}var o=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("white",a.i18n.getLocalizer("colors.white")),this.addItem("black",a.i18n.getLocalizer("colors.black")),this.addItem("red",a.i18n.getLocalizer("colors.red")),this.addItem("green",a.i18n.getLocalizer("colors.green")),this.addItem("blue",a.i18n.getLocalizer("colors.blue")),this.addItem("cyan",a.i18n.getLocalizer("colors.cyan")),this.addItem("yellow",a.i18n.getLocalizer("colors.yellow")),this.addItem("magenta",a.i18n.getLocalizer("colors.magenta"));this.onItemSelected.subscribe(function(e,t){o.settingsManager.fontColor.value=t}),this.settingsManager.fontColor.onChanged.subscribe(function(e,t){o.settingsManager.fontColor.isSet()?o.settingsManager.fontOpacity.isSet()||(o.settingsManager.fontOpacity.value="100"):o.settingsManager.fontOpacity.clear(),o.selectItem(t.value),n()}),this.settingsManager.fontOpacity.onChanged.subscribe(function(){n()}),this.settingsManager.fontColor.isSet()&&this.selectItem(this.settingsManager.fontColor.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitlesettingsfontcolorselectbox"]},t.config),t}n.FontColorSelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],61:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.FontFamilySelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("monospacedserif",a.i18n.getLocalizer("settings.subtitles.font.family.monospacedserif")),this.addItem("proportionalserif",a.i18n.getLocalizer("settings.subtitles.font.family.proportionalserif")),this.addItem("monospacedsansserif",a.i18n.getLocalizer("settings.subtitles.font.family.monospacedsansserif")),this.addItem("proportionalsansserif",a.i18n.getLocalizer("settings.subtitles.font.family.proportionalsansserif")),this.addItem("casual",a.i18n.getLocalizer("settings.subtitles.font.family.casual")),this.addItem("cursive",a.i18n.getLocalizer("settings.subtitles.font.family.cursive")),this.addItem("smallcapital",a.i18n.getLocalizer("settings.subtitles.font.family.smallcapital")),this.settingsManager.fontFamily.onChanged.subscribe(function(e,t){t.isSet()?n.toggleOverlayClass("fontfamily-"+t.value):n.toggleOverlayClass(null),n.selectItem(t.value)}),this.onItemSelected.subscribe(function(e,t){n.settingsManager.fontFamily.value=t}),this.settingsManager.fontFamily.isSet()&&this.selectItem(this.settingsManager.fontFamily.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitlesettingsfontfamilyselectbox"]},t.config),t}n.FontFamilySelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],62:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.FontOpacitySelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("100",a.i18n.getLocalizer("percent",{value:100})),this.addItem("75",a.i18n.getLocalizer("percent",{value:75})),this.addItem("50",a.i18n.getLocalizer("percent",{value:50})),this.addItem("25",a.i18n.getLocalizer("percent",{value:25})),this.onItemSelected.subscribe(function(e,t){n.settingsManager.fontOpacity.value=t,n.settingsManager.fontOpacity.isSet()?n.settingsManager.fontColor.isSet()||(n.settingsManager.fontColor.value="white"):n.settingsManager.fontColor.clear()}),this.settingsManager.fontOpacity.onChanged.subscribe(function(e,t){n.selectItem(t.value)}),this.settingsManager.fontOpacity.isSet()&&this.selectItem(this.settingsManager.fontOpacity.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitlesettingsfontopacityselectbox"]},t.config),t}n.FontOpacitySelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],63:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.FontSizeSelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.getFontSizeOptions=function(){return[{key:null,label:a.i18n.getLocalizer("default")},{key:"50",label:a.i18n.getLocalizer("percent",{value:50})},{key:"75",label:a.i18n.getLocalizer("percent",{value:75})},{key:"100",label:a.i18n.getLocalizer("percent",{value:100})},{key:"150",label:a.i18n.getLocalizer("percent",{value:150})},{key:"200",label:a.i18n.getLocalizer("percent",{value:200})},{key:"300",label:a.i18n.getLocalizer("percent",{value:300})},{key:"400",label:a.i18n.getLocalizer("percent",{value:400})}]},l.prototype.populateItemsWithFilter=function(){this.clearItems();for(var e=0,t=this.getFontSizeOptions();e<t.length;e++){var n=t[e];this.config.filter&&!this.config.filter(n)||this.addItem(n.key,n.label)}this.settingsManager.fontSize.isSet()&&this.selectItem(this.settingsManager.fontSize.value)},l.prototype.reapplyFilterAndReload=function(){this.populateItemsWithFilter()},l.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.populateItemsWithFilter(),this.onShow.subscribe(function(){n.populateItemsWithFilter()}),this.settingsManager.fontSize.onChanged.subscribe(function(e,t){t.isSet()?n.toggleOverlayClass("fontsize-"+t.value):n.toggleOverlayClass(null),n.selectItem(t.value)}),this.onItemSelected.subscribe(function(e,t){n.settingsManager.fontSize.value=t})},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitlesettingsfontsizeselectbox"]},t.config),t}n.FontSizeSelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],64:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.FontStyleSelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("italic",a.i18n.getLocalizer("settings.subtitles.font.style.italic")),this.addItem("bold",a.i18n.getLocalizer("settings.subtitles.font.style.bold")),null!=(e=this.settingsManager)&&e.fontStyle.onChanged.subscribe(function(e,t){t.isSet()?n.toggleOverlayClass("fontstyle-"+t.value):n.toggleOverlayClass(null),n.selectItem(t.value)}),this.onItemSelected.subscribe(function(e,t){n.settingsManager&&(n.settingsManager.fontStyle.value=t)}),null!=(t=this.settingsManager)&&t.fontStyle.isSet()&&this.selectItem(this.settingsManager.fontStyle.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitle-settings-font-style-select-box"]},t.config),t}n.FontStyleSelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],65:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),e=(Object.defineProperty(n,"__esModule",{value:!0}),n.SubtitleSettingSelectBox=void 0,e("../selectbox")),e=(i=e.SelectBox,r(s,i),s.prototype.toggleOverlayClass=function(e){this.currentCssClass&&(this.overlay.getDomElement().removeClass(this.currentCssClass),this.currentCssClass=null),e&&(this.currentCssClass=this.prefixCss(e),this.overlay.getDomElement().addClass(this.currentCssClass))},s.prototype.configure=function(e,t){this.settingsManager=t.getSubtitleSettingsManager()},s);function s(e){var t=i.call(this,e)||this;return t.overlay=e.overlay,t}n.SubtitleSettingSelectBox=e},{"../selectbox":44}],66:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SubtitleSettingsLabel=void 0,e("../container")),a=e("../../dom"),l=e("../../localization/i18n"),e=(i=s.Container,r(c,i),c.prototype.toDomElement=function(){return new a.DOM("label",{id:this.config.id,class:this.getCssClasses(),for:this.for},this).append(new a.DOM("span",{}).html(l.i18n.performLocalization(this.text)),this.opener.getDomElement())},c);function c(e){var t=i.call(this,e)||this;return t.opener=e.opener,t.text=e.text,t.for=e.for,t.config=t.mergeConfig(e,{cssClass:"ui-label",components:[t.opener]},t.config),t}n.SubtitleSettingsLabel=e},{"../../dom":84,"../../localization/i18n":91,"../container":19}],67:[function(e,t,n){"use strict";var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.SubtitleSettingsProperty=n.SubtitleSettingsManager=void 0,e("../../storageutils")),s=e("../component"),a=e("../../eventdispatcher");function l(){this._properties={fontColor:new g(this),fontOpacity:new g(this),fontFamily:new g(this),fontSize:new g(this),fontStyle:new g(this),characterEdge:new g(this),characterEdgeColor:new g(this),backgroundColor:new g(this),backgroundOpacity:new g(this),windowColor:new g(this),windowOpacity:new g(this)},this.userSettings={},this.localStorageKey=u.instance().prefixCss("subtitlesettings")}l.prototype.reset=function(){for(var e in this._properties)this._properties[e].clear()},Object.defineProperty(l.prototype,"fontColor",{get:function(){return this._properties.fontColor},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"fontOpacity",{get:function(){return this._properties.fontOpacity},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"fontFamily",{get:function(){return this._properties.fontFamily},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"fontSize",{get:function(){return this._properties.fontSize},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"fontStyle",{get:function(){return this._properties.fontStyle},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"characterEdge",{get:function(){return this._properties.characterEdge},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"characterEdgeColor",{get:function(){return this._properties.characterEdgeColor},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"backgroundColor",{get:function(){return this._properties.backgroundColor},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"backgroundOpacity",{get:function(){return this._properties.backgroundOpacity},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"windowColor",{get:function(){return this._properties.windowColor},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"windowOpacity",{get:function(){return this._properties.windowOpacity},enumerable:!1,configurable:!0}),l.prototype.initialize=function(){var e,o=this,t=this;for(e in this._properties)!function(n){t._properties[n].onChanged.subscribe(function(e,t){t.isSet()?o.userSettings[n]=t.value:delete o.userSettings[n],o.save()})}(e);this.load()},l.prototype.save=function(){r.StorageUtils.setObject(this.localStorageKey,this.userSettings)},l.prototype.load=function(){for(var e in this.userSettings=r.StorageUtils.getObject(this.localStorageKey)||{},this.userSettings)this._properties[e].value=this.userSettings[e]},n.SubtitleSettingsManager=l;c=s.Component,i(p,c),p.instance=function(){return p._instance=p._instance?p._instance:new p},p.prototype.prefixCss=function(e){return c.prototype.prefixCss.call(this,e)};var c,u=p;function p(){return null!==c&&c.apply(this,arguments)||this}f.prototype.isSet=function(){return null!=this._value},f.prototype.clear=function(){this._value=null,this.onChangedEvent(null)},Object.defineProperty(f.prototype,"value",{get:function(){return this._value},set:function(e){this._value=e="string"==typeof e&&"null"===e?null:e,this.onChangedEvent(e)},enumerable:!1,configurable:!0}),f.prototype.onChangedEvent=function(e){this._onChanged.dispatch(this._manager,this)},Object.defineProperty(f.prototype,"onChanged",{get:function(){return this._onChanged.getEvent()},enumerable:!1,configurable:!0});var g=f;function f(e){this._manager=e,this._onChanged=new a.EventDispatcher}n.SubtitleSettingsProperty=g},{"../../eventdispatcher":86,"../../storageutils":110,"../component":18}],68:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SubtitleSettingsPanelPage=void 0,e("../settingspanelpage")),a=e("./fontsizeselectbox"),l=e("./fontstyleselectbox"),c=e("./fontfamilyselectbox"),u=e("./fontcolorselectbox"),p=e("./fontopacityselectbox"),g=e("./characteredgeselectbox"),f=e("./characteredgecolorselectbox"),d=e("./backgroundcolorselectbox"),h=e("./backgroundopacityselectbox"),y=e("./windowcolorselectbox"),m=e("./windowopacityselectbox"),b=e("./subtitlesettingsresetbutton"),v=e("../settingspanelpagebackbutton"),C=e("../settingspanelitem"),S=e("../../localization/i18n"),e=(i=s.SettingsPanelPage,r(P,i),P.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.onActive.subscribe(function(){n.overlay.enablePreviewSubtitleLabel();var e=n.getComponents().find(function(e){return e instanceof C.SettingsPanelItem&&e.getComponents().some(function(e){return e instanceof a.FontSizeSelectBox})});e&&null!=(e=e.getComponents().find(function(e){return e instanceof a.FontSizeSelectBox}))&&e.reapplyFilterAndReload()}),this.onInactive.subscribe(function(){n.overlay.removePreviewSubtitleLabel()})},P);function P(e){var t=i.call(this,e)||this;return t.overlay=e.overlay,t.settingsPanel=e.settingsPanel,t.config=t.mergeConfig(e,{components:[new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.size"),new a.FontSizeSelectBox({overlay:t.overlay,filter:function(e){return t.overlay.filterFontSizeOptions(e)}})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.style"),new l.FontStyleSelectBox({overlay:t.overlay})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.family"),new c.FontFamilySelectBox({overlay:t.overlay})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.color"),new u.FontColorSelectBox({overlay:t.overlay})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.opacity"),new p.FontOpacitySelectBox({overlay:t.overlay})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.characterEdge"),new g.CharacterEdgeSelectBox({overlay:t.overlay})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.characterEdge.color"),new f.CharacterEdgeColorSelectBox({overlay:t.overlay})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.background.color"),new d.BackgroundColorSelectBox({overlay:t.overlay})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.background.opacity"),new h.BackgroundOpacitySelectBox({overlay:t.overlay})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.window.color"),new y.WindowColorSelectBox({overlay:t.overlay})),new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.window.opacity"),new m.WindowOpacitySelectBox({overlay:t.overlay})),new C.SettingsPanelItem(new v.SettingsPanelPageBackButton({container:t.settingsPanel,text:S.i18n.getLocalizer("back")}),new b.SubtitleSettingsResetButton({}),{role:"menubar"})]},t.config),t}n.SubtitleSettingsPanelPage=e},{"../../localization/i18n":91,"../settingspanelitem":46,"../settingspanelpage":47,"../settingspanelpagebackbutton":48,"./backgroundcolorselectbox":56,"./backgroundopacityselectbox":57,"./characteredgecolorselectbox":58,"./characteredgeselectbox":59,"./fontcolorselectbox":60,"./fontfamilyselectbox":61,"./fontopacityselectbox":62,"./fontsizeselectbox":63,"./fontstyleselectbox":64,"./subtitlesettingsresetbutton":69,"./windowcolorselectbox":70,"./windowopacityselectbox":71}],69:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.SubtitleSettingsResetButton=void 0,e("../button")),a=e("../../localization/i18n"),e=(i=s.Button,r(l,i),l.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.settingsManager=t.getSubtitleSettingsManager(),this.onClick.subscribe(function(){n.settingsManager.reset()})},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-subtitlesettingsresetbutton",text:a.i18n.getLocalizer("reset")},t.config),t}n.SubtitleSettingsResetButton=e},{"../../localization/i18n":91,"../button":12}],70:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.WindowColorSelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){function n(){o.settingsManager.windowColor.isSet()&&o.settingsManager.windowOpacity.isSet()?o.toggleOverlayClass("windowcolor-"+o.settingsManager.windowColor.value+o.settingsManager.windowOpacity.value):o.toggleOverlayClass(null)}var o=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("white",a.i18n.getLocalizer("colors.white")),this.addItem("black",a.i18n.getLocalizer("colors.black")),this.addItem("red",a.i18n.getLocalizer("colors.red")),this.addItem("green",a.i18n.getLocalizer("colors.green")),this.addItem("blue",a.i18n.getLocalizer("colors.blue")),this.addItem("cyan",a.i18n.getLocalizer("colors.cyan")),this.addItem("yellow",a.i18n.getLocalizer("colors.yellow")),this.addItem("magenta",a.i18n.getLocalizer("colors.magenta"));this.onItemSelected.subscribe(function(e,t){o.settingsManager.windowColor.value=t}),this.settingsManager.windowColor.onChanged.subscribe(function(e,t){o.settingsManager.windowColor.isSet()?o.settingsManager.windowOpacity.isSet()||(o.settingsManager.windowOpacity.value="100"):o.settingsManager.windowOpacity.clear(),o.selectItem(t.value),n()}),this.settingsManager.windowOpacity.onChanged.subscribe(function(){n()}),this.settingsManager.windowColor.isSet()&&this.selectItem(this.settingsManager.windowColor.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitlesettingswindowcolorselectbox"]},t.config),t}n.WindowColorSelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],71:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.WindowOpacitySelectBox=void 0,e("./subtitlesettingselectbox")),a=e("../../localization/i18n"),e=(i=s.SubtitleSettingSelectBox,r(l,i),l.prototype.configure=function(e,t){var n=this;i.prototype.configure.call(this,e,t),this.addItem(null,a.i18n.getLocalizer("default")),this.addItem("100",a.i18n.getLocalizer("percent",{value:100})),this.addItem("75",a.i18n.getLocalizer("percent",{value:75})),this.addItem("50",a.i18n.getLocalizer("percent",{value:50})),this.addItem("25",a.i18n.getLocalizer("percent",{value:25})),this.addItem("0",a.i18n.getLocalizer("percent",{value:0})),this.onItemSelected.subscribe(function(e,t){n.settingsManager.windowOpacity.value=t,n.settingsManager.windowOpacity.isSet()?n.settingsManager.windowColor.isSet()||(n.settingsManager.windowColor.value="black"):n.settingsManager.windowColor.clear()}),this.settingsManager.windowOpacity.onChanged.subscribe(function(e,t){n.selectItem(t.value)}),this.settingsManager.windowOpacity.isSet()&&this.selectItem(this.settingsManager.windowOpacity.value)},l);function l(e){var t=i.call(this,e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-subtitlesettingswindowopacityselectbox"]},t.config),t}n.WindowOpacitySelectBox=e},{"../../localization/i18n":91,"./subtitlesettingselectbox":65}],72:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.TimelineMarkersHandler=void 0;var i=e("../dom"),s=e("../playerutils"),o=e("../timeout");function r(e,t,n){this.config=e,this.getSeekBarWidth=t,this.markersContainer=n,this.timelineMarkers=[]}r.prototype.initialize=function(e,t){this.player=e,this.uimanager=t,this.configureMarkers()},r.prototype.configureMarkers=function(){var e=this;this.player.on(this.player.exports.PlayerEvent.SourceUnloaded,function(){return e.clearMarkers()}),this.player.on(this.player.exports.PlayerEvent.AdBreakStarted,function(){return e.clearMarkers()}),this.player.on(this.player.exports.PlayerEvent.AdBreakFinished,function(){return e.updateMarkers()}),this.player.on(this.player.exports.PlayerEvent.PlayerResized,function(){return e.updateMarkersDOM()}),this.player.on(this.player.exports.PlayerEvent.SourceLoaded,function(){e.player.isLive()&&(e.player.on(e.player.exports.PlayerEvent.TimeChanged,function(){return e.updateMarkers()}),e.configureLivePausedTimeshiftUpdater(function(){return e.updateMarkers()}))}),this.uimanager.getConfig().events.onUpdated.subscribe(function(){return e.updateMarkers()}),this.uimanager.onRelease.subscribe(function(){return e.uimanager.getConfig().events.onUpdated.unsubscribe(function(){return e.updateMarkers()})}),this.updateMarkers()},r.prototype.getMarkerAtPosition=function(n){var o=this.config.snappingRange;return this.timelineMarkers.find(function(e){var t=0<e.duration&&n>=e.position-o&&n<=e.position+e.duration+o,e=n>=e.position-o&&n<=e.position+o;return t||e})||null},r.prototype.clearMarkers=function(){this.timelineMarkers=[],this.markersContainer.empty()},r.prototype.removeMarkerFromConfig=function(t){this.uimanager.getConfig().metadata.markers=this.uimanager.getConfig().metadata.markers.filter(function(e){return t!==e})},r.prototype.filterRemovedMarkers=function(){var n=this;this.timelineMarkers=this.timelineMarkers.filter(function(t){var e=n.uimanager.getConfig().metadata.markers.find(function(e){return t.marker===e});return e||n.removeMarkerFromDOM(t),e})},r.prototype.removeMarkerFromDOM=function(e){e.element&&e.element.remove()},r.prototype.updateMarkers=function(){var r=this;!function(e,t){e=e.getDuration()!==1/0||e.isLive(),t=0<t.getConfig().metadata.markers.length;return e&&t}(this.player,this.uimanager)?this.clearMarkers():(this.filterRemovedMarkers(),this.uimanager.getConfig().metadata.markers.forEach(function(t){var e,n,o=function(e,t){var n=function(e){var t,n;return e.isLive()?(t=s.PlayerUtils.getSeekableRangeRespectingLive(e),n=t.start,t.end-n):e.getDuration()}(e),e=100/n*function(e,t,n){return t.isLive()?n-(s.PlayerUtils.getSeekableRangeRespectingLive(t).end-e.time):e.time}(t,e,n),n=100/n*t.duration;e<0&&!isNaN(n)&&(n+=e);100-e<n&&(n=100-e);return{markerDuration:n,markerPosition:e}}(r.player,t),i=o.markerPosition,o=o.markerDuration;e=i,((n=o)<0||isNaN(n))&&e<0?r.removeMarkerFromConfig(t):i<=100&&((n=r.timelineMarkers.find(function(e){return e.marker===t}))?(n.position=i,n.duration=o,r.updateMarkerDOM(n)):(r.timelineMarkers.push(e={marker:t,position:i,duration:o}),r.createMarkerDOM(e)))}))},r.prototype.getMarkerCssProperties=function(e){var t=this.getSeekBarWidth(),n=t/100*(e.position<0?0:e.position),n={transform:"translateX(".concat(n,"px)")};return 0<e.duration&&(t=Math.round(t/100*e.duration),n.width="".concat(t,"px")),n},r.prototype.updateMarkerDOM=function(e){e.element.css(this.getMarkerCssProperties(e))},r.prototype.createMarkerDOM=function(e){var t,n=this,o=["seekbar-marker"].concat(e.marker.cssClasses||[]).map(function(e){return n.prefixCss(e)}),o=new i.DOM("div",{class:o.join(" "),"data-marker-time":String(e.marker.time),"data-marker-title":String(e.marker.title)}).css(this.getMarkerCssProperties(e));e.marker.imageUrl&&(t=new i.DOM("img",{class:this.prefixCss("seekbar-marker-image"),src:e.marker.imageUrl}).on("error",function(){t.remove()}),o.append(t)),e.element=o,this.markersContainer.append(o)},r.prototype.updateMarkersDOM=function(){var t=this;this.timelineMarkers.forEach(function(e){e.element?t.updateMarkerDOM(e):t.createMarkerDOM(e)})},r.prototype.configureLivePausedTimeshiftUpdater=function(e){var t=this;this.pausedTimeshiftUpdater=new o.Timeout(1e3,e,!0),this.player.on(this.player.exports.PlayerEvent.Paused,function(){t.player.isLive()&&t.player.getMaxTimeShift()<0&&t.pausedTimeshiftUpdater.start()}),this.player.on(this.player.exports.PlayerEvent.Play,function(){return t.pausedTimeshiftUpdater.clear()}),this.player.on(this.player.exports.PlayerEvent.Destroy,function(){return t.pausedTimeshiftUpdater.clear()})},r.prototype.prefixCss=function(e){return this.config.cssPrefix+"-"+e},n.TimelineMarkersHandler=r},{"../dom":84,"../playerutils":98,"../timeout":113}],73:[function(e,t,n){"use strict";var o,u,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.TitleBar=void 0,e("./container")),p=e("./metadatalabel"),e=(u=r.Container,i(s,u),s.prototype.configure=function(e,t){for(var o=this,i=(u.prototype.configure.call(this,e,t),this.getConfig()),r=!this.isHidden(),s=!0,n=function(){s=!1;for(var e=0,t=o.getComponents();e<t.length;e++){var n=t[e];if(n instanceof p.MetadataLabel&&!n.isEmpty()){s=!0;break}}o.isShown()?i.keepHiddenWithoutMetadata&&!s&&o.hide():r&&o.show()},a=0,l=this.getComponents();a<l.length;a++){var c=l[a];c instanceof p.MetadataLabel&&c.onTextChanged.subscribe(n)}t.onControlsShow.subscribe(function(){r=!0,i.keepHiddenWithoutMetadata&&!s||o.show()}),t.onControlsHide.subscribe(function(){r=!1,o.hide()}),n()},s);function s(e){var t=u.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-titlebar",hidden:!0,components:[new p.MetadataLabel({content:p.MetadataLabelContent.Title}),new p.MetadataLabel({content:p.MetadataLabelContent.Description})],keepHiddenWithoutMetadata:!1},t.config),t}n.TitleBar=e},{"./container":19,"./metadatalabel":31}],74:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.ToggleButton=void 0,e("./button")),a=e("../eventdispatcher"),e=(i=s.Button,r(l,i),l.prototype.configure=function(e,t){i.prototype.configure.call(this,e,t);e=this.getConfig();this.getDomElement().addClass(this.prefixCss(e.offClass)),this.useAriaPressedAttributeAsToggleIndicator=!this.config.onAriaLabel||!this.config.offAriaLabel,this.useAriaPressedAttributeAsToggleIndicator?this.setAriaAttr("pressed","false"):this.setAriaLabel(this.config.offAriaLabel)},l.prototype.on=function(){var e;this.isOff()&&(e=this.getConfig(),this.onState=!0,this.getDomElement().removeClass(this.prefixCss(e.offClass)),this.getDomElement().addClass(this.prefixCss(e.onClass)),this.onToggleEvent(),this.onToggleOnEvent(),this.useAriaPressedAttributeAsToggleIndicator?this.setAriaAttr("pressed","true"):this.setAriaLabel(this.config.onAriaLabel))},l.prototype.off=function(){var e;this.isOn()&&(e=this.getConfig(),this.onState=!1,this.getDomElement().removeClass(this.prefixCss(e.onClass)),this.getDomElement().addClass(this.prefixCss(e.offClass)),this.onToggleEvent(),this.onToggleOffEvent(),this.useAriaPressedAttributeAsToggleIndicator?this.setAriaAttr("pressed","false"):this.setAriaLabel(this.config.offAriaLabel))},l.prototype.toggle=function(){this.isOn()?this.off():this.on()},l.prototype.isOn=function(){return this.onState},l.prototype.isOff=function(){return!this.isOn()},l.prototype.onClickEvent=function(){i.prototype.onClickEvent.call(this),this.onToggleEvent()},l.prototype.onToggleEvent=function(){this.toggleButtonEvents.onToggle.dispatch(this)},l.prototype.onToggleOnEvent=function(){this.toggleButtonEvents.onToggleOn.dispatch(this)},l.prototype.onToggleOffEvent=function(){this.toggleButtonEvents.onToggleOff.dispatch(this)},Object.defineProperty(l.prototype,"onToggle",{get:function(){return this.toggleButtonEvents.onToggle.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onToggleOn",{get:function(){return this.toggleButtonEvents.onToggleOn.getEvent()},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onToggleOff",{get:function(){return this.toggleButtonEvents.onToggleOff.getEvent()},enumerable:!1,configurable:!0}),l);function l(e){var t=i.call(this,e)||this;t.toggleButtonEvents={onToggle:new a.EventDispatcher,onToggleOn:new a.EventDispatcher,onToggleOff:new a.EventDispatcher};return e.onAriaLabel&&(e.ariaLabel=e.onAriaLabel),t.config=t.mergeConfig(e,{cssClass:"ui-togglebutton",onClass:"on",offClass:"off"},t.config),t.useAriaPressedAttributeAsToggleIndicator=!t.config.onAriaLabel||!t.config.offAriaLabel,t}n.ToggleButton=e},{"../eventdispatcher":86,"./button":12}],75:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.TvNoiseCanvas=void 0,e("./component")),a=e("../dom"),e=(i=s.Component,r(l,i),l.prototype.toDomElement=function(){return this.canvas=new a.DOM("canvas",{class:this.getCssClasses()},this)},l.prototype.start=function(){this.canvasElement=this.canvas.get(0),this.canvasContext=this.canvasElement.getContext("2d"),this.noiseAnimationWindowPos=-this.canvasHeight,this.lastFrameUpdate=0,this.canvasElement.width=this.canvasWidth,this.canvasElement.height=this.canvasHeight,this.renderFrame()},l.prototype.stop=function(){(this.useAnimationFrame?cancelAnimationFrame:clearTimeout)(this.frameUpdateHandlerId)},l.prototype.renderFrame=function(){if(!(this.lastFrameUpdate+this.frameInterval>(new Date).getTime())){for(var e,t=this.canvasWidth,n=this.canvasHeight,o=this.canvasContext.createImageData(t,n),i=0;i<n;i++)for(var r=0;r<t;r++)o.data[e=t*i*4+4*r]=255*Math.random(),(i<this.noiseAnimationWindowPos||i>this.noiseAnimationWindowPos+this.interferenceHeight)&&(o.data[e]*=.85),o.data[1+e]=o.data[e],o.data[2+e]=o.data[e],o.data[3+e]=50;this.canvasContext.putImageData(o,0,0),this.lastFrameUpdate=(new Date).getTime(),this.noiseAnimationWindowPos+=7,this.noiseAnimationWindowPos>n&&(this.noiseAnimationWindowPos=-n)}this.scheduleNextRender()},l.prototype.scheduleNextRender=function(){this.useAnimationFrame?this.frameUpdateHandlerId=window.requestAnimationFrame(this.renderFrame.bind(this)):this.frameUpdateHandlerId=window.setTimeout(this.renderFrame.bind(this),this.frameInterval)},l);function l(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.canvasWidth=160,t.canvasHeight=90,t.interferenceHeight=50,t.lastFrameUpdate=0,t.frameInterval=60,t.useAnimationFrame=!!window.requestAnimationFrame,t.config=t.mergeConfig(e,{cssClass:"ui-tvnoisecanvas"},t.config),t}n.TvNoiseCanvas=e},{"../dom":84,"./component":18}],76:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.UIContainer=void 0,e("./container")),u=e("../dom"),l=e("../timeout"),p=e("../playerutils"),a=e("../eventdispatcher"),c=e("../localization/i18n"),g=e("./button");n.UIContainer=(i=s.Container,r(f,i),f.prototype.configure=function(e,t){var n=this.getConfig();n.userInteractionEventSource?this.userInteractionEventSource=new u.DOM(n.userInteractionEventSource):this.userInteractionEventSource=this.getDomElement(),i.prototype.configure.call(this,e,t),this.configureUIShowHide(e,t),this.configurePlayerStates(e,t)},f.prototype.configureUIShowHide=function(n,t){var o,e,i,r,s=this,a=this.getConfig();-1===a.hideDelay?t.onConfigured.subscribe(function(){return t.onControlsShow.dispatch(s)}):(i=!(e=o=!1),this.hidingPrevented=function(){return a.hidePlayerStateExceptions&&-1<a.hidePlayerStateExceptions.indexOf(r)},this.showUi=function(){o||(t.onControlsShow.dispatch(s),o=!0),e||n.isCasting()||s.hidingPrevented()||s.uiHideTimeout.start()},this.hideUi=function(){var e;o&&!n.isCasting()&&(t.onPreviewControlsHide.dispatch(s,e={}),e.cancel?s.showUi():(t.onControlsHide.dispatch(s),o=!1))},this.uiHideTimeout=new l.Timeout(a.hideDelay,this.hideUi),this.userInteractionEvents=[{name:"touchend",handler:function(e){function t(e){function t(e){return!e||e===s.userInteractionEventSource.get(0)||e.component instanceof f?null:e.component&&e.component instanceof g.Button?e.component:t(e.parentElement)}return!((e=t(e.target))&&e.getConfig().acceptsTouchWithUiHidden)}o||(i&&!n.isPlaying()?i=!1:t(e)&&e.preventDefault(),s.showUi())}},{name:"mouseenter",handler:function(){s.showUi()}},{name:"mousemove",handler:function(){s.showUi()}},{name:"focusin",handler:function(){s.showUi()}},{name:"keydown",handler:function(){s.showUi()}},{name:"mouseleave",handler:function(){e||s.hidingPrevented()||(s.config.hideImmediatelyOnMouseLeave?s.hideUi():s.uiHideTimeout.start())}}],this.userInteractionEvents.forEach(function(e){return s.userInteractionEventSource.on(e.name,e.handler)}),t.onSeek.subscribe(function(){s.uiHideTimeout.clear(),e=!0}),t.onSeeked.subscribe(function(){e=!1,s.hidingPrevented()||s.uiHideTimeout.start()}),t.onComponentViewModeChanged.subscribe(function(e,t){t=t.mode;return s.trackComponentViewMode(t)}),n.on(n.exports.PlayerEvent.CastStarted,function(){s.showUi()}),this.playerStateChange.subscribe(function(e,t){r=t,s.hidingPrevented()?(s.uiHideTimeout.clear(),s.showUi()):s.uiHideTimeout.start()}))},f.prototype.configurePlayerStates=function(e,t){var n,o,i=this,r=this.getDomElement(),s=[];for(n in p.PlayerUtils.PlayerState)isNaN(Number(n))&&(o=p.PlayerUtils.PlayerState[p.PlayerUtils.PlayerState[n]],s[p.PlayerUtils.PlayerState[n]]=this.prefixCss(f.STATE_PREFIX+o.toLowerCase()));function a(e){c(),r.addClass(s[e]),i.playerStateChange.dispatch(i,e)}function l(e,t){r.removeClass(i.prefixCss("layout-max-width-400")),r.removeClass(i.prefixCss("layout-max-width-600")),r.removeClass(i.prefixCss("layout-max-width-800")),r.removeClass(i.prefixCss("layout-max-width-1200")),e<=400?r.addClass(i.prefixCss("layout-max-width-400")):e<=600?r.addClass(i.prefixCss("layout-max-width-600")):e<=800?r.addClass(i.prefixCss("layout-max-width-800")):e<=1200&&r.addClass(i.prefixCss("layout-max-width-1200"))}var c=function(){r.removeClass(s[p.PlayerUtils.PlayerState.Idle]),r.removeClass(s[p.PlayerUtils.PlayerState.Prepared]),r.removeClass(s[p.PlayerUtils.PlayerState.Playing]),r.removeClass(s[p.PlayerUtils.PlayerState.Paused]),r.removeClass(s[p.PlayerUtils.PlayerState.Finished])};e.on(e.exports.PlayerEvent.SourceLoaded,function(){a(p.PlayerUtils.PlayerState.Prepared)}),e.on(e.exports.PlayerEvent.Play,function(){a(p.PlayerUtils.PlayerState.Playing)}),e.on(e.exports.PlayerEvent.Playing,function(){a(p.PlayerUtils.PlayerState.Playing)}),e.on(e.exports.PlayerEvent.Paused,function(){a(p.PlayerUtils.PlayerState.Paused)}),e.on(e.exports.PlayerEvent.PlaybackFinished,function(){a(p.PlayerUtils.PlayerState.Finished)}),e.on(e.exports.PlayerEvent.SourceUnloaded,function(){a(p.PlayerUtils.PlayerState.Idle)}),t.getConfig().events.onUpdated.subscribe(function(){a(p.PlayerUtils.getState(e))}),e.on(e.exports.PlayerEvent.ViewModeChanged,function(){e.getViewMode()===e.exports.ViewMode.Fullscreen?r.addClass(i.prefixCss(f.FULLSCREEN)):r.removeClass(i.prefixCss(f.FULLSCREEN))}),e.getViewMode()===e.exports.ViewMode.Fullscreen&&r.addClass(this.prefixCss(f.FULLSCREEN)),e.on(e.exports.PlayerEvent.StallStarted,function(){r.addClass(i.prefixCss(f.BUFFERING))}),e.on(e.exports.PlayerEvent.StallEnded,function(){r.removeClass(i.prefixCss(f.BUFFERING))}),e.isStalled()&&r.addClass(this.prefixCss(f.BUFFERING)),e.on(e.exports.PlayerEvent.CastStarted,function(){r.addClass(i.prefixCss(f.REMOTE_CONTROL))}),e.on(e.exports.PlayerEvent.CastStopped,function(){r.removeClass(i.prefixCss(f.REMOTE_CONTROL))}),e.isCasting()&&r.addClass(this.prefixCss(f.REMOTE_CONTROL)),t.onControlsShow.subscribe(function(){r.removeClass(i.prefixCss(f.CONTROLS_HIDDEN)),r.addClass(i.prefixCss(f.CONTROLS_SHOWN))}),t.onControlsHide.subscribe(function(){r.removeClass(i.prefixCss(f.CONTROLS_SHOWN)),r.addClass(i.prefixCss(f.CONTROLS_HIDDEN))});e.on(e.exports.PlayerEvent.PlayerResized,function(e){var t=Math.round(Number(e.width.substring(0,e.width.length-2)));Math.round(Number(e.height.substring(0,e.height.length-2)));l(t)}),l(new u.DOM(e.getContainer()).width(),new u.DOM(e.getContainer()).height())},f.prototype.release=function(){var t=this;this.userInteractionEvents&&this.userInteractionEvents.forEach(function(e){return t.userInteractionEventSource.off(e.name,e.handler)}),i.prototype.release.call(this),this.uiHideTimeout&&this.uiHideTimeout.clear()},f.prototype.onPlayerStateChange=function(){return this.playerStateChange.getEvent()},f.prototype.suspendHideTimeout=function(){this.uiHideTimeout.suspend()},f.prototype.resumeHideTimeout=function(){this.uiHideTimeout.resume(!this.hidingPrevented())},f.prototype.toDomElement=function(){var e=i.prototype.toDomElement.call(this);return document&&void 0!==document.createElement("p").style.flex?e.addClass(this.prefixCss("flexbox")):e.addClass(this.prefixCss("no-flexbox")),e},f.STATE_PREFIX="player-state-",f.FULLSCREEN="fullscreen",f.BUFFERING="buffering",f.REMOTE_CONTROL="remote-control",f.CONTROLS_SHOWN="controls-shown",f.CONTROLS_HIDDEN="controls-hidden",f);function f(e){var t=i.call(this,e)||this;return t.hideUi=function(){},t.showUi=function(){},t.config=t.mergeConfig(e,{cssClass:"ui-uicontainer",role:"region",ariaLabel:c.i18n.getLocalizer("player"),hideDelay:5e3,hideImmediatelyOnMouseLeave:!1},t.config),t.playerStateChange=new a.EventDispatcher,t.hidingPrevented=function(){return!1},t}},{"../dom":84,"../eventdispatcher":86,"../localization/i18n":91,"../playerutils":98,"../timeout":113,"./button":12,"./container":19}],77:[function(e,t,n){"use strict";var o,a,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.VideoQualitySelectBox=void 0,e("./selectbox")),l=e("../localization/i18n"),e=(a=r.SelectBox,i(s,a),s.prototype.configure=function(i,e){function t(){var e=i.getAvailableVideoQualities();r.clearItems(),r.hasAuto="progressive"!==i.getStreamType(),r.hasAuto&&r.addItem("auto",l.i18n.getLocalizer("auto"));for(var t=0,n=e;t<n.length;t++){var o=n[t];r.addItem(o.id,o.label)}s()}var r=this,s=(a.prototype.configure.call(this,i,e),function(){r.selectItem(i.getVideoQuality().id)});this.onItemSelected.subscribe(function(e,t){i.setVideoQuality(t)}),i.on(i.exports.PlayerEvent.SourceUnloaded,t),i.on(i.exports.PlayerEvent.PeriodSwitched,t),i.on(i.exports.PlayerEvent.VideoQualityChanged,s),i.exports.PlayerEvent.VideoQualityAdded&&(i.on(i.exports.PlayerEvent.VideoQualityAdded,t),i.on(i.exports.PlayerEvent.VideoQualityRemoved,t)),e.getConfig().events.onUpdated.subscribe(t)},s.prototype.hasAutoItem=function(){return this.hasAuto},s);function s(e){var t=a.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClasses:["ui-videoqualityselectbox"]},t.config),t}n.VideoQualitySelectBox=e},{"../localization/i18n":91,"./selectbox":44}],78:[function(e,t,n){"use strict";var o,r,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.VolumeControlButton=void 0,e("./container")),a=e("./volumeslider"),l=e("./volumetogglebutton"),c=e("../timeout"),e=(r=s.Container,i(u,r),u.prototype.configure=function(e,t){var n=this,e=(r.prototype.configure.call(this,e,t),this.getVolumeToggleButton()),o=this.getVolumeSlider(),i=(this.volumeSliderHideTimeout=new c.Timeout(this.getConfig().hideDelay,function(){o.hide()}),!1);e.getDomElement().on("mouseenter",function(){o.isHidden()&&o.show(),n.volumeSliderHideTimeout.clear()}),e.getDomElement().on("mouseleave",function(){n.volumeSliderHideTimeout.reset()}),o.getDomElement().on("mouseenter",function(){n.volumeSliderHideTimeout.clear(),i=!0}),o.getDomElement().on("mouseleave",function(){o.isSeeking()?n.volumeSliderHideTimeout.clear():n.volumeSliderHideTimeout.reset(),i=!1}),o.onSeeked.subscribe(function(){i||n.volumeSliderHideTimeout.reset()})},u.prototype.release=function(){r.prototype.release.call(this),this.volumeSliderHideTimeout.clear()},u.prototype.getVolumeToggleButton=function(){return this.volumeToggleButton},u.prototype.getVolumeSlider=function(){return this.volumeSlider},u);function u(e){var t=r.call(this,e=void 0===e?{}:e)||this;return t.volumeToggleButton=new l.VolumeToggleButton,t.volumeSlider=new a.VolumeSlider({vertical:null==e.vertical||e.vertical,hidden:!0}),t.config=t.mergeConfig(e,{cssClass:"ui-volumecontrolbutton",components:[t.volumeToggleButton,t.volumeSlider],hideDelay:500},t.config),t}n.VolumeControlButton=e},{"../timeout":113,"./container":19,"./volumeslider":79,"./volumetogglebutton":80}],79:[function(e,t,n){"use strict";var o,r,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.VolumeSlider=void 0,e("./seekbar")),a=e("../localization/i18n"),e=(r=s.SeekBar,i(l,r),l.prototype.setVolumeAriaSliderValues=function(e){this.getDomElement().attr("aria-valuenow",Math.ceil(e).toString()),this.getDomElement().attr("aria-valuetext","".concat(a.i18n.performLocalization(a.i18n.getLocalizer("seekBar.value")),": ").concat(Math.ceil(e)))},l.prototype.configure=function(e,t){var n=this,o=(r.prototype.configure.call(this,e,t,!1),this.setAriaSliderMinMax("0","100"),this.getConfig()),i=t.getConfig().volumeController;o.hideIfVolumeControlProhibited&&!this.detectVolumeControlAvailability()?this.hide():(i.onChanged.subscribe(function(e,t){t.muted?(n.setVolumeAriaSliderValues(0),n.setPlaybackPosition(0)):(n.setPlaybackPosition(t.volume),n.setVolumeAriaSliderValues(t.volume))}),this.onSeek.subscribe(function(){n.volumeTransition=i.startTransition()}),this.onSeekPreview.subscribeRateLimited(this.updateVolumeWhileScrubbing,50),this.onSeeked.subscribe(function(e,t){n.volumeTransition&&n.volumeTransition.finish(t)}),e.on(e.exports.PlayerEvent.PlayerResized,function(){n.refreshPlaybackPosition()}),t.onConfigured.subscribe(function(){n.refreshPlaybackPosition()}),t.getConfig().events.onUpdated.subscribe(function(){n.refreshPlaybackPosition()}),t.onComponentShow.subscribe(function(){n.refreshPlaybackPosition()}),t.onComponentHide.subscribe(function(){n.refreshPlaybackPosition()}),i.onChangedEvent())},l.prototype.detectVolumeControlAvailability=function(){var e=document.createElement("video");return e.volume=.7,1!==e.volume},l.prototype.release=function(){r.prototype.release.call(this),this.onSeekPreview.unsubscribe(this.updateVolumeWhileScrubbing)},l);function l(e){var n=r.call(this,e=void 0===e?{}:e)||this;return n.updateVolumeWhileScrubbing=function(e,t){t.scrubbing&&n.volumeTransition&&n.volumeTransition.update(t.position)},n.config=n.mergeConfig(e,{cssClass:"ui-volumeslider",hideIfVolumeControlProhibited:!0,ariaLabel:a.i18n.getLocalizer("settings.audio.volume"),tabIndex:0},n.config),n}n.VolumeSlider=e},{"../localization/i18n":91,"./seekbar":40}],80:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.VolumeToggleButton=void 0,e("./togglebutton")),a=e("../localization/i18n"),e=(i=s.ToggleButton,r(l,i),l.prototype.configure=function(e,t){var n=this,o=(i.prototype.configure.call(this,e,t),t.getConfig().volumeController);o.onChanged.subscribe(function(e,t){t.muted?n.on():n.off();t=Math.ceil(t.volume/10);n.getDomElement().data(n.prefixCss("volume-level-tens"),String(t))}),this.onClick.subscribe(function(){o.toggleMuted()}),o.onChangedEvent()},l);function l(e){var t=i.call(this,e=void 0===e?{}:e)||this,n={cssClass:"ui-volumetogglebutton",text:a.i18n.getLocalizer("settings.audio.mute"),onClass:"muted",offClass:"unmuted",ariaLabel:a.i18n.getLocalizer("settings.audio.mute")};return t.config=t.mergeConfig(e,n,t.config),t}n.VolumeToggleButton=e},{"../localization/i18n":91,"./togglebutton":74}],81:[function(e,t,n){"use strict";var o,a,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=(Object.defineProperty(n,"__esModule",{value:!0}),n.VRToggleButton=void 0,e("./togglebutton")),s=e("../localization/i18n"),e=(a=r.ToggleButton,i(l,a),l.prototype.configure=function(t,e){function n(e){e.type===t.exports.PlayerEvent.Warning&&e.code!==t.exports.WarningCode.VR_RENDERING_ERROR||(r()&&s()?(i.show(),t.vr&&t.vr.getStereo()?i.on():i.off()):i.hide())}function o(){r()?i.show():i.hide()}var i=this,r=(a.prototype.configure.call(this,t,e),function(){var e=t.getSource();return e&&Boolean(e.vr)}),s=function(){var e=t.getSource();return t.vr&&Boolean(e.vr)};t.on(t.exports.PlayerEvent.VRStereoChanged,n),t.on(t.exports.PlayerEvent.Warning,n),t.on(t.exports.PlayerEvent.SourceUnloaded,o),e.getConfig().events.onUpdated.subscribe(o),this.onClick.subscribe(function(){s()?t.vr&&t.vr.getStereo()?t.vr.setStereo(!1):t.vr.setStereo(!0):console&&console.log("No VR content")}),o()},l);function l(e){var t=a.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-vrtogglebutton",text:s.i18n.getLocalizer("vr")},t.config),t}n.VRToggleButton=e},{"../localization/i18n":91,"./togglebutton":74}],82:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=(Object.defineProperty(n,"__esModule",{value:!0}),n.Watermark=void 0,e("./clickoverlay")),a=e("../localization/i18n"),e=(i=s.ClickOverlay,r(l,i),l);function l(e){var t=i.call(this,e=void 0===e?{}:e)||this;return t.config=t.mergeConfig(e,{cssClass:"ui-watermark",url:"http://bitmovin.com",role:"link",text:"logo",ariaLabel:a.i18n.getLocalizer("watermarkLink")},t.config),t}n.Watermark=e},{"../localization/i18n":91,"./clickoverlay":16}],83:[function(e,n,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DemoFactory=void 0;var r=e("./components/vrtogglebutton"),s=e("./components/settingstogglebutton"),a=e("./components/volumeslider"),l=e("./components/playbacktimelabel"),c=e("./components/airplaytogglebutton"),u=e("./components/errormessageoverlay"),p=e("./components/controlbar"),g=e("./components/casttogglebutton"),f=e("./components/fullscreentogglebutton"),d=e("./components/recommendationoverlay"),h=e("./components/playbackspeedselectbox"),y=e("./components/audioqualityselectbox"),m=e("./components/caststatusoverlay"),b=e("./components/uicontainer"),v=e("./components/watermark"),C=e("./components/subtitleoverlay"),S=e("./components/settingspanel"),P=e("./components/seekbarlabel"),w=e("./components/playbacktoggleoverlay"),E=e("./components/pictureinpicturetogglebutton"),_=e("./components/spacer"),O=e("./components/container"),k=e("./components/volumetogglebutton"),x=e("./components/playbacktogglebutton"),T=e("./components/seekbar"),M=e("./components/videoqualityselectbox"),L=e("./uimanager"),A=e("./components/titlebar"),I=e("./components/bufferingoverlay"),B=e("./components/subtitlelistbox"),R=e("./components/audiotracklistbox"),j=e("./components/settingspanelitem"),D=e("./components/settingspanelpage"),U=e("./uifactory"),z=e("./main");(t.DemoFactory||(t.DemoFactory={})).buildDemoWithSeparateAudioSubtitlesButtons=function(e,t){var n,o,i;return void 0===t&&(t={}),new L.UIManager(e,[{ui:U.UIFactory.modernSmallScreenAdsUI(),condition:function(e){return e.isMobile&&e.documentWidth<600&&e.isAd&&e.adRequiresUi}},{ui:U.UIFactory.modernAdsUI(),condition:function(e){return e.isAd&&e.adRequiresUi}},{ui:U.UIFactory.modernSmallScreenUI(),condition:function(e){return e.isMobile&&e.documentWidth<600}},{ui:(e=new C.SubtitleOverlay,n=new S.SettingsPanel({components:[new D.SettingsPanelPage({components:[new j.SettingsPanelItem("Video Quality",new M.VideoQualitySelectBox),new j.SettingsPanelItem("Speed",new h.PlaybackSpeedSelectBox),new j.SettingsPanelItem("Audio Quality",new y.AudioQualitySelectBox)]})],hidden:!0}),o=new B.SubtitleListBox,o=new S.SettingsPanel({components:[new D.SettingsPanelPage({components:[new j.SettingsPanelItem(null,o)]})],hidden:!0}),i=new R.AudioTrackListBox,i=new S.SettingsPanel({components:[new D.SettingsPanelPage({components:[new j.SettingsPanelItem(null,i)]})],hidden:!0}),i=new p.ControlBar({components:[i,o,n,new O.Container({components:[new l.PlaybackTimeLabel({timeLabelMode:l.PlaybackTimeLabelMode.CurrentTime,hideInLivePlayback:!0}),new T.SeekBar({label:new P.SeekBarLabel}),new l.PlaybackTimeLabel({timeLabelMode:l.PlaybackTimeLabelMode.TotalTime,cssClasses:["text-right"]})],cssClasses:["controlbar-top"]}),new O.Container({components:[new x.PlaybackToggleButton,new z.QuickSeekButton({seekSeconds:-10}),new z.QuickSeekButton({seekSeconds:10}),new k.VolumeToggleButton,new a.VolumeSlider,new _.Spacer,new E.PictureInPictureToggleButton,new c.AirPlayToggleButton,new g.CastToggleButton,new r.VRToggleButton,new s.SettingsToggleButton({settingsPanel:i,cssClass:"ui-audiotracksettingstogglebutton"}),new s.SettingsToggleButton({settingsPanel:o,cssClass:"ui-subtitlesettingstogglebutton"}),new s.SettingsToggleButton({settingsPanel:n}),new f.FullscreenToggleButton],cssClasses:["controlbar-bottom"]})]}),new b.UIContainer({components:[e,new I.BufferingOverlay,new w.PlaybackToggleOverlay,new m.CastStatusOverlay,i,new A.TitleBar,new d.RecommendationOverlay,new v.Watermark,new u.ErrorMessageOverlay]}))}],t)}},{"./components/airplaytogglebutton":7,"./components/audioqualityselectbox":8,"./components/audiotracklistbox":9,"./components/bufferingoverlay":11,"./components/caststatusoverlay":13,"./components/casttogglebutton":14,"./components/container":19,"./components/controlbar":20,"./components/errormessageoverlay":23,"./components/fullscreentogglebutton":24,"./components/pictureinpicturetogglebutton":32,"./components/playbackspeedselectbox":33,"./components/playbacktimelabel":34,"./components/playbacktogglebutton":35,"./components/playbacktoggleoverlay":36,"./components/recommendationoverlay":38,"./components/seekbar":40,"./components/seekbarlabel":43,"./components/settingspanel":45,"./components/settingspanelitem":46,"./components/settingspanelpage":47,"./components/settingstogglebutton":51,"./components/spacer":52,"./components/subtitlelistbox":53,"./components/subtitleoverlay":54,"./components/titlebar":73,"./components/uicontainer":76,"./components/videoqualityselectbox":77,"./components/volumeslider":79,"./components/volumetogglebutton":80,"./components/vrtogglebutton":81,"./components/watermark":82,"./main":96,"./uifactory":115,"./uimanager":116}],84:[function(e,t,n){"use strict";function o(e,t,n){if(this.document=document,e instanceof Array)0<e.length&&e[0]instanceof HTMLElement&&(this.elements=e);else if(e instanceof HTMLElement){var o=e;this.elements=[o]}else if(e instanceof Document)this.elements=null;else if(t){var i,o=document.createElement(e);for(i in t){var r=t[i];null!=r&&o.setAttribute(i,r)}n&&(o.component=n),this.elements=[o]}else this.elements=this.findChildElements(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.DOM=void 0,Object.defineProperty(o.prototype,"length",{get:function(){return this.elements?this.elements.length:0},enumerable:!1,configurable:!0}),o.prototype.get=function(e){return void 0===e?this.elements:!this.elements||e>=this.elements.length||e<-this.elements.length?void 0:e<0?this.elements[this.elements.length-e]:this.elements[e]},o.prototype.forEach=function(t){this.elements&&this.elements.forEach(function(e){t(e)})},o.prototype.findChildElementsOfElement=function(e,t){e=e.querySelectorAll(t);return[].slice.call(e)},o.prototype.findChildElements=function(t){var n=this,o=[];return this.elements?(this.forEach(function(e){o=o.concat(n.findChildElementsOfElement(e,t))}),o):this.findChildElementsOfElement(document,t)},o.prototype.find=function(e){return new o(this.findChildElements(e))},o.prototype.focusToFirstInput=function(){var e=this.findChildElements('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');0<e.length&&e[0].focus()},o.prototype.scrollTo=function(e,t){this.elements[0].scrollTo(e,t)},o.prototype.html=function(e){return 0<arguments.length?this.setHtml(e):this.getHtml()},o.prototype.getHtml=function(){return this.elements[0].innerHTML},o.prototype.setHtml=function(t){return void 0!==t&&null!=t||(t=""),this.forEach(function(e){e.innerHTML=t}),this},o.prototype.empty=function(){return this.forEach(function(e){e.innerHTML=""}),this},o.prototype.val=function(){var e=this.elements[0];if(e instanceof HTMLSelectElement||e instanceof HTMLInputElement)return e.value;throw new Error("val() not supported for ".concat(typeof e))},o.prototype.attr=function(e,t){return 1<arguments.length?this.setAttr(e,t):this.getAttr(e)},o.prototype.removeAttr=function(t){this.forEach(function(e){e.removeAttribute(t)})},o.prototype.getAttr=function(e){return this.elements[0].getAttribute(e)},o.prototype.setAttr=function(t,n){return this.forEach(function(e){e.setAttribute(t,n)}),this},o.prototype.data=function(e,t){return 1<arguments.length?this.setData(e,t):this.getData(e)},o.prototype.getData=function(e){return this.elements[0].getAttribute("data-"+e)},o.prototype.setData=function(t,n){return this.forEach(function(e){e.setAttribute("data-"+t,n)}),this},o.prototype.append=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return this.forEach(function(o){e.forEach(function(n){n.elements.forEach(function(e,t){o.appendChild(n.elements[t])})})}),this},o.prototype.remove=function(){this.forEach(function(e){var t=e.parentNode;t&&t.removeChild(e)})},o.prototype.offset=function(){var e=this.elements[0].getBoundingClientRect(),t=document.body.parentElement.getBoundingClientRect();return{top:e.top-t.top,left:e.left-t.left}},o.prototype.width=function(){return this.elements[0].offsetWidth},o.prototype.height=function(){return this.elements[0].offsetHeight},o.prototype.size=function(){return{width:this.width(),height:this.height()}},o.prototype.on=function(e,n,o){var i=this;return e.split(" ").forEach(function(t){null==i.elements?i.document.addEventListener(t,n,o):i.forEach(function(e){e.addEventListener(t,n,o)})}),this},o.prototype.off=function(e,n,o){var i=this;return e.split(" ").forEach(function(t){null==i.elements?i.document.removeEventListener(t,n,o):i.forEach(function(e){e.removeEventListener(t,n,o)})}),this},o.prototype.addClass=function(o){return this.forEach(function(e){var t,n;e.classList?0<(n=o.split(" ").filter(function(e){return 0<e.length})).length&&(t=e.classList).add.apply(t,n):e.className+=" "+o}),this},o.prototype.removeClass=function(o){return this.forEach(function(e){var t,n;e.classList?0<(n=o.split(" ").filter(function(e){return 0<e.length})).length&&(t=e.classList).remove.apply(t,n):e.className=e.className.replace(new RegExp("(^|\\b)"+o.split(" ").join("|")+"(\\b|$)","gi")," ")}),this},o.prototype.hasClass=function(t){var n=!1;return this.forEach(function(e){e.classList?e.classList.contains(t)&&(n=!0):new RegExp("(^| )"+t+"( |$)","gi").test(e.className)&&(n=!0)}),n},o.prototype.css=function(e,t){var n;return"string"==typeof e?(n=e,2===arguments.length?this.setCss(n,t):this.getCss(n)):this.setCssCollection(e)},o.prototype.removeCss=function(e,t){return this.elements[t=void 0===t?0:t].style.removeProperty(e)},o.prototype.getCss=function(e){return getComputedStyle(this.elements[0])[e]},o.prototype.setCss=function(t,n){return this.forEach(function(e){e.style[t]=n}),this},o.prototype.setCssCollection=function(t){return this.forEach(function(e){Object.assign(e.style,t)}),this},n.DOM=o},{}],85:[function(e,t,n){"use strict";var o;Object.defineProperty(n,"__esModule",{value:!0}),n.ErrorUtils=void 0,(o=n.ErrorUtils||(n.ErrorUtils={})).defaultErrorMessages={1e3:"Error is unknown",1001:"The player API is not available after a call to PlayerAPI.destroy.",1100:"General setup error",1101:"There was an error when inserting the HTML video element",1102:"No configuration was provided",1103:"The license is not valid",1104:"The the domain-locked player is not authorized to playback on this domain",1105:"The domain is not allowlisted",1106:"The license server URL is invalid",1107:"The impression server URL is invalid",1108:"Could not initialize a rendering engine",1109:"The used flash version does not support playback",1110:"Native Flash is not authorized by a valid Adobe token",1111:"Flash doesn't have sufficient resources",1112:"Flash container API not available",1113:'Protocol not supported. This site has been loaded using "file" protocol, but unfortunately this is not supported. Please load the page using a web server (using http or https)',1200:"General source error",1201:"No valid source was provided",1202:"The downloaded manifest is invalid",1203:"There was no technology detected to playback the provided source",1204:"The stream type is not supported",1205:"The forced technology is not supported",1206:"No stream found for supported technologies.",1207:"The downloaded segment is empty",1208:"The manifest could not be loaded",1209:"Progressive stream type not supported or the stream has an error",1210:"HLS stream has an error",1211:"The encryption method is not supported",1300:"General playback error",1301:"Video decoder or demuxer had an error with the content",1302:"General error if Flash renderer has an error",1303:"Flash doesn't have sufficient resources",1304:"The transmuxer could not be initialized",1400:"Network error while downloading",1401:"The manifest download timed out",1402:"The segment download timed out",1403:"The progressive stream download timed out",1404:"The Certificate could not be loaded",2e3:"General DRM error",2001:"Required DRM configuration is missing",2002:"The licensing server URL is missing",2003:"License request failed",2004:"Key or KeyId is missing",2005:"Key size is not supported",2006:"Unable to instantiate a key system supporting the required combinations",2007:"Unable to create or initialize key session",2008:"The MediaKey object could not be created/initialized",2009:"Key error",2010:"The key system is not supported",2011:"The certificate is not valid",2012:"Invalid header key/value pair for PlayReady license request",2013:"Content cannot be played back because the output is restricted on this machine",2014:"DRM error for the Flash renderer",2100:"General VR error",2101:"Player technology not compatible with VR playback",3e3:"General module error",3001:"The definition of the module is invalid (e.g. incomplete).",3002:"The module definition specifies dependencies but the module is not provided via a function for deferred loading.",3003:"A module cannot be loaded because it has not been added to the player core.",3004:"A module cannot be loaded because one or more dependencies are missing.",3100:"An Advertising module error has occurred. Refer to the attached AdvertisingError."},o.defaultMobileV3ErrorMessageTranslator=function(e){return e.message},o.defaultWebErrorMessageTranslator=function(e){var t=o.defaultErrorMessages[e.code];return t?"".concat(t,"\n(").concat(e.name,")"):"".concat(e.code," ").concat(e.name)}},{}],86:[function(e,t,n){"use strict";var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),l=(Object.defineProperty(n,"__esModule",{value:!0}),n.EventDispatcher=void 0,e("./arrayutils")),r=e("./timeout");function s(){this.listeners=[]}s.prototype.subscribe=function(e){this.listeners.push(new a(e))},s.prototype.subscribeOnce=function(e){this.listeners.push(new a(e,!0))},s.prototype.subscribeRateLimited=function(e,t){this.listeners.push(new p(e,t))},s.prototype.unsubscribe=function(e){for(var t=0;t<this.listeners.length;t++){var n=this.listeners[t];if(n.listener===e)return n.clear(),l.ArrayUtils.remove(this.listeners,n),!0}return!1},s.prototype.unsubscribeAll=function(){for(var e=0,t=this.listeners;e<t.length;e++)t[e].clear();this.listeners=[]},s.prototype.dispatch=function(e,t){void 0===t&&(t=null);for(var n=[],o=0,i=this.listeners.slice(0);o<i.length;o++){var r=i[o];r.fire(e,t),r.isOnce()&&n.push(r)}for(var s=0,a=n;s<a.length;s++)l.ArrayUtils.remove(this.listeners,a[s])},s.prototype.getEvent=function(){return this},n.EventDispatcher=s;Object.defineProperty(c.prototype,"listener",{get:function(){return this.eventListener},enumerable:!1,configurable:!0}),c.prototype.fire=function(e,t){this.eventListener(e,t)},c.prototype.isOnce=function(){return this.once},c.prototype.clear=function(){};var a=c;function c(e,t){void 0===t&&(t=!1),this.eventListener=e,this.once=t}i(g,u=a),g.prototype.shouldFireEvent=function(){return!this.rateLimitTimout.isActive()},g.prototype.fireSuper=function(e,t){u.prototype.fire.call(this,e,t)},g.prototype.fire=function(e,t){this.rateLimitingEventListener(e,t)},g.prototype.clear=function(){u.prototype.clear.call(this),this.rateLimitTimout.clear()};var u,p=g;function g(e,t){function n(){o.rateLimitTimout.start()}var o=u.call(this,e)||this;o.rateMs=t;return o.rateLimitTimout=new r.Timeout(o.rateMs,function(){o.lastSeenEvent&&(o.fireSuper(o.lastSeenEvent.sender,o.lastSeenEvent.args),n(),o.lastSeenEvent=null)}),o.rateLimitingEventListener=function(e,t){o.shouldFireEvent()?(o.fireSuper(e,t),n()):o.lastSeenEvent={sender:e,args:t}},o}},{"./arrayutils":1,"./timeout":113}],87:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.FocusVisibilityTracker=void 0;var o="bmpui-focus-visible";function i(e){var n=this;this.bitmovinUiPrefix=e,this.lastInteractionWasKeyboard=!0,this.onKeyDown=function(e){e.metaKey||e.altKey||e.ctrlKey||(n.lastInteractionWasKeyboard=!0)},this.onMouseOrPointerOrTouch=function(){return n.lastInteractionWasKeyboard=!1},this.onFocus=function(e){var t,e=e.target;n.lastInteractionWasKeyboard&&r(e)&&(t=n.bitmovinUiPrefix,0===e.id.indexOf(t))&&!e.classList.contains(o)&&e.classList.add(o)},this.onBlur=function(e){e=e.target;r(e)&&e.classList.remove(o)},this.eventHandlerMap={mousedown:this.onMouseOrPointerOrTouch,pointerdown:this.onMouseOrPointerOrTouch,touchstart:this.onMouseOrPointerOrTouch,keydown:this.onKeyDown,focus:this.onFocus,blur:this.onBlur},this.registerEventListeners()}function r(e){return e instanceof HTMLElement&&e.classList instanceof DOMTokenList}i.prototype.registerEventListeners=function(){for(var e in this.eventHandlerMap)document.addEventListener(e,this.eventHandlerMap[e],!0)},i.prototype.unregisterEventListeners=function(){for(var e in this.eventHandlerMap)document.removeEventListener(e,this.eventHandlerMap[e],!0)},i.prototype.release=function(){this.unregisterEventListeners()},n.FocusVisibilityTracker=i},{}],88:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.GroupPlaybackSuspensionReason=void 0,(n.GroupPlaybackSuspensionReason||(n.GroupPlaybackSuspensionReason={})).UserIsScrubbing="userIsScrubbing"},{}],89:[function(e,t,n){"use strict";var o;Object.defineProperty(n,"__esModule",{value:!0}),n.Guid=void 0,n=n.Guid||(n.Guid={}),o=1,n.next=function(){return o++}},{}],90:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ImageLoader=void 0;var r=e("./dom");function o(){this.state={}}o.prototype.load=function(e,t){var n,o,i=this;this.state[e]?((n=this.state[e]).loadedCallback=t,n.loaded&&this.callLoadedCallback(n)):(o={url:e,image:new r.DOM("img",{}),loadedCallback:t,loaded:!1,width:0,height:0},(this.state[e]=o).image.on("load",function(e){o.loaded=!0,o.width=o.image.get(0).width,o.height=o.image.get(0).height,i.callLoadedCallback(o)}),o.image.attr("src",o.url))},o.prototype.callLoadedCallback=function(e){e.loadedCallback(e.url,e.width,e.height)},n.ImageLoader=o},{"./dom":84}],91:[function(e,t,r){"use strict";var s=this&&this.__assign||function(){return(s=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},o=(Object.defineProperty(r,"__esModule",{value:!0}),r.i18n=r.I18n=r.defaultVocabularies=void 0,n(e("./languages/de.json"))),i=n(e("./languages/en.json")),a=n(e("./languages/es.json")),n=n(e("./languages/nl.json")),l=(r.defaultVocabularies={en:i.default,de:o.default,es:a.default,nl:n.default},{language:"en",vocabularies:r.defaultVocabularies}),e=(c.prototype.setConfig=function(e){var e=s(s({},l),e),t="auto"===e.language,n=this.mergeVocabulariesWithDefaultVocabularies(e.vocabularies);this.initializeLanguage(e.language,t,n),this.initializeVocabulary(n)},c.containsLanguage=function(e,t){return e.hasOwnProperty(t)},c.prototype.mergeVocabulariesWithDefaultVocabularies=function(o){void 0===o&&(o={});var i=s(s({},r.defaultVocabularies),o);return Object.keys(i).reduce(function(e,t){var n=i[t];return c.containsLanguage(r.defaultVocabularies,t)&&c.containsLanguage(o,t)&&(n=s(s({},r.defaultVocabularies[t]),o[t])),s(s({},e),((e={})[t]=n,e))},{})},c.prototype.initializeLanguage=function(e,t,n){if(t){t=window.navigator.language;if(c.containsLanguage(n,t))return void(this.language=t);t=t.slice(0,2);if(c.containsLanguage(n,t))return void(this.language=t)}this.language=e},c.prototype.initializeVocabulary=function(n){this.vocabulary=["en",this.language].reduce(function(e,t){return s(s({},e),n[t]||{})},{})},c.prototype.replaceVariableWithPlaceholderIfExists=function(e,o){var t=e.match(new RegExp("{[a-zA-Z0-9]+}","g"));return 0===t.length?e:t.map(function(e){return{match:e,key:e.slice(1,-1)}}).reduce(function(e,t){var n=t.key,t=t.match;return o.hasOwnProperty(n)?e.replace(t,o[n]):e},e)},c.prototype.getLocalizer=function(t,n){var o=this;return function(){var e;if(null!=t)return null==(e=o.vocabulary[t])&&(e=t),null!=n?o.replaceVariableWithPlaceholderIfExists(e,n):e}},c.prototype.performLocalization=function(e){return"function"==typeof e?e():e},c);function c(e){this.setConfig(e)}r.I18n=e,r.i18n=new e(l)},{"./languages/de.json":92,"./languages/en.json":93,"./languages/es.json":94,"./languages/nl.json":95}],92:[function(e,t,n){t.exports={"settings.video.quality":"Videoqualität","settings.audio.quality":"Audioqualität","settings.audio.track":"Audiospur",speed:"Geschwindigkeit",play:"Abspielen",pause:"Pause",playPause:"Abspielen/Pause",open:"öffnen",close:"Schließen","settings.audio.mute":"Stummschaltung","settings.audio.volume":"Lautstärke",pictureInPicture:"Bild im Bild",appleAirplay:"Apple AirPlay",googleCast:"Google Cast",vr:"VR",settings:"Einstellungen",fullscreen:"Vollbild",off:"aus","settings.subtitles":"Untertitel","settings.subtitles.font.size":"Größe","settings.subtitles.font.style":"Schriftstil","settings.subtitles.font.style.bold":"Fett","settings.subtitles.font.style.italic":"Kursiv","settings.subtitles.font.family":"Schriftart","settings.subtitles.font.color":"Farbe","settings.subtitles.font.opacity":"Deckkraft","settings.subtitles.characterEdge":"Ränder","settings.subtitles.characterEdge.color":"Buchstabenrandfarbe","settings.subtitles.background.color":"Hintergrundfarbe","settings.subtitles.background.opacity":"Hintergrunddeckkraft","settings.subtitles.window.color":"Hintergrundfarbe","settings.subtitles.window.opacity":"Hintergrunddeckkraft","settings.time.hours":"Stunden","settings.time.minutes":"Minuten","settings.time.seconds":"Sekunden",back:"Zurück",reset:"Zurücksetzen",replay:"Wiederholen","ads.remainingTime":"Diese Anzeige endet in {remainingTime} Sekunden",default:"standard","colors.white":"weiß","colors.black":"schwarz","colors.red":"rot","colors.green":"grün","colors.blue":"blau","colors.yellow":"gelb","subtitle.example":"Beispiel Untertitel","subtitle.select":"Untertitel auswählen",playingOn:"Spielt auf <strong>{castDeviceName}</strong>",connectingTo:"Verbindung mit <strong>{castDeviceName}</strong> wird hergestellt...",watermarkLink:"Link zum Homepage",controlBar:"Videoplayer Kontrollen",player:"Video player",seekBar:"Video-Timeline","seekBar.value":"Wert","seekBar.timeshift":"Timeshift","seekBar.durationText":"aus","quickseek.forward":"{seekSeconds} Sekunden Vor","quickseek.rewind":"{seekSeconds} Sekunden Zurück",ecoMode:"ecoMode","ecoMode.title":"Eco Mode"}},{}],93:[function(e,t,n){t.exports={"settings.video.quality":"Video Quality","settings.audio.quality":"Audio Quality","settings.audio.track":"Audio Track","settings.audio.mute":"Mute","settings.audio.volume":"Volume","settings.subtitles.window.color":"Window color","settings.subtitles.window.opacity":"Window opacity","settings.subtitles":"Subtitles","settings.subtitles.font.color":"Font color","settings.subtitles.font.opacity":"Font opacity","settings.subtitles.background.color":"Background color","settings.subtitles.background.opacity":"Background opacity","colors.white":"white","colors.black":"black","colors.red":"red","colors.green":"green","colors.blue":"blue","colors.cyan":"cyan","colors.yellow":"yellow","colors.magenta":"magenta",percent:"{value}%","settings.subtitles.font.size":"Font size","settings.subtitles.font.style":"Font style","settings.subtitles.font.style.bold":"bold","settings.subtitles.font.style.italic":"italic","settings.subtitles.characterEdge":"Character edge","settings.subtitles.characterEdge.raised":"raised","settings.subtitles.characterEdge.depressed":"depressed","settings.subtitles.characterEdge.uniform":"uniform","settings.subtitles.characterEdge.dropshadowed":"drop shadowed","settings.subtitles.characterEdge.color":"Character edge color","settings.subtitles.font.family":"Font family","settings.subtitles.font.family.monospacedserif":"monospaced serif","settings.subtitles.font.family.proportionalserif":"proportional serif","settings.subtitles.font.family.monospacedsansserif":"monospaced sans serif","settings.subtitles.font.family.proportionalsansserif":"proportional sans serif","settings.subtitles.font.family.casual":"casual","settings.subtitles.font.family.cursive":"cursive","settings.subtitles.font.family.smallcapital":"small capital","settings.time.hours":"Hours","settings.time.minutes":"Minutes","settings.time.seconds":"Seconds","ads.remainingTime":"This ad will end in {remainingTime} seconds.",settings:"Settings",fullscreen:"Fullscreen",speed:"Speed",playPause:"Play/Pause",play:"Play",pause:"Pause",open:"open",close:"Close",pictureInPicture:"Picture-in-Picture",appleAirplay:"Apple AirPlay",googleCast:"Google Cast",vr:"VR",off:"off",auto:"auto",ecoMode:"ecoMode","ecoMode.title":"Eco Mode",back:"Back",reset:"Reset",replay:"Replay",normal:"normal",default:"default",live:"Live","subtitle.example":"example subtitle","subtitle.select":"Select subtitle",playingOn:"Playing on <strong>{castDeviceName}</strong>",connectingTo:"Connecting to <strong>{castDeviceName}</strong>...",watermarkLink:"Link to Homepage",controlBar:"Video player controls",player:"Video player",seekBar:"Video timeline","seekBar.value":"Value","seekBar.timeshift":"Timeshift","seekBar.durationText":"out of","quickseek.forward":"Fast Forward {seekSeconds} seconds","quickseek.rewind":"Rewind {seekSeconds} seconds"}},{}],94:[function(e,t,n){t.exports={"settings.video.quality":"Calidad de Video","settings.audio.quality":"Calidad de Audio","settings.audio.track":"Pista de Audio","settings.audio.mute":"Silencio","settings.audio.volume":"Volumen","settings.subtitles.window.color":"color de Ventana","settings.subtitles.window.opacity":"opacidad de Ventana","settings.subtitles":"Subtítulos","settings.subtitles.font.color":"color de Fuente","settings.subtitles.font.opacity":"opacidad de Fuente","settings.subtitles.background.color":"color de Fondo","settings.subtitles.background.opacity":"opacidad de Fondo","colors.white":"blanco","colors.black":"negro","colors.red":"rojo","colors.green":"verde","colors.blue":"azul","colors.cyan":"cian","colors.yellow":"amarillo","colors.magenta":"magenta",percent:"{value}%","settings.subtitles.font.size":"tamaño de Fuente","settings.subtitles.font.style":"Estilo de Fuente","settings.subtitles.font.style.bold":"negrita","settings.subtitles.font.style.italic":"cursiva","settings.subtitles.characterEdge":"borde del Caracter","settings.subtitles.characterEdge.raised":"alzado","settings.subtitles.characterEdge.depressed":"discreto","settings.subtitles.characterEdge.uniform":"uniforme","settings.subtitles.characterEdge.dropshadowed":"sombreado","settings.subtitles.characterEdge.color":"color de contorno de texto","settings.subtitles.font.family":"tipo de Fuente","settings.subtitles.font.family.monospacedserif":"monospaced serif","settings.subtitles.font.family.proportionalserif":"proportional serif","settings.subtitles.font.family.monospacedsansserif":"monospaced sans serif","settings.subtitles.font.family.proportionalsansserif":"proportional sans serif","settings.subtitles.font.family.casual":"casual","settings.subtitles.font.family.cursive":"cursiva","settings.subtitles.font.family.smallcapital":"small capital","settings.time.hours":"Horas","settings.time.minutes":"Minutos","settings.time.seconds":"Segundos","ads.remainingTime":"Este anuncio acabará en {remainingTime} segundos.",settings:"Configuración",fullscreen:"Pantalla Completa",speed:"Velocidad",playPause:"Reproducir/Pausa",play:"Reproducir",pause:"Pausa",open:"Abrir",close:"Cerrar",pictureInPicture:"Imagen en Imagen",appleAirplay:"Apple AirPlay",googleCast:"Google Cast",vr:"VR",off:"off",auto:"auto",ecoMode:"ecoMode","ecoMode.title":"Eco Mode",back:"Atrás",reset:"Reiniciar",replay:"Rebobinar",normal:"normal",default:"predeterminado",live:"Directo","subtitle.example":"Ejemplo de Subtítulo","subtitle.select":"Seleccionar subtítulo",playingOn:"Reproduciendo en <strong>{castDeviceName}</strong>",connectingTo:"Conectando a <strong>{castDeviceName}</strong>...",watermarkLink:"Enlace al inicio",controlBar:"Controles del Reproductor",player:"Reproductor de Video",seekBar:"Línea de Tiempo","seekBar.value":"posición","seekBar.timeshift":"cambio de posición","seekBar.durationText":"de","quickseek.forward":"Adelantar {seekSeconds} segundos","quickseek.rewind":"Rebobinar {seekSeconds} segundos"}},{}],95:[function(e,t,n){t.exports={"settings.video.quality":"Videokwaliteit","settings.audio.quality":"Audiokwaliteit","settings.audio.track":"Audiospoor","settings.audio.mute":"Dempen","settings.audio.volume":"Volume","settings.subtitles.window.color":"Vensterkleur","settings.subtitles.window.opacity":"Venster doorzichtigheid","settings.subtitles":"Ondertiteling","settings.subtitles.font.color":"Lettertype kleur","settings.subtitles.font.opacity":"Lettertype doorzichtigheid","settings.subtitles.background.color":"Achtergrondkleur","settings.subtitles.background.opacity":"Achtergrond doorzichtigheid","colors.white":"wit","colors.black":"zwart","colors.red":"rood","colors.green":"groen","colors.blue":"blauw","colors.cyan":"cyaan","colors.yellow":"geel","colors.magenta":"magenta",percent:"{value}%","settings.subtitles.font.size":"Lettertype grootte","settings.subtitles.characterEdge":"Lettertype rand","settings.subtitles.characterEdge.raised":"verhoogd","settings.subtitles.characterEdge.depressed":"verlaagd","settings.subtitles.characterEdge.uniform":"uniform","settings.subtitles.characterEdge.dropshadowed":"schaduw","settings.subtitles.font.family":"Standaard lettertype","settings.subtitles.font.family.monospacedserif":"monospace serif","settings.subtitles.font.family.proportionalserif":"proportioneel serif","settings.subtitles.font.family.monospacedsansserif":"monospace sans-serif","settings.subtitles.font.family.proportionalsansserif":"proportioneel sans-serif","settings.subtitles.font.family.casual":"casual","settings.subtitles.font.family.cursive":"cursief","settings.subtitles.font.family.smallcapital":"kleine hoofdletters","settings.time.hours":"Uren","settings.time.minutes":"Minuten","settings.time.seconds":"Seconden","ads.remainingTime":"Deze advertentie eindigt in {remainingTime} seconden.",settings:"Instellingen",fullscreen:"Volledig scherm",speed:"Snelheid",playPause:"Afspelen/Pauzeren",play:"Afspelen",pause:"Pauzeren",open:"Openen",close:"Sluiten",pictureInPicture:"Picture-in-Picture",appleAirplay:"Apple AirPlay",googleCast:"Google Cast",vr:"VR",off:"uit",auto:"automatisch",ecoMode:"Eco-modus","ecoMode.title":"Eco-modus",back:"Terug",reset:"Reset",replay:"Opnieuw afspelen",normal:"normaal",default:"standaard",live:"Live","subtitle.example":"voorbeeld ondertiteling","subtitle.select":"Selecteer ondertiteling",playingOn:"Speelt af op <strong>{castDeviceName}</strong>",connectingTo:"Verbinden met <strong>{castDeviceName}</strong>...",watermarkLink:"Link naar homepage",controlBar:"Videospeler bediening",player:"Videospeler",seekBar:"Video tijdlijn","seekBar.value":"Waarde","seekBar.timeshift":"Tijdverschuiving","seekBar.durationText":"van","quickseek.forward":"{seekSeconds} seconden vooruitspoelen","quickseek.rewind":"{seekSeconds} seconden terugspoelen"}},{}],96:[function(e,D,t){"use strict";var o=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&("get"in i?t.__esModule:!i.writable&&!i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,o,i)}:function(e,t,n,o){e[o=void 0===o?n:o]=t[n]}),n=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||o(t,e,n)},i=(Object.defineProperty(t,"__esModule",{value:!0}),t.ClickOverlay=t.VolumeControlButton=t.TitleBar=t.SubtitleSelectBox=t.SubtitleOverlay=t.SeekBarLabel=t.RecommendationOverlay=t.ErrorMessageOverlay=t.Component=t.CastToggleButton=t.CastStatusOverlay=t.AudioTrackSelectBox=t.AudioQualitySelectBox=t.Label=t.Container=t.UIContainer=t.Watermark=t.VRToggleButton=t.VolumeToggleButton=t.VideoQualitySelectBox=t.ToggleButton=t.SettingsToggleButton=t.SettingsPanel=t.ItemSelectionList=t.SelectBox=t.SeekBar=t.PlaybackToggleButton=t.PlaybackTimeLabelMode=t.PlaybackTimeLabel=t.HugePlaybackToggleButton=t.FullscreenToggleButton=t.ControlBar=t.Button=t.ListOrientation=t.ListNavigationGroup=t.RootNavigationGroup=t.NavigationGroup=t.SpatialNavigation=t.I18n=t.i18n=t.ErrorUtils=t.StorageUtils=t.BrowserUtils=t.UIUtils=t.PlayerUtils=t.StringUtils=t.ArrayUtils=t.DemoFactory=t.UIFactory=t.version=void 0,t.ListSelector=t.QuickSeekButton=t.ReplayButton=t.SettingsPanelItem=t.SubtitleSettingsPanelPage=t.SettingsPanelPageOpenButton=t.SettingsPanelPageBackButton=t.SettingsPanelPage=t.AudioTrackListBox=t.SubtitleListBox=t.ListBox=t.SubtitleSettingsResetButton=t.WindowOpacitySelectBox=t.WindowColorSelectBox=t.SubtitleSettingsLabel=t.SubtitleSettingSelectBox=t.FontSizeSelectBox=t.FontOpacitySelectBox=t.FontFamilySelectBox=t.FontColorSelectBox=t.CharacterEdgeSelectBox=t.BackgroundOpacitySelectBox=t.BackgroundColorSelectBox=t.Spacer=t.PictureInPictureToggleButton=t.VolumeSlider=t.AirPlayToggleButton=t.MetadataLabelContent=t.MetadataLabel=t.CloseButton=t.PlaybackToggleOverlay=t.CastUIContainer=t.BufferingOverlay=t.HugeReplayButton=t.PlaybackSpeedSelectBox=t.AdClickOverlay=t.AdMessageLabel=t.AdSkipButton=void 0,t.version="3.98.0",n(e("./uimanager"),t),n(e("./uiconfig"),t),e("./uifactory")),r=(Object.defineProperty(t,"UIFactory",{enumerable:!0,get:function(){return i.UIFactory}}),e("./demofactory")),s=(Object.defineProperty(t,"DemoFactory",{enumerable:!0,get:function(){return r.DemoFactory}}),e("./arrayutils")),a=(Object.defineProperty(t,"ArrayUtils",{enumerable:!0,get:function(){return s.ArrayUtils}}),e("./stringutils")),l=(Object.defineProperty(t,"StringUtils",{enumerable:!0,get:function(){return a.StringUtils}}),e("./playerutils")),c=(Object.defineProperty(t,"PlayerUtils",{enumerable:!0,get:function(){return l.PlayerUtils}}),e("./uiutils")),u=(Object.defineProperty(t,"UIUtils",{enumerable:!0,get:function(){return c.UIUtils}}),e("./browserutils")),p=(Object.defineProperty(t,"BrowserUtils",{enumerable:!0,get:function(){return u.BrowserUtils}}),e("./storageutils")),g=(Object.defineProperty(t,"StorageUtils",{enumerable:!0,get:function(){return p.StorageUtils}}),e("./errorutils")),f=(Object.defineProperty(t,"ErrorUtils",{enumerable:!0,get:function(){return g.ErrorUtils}}),e("./localization/i18n")),d=(Object.defineProperty(t,"i18n",{enumerable:!0,get:function(){return f.i18n}}),Object.defineProperty(t,"I18n",{enumerable:!0,get:function(){return f.I18n}}),e("./spatialnavigation/spatialnavigation")),h=(Object.defineProperty(t,"SpatialNavigation",{enumerable:!0,get:function(){return d.SpatialNavigation}}),e("./spatialnavigation/navigationgroup")),y=(Object.defineProperty(t,"NavigationGroup",{enumerable:!0,get:function(){return h.NavigationGroup}}),e("./spatialnavigation/rootnavigationgroup")),m=(Object.defineProperty(t,"RootNavigationGroup",{enumerable:!0,get:function(){return y.RootNavigationGroup}}),e("./spatialnavigation/ListNavigationGroup")),b=(Object.defineProperty(t,"ListNavigationGroup",{enumerable:!0,get:function(){return m.ListNavigationGroup}}),Object.defineProperty(t,"ListOrientation",{enumerable:!0,get:function(){return m.ListOrientation}}),e("./components/button")),v=(Object.defineProperty(t,"Button",{enumerable:!0,get:function(){return b.Button}}),e("./components/controlbar")),C=(Object.defineProperty(t,"ControlBar",{enumerable:!0,get:function(){return v.ControlBar}}),e("./components/fullscreentogglebutton")),S=(Object.defineProperty(t,"FullscreenToggleButton",{enumerable:!0,get:function(){return C.FullscreenToggleButton}}),e("./components/hugeplaybacktogglebutton")),P=(Object.defineProperty(t,"HugePlaybackToggleButton",{enumerable:!0,get:function(){return S.HugePlaybackToggleButton}}),e("./components/playbacktimelabel")),w=(Object.defineProperty(t,"PlaybackTimeLabel",{enumerable:!0,get:function(){return P.PlaybackTimeLabel}}),Object.defineProperty(t,"PlaybackTimeLabelMode",{enumerable:!0,get:function(){return P.PlaybackTimeLabelMode}}),e("./components/playbacktogglebutton")),E=(Object.defineProperty(t,"PlaybackToggleButton",{enumerable:!0,get:function(){return w.PlaybackToggleButton}}),e("./components/seekbar")),_=(Object.defineProperty(t,"SeekBar",{enumerable:!0,get:function(){return E.SeekBar}}),e("./components/selectbox")),O=(Object.defineProperty(t,"SelectBox",{enumerable:!0,get:function(){return _.SelectBox}}),e("./components/itemselectionlist")),k=(Object.defineProperty(t,"ItemSelectionList",{enumerable:!0,get:function(){return O.ItemSelectionList}}),e("./components/settingspanel")),x=(Object.defineProperty(t,"SettingsPanel",{enumerable:!0,get:function(){return k.SettingsPanel}}),e("./components/settingstogglebutton")),T=(Object.defineProperty(t,"SettingsToggleButton",{enumerable:!0,get:function(){return x.SettingsToggleButton}}),e("./components/togglebutton")),M=(Object.defineProperty(t,"ToggleButton",{enumerable:!0,get:function(){return T.ToggleButton}}),e("./components/videoqualityselectbox")),L=(Object.defineProperty(t,"VideoQualitySelectBox",{enumerable:!0,get:function(){return M.VideoQualitySelectBox}}),e("./components/volumetogglebutton")),A=(Object.defineProperty(t,"VolumeToggleButton",{enumerable:!0,get:function(){return L.VolumeToggleButton}}),e("./components/vrtogglebutton")),I=(Object.defineProperty(t,"VRToggleButton",{enumerable:!0,get:function(){return A.VRToggleButton}}),e("./components/watermark")),B=(Object.defineProperty(t,"Watermark",{enumerable:!0,get:function(){return I.Watermark}}),e("./components/uicontainer")),U=(Object.defineProperty(t,"UIContainer",{enumerable:!0,get:function(){return B.UIContainer}}),e("./components/container")),z=(Object.defineProperty(t,"Container",{enumerable:!0,get:function(){return U.Container}}),e("./components/label")),R=(Object.defineProperty(t,"Label",{enumerable:!0,get:function(){return z.Label}}),e("./components/audioqualityselectbox")),F=(Object.defineProperty(t,"AudioQualitySelectBox",{enumerable:!0,get:function(){return R.AudioQualitySelectBox}}),e("./components/audiotrackselectbox")),V=(Object.defineProperty(t,"AudioTrackSelectBox",{enumerable:!0,get:function(){return F.AudioTrackSelectBox}}),e("./components/caststatusoverlay")),H=(Object.defineProperty(t,"CastStatusOverlay",{enumerable:!0,get:function(){return V.CastStatusOverlay}}),e("./components/casttogglebutton")),N=(Object.defineProperty(t,"CastToggleButton",{enumerable:!0,get:function(){return H.CastToggleButton}}),e("./components/component")),W=(Object.defineProperty(t,"Component",{enumerable:!0,get:function(){return N.Component}}),e("./components/errormessageoverlay")),G=(Object.defineProperty(t,"ErrorMessageOverlay",{enumerable:!0,get:function(){return W.ErrorMessageOverlay}}),e("./components/recommendationoverlay")),q=(Object.defineProperty(t,"RecommendationOverlay",{enumerable:!0,get:function(){return G.RecommendationOverlay}}),e("./components/seekbarlabel")),K=(Object.defineProperty(t,"SeekBarLabel",{enumerable:!0,get:function(){return q.SeekBarLabel}}),e("./components/subtitleoverlay")),Q=(Object.defineProperty(t,"SubtitleOverlay",{enumerable:!0,get:function(){return K.SubtitleOverlay}}),e("./components/subtitleselectbox")),Y=(Object.defineProperty(t,"SubtitleSelectBox",{enumerable:!0,get:function(){return Q.SubtitleSelectBox}}),e("./components/titlebar")),X=(Object.defineProperty(t,"TitleBar",{enumerable:!0,get:function(){return Y.TitleBar}}),e("./components/volumecontrolbutton")),Z=(Object.defineProperty(t,"VolumeControlButton",{enumerable:!0,get:function(){return X.VolumeControlButton}}),e("./components/clickoverlay")),J=(Object.defineProperty(t,"ClickOverlay",{enumerable:!0,get:function(){return Z.ClickOverlay}}),e("./components/adskipbutton")),$=(Object.defineProperty(t,"AdSkipButton",{enumerable:!0,get:function(){return J.AdSkipButton}}),e("./components/admessagelabel")),ee=(Object.defineProperty(t,"AdMessageLabel",{enumerable:!0,get:function(){return $.AdMessageLabel}}),e("./components/adclickoverlay")),te=(Object.defineProperty(t,"AdClickOverlay",{enumerable:!0,get:function(){return ee.AdClickOverlay}}),e("./components/playbackspeedselectbox")),ne=(Object.defineProperty(t,"PlaybackSpeedSelectBox",{enumerable:!0,get:function(){return te.PlaybackSpeedSelectBox}}),e("./components/hugereplaybutton")),oe=(Object.defineProperty(t,"HugeReplayButton",{enumerable:!0,get:function(){return ne.HugeReplayButton}}),e("./components/bufferingoverlay")),ie=(Object.defineProperty(t,"BufferingOverlay",{enumerable:!0,get:function(){return oe.BufferingOverlay}}),e("./components/castuicontainer")),re=(Object.defineProperty(t,"CastUIContainer",{enumerable:!0,get:function(){return ie.CastUIContainer}}),e("./components/playbacktoggleoverlay")),se=(Object.defineProperty(t,"PlaybackToggleOverlay",{enumerable:!0,get:function(){return re.PlaybackToggleOverlay}}),e("./components/closebutton")),j=(Object.defineProperty(t,"CloseButton",{enumerable:!0,get:function(){return se.CloseButton}}),e("./components/metadatalabel")),ae=(Object.defineProperty(t,"MetadataLabel",{enumerable:!0,get:function(){return j.MetadataLabel}}),Object.defineProperty(t,"MetadataLabelContent",{enumerable:!0,get:function(){return j.MetadataLabelContent}}),e("./components/airplaytogglebutton")),le=(Object.defineProperty(t,"AirPlayToggleButton",{enumerable:!0,get:function(){return ae.AirPlayToggleButton}}),e("./components/volumeslider")),ce=(Object.defineProperty(t,"VolumeSlider",{enumerable:!0,get:function(){return le.VolumeSlider}}),e("./components/pictureinpicturetogglebutton")),ue=(Object.defineProperty(t,"PictureInPictureToggleButton",{enumerable:!0,get:function(){return ce.PictureInPictureToggleButton}}),e("./components/spacer")),pe=(Object.defineProperty(t,"Spacer",{enumerable:!0,get:function(){return ue.Spacer}}),e("./components/subtitlesettings/backgroundcolorselectbox")),ge=(Object.defineProperty(t,"BackgroundColorSelectBox",{enumerable:!0,get:function(){return pe.BackgroundColorSelectBox}}),e("./components/subtitlesettings/backgroundopacityselectbox")),fe=(Object.defineProperty(t,"BackgroundOpacitySelectBox",{enumerable:!0,get:function(){return ge.BackgroundOpacitySelectBox}}),e("./components/subtitlesettings/characteredgeselectbox")),de=(Object.defineProperty(t,"CharacterEdgeSelectBox",{enumerable:!0,get:function(){return fe.CharacterEdgeSelectBox}}),e("./components/subtitlesettings/fontcolorselectbox")),he=(Object.defineProperty(t,"FontColorSelectBox",{enumerable:!0,get:function(){return de.FontColorSelectBox}}),e("./components/subtitlesettings/fontfamilyselectbox")),ye=(Object.defineProperty(t,"FontFamilySelectBox",{enumerable:!0,get:function(){return he.FontFamilySelectBox}}),e("./components/subtitlesettings/fontopacityselectbox")),me=(Object.defineProperty(t,"FontOpacitySelectBox",{enumerable:!0,get:function(){return ye.FontOpacitySelectBox}}),e("./components/subtitlesettings/fontsizeselectbox")),be=(Object.defineProperty(t,"FontSizeSelectBox",{enumerable:!0,get:function(){return me.FontSizeSelectBox}}),e("./components/subtitlesettings/subtitlesettingselectbox")),ve=(Object.defineProperty(t,"SubtitleSettingSelectBox",{enumerable:!0,get:function(){return be.SubtitleSettingSelectBox}}),e("./components/subtitlesettings/subtitlesettingslabel")),Ce=(Object.defineProperty(t,"SubtitleSettingsLabel",{enumerable:!0,get:function(){return ve.SubtitleSettingsLabel}}),e("./components/subtitlesettings/windowcolorselectbox")),Se=(Object.defineProperty(t,"WindowColorSelectBox",{enumerable:!0,get:function(){return Ce.WindowColorSelectBox}}),e("./components/subtitlesettings/windowopacityselectbox")),Pe=(Object.defineProperty(t,"WindowOpacitySelectBox",{enumerable:!0,get:function(){return Se.WindowOpacitySelectBox}}),e("./components/subtitlesettings/subtitlesettingsresetbutton")),we=(Object.defineProperty(t,"SubtitleSettingsResetButton",{enumerable:!0,get:function(){return Pe.SubtitleSettingsResetButton}}),e("./components/listbox")),Ee=(Object.defineProperty(t,"ListBox",{enumerable:!0,get:function(){return we.ListBox}}),e("./components/subtitlelistbox")),_e=(Object.defineProperty(t,"SubtitleListBox",{enumerable:!0,get:function(){return Ee.SubtitleListBox}}),e("./components/audiotracklistbox")),Oe=(Object.defineProperty(t,"AudioTrackListBox",{enumerable:!0,get:function(){return _e.AudioTrackListBox}}),e("./components/settingspanelpage")),ke=(Object.defineProperty(t,"SettingsPanelPage",{enumerable:!0,get:function(){return Oe.SettingsPanelPage}}),e("./components/settingspanelpagebackbutton")),xe=(Object.defineProperty(t,"SettingsPanelPageBackButton",{enumerable:!0,get:function(){return ke.SettingsPanelPageBackButton}}),e("./components/settingspanelpageopenbutton")),Te=(Object.defineProperty(t,"SettingsPanelPageOpenButton",{enumerable:!0,get:function(){return xe.SettingsPanelPageOpenButton}}),e("./components/subtitlesettings/subtitlesettingspanelpage")),Me=(Object.defineProperty(t,"SubtitleSettingsPanelPage",{enumerable:!0,get:function(){return Te.SubtitleSettingsPanelPage}}),e("./components/settingspanelitem")),Le=(Object.defineProperty(t,"SettingsPanelItem",{enumerable:!0,get:function(){return Me.SettingsPanelItem}}),e("./components/replaybutton")),Ae=(Object.defineProperty(t,"ReplayButton",{enumerable:!0,get:function(){return Le.ReplayButton}}),e("./components/quickseekbutton")),Ie=(Object.defineProperty(t,"QuickSeekButton",{enumerable:!0,get:function(){return Ae.QuickSeekButton}}),e("./components/listselector"));Object.defineProperty(t,"ListSelector",{enumerable:!0,get:function(){return Ie.ListSelector}}),"function"!=typeof Object.assign&&(Object.assign=function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");e=Object(e);for(var t=1;t<arguments.length;t++){var n=arguments[t];if(null!=n)for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e})},{"./arrayutils":1,"./browserutils":3,"./components/adclickoverlay":4,"./components/admessagelabel":5,"./components/adskipbutton":6,"./components/airplaytogglebutton":7,"./components/audioqualityselectbox":8,"./components/audiotracklistbox":9,"./components/audiotrackselectbox":10,"./components/bufferingoverlay":11,"./components/button":12,"./components/caststatusoverlay":13,"./components/casttogglebutton":14,"./components/castuicontainer":15,"./components/clickoverlay":16,"./components/closebutton":17,"./components/component":18,"./components/container":19,"./components/controlbar":20,"./components/errormessageoverlay":23,"./components/fullscreentogglebutton":24,"./components/hugeplaybacktogglebutton":25,"./components/hugereplaybutton":26,"./components/itemselectionlist":27,"./components/label":28,"./components/listbox":29,"./components/listselector":30,"./components/metadatalabel":31,"./components/pictureinpicturetogglebutton":32,"./components/playbackspeedselectbox":33,"./components/playbacktimelabel":34,"./components/playbacktogglebutton":35,"./components/playbacktoggleoverlay":36,"./components/quickseekbutton":37,"./components/recommendationoverlay":38,"./components/replaybutton":39,"./components/seekbar":40,"./components/seekbarlabel":43,"./components/selectbox":44,"./components/settingspanel":45,"./components/settingspanelitem":46,"./components/settingspanelpage":47,"./components/settingspanelpagebackbutton":48,"./components/settingspanelpageopenbutton":50,"./components/settingstogglebutton":51,"./components/spacer":52,"./components/subtitlelistbox":53,"./components/subtitleoverlay":54,"./components/subtitleselectbox":55,"./components/subtitlesettings/backgroundcolorselectbox":56,"./components/subtitlesettings/backgroundopacityselectbox":57,"./components/subtitlesettings/characteredgeselectbox":59,"./components/subtitlesettings/fontcolorselectbox":60,"./components/subtitlesettings/fontfamilyselectbox":61,"./components/subtitlesettings/fontopacityselectbox":62,"./components/subtitlesettings/fontsizeselectbox":63,"./components/subtitlesettings/subtitlesettingselectbox":65,"./components/subtitlesettings/subtitlesettingslabel":66,"./components/subtitlesettings/subtitlesettingspanelpage":68,"./components/subtitlesettings/subtitlesettingsresetbutton":69,"./components/subtitlesettings/windowcolorselectbox":70,"./components/subtitlesettings/windowopacityselectbox":71,"./components/titlebar":73,"./components/togglebutton":74,"./components/uicontainer":76,"./components/videoqualityselectbox":77,"./components/volumecontrolbutton":78,"./components/volumeslider":79,"./components/volumetogglebutton":80,"./components/vrtogglebutton":81,"./components/watermark":82,"./demofactory":83,"./errorutils":85,"./localization/i18n":91,"./playerutils":98,"./spatialnavigation/ListNavigationGroup":99,"./spatialnavigation/navigationgroup":103,"./spatialnavigation/rootnavigationgroup":105,"./spatialnavigation/spatialnavigation":107,"./storageutils":110,"./stringutils":111,"./uiconfig":114,"./uifactory":115,"./uimanager":116,"./uiutils":117}],97:[function(e,t,n){"use strict";var o,i;Object.defineProperty(n,"__esModule",{value:!0}),n.isMobileV3PlayerAPI=n.MobileV3PlayerEvent=void 0,(i=o=n.MobileV3PlayerEvent||(n.MobileV3PlayerEvent={})).SourceError="sourceerror",i.PlayerError="playererror",i.PlaylistTransition="playlisttransition",n.isMobileV3PlayerAPI=function(e){for(var t in o)if(o.hasOwnProperty(t)&&!e.exports.PlayerEvent.hasOwnProperty(t))return!1;return!0}},{}],98:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.PlayerUtils=void 0;var o,i,r=e("./eventdispatcher"),s=e("./browserutils");function a(e){function t(){n.detect()}var n=this;this.timeShiftAvailabilityChangedEvent=new r.EventDispatcher,this.player=e,this.timeShiftAvailable=void 0;e.on(e.exports.PlayerEvent.SourceLoaded,t),e.on(e.exports.PlayerEvent.TimeChanged,t)}function l(e,t){function n(){o.detect()}var o=this;this.liveChangedEvent=new r.EventDispatcher,this.player=e,this.uimanager=t,this.live=void 0;this.uimanager.getConfig().events.onUpdated.subscribe(n),e.on(e.exports.PlayerEvent.Play,n),s.BrowserUtils.isAndroid&&s.BrowserUtils.isChrome&&e.on(e.exports.PlayerEvent.TimeChanged,n),e.exports.PlayerEvent.DurationChanged&&e.on(e.exports.PlayerEvent.DurationChanged,n),e.on(e.exports.PlayerEvent.AdBreakStarted,n),e.on(e.exports.PlayerEvent.AdBreakFinished,n)}o=n.PlayerUtils||(n.PlayerUtils={}),(e=i=o.PlayerState||(o.PlayerState={}))[e.Idle=0]="Idle",e[e.Prepared=1]="Prepared",e[e.Playing=2]="Playing",e[e.Paused=3]="Paused",e[e.Finished=4]="Finished",o.isTimeShiftAvailable=function(e){return e.isLive()&&0!==e.getMaxTimeShift()},o.getState=function(e){return e.hasEnded()?i.Finished:e.isPlaying()?i.Playing:e.isPaused()?i.Paused:null!=e.getSource()?i.Prepared:i.Idle},o.getCurrentTimeRelativeToSeekableRange=function(e){var t=e.getCurrentTime();return e.isLive()?t:t-o.getSeekableRangeStart(e,0)},o.getSeekableRangeStart=function(e,t){return void 0===t&&(t=0),e.getSeekableRange()&&e.getSeekableRange().start||t},o.getSeekableRangeRespectingLive=function(e){var t,n,o;return e.isLive()?(t=-e.getTimeShift(),n=-e.getMaxTimeShift(),{start:(o=e.getCurrentTime())-(n-t),end:o+t}):e.getSeekableRange()},a.prototype.detect=function(){var e;this.player.isLive()&&(e=o.isTimeShiftAvailable(this.player))!==this.timeShiftAvailable&&(this.timeShiftAvailabilityChangedEvent.dispatch(this.player,{timeShiftAvailable:e}),this.timeShiftAvailable=e)},Object.defineProperty(a.prototype,"onTimeShiftAvailabilityChanged",{get:function(){return this.timeShiftAvailabilityChangedEvent.getEvent()},enumerable:!1,configurable:!0}),o.TimeShiftAvailabilityDetector=a,l.prototype.detect=function(){var e=this.player.isLive();e!==this.live&&(this.liveChangedEvent.dispatch(this.player,{live:e}),this.live=e)},Object.defineProperty(l.prototype,"onLiveChanged",{get:function(){return this.liveChangedEvent.getEvent()},enumerable:!1,configurable:!0}),o.LiveStreamDetector=l,o.clampValueToRange=function(e,t,n){var o=Math.min(t,n),t=Math.max(t,n);return Math.min(Math.max(e,o),t)}},{"./browserutils":3,"./eventdispatcher":86}],99:[function(e,t,n){"use strict";var o,r,s,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),a=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var o,i=0,r=t.length;i<r;i++)!o&&i in t||((o=o||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(o||Array.prototype.slice.call(t))},l=(Object.defineProperty(n,"__esModule",{value:!0}),n.ListNavigationGroup=n.ListOrientation=void 0,e("./navigationgroup")),c=e("./types"),e=((e=r=n.ListOrientation||(n.ListOrientation={})).Horizontal="horizontal",e.Vertical="vertical",s=l.NavigationGroup,i(u,s),u.prototype.handleAction=function(e){s.prototype.handleAction.call(this,e),e===c.Action.SELECT&&this.handleAction(c.Action.BACK)},u.prototype.handleNavigation=function(e){s.prototype.handleNavigation.call(this,e),this.listNavigationDirections.includes(e)||this.handleAction(c.Action.BACK)},u);function u(e,t){for(var n=[],o=2;o<arguments.length;o++)n[o-2]=arguments[o];var i=s.apply(this,a([t],n,!1))||this;switch(e){case r.Vertical:i.listNavigationDirections=[c.Direction.UP,c.Direction.DOWN];break;case r.Horizontal:i.listNavigationDirections=[c.Direction.LEFT,c.Direction.RIGHT]}return i}n.ListNavigationGroup=e},{"./navigationgroup":103,"./types":109}],100:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getHtmlElementsFromComponents=void 0;var o=e("../components/container"),i=e("./typeguards");n.getHtmlElementsFromComponents=function(e){var t=[];return e.filter(function(e){return!e.isHidden()}).forEach(function(e){(e instanceof o.Container?function t(e){var n=[];return e.getComponents().forEach(function(e){(0,i.isContainer)(e)?n.push.apply(n,t(e)):(0,i.isComponent)(e)&&n.push(e)}),n}(e):[e]).forEach(function(e){t.push.apply(t,(e=e,(0,i.isListBox)(e)?[].slice.call(e.getDomElement().get()[0].children):e.getDomElement().get().slice(0,1)))})}),t}},{"../components/container":19,"./typeguards":108}],101:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getKeyMapForPlatform=void 0;var o=e("./types"),i=e("../browserutils"),r={isApplicable:function(){return i.BrowserUtils.isTizen},keyCodes:{38:o.Direction.UP,40:o.Direction.DOWN,37:o.Direction.LEFT,39:o.Direction.RIGHT,13:o.Action.SELECT,10009:o.Action.BACK}},s={isApplicable:function(){return i.BrowserUtils.isWebOs},keyCodes:{38:o.Direction.UP,40:o.Direction.DOWN,37:o.Direction.LEFT,39:o.Direction.RIGHT,13:o.Action.SELECT,461:o.Action.BACK}},a={isApplicable:function(){return i.BrowserUtils.isPlayStation},keyCodes:{38:o.Direction.UP,40:o.Direction.DOWN,37:o.Direction.LEFT,39:o.Direction.RIGHT,13:o.Action.SELECT,27:o.Action.BACK}},l={isApplicable:function(){return i.BrowserUtils.isAndroid},keyCodes:{38:o.Direction.UP,40:o.Direction.DOWN,37:o.Direction.LEFT,39:o.Direction.RIGHT,13:o.Action.SELECT,27:o.Action.BACK}},c={isApplicable:function(){return i.BrowserUtils.isHisense},keyCodes:{38:o.Direction.UP,40:o.Direction.DOWN,37:o.Direction.LEFT,39:o.Direction.RIGHT,13:o.Action.SELECT,8:o.Action.BACK}},u={38:o.Direction.UP,40:o.Direction.DOWN,37:o.Direction.LEFT,39:o.Direction.RIGHT,13:o.Action.SELECT,27:o.Action.BACK};n.getKeyMapForPlatform=function(){var e=[s,r,a,c,l].find(function(e){return e.isApplicable()});return e?e.keyCodes:u}},{"../browserutils":3,"./types":109}],102:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getBoundingRectFromElement=n.getElementInDirection=void 0;var o=e("./types");function r(e){return Math.sqrt(Math.pow(e.x,2)+Math.pow(e.y,2))}function s(e){e=i(e);return{x:e.x+e.width/2,y:e.y+e.height/2}}function a(e,t,n){n={x:n===o.Direction.LEFT?-1:n===o.Direction.RIGHT?1:0,y:n===o.Direction.UP?-1:n===o.Direction.DOWN?1:0},t={x:t.x-e.x,y:t.y-e.y},e=r(t),t={x:t.x/e,y:t.y/e},e=(n.x*t.x+n.y*t.y)/(r(n)*r(t));return 180*Math.acos(e)/Math.PI}function i(e){e=e.getBoundingClientRect();return"number"!=typeof e.x&&"number"!=typeof e.y&&(e.x=e.left,e.y=e.top),e}n.getElementInDirection=function(t,e,o){var i;if(t)return i=s(t),null==(e=e.filter(function(e){return e!==t}).map(function(e){var t=s(e),n=r({x:t.x-i.x,y:t.y-i.y});return{angle:a(i,t,o),dist:n,element:e}}).filter(function(e){return e.angle<=45}).sort(function(e,t){var n=e.angle,e=e.dist;return n-t.angle+(e-t.dist)}).shift())?void 0:e.element},n.getBoundingRectFromElement=i},{"./types":109}],103:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.NavigationGroup=void 0;var o=e("./navigationalgorithm"),i=e("./gethtmlelementsfromcomponents"),r=e("./nodeeventsubscriber"),s=e("./typeguards"),a=e("./types");function l(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.container=e,this.removeElementHoverEventListeners=function(){},this.components=t,this.eventSubscriber=new r.NodeEventSubscriber}l.prototype.getActiveElement=function(){return this.activeElement},l.prototype.focusElement=function(e){this.blurActiveElement(),this.activeElement=e,this.activeElement.focus()},l.prototype.blurActiveElement=function(){var e;null!=(e=this.activeElement)&&e.blur()},l.prototype.focusFirstElement=function(){var e=(0,i.getHtmlElementsFromComponents)(this.components)[0];e&&this.focusElement(e)},l.prototype.defaultNavigationHandler=function(e){e=(0,o.getElementInDirection)(this.activeElement,(0,i.getHtmlElementsFromComponents)(this.components),e);e&&this.focusElement(e)},l.prototype.defaultActionHandler=function(e){switch(e){case a.Action.SELECT:this.activeElement.click();break;case a.Action.BACK:this.container.hide()}},l.prototype.handleInput=function(e,t,n){var o=!0;null!=n&&n(e,this.activeElement,function(){return o=!1}),o&&t.call(this,e)},l.prototype.handleNavigation=function(e){this.activeElement?this.handleInput(e,this.defaultNavigationHandler,this.onNavigation):this.activeElementBeforeDisable?this.focusElement(this.activeElementBeforeDisable):this.focusFirstElement()},l.prototype.handleAction=function(e){this.handleInput(e,this.defaultActionHandler,this.onAction)},l.prototype.disable=function(){this.activeElement&&(this.activeElementBeforeDisable=this.activeElement,this.blurActiveElement(),this.activeElement=void 0)},l.prototype.enable=function(){this.activeElementBeforeDisable&&!(0,s.isSettingsPanel)(this.container)?(this.focusElement(this.activeElementBeforeDisable),this.activeElementBeforeDisable=void 0):this.focusFirstElement(),this.trackElementHover()},l.prototype.trackElementHover=function(){var o=this,e=(this.removeElementHoverEventListeners(),(0,i.getHtmlElementsFromComponents)(this.components).map(function(e){function t(){return o.disable()}var n=o.focusElement.bind(o,e);return o.eventSubscriber.on(e,"mouseenter",n),o.eventSubscriber.on(e,"mouseleave",t),function(){o.eventSubscriber.off(e,"mouseenter",n),o.eventSubscriber.off(e,"mouseleave",t)}}));this.removeElementHoverEventListeners=function(){return e.forEach(function(e){return e()})}},l.prototype.release=function(){this.eventSubscriber.release(),this.activeElement=void 0,this.components.splice(0,this.components.length),this.removeElementHoverEventListeners()},n.NavigationGroup=l},{"./gethtmlelementsfromcomponents":100,"./navigationalgorithm":102,"./nodeeventsubscriber":104,"./typeguards":108,"./types":109}],104:[function(e,t,n){"use strict";function o(){this.attachedListeners=new Map}Object.defineProperty(n,"__esModule",{value:!0}),n.NodeEventSubscriber=void 0,o.prototype.getEventListenersOfType=function(e){return this.attachedListeners.has(e)||this.attachedListeners.set(e,[]),this.attachedListeners.get(e)},o.prototype.on=function(e,t,n,o){e.addEventListener(t,n,o),this.getEventListenersOfType(t).push([e,n,o])},o.prototype.off=function(o,e,i,r){var t=this.getEventListenersOfType(e),n=t.findIndex(function(e){var t=e[0],n=e[1],e=e[2];return t===o&&n===i&&e===r});o.removeEventListener(e,i,r),-1<n&&t.splice(n,1)},o.prototype.release=function(){var i=this;this.attachedListeners.forEach(function(e,o){e.forEach(function(e){var t=e[0],n=e[1],e=e[2];i.off(t,o,n,e)})}),this.attachedListeners.clear()},n.NodeEventSubscriber=o},{}],105:[function(e,t,n){"use strict";var o,i,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var o,i=0,r=t.length;i<r;i++)!o&&i in t||((o=o||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(o||Array.prototype.slice.call(t))},a=(Object.defineProperty(n,"__esModule",{value:!0}),n.RootNavigationGroup=void 0,e("./navigationgroup")),l=e("./types"),e=(i=a.NavigationGroup,r(c,i),c.prototype.handleAction=function(e){this.container.showUi(),i.prototype.handleAction.call(this,e)},c.prototype.handleNavigation=function(e){this.container.showUi(),i.prototype.handleNavigation.call(this,e)},c.prototype.defaultActionHandler=function(e){e===l.Action.BACK?this.container.hideUi():i.prototype.defaultActionHandler.call(this,e)},c.prototype.release=function(){i.prototype.release.call(this)},c);function c(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=i.apply(this,s([e],t,!1))||this;return o.container=e,o}n.RootNavigationGroup=e},{"./navigationgroup":103,"./types":109}],106:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.SeekBarHandler=void 0;var i=e("./nodeeventsubscriber"),r=e("./types"),o=e("./navigationalgorithm");function s(e){var o=this;this.rootNavigationGroup=e,this.cursorPosition={x:0,y:0},this.isScrubbing=!1,this.scrubSpeedPercentage=.005,this.onNavigation=function(e,t,n){a(t)&&(e===r.Direction.UP||e===r.Direction.DOWN?o.stopSeeking(l(t)):(o.initializeOrUpdateCursorPosition(t,e),o.dispatchMouseMoveEvent(l(t)),n()))},this.onAction=function(e,t,n){a(t)&&(t=l(t),e===r.Action.SELECT&&o.isScrubbing?(o.dispatchMouseClickEvent(t),n()):e===r.Action.BACK&&(o.stopSeeking(t),n()))},this.rootNavigationGroup.onAction=this.onAction,this.eventSubscriber=new i.NodeEventSubscriber,this.rootNavigationGroup.onNavigation=this.onNavigation}function a(e){return-1<Array.from(e.classList).findIndex(function(e){return/-ui-seekbar$/.test(e)})}function l(e){return e.children.item(0)}s.prototype.updateScrubSpeedPercentage=function(){var e=this;clearTimeout(this.scrubSpeedResetTimeout),this.scrubSpeedPercentage*=1.1,this.scrubSpeedResetTimeout=window.setTimeout(function(){return e.scrubSpeedPercentage=.005},100)},s.prototype.getIncrement=function(e,t){this.updateScrubSpeedPercentage();t=t.getBoundingClientRect().width*this.scrubSpeedPercentage;return e===r.Direction.RIGHT?t:-t},s.prototype.resetCursorPosition=function(){this.cursorPosition.x=0,this.cursorPosition.y=0},s.prototype.updateCursorPosition=function(e,t){this.cursorPosition.x+=this.getIncrement(e,t)},s.prototype.initializeCursorPosition=function(e){var e=e.querySelector('[class*="seekbar-playbackposition-marker"]'),e=(0,o.getBoundingRectFromElement)(e),t=e.x+e.width/2,e=e.y;this.cursorPosition.x=t,this.cursorPosition.y=e},s.prototype.initializeOrUpdateCursorPosition=function(e,t){this.isScrubbing?this.updateCursorPosition(t,e):this.initializeCursorPosition(e),this.isScrubbing=!0},s.prototype.getCursorPositionMouseEventInit=function(){return{clientX:this.cursorPosition.x,clientY:this.cursorPosition.y}},s.prototype.dispatchMouseMoveEvent=function(e){e.dispatchEvent(new MouseEvent("mousemove",this.getCursorPositionMouseEventInit()))},s.prototype.dispatchMouseClickEvent=function(t){function n(){var e=o.getCursorPositionMouseEventInit();document.dispatchEvent(new MouseEvent("mouseup",e)),o.eventSubscriber.off(t,"mousedown",n),o.stopSeeking(t)}var o=this;this.eventSubscriber.on(t,"mousedown",n),t.dispatchEvent(new MouseEvent("mousedown"))},s.prototype.stopSeeking=function(e){this.resetCursorPosition(),this.isScrubbing=!1,this.dispatchMouseLeaveEvent(e)},s.prototype.dispatchMouseLeaveEvent=function(e){e.dispatchEvent(new MouseEvent("mouseleave"))},s.prototype.release=function(){this.eventSubscriber.release(),this.rootNavigationGroup.onAction=void 0,this.rootNavigationGroup.onNavigation=void 0},n.SeekBarHandler=s},{"./navigationalgorithm":102,"./nodeeventsubscriber":104,"./types":109}],107:[function(e,t,n){"use strict";var i=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var o,i=0,r=t.length;i<r;i++)!o&&i in t||((o=o||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(o||Array.prototype.slice.call(t))},r=(Object.defineProperty(n,"__esModule",{value:!0}),n.SpatialNavigation=void 0,e("./nodeeventsubscriber")),s=e("./seekbarhandler"),a=e("./keymap"),l=e("./typeguards");function o(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=this;this.navigationGroups=[],this.onShow=function(e){o.activeNavigationGroups.push(e),o.updateEnabledNavigationGroup()},this.onHide=function(t){var e=o.activeNavigationGroups.findIndex(function(e){return e===t});-1<e&&(t.disable(),o.activeNavigationGroups.splice(e,1),o.updateEnabledNavigationGroup())},this.handleKeyEvent=function(e){var t=o.keyMap[e.keyCode],n=o.getActiveNavigationGroup();n&&n.container&&!n.container.isHidden()&&!n.container.isDisabled()&&((0,l.isDirection)(t)&&(n.handleNavigation(t),e.preventDefault(),e.stopPropagation()),(0,l.isAction)(t))&&(n.handleAction(t),e.preventDefault(),e.stopPropagation())},this.seekBarHandler=new s.SeekBarHandler(e),this.activeNavigationGroups=[],this.unsubscribeVisibilityChangesFns=[],this.eventSubscriber=new r.NodeEventSubscriber,this.navigationGroups=i([e],t,!0),this.keyMap=(0,a.getKeyMapForPlatform)(),this.subscribeToNavigationGroupVisibilityChanges(),this.attachKeyEventHandler(),this.enableDefaultNavigationGroup()}o.prototype.attachKeyEventHandler=function(){this.eventSubscriber.on(document,"keydown",this.handleKeyEvent,!0)},o.prototype.subscribeToNavigationGroupVisibilityChanges=function(){var o=this;this.navigationGroups.forEach(function(e){function t(){return o.onShow(e)}function n(){return o.onHide(e)}e.container.onShow.subscribe(t),e.container.onHide.subscribe(n),o.unsubscribeVisibilityChangesFns.push(function(){return e.container.onShow.unsubscribe(t)},function(){return e.container.onHide.unsubscribe(n)})})},o.prototype.unsubscribeFromNavigationGroupVisibilityChanges=function(){this.unsubscribeVisibilityChangesFns.forEach(function(e){return e()}),this.unsubscribeVisibilityChangesFns=[]},o.prototype.enableDefaultNavigationGroup=function(){var e=null!=(e=this.navigationGroups.find(function(e){return e.container.isShown()}))?e:this.navigationGroups[0];e&&(this.activeNavigationGroups.push(e),this.updateEnabledNavigationGroup())},o.prototype.updateEnabledNavigationGroup=function(){var n=this;this.activeNavigationGroups.forEach(function(e,t){t<n.activeNavigationGroups.length-1?e.disable():e.enable()})},o.prototype.getActiveNavigationGroup=function(){return this.activeNavigationGroups[this.activeNavigationGroups.length-1]},o.prototype.release=function(){this.unsubscribeFromNavigationGroupVisibilityChanges(),this.eventSubscriber.release(),this.navigationGroups.forEach(function(e){return e.release()}),this.seekBarHandler.release()},n.SpatialNavigation=o},{"./keymap":101,"./nodeeventsubscriber":104,"./seekbarhandler":106,"./typeguards":108}],108:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.isAction=n.isDirection=n.isListBox=n.isContainer=n.isComponent=n.isSettingsPanel=void 0;var o=e("../components/component"),i=e("../components/settingspanel"),r=e("../components/container"),s=e("../components/listbox"),a=e("./types");n.isSettingsPanel=function(e){return e instanceof i.SettingsPanel},n.isComponent=function(e){return null!=e&&e instanceof o.Component},n.isContainer=function(e){return null!=e&&e instanceof r.Container},n.isListBox=function(e){return e instanceof s.ListBox},n.isDirection=function(e){return"string"==typeof e&&Object.values(a.Direction).includes(e)},n.isAction=function(e){return"string"==typeof e&&Object.values(a.Action).includes(e)}},{"../components/component":18,"../components/container":19,"../components/listbox":29,"../components/settingspanel":45,"./types":109}],109:[function(e,t,n){"use strict";var o;Object.defineProperty(n,"__esModule",{value:!0}),n.Action=n.Direction=void 0,(o=n.Direction||(n.Direction={})).UP="up",o.DOWN="down",o.LEFT="left",o.RIGHT="right",(o=n.Action||(n.Action={})).SELECT="select",o.BACK="back"},{}],110:[function(e,t,n){"use strict";var o;function i(){try{return!o&&window.localStorage&&"function"==typeof localStorage.getItem&&"function"==typeof localStorage.setItem}catch(e){}}function r(t,e){if(i())try{window.localStorage.setItem(t,e)}catch(e){console.debug("Failed to set storage item ".concat(t),e)}}function s(t){if(i())try{return window.localStorage.getItem(t)}catch(e){console.debug("Failed to get storage item ".concat(t),e)}return null}Object.defineProperty(n,"__esModule",{value:!0}),n.StorageUtils=void 0,(n=n.StorageUtils||(n.StorageUtils={})).setStorageApiDisabled=function(e){o=e.disableStorageApi},n.setItem=r,n.getItem=s,n.setObject=function(e,t){r(e,JSON.stringify(t))},n.getObject=function(e){return(e=s(e))?JSON.parse(e):null}},{}],111:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.StringUtils=void 0;var r,i=e("./localization/i18n");function a(e,t){void 0===t&&(t=r.FORMAT_HHMMSS);var n=e<0,o=(n&&(e=-e),Math.floor(e/3600)),i=Math.floor(e/60)-60*o,e=Math.floor(e)%60;return(n?"-":"")+t.replace("hh",l(o,2)).replace("mm",l(i,2)).replace("ss",l(e,2))}function l(e,t){e+="";return"0000000000".substr(0,t-e.length)+e}(r=n.StringUtils||(n.StringUtils={})).FORMAT_HHMMSS="hh:mm:ss",r.FORMAT_MMSS="mm:ss",r.secondsToTime=a,r.secondsToText=function(e){var t=e<0,n=(t&&(e=-e),Math.floor(e/3600)),o=Math.floor(e/60)-60*n,e=Math.floor(e)%60;return(t?"-":"")+(0!==n?"".concat(n," ").concat(i.i18n.performLocalization(i.i18n.getLocalizer("settings.time.hours"))," "):"")+(0!=o?"".concat(o," ").concat(i.i18n.performLocalization(i.i18n.getLocalizer("settings.time.minutes"))," "):"")+"".concat(e," ").concat(i.i18n.performLocalization(i.i18n.getLocalizer("settings.time.seconds")))},r.replaceAdMessagePlaceholders=function(e,r,s){var t=new RegExp("\\{(remainingTime|playedTime|adDuration|adBreakRemainingTime)(}|%((0[1-9]\\d*(\\.\\d+(d|f)|d|f)|\\.\\d+f|d|f)|hh:mm:ss|mm:ss)})","g");return e.replace(t,function(e){var t=0,n=(-1<e.indexOf("remainingTime")?t=r?Math.ceil(r-s.getCurrentTime()):s.getDuration()-s.getCurrentTime():-1<e.indexOf("playedTime")?t=s.getCurrentTime():-1<e.indexOf("adDuration")?t=s.getDuration():-1<e.indexOf("adBreakRemainingTime")&&(t=0,s.ads.isLinearAdActive())&&(n=s.ads.getActiveAdBreak().ads.findIndex(function(e){return s.ads.getActiveAd().id===e.id}),t=s.ads.getActiveAdBreak().ads.slice(n).reduce(function(e,t){return e+(t.isLinear?t.duration:0)},0)-s.getCurrentTime()),Math.round(t)),t=e,e=(/%((0[1-9]\d*(\.\d+(d|f)|d|f)|\.\d+f|d|f)|hh:mm:ss|mm:ss)/.test(t)||(t="%d"),0),o=((o=t.match(/(%0[1-9]\d*)(?=(\.\d+f|f|d))/))&&(e=parseInt(o[0].substring(2))),null),i=t.match(/\.\d*(?=f)/);return i&&!isNaN(parseInt(i[0].substring(1)))&&20<(o=parseInt(i[0].substring(1)))&&(o=20),-1<t.indexOf("f")?(i="",-1<(i=null!==o?n.toFixed(o):""+n).indexOf(".")?l(i,i.length+(e-i.indexOf("."))):l(i,e)):-1<t.indexOf(":")?(o=Math.ceil(n),-1<t.indexOf("hh")?a(o):(i=Math.floor(o/60),t=o%60,l(i,2)+":"+l(t,2))):l(Math.ceil(n),e)})}},{"./localization/i18n":91}],112:[function(e,t,n){"use strict";var i=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var o,i=0,r=t.length;i<r;i++)!o&&i in t||((o=o||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(o||Array.prototype.slice.call(t))},r=(Object.defineProperty(n,"__esModule",{value:!0}),n.SubtitleSwitchHandler=void 0,e("./localization/i18n"));n.SubtitleSwitchHandler=(s.prototype.bindSelectionEvent=function(){var o=this;this.listElement.onItemSelected.subscribe(function(e,t){var n;t===s.SUBTITLES_OFF_KEY?(n=o.player.subtitles.list().filter(function(e){return e.enabled}).pop())&&o.player.subtitles.disable(n.id):o.player.subtitles.enable(t,!0)})},s.prototype.bindPlayerEvents=function(){this.player.on(this.player.exports.PlayerEvent.SubtitleAdded,this.addSubtitle),this.player.on(this.player.exports.PlayerEvent.SubtitleEnabled,this.selectCurrentSubtitle),this.player.on(this.player.exports.PlayerEvent.SubtitleDisabled,this.selectCurrentSubtitle),this.player.on(this.player.exports.PlayerEvent.SubtitleRemoved,this.removeSubtitle),this.player.on(this.player.exports.PlayerEvent.SourceUnloaded,this.clearSubtitles),this.player.on(this.player.exports.PlayerEvent.PeriodSwitched,this.refreshSubtitles),this.uimanager.getConfig().events.onUpdated.subscribe(this.refreshSubtitles)},s.SUBTITLES_OFF_KEY="null",s);function s(e,t,n){var o=this;this.addSubtitle=function(e){e=e.subtitle;o.listElement.hasItem(e.id)||o.listElement.addItem(e.id,e.label)},this.removeSubtitle=function(e){e=e.subtitle;o.listElement.hasItem(e.id)&&o.listElement.removeItem(e.id)},this.selectCurrentSubtitle=function(){var e;o.player.subtitles&&(e=o.player.subtitles.list().filter(function(e){return e.enabled}).pop(),o.listElement.selectItem(e?e.id:s.SUBTITLES_OFF_KEY))},this.clearSubtitles=function(){o.listElement.clearItems()},this.refreshSubtitles=function(){var e,t;o.player.subtitles&&(e={key:s.SUBTITLES_OFF_KEY,label:r.i18n.getLocalizer("off")},t=o.player.subtitles.list(),o.listElement.synchronizeItems(i([e],t.map(function(e){return{key:e.id,label:e.label}}),!0)),o.selectCurrentSubtitle())},this.player=e,this.listElement=t,this.uimanager=n,this.bindSelectionEvent(),this.bindPlayerEvents(),this.refreshSubtitles()}},{"./localization/i18n":91}],113:[function(e,t,n){"use strict";function o(e,t,n){void 0===n&&(n=!1),this.delay=e,this.callback=t,this.repeat=n,this.timeoutOrIntervalId=0,this.active=!1,this.suspended=!1}Object.defineProperty(n,"__esModule",{value:!0}),n.Timeout=void 0,o.prototype.start=function(){return this.reset(),this},o.prototype.clear=function(){this.clearInternal()},o.prototype.suspend=function(){return this.suspended=!0,this.clearInternal(),this},o.prototype.resume=function(e){return this.suspended=!1,e&&this.reset(),this},o.prototype.reset=function(){var e=this;this.clearInternal(),this.suspended||(this.repeat?this.timeoutOrIntervalId=setInterval(this.callback,this.delay):this.timeoutOrIntervalId=setTimeout(function(){e.active=!1,e.callback()},this.delay),this.active=!0)},o.prototype.isActive=function(){return this.active},o.prototype.clearInternal=function(){(this.repeat?clearInterval:clearTimeout)(this.timeoutOrIntervalId),this.active=!1},n.Timeout=o},{}],114:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0})},{}],115:[function(e,D,t){"use strict";var n,o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},c=(Object.defineProperty(t,"__esModule",{value:!0}),t.UIFactory=void 0,e("./components/subtitleoverlay")),u=e("./components/settingspanelpage"),p=e("./components/settingspanelitem"),s=e("./components/videoqualityselectbox"),a=e("./components/playbackspeedselectbox"),U=e("./components/audiotrackselectbox"),z=e("./components/audioqualityselectbox"),g=e("./components/settingspanel"),R=e("./components/subtitlesettings/subtitlesettingspanelpage"),F=e("./components/settingspanelpageopenbutton"),V=e("./components/subtitlesettings/subtitlesettingslabel"),H=e("./components/subtitleselectbox"),f=e("./components/controlbar"),d=e("./components/container"),h=e("./components/playbacktimelabel"),y=e("./components/seekbar"),m=e("./components/seekbarlabel"),l=e("./components/playbacktogglebutton"),b=e("./components/volumetogglebutton"),N=e("./components/volumeslider"),W=e("./components/spacer"),G=e("./components/pictureinpicturetogglebutton"),q=e("./components/airplaytogglebutton"),K=e("./components/casttogglebutton"),Q=e("./components/vrtogglebutton"),v=e("./components/settingstogglebutton"),C=e("./components/fullscreentogglebutton"),S=e("./components/uicontainer"),P=e("./components/bufferingoverlay"),w=e("./components/playbacktoggleoverlay"),Y=e("./components/caststatusoverlay"),E=e("./components/titlebar"),_=e("./components/recommendationoverlay"),O=e("./components/watermark"),k=e("./components/errormessageoverlay"),i=e("./components/adclickoverlay"),r=e("./components/admessagelabel"),x=e("./components/adskipbutton"),X=e("./components/closebutton"),T=e("./components/metadatalabel"),M=e("./playerutils"),Z=e("./components/label"),J=e("./components/castuicontainer"),L=e("./uimanager"),A=e("./localization/i18n"),$=e("./components/subtitlelistbox"),ee=e("./components/audiotracklistbox"),te=e("./spatialnavigation/spatialnavigation"),ne=e("./spatialnavigation/rootnavigationgroup"),I=e("./spatialnavigation/ListNavigationGroup"),oe=e("./components/ecomodecontainer");function ie(e){var t=new c.SubtitleOverlay,n=[new p.SettingsPanelItem(A.i18n.getLocalizer("settings.video.quality"),new s.VideoQualitySelectBox),new p.SettingsPanelItem(A.i18n.getLocalizer("speed"),new a.PlaybackSpeedSelectBox),new p.SettingsPanelItem(A.i18n.getLocalizer("settings.audio.track"),new U.AudioTrackSelectBox),new p.SettingsPanelItem(A.i18n.getLocalizer("settings.audio.quality"),new z.AudioQualitySelectBox)],o=(e.ecoMode&&((e=new oe.EcoModeContainer).setOnToggleCallback(function(){o.getDomElement().css({width:"",height:""})}),n.unshift(e)),e=new u.SettingsPanelPage({components:n}),new g.SettingsPanel({components:[e],hidden:!0})),n=new R.SubtitleSettingsPanelPage({settingsPanel:o,overlay:t}),i=new H.SubtitleSelectBox,r=new F.SettingsPanelPageOpenButton({targetPage:n,container:o,ariaLabel:A.i18n.getLocalizer("settings.subtitles"),text:A.i18n.getLocalizer("open")}),e=(e.addComponent(new p.SettingsPanelItem(new V.SubtitleSettingsLabel({text:A.i18n.getLocalizer("settings.subtitles"),opener:r}),i,{role:"menubar"})),o.addComponent(n),new f.ControlBar({components:[o,new d.Container({components:[new h.PlaybackTimeLabel({timeLabelMode:h.PlaybackTimeLabelMode.CurrentTime,hideInLivePlayback:!0}),new y.SeekBar({label:new m.SeekBarLabel}),new h.PlaybackTimeLabel({timeLabelMode:h.PlaybackTimeLabelMode.TotalTime,cssClasses:["text-right"]})],cssClasses:["controlbar-top"]}),new d.Container({components:[new l.PlaybackToggleButton,new b.VolumeToggleButton,new N.VolumeSlider,new W.Spacer,new G.PictureInPictureToggleButton,new q.AirPlayToggleButton,new K.CastToggleButton,new Q.VRToggleButton,new v.SettingsToggleButton({settingsPanel:o}),new C.FullscreenToggleButton],cssClasses:["controlbar-bottom"]})]}));return new S.UIContainer({components:[t,new P.BufferingOverlay,new w.PlaybackToggleOverlay,new Y.CastStatusOverlay,e,new E.TitleBar,new _.RecommendationOverlay,new O.Watermark,new k.ErrorMessageOverlay],hideDelay:2e3,hidePlayerStateExceptions:[M.PlayerUtils.PlayerState.Prepared,M.PlayerUtils.PlayerState.Paused,M.PlayerUtils.PlayerState.Finished]})}function re(){return new S.UIContainer({components:[new P.BufferingOverlay,new i.AdClickOverlay,new w.PlaybackToggleOverlay,new d.Container({components:[new r.AdMessageLabel({text:A.i18n.getLocalizer("ads.remainingTime")}),new x.AdSkipButton],cssClass:"ui-ads-status"}),new f.ControlBar({components:[new d.Container({components:[new l.PlaybackToggleButton,new b.VolumeToggleButton,new N.VolumeSlider,new W.Spacer,new C.FullscreenToggleButton],cssClasses:["controlbar-bottom"]})]})],cssClasses:["ui-skin-ads"],hideDelay:2e3,hidePlayerStateExceptions:[M.PlayerUtils.PlayerState.Prepared,M.PlayerUtils.PlayerState.Paused,M.PlayerUtils.PlayerState.Finished]})}function B(){var e=new c.SubtitleOverlay,t=new u.SettingsPanelPage({components:[new p.SettingsPanelItem(A.i18n.getLocalizer("settings.video.quality"),new s.VideoQualitySelectBox),new p.SettingsPanelItem(A.i18n.getLocalizer("speed"),new a.PlaybackSpeedSelectBox),new p.SettingsPanelItem(A.i18n.getLocalizer("settings.audio.track"),new U.AudioTrackSelectBox),new p.SettingsPanelItem(A.i18n.getLocalizer("settings.audio.quality"),new z.AudioQualitySelectBox)]}),n=new g.SettingsPanel({components:[t],hidden:!0,pageTransitionAnimation:!1,hideDelay:-1}),o=new R.SubtitleSettingsPanelPage({settingsPanel:n,overlay:e}),i=new F.SettingsPanelPageOpenButton({targetPage:o,container:n,ariaLabel:A.i18n.getLocalizer("settings.subtitles"),text:A.i18n.getLocalizer("open")}),r=new H.SubtitleSelectBox,t=(t.addComponent(new p.SettingsPanelItem(new V.SubtitleSettingsLabel({text:A.i18n.getLocalizer("settings.subtitles"),opener:i}),r,{role:"menubar"})),n.addComponent(o),n.addComponent(new X.CloseButton({target:n})),o.addComponent(new X.CloseButton({target:n})),new f.ControlBar({components:[new d.Container({components:[new h.PlaybackTimeLabel({timeLabelMode:h.PlaybackTimeLabelMode.CurrentTime,hideInLivePlayback:!0}),new y.SeekBar({label:new m.SeekBarLabel}),new h.PlaybackTimeLabel({timeLabelMode:h.PlaybackTimeLabelMode.TotalTime,cssClasses:["text-right"]})],cssClasses:["controlbar-top"]})]}));return new S.UIContainer({components:[e,new P.BufferingOverlay,new Y.CastStatusOverlay,new w.PlaybackToggleOverlay,new _.RecommendationOverlay,t,new E.TitleBar({components:[new T.MetadataLabel({content:T.MetadataLabelContent.Title}),new K.CastToggleButton,new Q.VRToggleButton,new G.PictureInPictureToggleButton,new q.AirPlayToggleButton,new b.VolumeToggleButton,new v.SettingsToggleButton({settingsPanel:n}),new C.FullscreenToggleButton]}),n,new O.Watermark,new k.ErrorMessageOverlay],cssClasses:["ui-skin-smallscreen"],hideDelay:2e3,hidePlayerStateExceptions:[M.PlayerUtils.PlayerState.Prepared,M.PlayerUtils.PlayerState.Paused,M.PlayerUtils.PlayerState.Finished]})}function j(){return new S.UIContainer({components:[new P.BufferingOverlay,new i.AdClickOverlay,new w.PlaybackToggleOverlay,new E.TitleBar({components:[new Z.Label({cssClass:"label-metadata-title"}),new C.FullscreenToggleButton]}),new d.Container({components:[new r.AdMessageLabel({text:"Ad: {remainingTime} secs"}),new x.AdSkipButton],cssClass:"ui-ads-status"})],cssClasses:["ui-skin-ads","ui-skin-smallscreen"],hideDelay:2e3,hidePlayerStateExceptions:[M.PlayerUtils.PlayerState.Prepared,M.PlayerUtils.PlayerState.Paused,M.PlayerUtils.PlayerState.Finished]})}function se(){var e=new f.ControlBar({components:[new d.Container({components:[new h.PlaybackTimeLabel({timeLabelMode:h.PlaybackTimeLabelMode.CurrentTime,hideInLivePlayback:!0}),new y.SeekBar({smoothPlaybackPositionUpdateIntervalMs:-1}),new h.PlaybackTimeLabel({timeLabelMode:h.PlaybackTimeLabelMode.TotalTime,cssClasses:["text-right"]})],cssClasses:["controlbar-top"]})]});return new J.CastUIContainer({components:[new c.SubtitleOverlay,new P.BufferingOverlay,new w.PlaybackToggleOverlay,new O.Watermark,e,new E.TitleBar({keepHiddenWithoutMetadata:!0}),new k.ErrorMessageOverlay],cssClasses:["ui-skin-cast-receiver"],hideDelay:2e3,hidePlayerStateExceptions:[M.PlayerUtils.PlayerState.Prepared,M.PlayerUtils.PlayerState.Paused,M.PlayerUtils.PlayerState.Finished]})}function ae(){var e=new $.SubtitleListBox,t=new g.SettingsPanel({components:[new u.SettingsPanelPage({components:[new p.SettingsPanelItem(null,e)]})],hidden:!0}),n=new ee.AudioTrackListBox,o=new g.SettingsPanel({components:[new u.SettingsPanelPage({components:[new p.SettingsPanelItem(null,n)]})],hidden:!0}),i=new y.SeekBar({label:new m.SeekBarLabel}),r=new w.PlaybackToggleOverlay,s=new v.SettingsToggleButton({settingsPanel:t,autoHideWhenNoActiveSettings:!0,cssClass:"ui-subtitlesettingstogglebutton",text:A.i18n.getLocalizer("settings.subtitles")}),a=new v.SettingsToggleButton({settingsPanel:o,autoHideWhenNoActiveSettings:!0,cssClass:"ui-audiotracksettingstogglebutton",ariaLabel:A.i18n.getLocalizer("settings.audio.track"),text:A.i18n.getLocalizer("settings.audio.track")}),l=new S.UIContainer({components:[new c.SubtitleOverlay,new P.BufferingOverlay,r,new f.ControlBar({components:[new d.Container({components:[new h.PlaybackTimeLabel({timeLabelMode:h.PlaybackTimeLabelMode.CurrentTime,hideInLivePlayback:!0}),i,new h.PlaybackTimeLabel({timeLabelMode:h.PlaybackTimeLabelMode.RemainingTime,cssClasses:["text-right"]})],cssClasses:["controlbar-top"]})]}),new E.TitleBar({components:[new d.Container({components:[new T.MetadataLabel({content:T.MetadataLabelContent.Title}),s,a],cssClasses:["ui-titlebar-top"]}),new d.Container({components:[new T.MetadataLabel({content:T.MetadataLabelContent.Description}),t,o],cssClasses:["ui-titlebar-bottom"]})]}),new _.RecommendationOverlay,new k.ErrorMessageOverlay],cssClasses:["ui-skin-tv"],hideDelay:2e3,hidePlayerStateExceptions:[M.PlayerUtils.PlayerState.Prepared,M.PlayerUtils.PlayerState.Paused,M.PlayerUtils.PlayerState.Finished]});return{ui:l,spatialNavigation:new te.SpatialNavigation(new ne.RootNavigationGroup(l,r,i,a,s),new I.ListNavigationGroup(I.ListOrientation.Vertical,t,e),new I.ListNavigationGroup(I.ListOrientation.Vertical,o,n))}}function le(){var e=new x.AdSkipButton,t=new l.PlaybackToggleButton,n=new S.UIContainer({components:[new P.BufferingOverlay,new i.AdClickOverlay,new w.PlaybackToggleOverlay,new d.Container({components:[new r.AdMessageLabel({text:A.i18n.getLocalizer("ads.remainingTime")}),e],cssClass:"ui-ads-status"}),new f.ControlBar({components:[new d.Container({components:[t],cssClasses:["controlbar-bottom"]})]})],cssClasses:["ui-skin-tv","ui-skin-ads"],hideDelay:2e3,hidePlayerStateExceptions:[M.PlayerUtils.PlayerState.Prepared,M.PlayerUtils.PlayerState.Paused,M.PlayerUtils.PlayerState.Finished]});return{ui:n,spatialNavigation:new te.SpatialNavigation(new ne.RootNavigationGroup(n,t,e))}}(n=t.UIFactory||(t.UIFactory={})).buildDefaultUI=function(e,t){return n.buildModernUI(e,t=void 0===t?{}:t)},n.buildDefaultSmallScreenUI=function(e,t){return n.buildModernSmallScreenUI(e,t=void 0===t?{}:t)},n.buildDefaultCastReceiverUI=function(e,t){return n.buildModernCastReceiverUI(e,t=void 0===t?{}:t)},n.buildDefaultTvUI=function(e,t){return n.buildModernTvUI(e,t=void 0===t?{}:t)},n.modernUI=ie,n.modernAdsUI=re,n.modernSmallScreenUI=B,n.modernSmallScreenAdsUI=j,n.modernCastReceiverUI=se,n.buildModernUI=function(e,t){return void 0===t&&(t={}),new L.UIManager(e,[{ui:j(),condition:function(e){return e.isMobile&&e.documentWidth<600&&e.isAd&&e.adRequiresUi}},{ui:re(),condition:function(e){return e.isAd&&e.adRequiresUi}},{ui:B(),condition:function(e){return!e.isAd&&!e.adRequiresUi&&e.isMobile&&e.documentWidth<600}},{ui:ie(t),condition:function(e){return!e.isAd&&!e.adRequiresUi}}],t)},n.buildModernSmallScreenUI=function(e,t){return void 0===t&&(t={}),new L.UIManager(e,[{ui:j(),condition:function(e){return e.isAd&&e.adRequiresUi}},{ui:B(),condition:function(e){return!e.isAd&&!e.adRequiresUi}}],t)},n.buildModernCastReceiverUI=function(e,t){return void 0===t&&(t={}),new L.UIManager(e,se(),t)},n.buildModernTvUI=function(e,t){return void 0===t&&(t={}),new L.UIManager(e,[o(o({},le()),{condition:function(e){return e.isAd&&e.adRequiresUi}}),o(o({},ae()),{condition:function(e){return!e.isAd&&!e.adRequiresUi}})],t)},n.modernTvUI=ae,n.modernTvAdsUI=le},{"./components/adclickoverlay":4,"./components/admessagelabel":5,"./components/adskipbutton":6,"./components/airplaytogglebutton":7,"./components/audioqualityselectbox":8,"./components/audiotracklistbox":9,"./components/audiotrackselectbox":10,"./components/bufferingoverlay":11,"./components/caststatusoverlay":13,"./components/casttogglebutton":14,"./components/castuicontainer":15,"./components/closebutton":17,"./components/container":19,"./components/controlbar":20,"./components/ecomodecontainer":21,"./components/errormessageoverlay":23,"./components/fullscreentogglebutton":24,"./components/label":28,"./components/metadatalabel":31,"./components/pictureinpicturetogglebutton":32,"./components/playbackspeedselectbox":33,"./components/playbacktimelabel":34,"./components/playbacktogglebutton":35,"./components/playbacktoggleoverlay":36,"./components/recommendationoverlay":38,"./components/seekbar":40,"./components/seekbarlabel":43,"./components/settingspanel":45,"./components/settingspanelitem":46,"./components/settingspanelpage":47,"./components/settingspanelpageopenbutton":50,"./components/settingstogglebutton":51,"./components/spacer":52,"./components/subtitlelistbox":53,"./components/subtitleoverlay":54,"./components/subtitleselectbox":55,"./components/subtitlesettings/subtitlesettingslabel":66,"./components/subtitlesettings/subtitlesettingspanelpage":68,"./components/titlebar":73,"./components/uicontainer":76,"./components/videoqualityselectbox":77,"./components/volumeslider":79,"./components/volumetogglebutton":80,"./components/vrtogglebutton":81,"./components/watermark":82,"./localization/i18n":91,"./playerutils":98,"./spatialnavigation/ListNavigationGroup":99,"./spatialnavigation/rootnavigationgroup":105,"./spatialnavigation/spatialnavigation":107,"./uimanager":116}],116:[function(e,t,n){"use strict";var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),g=this&&this.__assign||function(){return(g=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},d=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var o,i=0,r=t.length;i<r;i++)!o&&i in t||((o=o||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(o||Array.prototype.slice.call(t))},f=(Object.defineProperty(n,"__esModule",{value:!0}),n.PlayerWrapper=n.UIInstanceManager=n.UIManager=void 0,e("./components/uicontainer")),h=e("./dom"),r=e("./components/container"),y=e("./eventdispatcher"),s=e("./uiutils"),m=e("./arrayutils"),c=e("./browserutils"),b=e("./volumecontroller"),a=e("./localization/i18n"),v=e("./focusvisibilitytracker"),C=e("./mobilev3playerapi"),S=e("./components/subtitlesettings/subtitlesettingsmanager"),P=e("./storageutils");function l(i,e,t){void 0===t&&(t={});for(var r=this,n=(this.events={onUiVariantResolve:new y.EventDispatcher,onActiveUiChanged:new y.EventDispatcher},e instanceof f.UIContainer?((o=[]).push({ui:e}),this.uiVariants=o):this.uiVariants=e,this.subtitleSettingsManager=new S.SubtitleSettingsManager,this.player=i,this.managerPlayerWrapper=new _(i),t.metadata=t.metadata||{},this.config=g(g({playbackSpeedSelectionEnabled:!0,autoUiVariantResolve:!0,disableAutoHideWhenHovered:!1,enableSeekPreview:!0},t),{events:{onUpdated:new y.EventDispatcher},volumeController:new b.VolumeController(this.managerPlayerWrapper.getPlayer())}),function(){var e=i.getSource()||{},e=(r.config.metadata=JSON.parse(JSON.stringify(t.metadata||{})),{metadata:{title:e.title,description:e.description,markers:e.markers},recommendations:e.recommendations});r.config.metadata.title=e.metadata.title||t.metadata.title,r.config.metadata.description=e.metadata.description||t.metadata.description,r.config.metadata.markers=e.metadata.markers||t.metadata.markers||[],r.config.recommendations=e.recommendations||t.recommendations||[],P.StorageUtils.setStorageApiDisabled(t)}),o=(n(),this.subtitleSettingsManager.initialize(),function(){n(),r.config.events.onUpdated.dispatch(r)}),e=this.managerPlayerWrapper.getPlayer(),s=(e.on(this.player.exports.PlayerEvent.SourceLoaded,o),(0,C.isMobileV3PlayerAPI)(e)&&e.on(C.MobileV3PlayerEvent.PlaylistTransition,o),t.container?this.uiContainerElement=(t.container instanceof HTMLElement,new h.DOM(t.container)):this.uiContainerElement=new h.DOM(i.getContainer()),this.uiInstanceManagers=[],[]),a=0,l=this.uiVariants;a<l.length;a++){var c=l[a];null==c.condition&&s.push(c),this.uiInstanceManagers.push(new w(i,c.ui,this.config,this.subtitleSettingsManager,c.spatialNavigation))}if(1<s.length)throw Error("Too many UIs without a condition: You cannot have more than one default UI");if(0<s.length&&s[0]!==this.uiVariants[this.uiVariants.length-1])throw Error("Invalid UI variant order: the default UI (without condition) must be at the end of the list");function u(e){if(null!=e)switch(e.type){case i.exports.PlayerEvent.AdStarted:p=e;break;case i.exports.PlayerEvent.AdBreakFinished:p=null,r.config.events.onUpdated.dispatch(r);break;case i.exports.PlayerEvent.SourceLoaded:case i.exports.PlayerEvent.SourceUnloaded:p=null}var t,n=null!=p,o=!1;(o=n&&(t=p.ad).isLinear?t.uiConfig&&t.uiConfig.requestsUi||!1:o)&&r.config.events.onUpdated.dispatch(r),r.resolveUiVariant({isAd:n,adRequiresUi:o},function(e){e.isAd&&r.currentUi.getWrappedPlayer().fireEventInUI(r.player.exports.PlayerEvent.AdStarted,p)})}var p=null;this.config.autoUiVariantResolve&&(this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.SourceLoaded,u),this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.SourceUnloaded,u),this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.Play,u),this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.Paused,u),this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.AdStarted,u),this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.AdBreakFinished,u),this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.PlayerResized,u),this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.ViewModeChanged,u)),this.focusVisibilityTracker=new v.FocusVisibilityTracker("bmpui"),u(null)}l.localize=function(e){return a.i18n.getLocalizer(e)},l.setLocalizationConfig=function(e){a.i18n.setConfig(e)},l.prototype.getSubtitleSettingsManager=function(){return this.subtitleSettingsManager},l.prototype.getConfig=function(){return this.config},l.prototype.getUiVariants=function(){return this.uiVariants},l.prototype.switchToUiVariant=function(e,t){var e=this.uiVariants.indexOf(e),n=this.currentUi,e=this.uiInstanceManagers[e];e!==this.currentUi&&(this.currentUi&&this.currentUi.getUI().hide(),this.currentUi=e,null!=this.currentUi)&&(this.currentUi.isConfigured()||(this.addUi(this.currentUi),this.currentUi.getUI().isHidden())||this.currentUi.getUI().hide(),t&&t(),this.currentUi.getUI().show(),this.events.onActiveUiChanged.dispatch(this,{previousUi:n,currentUi:e}))},l.prototype.resolveUiVariant=function(e,t){void 0===e&&(e={});for(var n={isAd:!1,adRequiresUi:!1,isFullscreen:this.player.getViewMode()===this.player.exports.ViewMode.Fullscreen,isMobile:c.BrowserUtils.isMobile,isPlaying:this.player.isPlaying(),width:this.uiContainerElement.width(),documentWidth:document.body.clientWidth},o=g(g({},n),e),i=(this.events.onUiVariantResolve.dispatch(this,o),null),r=0,s=this.uiVariants;r<s.length;r++){var a=s[r],l=null==a.condition||!0===a.condition(o);null==i&&l?i=a:a.ui.hide()}this.switchToUiVariant(i,function(){t&&t(o)})},l.prototype.addUi=function(e){var t=e.getUI().getDomElement(),n=e.getWrappedPlayer();e.configureControls(),this.uiContainerElement.append(t),n.getSource()&&this.config.events.onUpdated.dispatch(this),window.requestAnimationFrame?requestAnimationFrame(function(){e.onConfigured.dispatch(e.getUI())}):setTimeout(function(){e.onConfigured.dispatch(e.getUI())},0)},l.prototype.releaseUi=function(e){e.releaseControls();var t=e.getUI();t.hasDomElement()&&t.getDomElement().remove(),e.clearEventHandlers()},l.prototype.release=function(){for(var e=0,t=this.uiInstanceManagers;e<t.length;e++){var n=t[e];this.releaseUi(n)}this.managerPlayerWrapper.clearEventHandlers(),this.focusVisibilityTracker.release()},Object.defineProperty(l.prototype,"onUiVariantResolve",{get:function(){return this.events.onUiVariantResolve},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onActiveUiChanged",{get:function(){return this.events.onActiveUiChanged},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"activeUi",{get:function(){return this.currentUi},enumerable:!1,configurable:!0}),l.prototype.getTimelineMarkers=function(){return this.config.metadata.markers},l.prototype.addTimelineMarker=function(e){this.config.metadata.markers.push(e),this.config.events.onUpdated.dispatch(this)},l.prototype.removeTimelineMarker=function(e){return m.ArrayUtils.remove(this.config.metadata.markers,e)===e&&(this.config.events.onUpdated.dispatch(this),!0)},n.UIManager=l;u.prototype.getSubtitleSettingsManager=function(){return this.subtitleSettingsManager},u.prototype.getConfig=function(){return this.config},u.prototype.getUI=function(){return this.ui},u.prototype.getPlayer=function(){return this.playerWrapper.getPlayer()},Object.defineProperty(u.prototype,"onConfigured",{get:function(){return this.events.onConfigured},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onSeek",{get:function(){return this.events.onSeek},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onSeekPreview",{get:function(){return this.events.onSeekPreview},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onSeeked",{get:function(){return this.events.onSeeked},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onComponentShow",{get:function(){return this.events.onComponentShow},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onComponentHide",{get:function(){return this.events.onComponentHide},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onControlsShow",{get:function(){return this.events.onControlsShow},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onPreviewControlsHide",{get:function(){return this.events.onPreviewControlsHide},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onControlsHide",{get:function(){return this.events.onControlsHide},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onRelease",{get:function(){return this.events.onRelease},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"onComponentViewModeChanged",{get:function(){return this.events.onComponentViewModeChanged},enumerable:!1,configurable:!0}),u.prototype.clearEventHandlers=function(){this.playerWrapper.clearEventHandlers();var e,t=this.events;for(e in t)t[e].unsubscribeAll()};e=u;function u(e,t,n,o,i){this.events={onConfigured:new y.EventDispatcher,onSeek:new y.EventDispatcher,onSeekPreview:new y.EventDispatcher,onSeeked:new y.EventDispatcher,onComponentShow:new y.EventDispatcher,onComponentHide:new y.EventDispatcher,onComponentViewModeChanged:new y.EventDispatcher,onControlsShow:new y.EventDispatcher,onPreviewControlsHide:new y.EventDispatcher,onControlsHide:new y.EventDispatcher,onRelease:new y.EventDispatcher},this.playerWrapper=new _(e),this.ui=t,this.config=n,this.subtitleSettingsManager=o,this.spatialNavigation=i}n.UIInstanceManager=e;i(E,p=e),E.prototype.getWrappedPlayer=function(){return this.getPlayer()},E.prototype.configureControls=function(){this.configureControlsTree(this.getUI()),this.configured=!0},E.prototype.isConfigured=function(){return this.configured},E.prototype.configureControlsTree=function(e){var o=this,i=[];s.UIUtils.traverseTree(e,function(e){for(var t=0,n=i;t<n.length;t++)if(n[t]===e)throw console&&console.error("Circular reference in UI tree",e),Error("Circular reference in UI tree: "+e.constructor.name);e.initialize(),e.configure(o.getPlayer(),o),i.push(e)})},E.prototype.releaseControls=function(){var e;this.configured&&(this.onRelease.dispatch(this.getUI()),this.releaseControlsTree(this.getUI()),this.configured=!1),null!=(e=this.spatialNavigation)&&e.release(),this.released=!0},E.prototype.isReleased=function(){return this.released},E.prototype.releaseControlsTree=function(e){if(e.release(),e instanceof r.Container)for(var t=0,n=e.getComponents();t<n.length;t++){var o=n[t];this.releaseControlsTree(o)}},E.prototype.clearEventHandlers=function(){p.prototype.clearEventHandlers.call(this)};var p,w=E;function E(){return null!==p&&p.apply(this,arguments)||this}O.prototype.getPlayer=function(){return this.wrapper},O.prototype.clearEventHandlers=function(){try{this.player.getSource()}catch(e){e instanceof this.player.exports.PlayerAPINotAvailableError&&(this.eventHandlers={})}for(var e in this.eventHandlers)for(var t=0,n=this.eventHandlers[e];t<n.length;t++){var o=n[t];this.player.off(e,o)}};var _=O;function O(o){for(var r=this,e=(this.eventHandlers={},this.player=o,Object.getOwnPropertyNames(Object.getPrototypeOf({}))),t=d(["constructor"],e,!0),n=[],i=[],s=0,a=function(e){var t=[];for(;e;){var n=Object.getOwnPropertyNames(e).filter(function(e){return-1===t.indexOf(e)});t=t.concat(n),e=Object.getPrototypeOf(e)}return t}(o).filter(function(e){return-1===t.indexOf(e)});s<a.length;s++){var l=a[s];("function"==typeof o[l]?n:i).push(l)}for(var c={},u=0,p=n;u<p.length;u++)!function(e){c[e]=function(){return o[e].apply(o,arguments)}}(p[u]);for(var g=0,f=i;g<f.length;g++)!function(n){var t=function(e){for(;e;){var t=Object.getOwnPropertyDescriptor(e,n);if(t)return t;e=Object.getPrototypeOf(e)}}(o);t&&(t.get||t.set)?Object.defineProperty(c,n,{get:function(){return t.get.call(o)},set:function(e){return t.set.call(o,e)}}):c[n]=o[n]}(f[g]);c.on=function(e,t){return o.on(e,t),r.eventHandlers[e]||(r.eventHandlers[e]=[]),r.eventHandlers[e].push(t),c},c.off=function(e,t){return o.off(e,t),r.eventHandlers[e]&&m.ArrayUtils.remove(r.eventHandlers[e],t),c},c.fireEventInUI=function(e,t){if(r.eventHandlers[e])for(var n=Object.assign({},{timestamp:Date.now(),type:e,uiSourced:!0},t),o=0,i=r.eventHandlers[e];o<i.length;o++)(0,i[o])(n)},this.wrapper=c}n.PlayerWrapper=_},{"./arrayutils":1,"./browserutils":3,"./components/container":19,"./components/subtitlesettings/subtitlesettingsmanager":67,"./components/uicontainer":76,"./dom":84,"./eventdispatcher":86,"./focusvisibilitytracker":87,"./localization/i18n":91,"./mobilev3playerapi":97,"./storageutils":110,"./uiutils":117,"./volumecontroller":118}],117:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.UIUtils=void 0;var a=e("./components/container");(e=n.UIUtils||(n.UIUtils={})).traverseTree=function(e,r){function s(e,t){if(r(e,t),e instanceof a.Container)for(var n=0,o=e.getComponents();n<o.length;n++){var i=o[n];s(i,e)}}s(e)},(e=e.KeyCode||(e.KeyCode={}))[e.LeftArrow=37]="LeftArrow",e[e.UpArrow=38]="UpArrow",e[e.RightArrow=39]="RightArrow",e[e.DownArrow=40]="DownArrow",e[e.Space=32]="Space",e[e.End=35]="End",e[e.Home=36]="Home"},{"./components/container":19}],118:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.VolumeTransition=n.VolumeController=void 0;var o=e("./eventdispatcher");n.VolumeController=(i.prototype.setVolume=function(e){this.player.setVolume(e,i.issuerName)},i.prototype.getVolume=function(){return this.player.getVolume()},i.prototype.setMuted=function(e){e?this.player.mute(i.issuerName):this.player.unmute(i.issuerName)},i.prototype.toggleMuted=function(){this.isMuted()||0===this.getVolume()?this.recallVolume():this.setMuted(!0)},i.prototype.isMuted=function(){return this.player.isMuted()},i.prototype.storeVolume=function(){this.storedVolume=this.getVolume()},i.prototype.recallVolume=function(){this.setMuted(0===this.storedVolume),this.setVolume(this.storedVolume)},i.prototype.startTransition=function(){return new r(this)},i.prototype.onChangedEvent=function(){var e=this.isMuted(),t=this.getVolume(),n=e||0===t,e=e?0:t;this.storeVolume(),this.events.onChanged.dispatch(this,{volume:e,muted:n})},Object.defineProperty(i.prototype,"onChanged",{get:function(){return this.events.onChanged.getEvent()},enumerable:!1,configurable:!0}),i.issuerName="ui-volumecontroller",i);function i(e){function t(){n.onChangedEvent()}var n=this;this.player=e,this.events={onChanged:new o.EventDispatcher},this.storeVolume();e.on(e.exports.PlayerEvent.SourceLoaded,t),e.on(e.exports.PlayerEvent.VolumeChanged,t),e.on(e.exports.PlayerEvent.Muted,t),e.on(e.exports.PlayerEvent.Unmuted,t)}s.prototype.update=function(e){this.controller.setMuted(!1),this.controller.setVolume(e)},s.prototype.finish=function(e){0===e?(this.controller.recallVolume(),this.controller.setMuted(!0)):(this.controller.setMuted(!1),this.controller.setVolume(e),this.controller.storeVolume())};var r=s;function s(e){(this.controller=e).storeVolume()}n.VolumeTransition=r},{"./eventdispatcher":86}],119:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.VttUtils=void 0;function l(e,t,n,o){var i=t===u.Right?"vertical-lr":"vertical-rl";e.css("writing-mode",i),e.css(u.Top,"0"),d(e,n,t,o)}function c(e,t,n){if("auto"===t.position)e.css(n,"0");else switch(t.positionAlign){case"line-left":e.css(n,"".concat(t.position,"%")),e.css(g.get(n),"auto"),e.css("justify-content","flex-start");break;case"center":e.css(n,"".concat(t.position-t.size/2,"%")),e.css(g.get(n),"auto"),e.css("justify-content","center");break;case"line-right":e.css(n,"auto"),e.css(g.get(n),"".concat(100-t.position,"%")),e.css("justify-content","flex-end");break;default:e.css(n,"".concat(t.position,"%")),e.css("justify-content","flex-start")}}var u,p,o,a=21,g=((o=u=u||{}).Top="top",o.Bottom="bottom",o.Left="left",o.Right="right",(o=p=p||{}).GrowingRight="lr",o.GrowingLeft="rl",new Map([[u.Top,u.Bottom],[u.Bottom,u.Top],[u.Left,u.Right],[u.Right,u.Left]])),f=function(e,t,n,o){switch(t.lineAlign){case"center":var i=e;switch(n){case u.Bottom:i.css("transform","translateY(-50%)");break;case u.Left:i.css("transform","translateX(50%)");break;case u.Right:i.css("transform","translateX(-50%)");break}break;case"end":e.css(n,"".concat(100-o,"%"))}},d=function(e,t,n,o){var i,r,s=g.get(n);"auto"===t.line&&t.vertical?e.css(s,"0"):"auto"===t.line&&!t.vertical||(r=parseFloat(t.line),t.snapToLines&&(i=Number(t.line),r=100*(o.height/a*(i=i<0?a+i:i))/o.height),"end"!==t.lineAlign&&e.css(s,"".concat(r,"%")),f(e,t,n,r))};(o=n.VttUtils||(n.VttUtils={})).setVttCueBoxStyles=function(e,t){var n=e.vtt,o=e.getDomElement(),i=(a=o,n.region?(a.css("position","relative"),a.css("unicode-bidi","plaintext")):(a.css("position","absolute"),a.css("overflow-wrap","break-word"),a.css("overflow","hidden"),a.css("flex-flow","column")),a.css("display","inline-flex"),e.getText().split("<br />").length,o),r=n,s=t;switch(r.vertical){case"":i.css("writing-mode","horizontal-tb"),i.css(u.Bottom,"0"),d(i,r,u.Bottom,s);break;case p.GrowingRight:l(i,u.Right,r,s);break;case p.GrowingLeft:l(i,u.Left,r,s)}var a="middle"===n.align?"center":n.align,e=(o.css("text-align",a),n.size);""===n.vertical?(o.css("width","".concat(e,"%")),c(o,n,u.Left)):(o.css("height","".concat(e,"%")),c(o,n,u.Top))},o.setVttRegionStyles=function(e,t,n){var e=e.getDomElement(),o=n.width*t.viewportAnchorX/100-n.width*t.width/100*t.regionAnchorX/100,n=n.height*t.viewportAnchorY/100-28*t.lines*t.regionAnchorY/100;e.css("position","absolute"),e.css("overflow","hidden"),e.css("width","".concat(t.width,"%")),e.css(u.Left,"".concat(o,"px")),e.css(u.Right,"unset"),e.css(u.Top,"".concat(n,"px")),e.css(u.Bottom,"unset"),e.css("height","".concat(28*t.lines,"px"))}},{}]},{},[96])(96)});

})();
