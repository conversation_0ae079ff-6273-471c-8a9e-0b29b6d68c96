{"name": "bitmovin-player", "description": "The Bitmovin HTML5 Adaptive Streaming Player for MPEG-DASH and HLS", "version": "8.219.0", "homepage": "https://bitmovin.com", "keywords": ["html5 video player", "html5 video", "html5 video thumbnail", "video player with thumbnails", "html5 360 video player", "bitmovin player", "bitdash player", "dash player", "mpeg dash player", "bitmovin dash player", "mpeg dash example", "mpeg dash sample streams", "hls player", "hls html5", "HTML5 player skin", "video player skins", "html5 video browser support", "html5 web player"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "repository": {"type": "git", "url": ""}, "main": "./bitmovinplayer.js", "types": "./bitmovinplayer.d.ts", "private": false}