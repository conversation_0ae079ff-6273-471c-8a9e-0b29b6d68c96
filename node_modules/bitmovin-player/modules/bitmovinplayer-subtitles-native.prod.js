/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports["subtitles-native"]=e():(t.bitmovin=t.bitmovin||{},t.bitmovin.player=t.bitmovin.player||{},t.bitmovin.player["subtitles-native"]=e())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[826],{61442:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0}),e.NativeSubtitlesModuleDefinition=void 0;var i=a(86246),r=a(68661);e.NativeSubtitlesModuleDefinition={name:i.ModuleName.SubtitlesNative,module:function(){return{TextTrackController:r.TextTrackController}},dependencies:[i.ModuleName.Subtitles]},e.default=e.NativeSubtitlesModuleDefinition},68661:function(t,e,a){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,a=1,i=arguments.length;a<i;a++)for(var r in e=arguments[a])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},i.apply(this,arguments)},r=this&&this.__spreadArray||function(t,e,a){if(a||2===arguments.length)for(var i,r=0,n=e.length;r<n;r++)!i&&r in e||(i||(i=Array.prototype.slice.call(e,0,r)),i[r]=e[r]);return t.concat(i||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.TextTrackController=void 0;var n,o,s,l=a(4792),u=a(65622),c=a(33964),d=a(51243),h=a(55423),v=a(12291),b=a(83275),f=a(47122),T=a(86246),p=a(77362);!function(t){t.Showing="showing",t.Hidden="hidden",t.Disabled="disabled"}(n||(n={})),function(t){t.Subtitle="subtitles",t.ForcedSubtitle="forced",t.Caption="captions",t.Metadata="metadata"}(o||(o={})),function(t){t.ID3="org.id3",t.DATERANGE="com.apple.quicktime.HLS",t.EMSG="https://aomedia.org/emsg/ID3"}(s||(s={}));var k=function(t){var e=80/15,a=2.5,i=t.line-10,r=t.position-10;return i-=i%e,r-=r%a,{row:i=Math.round(i/e),column:r=Math.round(r/a)}},g=new RegExp("^(?:[a-z]+:)?//","i"),m=function(t){return"https:"===location.protocol?t.replace(g,"//"):t},y=function(t){var e={id:t.id,lang:t.lang,label:t.label,kind:t.kind,isFragmented:t.isFragmented,isSideloaded:t.isSideloaded,enabled:t.textTrack&&t.textTrack.mode!==n.Disabled,forced:t.kind===o.ForcedSubtitle};return t.url&&(e.url=t.url),e},x=function(){function t(t,e,a,r){var o=this;this.onFileLoaded=function(t,e,a){var i=t.body;i&&o.add(i.trim(),a)},this.onFileLoadError=function(t,e){o.removeSubtitle(o.getSubtitleIDFromUrl(t.url)),o.eventHandler.dispatchEvent(c.PlayerEvent.Warning,new l.PlayerWarning(u.WarningCode.NETWORK_COULD_NOT_LOAD_SUBTITLE))},this.onMetadataCueChangeHandler=function(t){var e=t.currentTarget.activeCues,a={frames:[]},r=[];for(var n in e)if(e[n].type===s.ID3&&void 0!==e[n].value){var l=e[n].value;v.ArrayHelper.isArrayBuffer(l.data)&&((l=i({},l)).data=v.ArrayHelper.getArray(new Uint8Array(l.data))),a[l.key]=l.data,a.frames.push(l),r.push(e[n])}a.frames.length&&o.eventHandler.dispatchEvent(c.PlayerEvent.Metadata,{metadataType:c.MetadataType.ID3,metadata:a}),r.forEach((function(e){return t.currentTarget.removeCue(e)}))},this.onCueChange=function(t){var e=t.target;if(e.activeCues&&e.activeCues.length>0){var a=p.TextTrackCueHelper.cueListToArray(e.activeCues),i=o.getIdFromTextTrack(e);p.TextTrackCueHelper.sort(a).forEach((function(t){return o.processCue(i,t)}))}},this.addTrack=function(t){var e=window.setTimeout((function(){return o.asyncAddTrack(t)}),0);o.timeoutsToClear.push(e)},this.getDefaultLabelForSubtitle=function(){return null},this.setForcedSubtitles=function(){var t;o.activeSubtitleId?o.activeForcedSubtitleId&&o.disableSubtitle(o.activeForcedSubtitleId):(o.activeForcedSubtitleId=null===(t=o.getForcedSubtitleForSelectedLanguage())||void 0===t?void 0:t.id,o.activeForcedSubtitleId&&o.enableSubtitle(o.activeForcedSubtitleId,!0))},this.audioChangeHandler=function(){o.disableSubtitle(o.activeForcedSubtitleId),o.setForcedSubtitles()},this.textTracksChangeHandler=function(){var t=o.getTextTrackAndSubtitleId(),e=t[0],a=t[1];if(o.shouldIgnoreTextTrackChange(a))return o.disableSubtitle(a),void o.setForcedSubtitles();a||(o.activeSubtitleId=void 0),o.setForcedSubtitles(),o.isNative&&o.syncTracksSelectedThroughNativeUI(),a&&e&&e.mode!==n.Hidden&&!o.recentlyAddedTracks.includes(e)&&o.enableSubtitle(a)},this.context=t,this.eventHandler=t.eventHandler,this.video=e,this.type=r,this.metadataTracks=[],this.availableTracks={},this.currentCCIdx=1,this.currentSubIdx=0,this.showNative=!1,this.timeoutsToClear=[],this.recentlyAddedTracks=[],this.activeCues=[],this.subtitleLoader=null,a.hasOwnProperty("style")&&a.style.hasOwnProperty("nativeSubtitles")?this.showNativeAlways=!!a.style.nativeSubtitles:this.showNativeAlways=!1,e.audioTracks&&e.audioTracks.addEventListener("change",this.audioChangeHandler),e.textTracks&&"function"==typeof e.textTracks.addEventListener&&(e.textTracks.addEventListener("addtrack",this.addTrack),e.textTracks.addEventListener("change",this.textTracksChangeHandler))}return Object.defineProperty(t.prototype,"isNative",{get:function(){return this.showNative||this.showNativeAlways},enumerable:!1,configurable:!0}),t.prototype.getAvailableSubtitles=function(){return Object.values(this.availableTracks||{}).filter((function(t){return t.kind!==o.ForcedSubtitle})).map(y)},t.prototype.enableSubtitle=function(t,e){void 0===e&&(e=!1);var a=this.availableTracks[t];return a?(e||(this.activeSubtitleId=t),a.textTrack&&(this.showNativeAlways||this.showNative?a.textTrack.mode=n.Showing:a.textTrack.mode=n.Hidden),a.enabled?Promise.resolve(!1):(a.enabled=!0,a.textTrack?a.loadPromise.then((function(){return!0}),(function(){return!1})):Promise.resolve(!0))):Promise.resolve(!1)},t.prototype.disableSubtitle=function(t){var e=this,a=this.availableTracks[t];return!!a&&(a.textTrack&&(a.textTrack.mode=n.Disabled),a.enabled=!1,r([],this.activeCues,!0).forEach((function(a){return a.subtitleId===t&&e.deactivateCue(t,a)})),!0)},t.prototype.toTextTrackCue=function(t){var e,a;return window.VTTCue?new window.VTTCue(t.start,t.end,null!==(e=t.html)&&void 0!==e?e:t.text):window.TextTrackCue?new TextTrackCue(t.start,t.end,null!==(a=t.html)&&void 0!==a?a:t.text):void 0},t.prototype.getSubtitleIDFromUrl=function(t){if(t&&this.availableTracks)for(var e in this.availableTracks)if(this.availableTracks.hasOwnProperty(e)&&this.availableTracks[e].url===t)return e},t.prototype.add=function(t,e){var a=this.getSubtitleIDFromUrl(e);if(a&&t){for(var r=f.ModuleManager.get(T.ModuleName.Subtitles).SubtitleParserFactory.createInstance(this.context,t).parse(t),n=0;n<r.length;n++)this.eventHandler.dispatchEvent(c.PlayerEvent.CueParsed,i(i({subtitleId:a},r[n]),{periodId:null})),this.availableTracks[a].textTrack.addCue(this.toTextTrackCue(r[n]));this.eventHandler.dispatchEvent(c.PlayerEvent.SubtitleAdded,{subtitle:y(this.availableTracks[a])})}},t.prototype.loadFile=function(t){if(t&&t.trim())return this.subtitleLoader=new d.DefaultContentLoader(this.context,{maxRetries:this.context.settings.MAX_RETRIES,retryDelay:this.context.settings.RETRY_DELAY,onSuccess:this.onFileLoaded,onFailure:this.onFileLoadError,requestType:h.HttpRequestType.MEDIA_SUBTITLES}),this.subtitleLoader.load(t).catch((function(t){}))},t.prototype.createCueEvents=function(t,e){var a=f.ModuleManager.get(T.ModuleName.Subtitles).HtmlTransformer,i=a.htmlToText,r=a.stripHtmlTags,n=(f.ModuleManager.get(T.ModuleName.SubtitlesWebVTT,!1)||{}).extractVttProperties,o={subtitleId:t,text:i(e.text),start:e.startTime,end:e.endTime},s=[o];if("subtitles"===e.track.kind)o.html=E(e.text),window.VTTCue&&(e instanceof VTTCue&&n?o.vtt=n(e):VTTCue);else if("captions"===e.track.kind){o.position=k(e);var l=r(o.text);o.position.column+l.length>p.CEA_608_MAX_LINE_LENGTH-1&&(s=p.TextTrackCueHelper.splitCea608Cue(o))}return s},t.prototype.processCue=function(t,e){var a=this;if(e){var i=Object.values(this.availableTracks).find((function(t){return t.textTrack===e.track}));if(C(this.showNativeAlways||this.showNative,e,i),!this.showNativeAlways&&!this.showNative){var r=this.createCueEvents(t,e);r.forEach((function(i){a.updateCue(i)?a.eventHandler.dispatchEvent(c.PlayerEvent.CueUpdate,i):a.activateCue(t,e,i)})),this.removeFloatingCues(t,r)}}},t.prototype.removeFloatingCues=function(t,e){var a=this,i=this.activeCues.filter((function(a){return!e.find((function(t){return a.start===t.start&&a.text===t.text&&a.subtitleId===t.subtitleId}))&&a.end===1/0&&a.subtitleId===t}));i.length>0&&i.forEach((function(e){a.deactivateCue(t,e)}))},t.prototype.updateCue=function(t){var e=this,a=this.activeCues.find((function(a){var i=a.start===t.start,r=a.text===t.text,n=!t.end||a.end===1/0||a.end===e.video.duration||a.end===t.end;return i&&r&&n}));return!!a&&(a.end=t.end,!0)},t.prototype.activateCue=function(t,e,a){var i=this;this.activeCues.push(a),e.onexit=function(){i.createCueEvents(t,e).forEach((function(e){return i.deactivateCue(t,e)}))},this.eventHandler.dispatchEvent(c.PlayerEvent.CueEnter,a)},t.prototype.deactivateCue=function(t,e){var a=this.activeCues.find((function(t){return t.start===e.start&&t.end===e.end&&t.text===e.text}));if(a){this.activeCues=this.activeCues.filter((function(t){return t!==a}));var i={subtitleId:t,text:a.text,start:a.start,end:a.end};a.position&&(i.position=a.position),this.eventHandler.dispatchEvent(c.PlayerEvent.CueExit,i)}},t.prototype.removeSubtitle=function(t){if(this.availableTracks.hasOwnProperty(t)){this.availableTracks[t].textTrack.mode!==n.Disabled&&this.disableSubtitle(t),this.availableTracks[t].textTrack.mode=n.Disabled;var e=this.availableTracks[t];delete this.availableTracks[t],this.eventHandler.dispatchEvent(c.PlayerEvent.SubtitleRemoved,{subtitle:y(e)})}},t.prototype.signalSourceChange=function(){this.video&&this.video.textTracks&&"function"==typeof this.video.textTracks.addEventListener&&this.video.textTracks.removeEventListener("addtrack",this.addTrack),this.removeAll()},t.prototype.removeAll=function(){for(var t in this.timeoutsToClear.forEach((function(t){return clearTimeout(t)})),this.timeoutsToClear=[],this.availableTracks)this.availableTracks.hasOwnProperty(t)&&(this.availableTracks[t].textTrack.mode=n.Disabled,this.availableTracks[t].textTrack.removeEventListener("cuechange",this.onCueChange));this.availableTracks={},this.activeCues=[],this.currentCCIdx=1,this.currentSubIdx=0},t.prototype.addSubtitle=function(t){if("function"!=typeof this.video.addTextTrack)return Promise.reject();this.availableTracks.hasOwnProperty(t.id)&&this.removeSubtitle(t.id);var e=t.url?b.URLHelper.toFullUrl(m(t.url.trim())):null,a={id:t.id,url:e,kind:t.kind,lang:t.lang,label:t.label,textTrack:null,isFragmented:!1,isSideloaded:!0,loadPromise:null};return this.availableTracks[t.id]=a,this.availableTracks[t.id].textTrack=this.video.addTextTrack(t.kind,t.label),this.availableTracks[t.id].textTrack.mode=n.Disabled,this.availableTracks[t.id].textTrack.addEventListener("cuechange",this.onCueChange),this.loadSubtitleFile(a)},t.prototype.loadSubtitleFile=function(t){var e=this;return t.url?t.loadPromise=this.loadFile(t.url).then((function(){if(!e.getAvailableSubtitles().find((function(e){return e.id===t.id})))throw"subtitle adding failed"})):(t.loadPromise=Promise.resolve(),this.eventHandler.dispatchEvent(c.PlayerEvent.SubtitleAdded,{subtitle:y(t)})),t.loadPromise},t.prototype.asyncAddTrack=function(t){for(var e,a,i=this,r=t.track,s=t.isFragmented&&r.kind===o.Subtitle||!1,l=0,u=Object.keys(this.availableTracks);l<u.length;l++){var d=u[l],h=this.availableTracks[d];if(h.textTrack===r&&h.isSideloaded)return}switch(r.kind){case o.Subtitle:e=r.id||"sub".concat(this.currentSubIdx),a=r.label||"Subtitles (".concat(this.currentSubIdx,")"),this.currentSubIdx++;break;case o.ForcedSubtitle:e=r.id||"fsub".concat(this.currentSubIdx),a=r.label||"Subtitles (".concat(this.currentSubIdx,")) (Forced)"),this.currentSubIdx++;break;case o.Caption:e="CC".concat(this.currentCCIdx),a=r.label||"Captions (CC ".concat(this.currentCCIdx,")"),this.currentCCIdx++;break;default:return void this.addMetadataTrack(r)}this.availableTracks[e]={id:e,url:null,kind:r.kind,lang:r.language||"unknown",label:a,textTrack:r,isFragmented:s,isSideloaded:!1,loadPromise:Promise.resolve()},(a=this.getLabelForSubtitle(this.availableTracks[e]))&&"string"==typeof a&&(this.availableTracks[e].label=a),this.recentlyAddedTracks.push(r),r.addEventListener("cuechange",this.onCueChange),r.kind!==o.ForcedSubtitle&&this.eventHandler.dispatchEvent(c.PlayerEvent.SubtitleAdded,{subtitle:y(this.availableTracks[e])}),r.mode=n.Disabled;var v=window.setTimeout((function(){i.recentlyAddedTracks=i.recentlyAddedTracks.filter((function(t){return t!==r})),Object.keys(i.availableTracks).map((function(t){return i.availableTracks[t]})).filter((function(t){return t.enabled&&Boolean(t.textTrack)&&t.textTrack.mode===n.Disabled})).forEach((function(t){i.isNative?t.textTrack.mode=n.Showing:t.textTrack.mode=n.Hidden}))}),0);this.timeoutsToClear.push(v),this.hasStoredAllTracks()&&this.setForcedSubtitles()},t.prototype.addMetadataTrack=function(t){this.context.settings.HLS_PARSE_NATIVE_METADATA&&(this.metadataTracks.push(t),t.mode=n.Hidden,t.addEventListener("cuechange",this.onMetadataCueChangeHandler))},t.prototype.hasStoredAllTracks=function(){var t=Object.keys(this.availableTracks).length;return this.video.textTracks.length===t},t.prototype.getLabelForSubtitle=function(t){var e={kind:t.kind,id:t.id,label:t.label,lang:t.lang};return this.getLabelingFunctionForSubtitle()(e)},t.prototype.getLabelingFunctionForSubtitle=function(){var t=this.context.sourceContext,e=t&&t.source&&t.source.labeling;return e&&e[this.type]&&e[this.type].subtitles?"function"!=typeof e[this.type].subtitles?this.getDefaultLabelForSubtitle:e[this.type].subtitles:this.getDefaultLabelForSubtitle},t.prototype.getIdFromTextTrack=function(t){var e;return(null===(e=Object.values(this.availableTracks).find((function(e){return e.textTrack===t})))||void 0===e?void 0:e.id)||null},t.prototype.getTextTrackAndSubtitleId=function(){for(var t=0,e=this.video.textTracks;t<e.length;t++){var a=e[t];if((a.mode===n.Showing||a.mode===n.Hidden)&&a.kind!==o.Metadata&&a.kind!==o.ForcedSubtitle)return[a,this.getIdFromTextTrack(a)]}return[void 0,void 0]},t.prototype.enableNative=function(){var t=this;Object.keys(this.availableTracks).map((function(e){return t.availableTracks[e]})).filter((function(t){return t.enabled&&Boolean(t.textTrack)})).forEach((function(t){return t.textTrack.mode=n.Showing})),this.showNative=!0},t.prototype.disableNative=function(){var t=this;Object.keys(this.availableTracks).map((function(e){return t.availableTracks[e]})).filter((function(t){return t.enabled&&Boolean(t.textTrack)})).forEach((function(t){return t.textTrack.mode=n.Hidden})),this.showNative=!1},t.prototype.seek=function(){var t=this;Object.keys(this.availableTracks).map((function(e){return t.availableTracks[e]})).filter((function(t){return t.enabled&&Boolean(t.textTrack)})).forEach((function(t){var e=t.textTrack.mode;t.textTrack.mode=n.Disabled,t.textTrack.mode=e}))},t.prototype.getForcedSubtitleForSelectedLanguage=function(){var t,e,a,i=this.video.audioTracks,r=f.ModuleManager.get(T.ModuleName.Subtitles).LanguageComparer.isEqualIsoLanguage;if(this.activeSubtitleId)a=null===(t=this.availableTracks[this.activeSubtitleId])||void 0===t?void 0:t.lang;else{if(!i)return;var n=v.ArrayHelper.toArray(i).find((function(t){return t.enabled}));a=null==n?void 0:n.language}a=null!==(e=null!=a?a:navigator.language)&&void 0!==e?e:navigator.userLanguage;var s=Object.values(this.availableTracks).filter((function(t){return t.kind===o.ForcedSubtitle})),l=s.find((function(t){return r(t.lang,a)}));return null!=l?l:s[0]},t.prototype.shouldIgnoreTextTrackChange=function(t){var e=this,a=Object.keys(this.availableTracks).some((function(a){return a===t&&e.availableTracks[a].enabled}));return t&&!this.isNative&&!a},t.prototype.syncTracksSelectedThroughNativeUI=function(){var t=this,e=function(t){var e=i({},t);return delete e.loadPromise,delete e.textTrack,i({},e)};this.getUnsyncedDisabledTracks().forEach((function(a){t.disableSubtitle(a.id),t.eventHandler.dispatchEvent(c.PlayerEvent.SubtitleDisabled,{subtitle:e(a)})})),this.getUnsyncedEnabledTracks().forEach((function(a){t.eventHandler.dispatchEvent(c.PlayerEvent.SubtitleEnabled,{subtitle:e(a)})}))},t.prototype.getUnsyncedDisabledTracks=function(){var t=this.video.textTracks;return Object.values(this.availableTracks).filter((function(t){return t.enabled})).filter((function(e){var a=S(t,e);return a&&a.mode===n.Disabled}))},t.prototype.getUnsyncedEnabledTracks=function(){var t=this.video.textTracks;return Object.values(this.availableTracks).filter((function(t){return!t.enabled})).filter((function(e){var a=S(t,e);return a&&a.mode!==n.Disabled}))},t.prototype.dispose=function(){var t;this.removeAll(),this.eventHandler=null,this.activeCues=null,this.availableTracks=null,this.metadataTracks=null,null===(t=this.video.audioTracks)||void 0===t||t.removeEventListener("change",this.audioChangeHandler),this.video&&this.video.textTracks&&"function"==typeof this.video.textTracks.addEventListener&&(this.video.textTracks.removeEventListener("addtrack",this.addTrack),this.video.textTracks.removeEventListener("change",this.textTracksChangeHandler)),this.subtitleLoader&&this.subtitleLoader.dispose(),this.subtitleLoader=null},t}();function S(t,e){for(var a=0,i=t;a<i.length;a++){var r=i[a];if(r===e.textTrack)return r}}function C(t,e,a){if(a){var i=f.ModuleManager.get(T.ModuleName.Subtitles).HtmlTransformer,r=i.stripAwayHtmlBreakPoints,n=i.replaceHtmlBreakPointsWithNewLine;t&&a.isSideloaded&&(e.text=n(e.text)),t||a.isSideloaded||(e.text=r(e.text))}}function E(t){var e=(0,f.ModuleManager.get(T.ModuleName.Subtitles).HtmlTransformer.prepareHtml)(t);return e.startsWith("<span>")?e:"<span>".concat(e,"</span>")}e.TextTrackController=x},77362:function(t,e){var a=this&&this.__assign||function(){return a=Object.assign||function(t){for(var e,a=1,i=arguments.length;a<i;a++)for(var r in e=arguments[a])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},a.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.TextTrackCueHelper=e.CEA_608_MAX_LINE_LENGTH=void 0,e.CEA_608_MAX_LINE_LENGTH=32;var i=function(){function t(){}return t.cueListToArray=function(t){for(var e=[],a=0;a<t.length;a++)e.push(t[a]);return e},t.sortEqualStartTimeByLineFn=function(t,e){if("line"in t&&"line"in e&&t.startTime===e.startTime){if(t.line<e.line)return-1;if(t.line>e.line)return 1}return 0},t.sortByStartTimeFn=function(t,e){return t.startTime<e.startTime?-1:t.startTime>e.startTime?1:0},t.sort=function(e){return e.sort(t.sortByStartTimeFn),e.sort(t.sortEqualStartTimeByLineFn),e},t.getTagIndexRanges=function(t){for(var e,a=RegExp("<[^>]*>","g"),i=[];e=a.exec(t);){var r=e[0];i.push({start:e.index,end:e.index+r.length-1})}return i},t.splitCea608Cue=function(a){for(var i=e.CEA_608_MAX_LINE_LENGTH-a.position.column,r=a.text,n=t.getTagIndexRanges(r),o=[],s=0,l=0,u=!1,c=0,d=0;d<r.length;){if(d===r.length-1){o.push(r.substring(s));break}if(n.length>0&&d===n[0].start&&(u=!0),u);else if(l++," "===r[d]&&(c=d),l>=i&&c>s){d=c+1;var h=r.substring(s,d);h&&o.push(h),s=d,l=1}u&&n.length>0&&d===n[0].end&&(u=!1,n.shift()),d++}var v=t.createCueEvents(o,a);return t.adjustCea608CaptionPositioning(v),v},t.createCueEvents=function(t,e){var i=t.length-1;return t.map((function(t){var r=a({},e);return r.text=t,r.position={column:r.position.column,row:r.position.row-i},i--,r}))},t.adjustCea608CaptionPositioning=function(t){var e=14,a=Math.max.apply(Math,t.map((function(t){return t.position.row}))),i=Math.min.apply(Math,t.map((function(t){return t.position.row})));if(a>e){var r=e-a;t.forEach((function(t){return t.position.row+=r}))}if(i<0){var n=0-i;t.forEach((function(t){return t.position.row+=n}))}},t}();e.TextTrackCueHelper=i}},function(t){return function(e){return t(t.s=e)}(61442)}])}));
})();
