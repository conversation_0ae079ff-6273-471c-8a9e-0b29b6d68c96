/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.subtitles=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player.subtitles=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[482],{8679:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.SubtitleType=void 0,function(e){e[e.EXTERNAL_FILE=0]="EXTERNAL_FILE",e[e.FRAGMENTED=1]="FRAGMENTED",e[e.IN_STREAM=2]="IN_STREAM"}(n||(t.SubtitleType=n={}))},10842:function(e,t,n){var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SubtitleService=void 0;var r=n(18665),o=n(60997),a=n(62510),s=n(76650),u=n(3464),l=n(79814),c=n(54838),d=n(91520),f=n(16368),p=n(57042),v=n(29495),h=n(59272),g=n(68466),b=n(45684),S=n(14200),m=n(8679),y=n(24855),M=function(){function e(e){var t=this;this.context=e,this.getTransmuxer=function(){return t.transmuxer},this.onDiscontinuitySequenceNumberChanged=function(){t.resetCaptionExtractor()},this.onSeek=function(e){var n;t.resetCaptionExtractor(),t.timeBeforeSeek=e.position;var i=null!==(n=t.timeBeforeSeek)&&void 0!==n?n:0,r=E(e);Object.values(t.subtitleHandlers).forEach((function(e){return e.onSeek(i,r)})),t.forcedSubtitleController.onSeek(i,r)},this.onSeeked=function(){var e,n=null!==(e=t.timeBeforeSeek)&&void 0!==e?e:0,i=t.context.internalPlayer.getCurrentTime();Object.values(t.subtitleHandlers).forEach((function(e){return e.onSeeked(n,i)})),t.forcedSubtitleController.onSeeked(n,i)},this.handlePeriodUpdate=function(e,n){t.context.logger.debug("Switching periods in subtitleService");var i=t.manifestService.findPeriod(e);i?(t.adjustSubtitleHandlersForNewPeriod(e,n),t.setupControllersForNextPeriod(i),t.reactivateAfterSwitched()):t.context.logger.log("Can not find period with id = ".concat(e," in Manifest. Skipping period update"))},this.reset=function(){Object.keys(t.subtitleHandlers).forEach((function(e){return t.subtitleHandlers[e].dispose()})),t.subtitleHandlers={},t.cea608Extractor&&(t.cea608Extractor.dispose(),t.cea608Extractor=void 0),t.discontinuitySequenceNumberTracker&&t.discontinuitySequenceNumberTracker.reset()},this.subtitleHandlers={},this.eventHandler=e.eventHandler,this.manifestService=e.serviceManager.get(r.ServiceName.ManifestService,e.sourceContext.sourceIdentifier),this.enabledSubtitlesTracker=new v.EnabledSubtitlesTracker,this.forcedSubtitleController=new h.ForcedSubtitleController,this.eventHandler.on(a.PlayerEvent.Seek,this.onSeek),this.eventHandler.on(a.PlayerEvent.TimeShift,this.onSeek),this.eventHandler.on(a.PlayerEvent.Seeked,this.onSeeked),this.eventHandler.on(a.PlayerEvent.TimeShifted,this.onSeeked),this.eventHandler.on(a.PlayerEvent.SourceUnloaded,this.reset),this.subscribeToPeriodSwitch()}return e.prototype.shouldReactToPeriodUpdate=function(e,t){var n=e!==t,i=this.manifestService.isHlsManifest()&&!(t||!e);return n||i},e.prototype.subscribeToPeriodSwitch=function(){var e=this,t=this.context.serviceManager.get(r.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier);this.unsubscribeFromPeriodSwitch=(0,u.subscribe)(t)(d.ModuleManager.get(f.ModuleName.EngineBitmovin).selectors.getPlayingPeriodId,this.handlePeriodUpdate,(function(t,n){return e.shouldReactToPeriodUpdate(t,n)}))},e.prototype.setupControllersForNextPeriod=function(e){var t=this,n=d.ModuleManager.get(f.ModuleName.EngineBitmovin).getMimeTypeForAdaptationSet;e.AdaptationSet.forEach((function(e){var i=n(e);(0,y.isSubtitleTrack)(i,t.manifestService.isHlsManifest())&&t.setupControllerForSubtitleAdaptationSet(e,i)}))},e.prototype.setupControllerForSubtitleAdaptationSet=function(e,t){if((0,y.isFragmentedAdaptationSet)(e))return this.context.store.dispatch((0,s.initializeMetricsForMimeType)(t,this.context.settings)),void this.addFragmentedSubtitleTrack(e,t);this.addExternalSubtitleTrack(e,t)},e.prototype.addFragmentedSubtitleTrack=function(e,t){var n=this.context.sourceContext.sourceIdentifier,i=this.context.serviceManager.get(r.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier);if(i){var o={mimeType:t,codec:d.ModuleManager.get(f.ModuleName.EngineBitmovin).getCodecsFromAdaptationSet(e),isHls:this.manifestService.isHlsManifest(),manifestLoader:this.context.serviceManager.get(r.ServiceName.ManifestLoadingService,n),periodId:e._internalId.periodId,sourceStore:i};this.addFragmentSubtitle(o,e)}},e.prototype.addExternalSubtitleTrack=function(e,t){if(d.ModuleManager.has(f.ModuleName.DASH)){var n=new(d.ModuleManager.get(f.ModuleName.DASH).SegmentTemplateMPDHandler)(this.context),i=this.manifestService.toSubtitleTrack(e,t);n.setAdaptationSetId(e._internalId),i.url=n.getSubtitleUrl().toString(),this.addExternalSubtitle(i,e)}},e.prototype.setTransmuxer=function(e){this.transmuxer=e},e.prototype.hasSubtitle=function(e){return Boolean(this.subtitleHandlers[e])},e.prototype.enableSubtitle=function(e){var t=this,n=this.subtitleHandlers[e];return n&&!n.isActive()?n.activate().then((function(){n.refresh(),t.forcedSubtitleController.deactivate()})).then((function(){return t.enabledSubtitlesTracker.addSubtitle(n.getSubtitle()),!0})).catch((function(e){return t.context.logger.debug("Error during subtitle activation",e),!1})):Promise.resolve(!1)},e.prototype.addExternalSubtitle=function(e,t){if(void 0===t&&(t=null),e.id||(e.id=e.url),this.getExistingExternalSubtitleHandler(e,t))return e;var n=new b.ExternalFileSubtitleHandler(this.context,e,t);return this.reactivateHandler(n),e},e.prototype.getExistingExternalSubtitleHandler=function(e,t){var n,i,r=this.subtitleHandlers[e.id],o=null!==(n=null==r?void 0:r.periodId)&&void 0!==n?n:null,a=null!==(i=null==t?void 0:t._internalId.periodId)&&void 0!==i?i:null;return r&&o===a?r:this.forcedSubtitleController.getForcedSubtitleHandler((function(t){var n,i=t.getSubtitle().id===e.id,r=(null!==(n=t.periodId)&&void 0!==n?n:null)===a;return i&&r}))},e.prototype.reactivateHandler=function(e){var t=e.getSubtitle(),n=this.context.config.playback.isForcedSubtitle;if("function"==typeof n?(0,c.safeUserCallback)((function(){return n(t)}),this.context.logger):t.forced)this.forcedSubtitleController.addSubtitleHandler(e);else{var i=!1,r=t.id;this.subtitleHandlers[r]&&(i=this.subtitleHandlers[r].isActive(),this.removeSubtitleHandler(r)),this.addSubtitleHandler(e),i&&e.activate()}},e.prototype.addSubtitleHandler=function(e){var t=e.getSubtitle();this.applyLabelFunctionForSubtitle(t),this.subtitleHandlers[t.id]=e,this.signalNewSubtitle(t)},e.prototype.applyLabelFunctionForSubtitle=function(e){var t,n,i=this.context.sourceContext.source.labeling,r=this.manifestService.isHlsManifest()?null===(t=null==i?void 0:i.hls)||void 0===t?void 0:t.subtitles:null===(n=null==i?void 0:i.dash)||void 0===n?void 0:n.subtitles;"function"==typeof r&&(e.label=(0,c.safeUserCallback)((function(){return r(e)}),this.context.logger))},e.prototype.addFragmentSubtitle=function(e,t){if(!d.ModuleManager.has(f.ModuleName.EngineBitmovin))return this.context.logger.warn("Unable to add fragmented subtitles because MSE Module is missing"),null;var n=this.manifestService.toSubtitleTrack(t,e.mimeType);n.isFragmented=!0,n.url=null;var i=new S.FragmentedSubtitleHandler(this.context,e,n,this.getTransmuxer);return this.reactivateHandler(i),n},e.prototype.setupCea608CaptionExtractor=function(e){var t;if(!d.ModuleManager.has(f.ModuleName.SubtitlesCEA608))return this.eventHandler.dispatchEvent(a.PlayerEvent.Warning,new p.PlayerModuleMissingWarning(f.ModuleName.SubtitlesCEA608,"The ".concat(f.ModuleName.SubtitlesCEA608," module is required to parse CEA captions."))),this.context.logger.log("CEA Caption parsing is disabled, because the Cea680 module is not loaded"),null;if(!d.ModuleManager.has(f.ModuleName.ContainerMP4))return this.eventHandler.dispatchEvent(a.PlayerEvent.Warning,new p.PlayerModuleMissingWarning(f.ModuleName.ContainerMP4,"The ".concat(f.ModuleName.ContainerMP4," module is required to parse CEA captions."))),this.context.logger.log("CEA Caption parsing is disabled, because the ContainerMP4 module is not loaded"),null;this.cea608Extractor&&this.cea608Extractor.dispose();var n=d.ModuleManager.get(f.ModuleName.ContainerMP4),i=d.ModuleManager.get(f.ModuleName.SubtitlesCEA608).Cea608Extractor;return this.cea608Extractor=new i(e,n,null===(t=this.context.config.tweaks)||void 0===t?void 0:t.parse_cea_708_caption),this.cea608Extractor},e.prototype.resetCaptionExtractor=function(){this.cea608Extractor&&(this.context.logger.debug("Resetting CEA caption extractor..."),this.cea608Extractor.reset())},e.prototype.createDiscontinuitySequenceNumberTracker=function(){var e=d.ModuleManager.get(f.ModuleName.HLS);this.discontinuitySequenceNumberTracker=new e.DiscontinuitySequenceNumberTracker(l.MimeTypeHelper.isVideo),this.discontinuitySequenceNumberTracker.subscribe(this.onDiscontinuitySequenceNumberChanged)},e.prototype.trackDiscontinuitySequenceNumber=function(e){this.discontinuitySequenceNumberTracker||this.createDiscontinuitySequenceNumberTracker(),this.discontinuitySequenceNumberTracker.trackSegment(e)},e.prototype.shouldDiscontinuitySequenceNumberBeTracked=function(){return this.manifestService.getManifest()&&this.manifestService.isHlsManifest()&&d.ModuleManager.has(f.ModuleName.HLS)},e.prototype.handleClosedCaptions=function(e,t,n,i,r){return void 0===r&&(r=""),!i&&l.MimeTypeHelper.isVideo(t.getMimeType())&&r.toLowerCase().includes("avc")?this.extractCaptionsFromSegment(t,e.getTimestampOffset()):(this.addClosedCaptions(n,t.getPeriodId()),Promise.resolve())},e.prototype.extractCaptionsFromSegment=function(e,t){var n=this;return this.cea608Extractor?(this.shouldDiscontinuitySequenceNumberBeTracked()&&this.trackDiscontinuitySequenceNumber(e),this.cea608Extractor.extract(e).then((function(i){T(i,t),i.length>0&&(n.context.logger.insane("Extracted Closed Captions from fMP4: "+JSON.stringify(i)),n.addClosedCaptions(i,e.getPeriodId()))})).catch((function(e){var t;e instanceof p.PlayerModuleMissingWarning?(null===(t=n.cea608Extractor)||void 0===t||t.dispose(),n.cea608Extractor=void 0,n.eventHandler.dispatchEvent(a.PlayerEvent.Warning,e)):n.context.logger.debug("Error parsing CEA-608 Captions from fMP4: "+e)}))):Promise.resolve()},e.prototype.addClosedCaptions=function(e,t){if(e&&Array.isArray(e)&&0!==e.length)for(var n=this.manifestService.getClosedCaptionLabels(t),i=function(e){var i=e.channel;if(!r.subtitleHandlers[i]){var o=n.find((function(e){return e.id===i}))||{id:i,kind:"captions",lang:"unknown",label:"Captions (".concat(e.channel,")")};r.addSubtitleHandler(new g.Cea608Handler(r.context,o))}r.subtitleHandlers[i].addCaption(e,t)},r=this,o=0,a=e;o<a.length;o++){i(a[o])}},e.prototype.signalNewSubtitle=function(e){this.eventHandler.dispatchEvent(a.PlayerEvent.SubtitleAdded,{subtitle:i({},e)})},e.prototype.disableSubtitle=function(e){var t=this,n=this.subtitleHandlers[e];return n?n.deactivate().then((function(){return t.enabledSubtitlesTracker.removeSubtitle(e),0===t.getActiveSubtitles().length&&t.forcedSubtitleController.activate().catch((function(){})),!0})).catch((function(){return!1})):Promise.resolve(!1)},e.prototype.disableAllSubtitles=function(){var e=this;this.getActiveSubtitles().forEach((function(t){return e.disableSubtitle(t.id)}))},e.prototype.removeSubtitle=function(e){this.removeSubtitleHandler(e),this.enabledSubtitlesTracker.removeSubtitle(e)},e.prototype.getActiveSubtitles=function(){var e=this;return Object.keys(this.subtitleHandlers).filter((function(t){return e.subtitleHandlers[t].isActive()})).map((function(t){return e.subtitleHandlers[t].getSubtitle()}))},e.prototype.hasSubtitles=function(){return Object.keys(this.subtitleHandlers).length>0},e.prototype.getAvailableSubtitles=function(e){return Object.values(this.subtitleHandlers).filter((function(t){var n=t.periodId;return!n||e===n})).map((function(e){return e.getSubtitle()}))},e.prototype.enableForcedSubtitle=function(e){this.forcedSubtitleController.activate(e).catch((function(){}))},e.prototype.shouldRemoveSubtitleHandler=function(e,t,n){var i=e.getSubtitleType()===m.SubtitleType.FRAGMENTED&&e.periodId===n,r=e.getSubtitleType()===m.SubtitleType.EXTERNAL_FILE&&e.periodId&&e.periodId!==t;return i||r},e.prototype.adjustSubtitleHandlersForNewPeriod=function(e,t){var n=this;Object.keys(this.subtitleHandlers).forEach((function(i){var r=n.subtitleHandlers[i];n.shouldRemoveSubtitleHandler(r,e,t)&&n.removeSubtitleHandler(i)}))},e.prototype.reactivateAfterSwitched=function(){var e=this;Object.keys(this.subtitleHandlers).forEach((function(t){var n,i=e.subtitleHandlers[t];if(!i.isActive()&&e.enabledSubtitlesTracker.shouldEnableSubtitle(i.getSubtitle(),e.subtitleHandlers)){var r={subtitle:i.getSubtitle()};null===(n=e.eventHandler)||void 0===n||n.dispatchEvent(a.PlayerEvent.SubtitleEnable,r),i.activate().then((function(){var t;null===(t=e.eventHandler)||void 0===t||t.dispatchEvent(a.PlayerEvent.SubtitleEnabled,r)}))}}))},e.prototype.removeSubtitleHandler=function(e){if(this.subtitleHandlers[e]){var t=this.subtitleHandlers[e].getSubtitle();this.subtitleHandlers[e].dispose(),delete this.subtitleHandlers[e],this.eventHandler&&this.eventHandler.dispatchEvent(a.PlayerEvent.SubtitleRemoved,{subtitle:t})}},e.prototype.clearBuffersUntil=function(e){var t=this;Object.keys(this.subtitleHandlers).forEach((function(n){return t.subtitleHandlers[n].clearBuffersUntil(e)}))},e.prototype.dispose=function(){this.reset(),this.enabledSubtitlesTracker=(0,o.dispose)(this.enabledSubtitlesTracker),this.forcedSubtitleController=(0,o.dispose)(this.forcedSubtitleController),this.discontinuitySequenceNumberTracker=(0,o.dispose)(this.discontinuitySequenceNumberTracker),this.transmuxer=void 0,this.manifestService=null,this.eventHandler.off(a.PlayerEvent.Seek,this.onSeek),this.eventHandler.off(a.PlayerEvent.TimeShift,this.onSeek),this.eventHandler.off(a.PlayerEvent.Seeked,this.onSeeked),this.eventHandler.off(a.PlayerEvent.TimeShifted,this.onSeeked),this.eventHandler.off(a.PlayerEvent.SourceUnloaded,this.reset),this.unsubscribeFromPeriodSwitch(),this.subtitleHandlers={},this.eventHandler=null},e}();function T(e,t){e.forEach((function(e){e.start-=t,e.end-=t}))}function E(e){var t=function(e){return"seekTarget"in e};return t(e)?e.seekTarget:e.target}t.SubtitleService=M},14200:function(e,t,n){var i=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.FragmentedSubtitleHandler=void 0;var o=n(92712),a=n(18665),s=n(62510),u=n(33696),l=n(58975),c=n(33669),d=n(3464),f=n(42283),p=n(3941),v=n(79814),h=n(34586),g=n(70016),b=n(91520),S=n(16368),m=n(66774),y=n(81253),M=n(27338),T=n(49625),E=n(8679),P=-1,C=function(e){function t(t,n,i,r){var o=e.call(this,t,i,n.periodId)||this;o.onSegmentDataAvailable=function(e){var t,n=I(o.context.serviceManager.get(a.ServiceName.SourceStoreService,o.context.sourceContext.sourceIdentifier)),i=null===(t=e.getSegmentInfo())||void 0===t?void 0:t.discontinuitySequenceNumber;return void 0!==i&&(n=n.then((function(){return o.waitForTransmuxerReady(i)})).then((function(){return o.awaitPtoKnownForHlsDiscoSequence(i)})).then((function(){return o.isActive()})).catch((function(){return!1}))),n.then((function(t){o.removeLoadingRange(e),t&&o.processSegment(e)}))},o.onTimeUpdate=function(e){var t;if(e&&void 0!==e.time){var n=e.time;o.clearBuffersUntil(n);var i=(null===(t=o.streamTimeService)||void 0===t?void 0:t.getTimeForNextSegment(o.trackIdentifier))||n,r=i-n,a=L(o.context.internalPlayer);isNaN(i)||r>=a||o.segmentController.hasNext(i)&&(o.segmentController.getMPDHandler().resolvePendingSegmentInfoRequests(),o.segmentController.getNext(i))}},o.onMpdUpdated=function(){o.segmentController.updateMpd()},o.eventHandler=t.eventHandler,o.sourceStoreService=n.sourceStore,o.streamTimeService=t.serviceManager.get(a.ServiceName.StreamTimeService),o.manifestUpdateScheduler=t.serviceManager.get(a.ServiceName.ManifestUpdateSchedulingService,t.sourceContext.sourceIdentifier),o.getTransmuxer=r,o.isHls=n.isHls,o.bufferSettings=t.bufferSettings;var s=b.ModuleManager.get(S.ModuleName.EngineBitmovin).SegmentController;return o.segmentController=new s(t,o.onSegmentDataAvailable,n.mimeType,n.codec,n.isHls,n.manifestLoader,n.periodId,n.sourceStore),o.segmentController.setCurrentLangObj(i),o.trackIdentifier="".concat(n.mimeType,"/").concat(i.id),o.HlsModule=b.ModuleManager.get(S.ModuleName.HLS,!1),o.subscribeToManifestChanges(),o}return i(t,e),t.prototype.subscribeToManifestChanges=function(){!this.unsubscribeFromManifestStore&&this.sourceStoreService&&(this.unsubscribeFromManifestStore=(0,d.subscribe)(this.sourceStoreService)((function(e){return null==e?void 0:e.manifest}),this.onMpdUpdated,(function(e,t){return Boolean((null==e?void 0:e.isInitialized)&&!(null==t?void 0:t.isInitialized))})))},t.prototype.unsubscribeFromManifestChanges=function(){this.unsubscribeFromManifestStore&&(this.unsubscribeFromManifestStore(),this.unsubscribeFromManifestStore=void 0)},t.prototype.extractSubtitlesFromSegment=function(e){return x(e)?(0,y.parsePlainTextSubtitles)(e):F(e)?(0,m.parseMP4WrappedSubtitles)(e,this.context.logger):e.getMetadata().subtitles},t.prototype.addMetadataToSegment=function(e){var t=e.getMetadata();t.subtitles=this.extractSubtitlesFromSegment(e),e.setMetadata(t)},t.prototype.addSegmentPlaybackTime=function(e){var t,n=null!==(t=e.getPlaybackTime())&&void 0!==t?t:e.getSegmentInfo().startTime;e.setPlaybackTime(n)},t.prototype.waitForTransmuxerReady=function(e){var t=this.getTransmuxer();return this.isHls&&t?(void 0!==e&&this.areSubtitleDiscontinuitiesAligned()||(e=-1),t.waitForInitializationForDiscontinuity(e)):Promise.resolve()},t.prototype.awaitPtoKnownForHlsDiscoSequence=function(e){if(!this.areSubtitleDiscontinuitiesAligned())return Promise.resolve();if(void 0!==this.getPtoForHlsDiscoFromStore(e))return Promise.resolve();if(!this.HlsModule)return Promise.reject(new Error("HLS Module missing"));var t=this.HlsModule.selectors,n=t.getHlsState,i=t.getPresentationTimeOffset;return(0,d.waitForStateChangeThen)(this.sourceStoreService)((function(t){return i(n(t),String(e))}),(function(e){return void 0!==e})).then()},t.prototype.getSubtitleSegmentInfoFromStore=function(e){var t,n,i=null===(t=this.context.serviceManager.get(a.ServiceName.SourceStoreService,e))||void 0===t?void 0:t.getState(),r=null===(n=this.segmentController.getCurrentAdaptationSet())||void 0===n?void 0:n.Representation[0];return r&&i?(0,b.ModuleManager.get(S.ModuleName.EngineBitmovin).selectors.getSegmentInfos)(i,r._internalId.key()):[]},t.prototype.getSubtitleSegmentsFromMpd=function(e,t){var n,i,r,o,s=b.ModuleManager.get(S.ModuleName.EngineBitmovin).AdaptationSetId,u=this.context.serviceManager.get(a.ServiceName.ManifestService,e),l=new s(p.DEFAULT_PERIOD_ID,t),c=null==u?void 0:u.getAdaptationSet(l);return null!==(o=null===(r=null===(i=null===(n=null==c?void 0:c.Representation[0])||void 0===n?void 0:n.SegmentList)||void 0===i?void 0:i[0])||void 0===r?void 0:r.SegmentURL)&&void 0!==o?o:[]},t.prototype.getSegmentDiscoData=function(){var e=this.context.sourceContext.sourceIdentifier;return this.context.settings.ENABLE_SEGMENT_INFO_PROVIDER_FROM_STORE?this.getSubtitleSegmentInfoFromStore(e):this.getSubtitleSegmentsFromMpd(e,this.getSubtitle().id)},t.prototype.getSubtitleDiscoStartTimes=function(){var e=this.getSegmentDiscoData();if(e&&0!==e.length)return e.reduce((function(e,t){var n=j(t)?t.startTime:t._playbackTime,i=j(t)?t.discontinuitySequenceNumber:t._discontinuitySequenceNumber;return(0,g.isNumber)(n)&&(0,g.isNumber)(i)&&void 0===e[i]&&(e[i]=n),e}),{})},t.prototype.areSubtitleDiscontinuitiesAligned=function(){var e,t=b.ModuleManager.get(S.ModuleName.HLS,!1),n=null===(e=this.sourceStoreService)||void 0===e?void 0:e.getState();if(!t||!n)return!1;var i=t.selectors,r=i.getHlsState,o=i.getMergedDiscontinuityTimings,a=r(n);if(!a)return!1;var s=this.getSubtitleDiscoStartTimes();if(!s)return!1;var u=o(a),l=Object.keys(s).find((function(e){return!u[e]||Math.abs(u[e].startTime-s[e])>1})),c=Object.keys(u).find((function(e){return null==s[e]}));return null==l&&null==c},t.prototype.processSegment=function(e){this.updateBufferLevel(e),this.addMetadataToSegment(e),this.addSegmentPlaybackTime(e),this.addCues(e),this.isActive()&&this.timeline.show()},t.prototype.getPtoForHlsDiscoFromStore=function(e){var t;if(this.HlsModule){var n=this.HlsModule.selectors,i=n.getHlsState,r=n.getPresentationTimeOffset,o=null===(t=this.sourceStoreService)||void 0===t?void 0:t.getState(),a=o?i(o):void 0;if(a)return r(a,String(e))}},t.prototype.removeLoadingRange=function(e){var t,n=e.getPlaybackTimeRange();if(n){var i=b.ModuleManager.get(S.ModuleName.EngineBitmovin),r=i.actions.removeStreamTimeRange,o=i.mseModuleTypes.streamTimeRangeType;null===(t=this.sourceStoreService)||void 0===t||t.dispatch(r(this.trackIdentifier,n,o.Loading))}},t.prototype.updateBufferLevel=function(e){var t,n=e.getPlaybackTimeRange();if(!e.isInit()&&n){var i=b.ModuleManager.get(S.ModuleName.EngineBitmovin).actions.addLoadedRange,o=r(r({},n),{bufferBlockId:P});null===(t=this.sourceStoreService)||void 0===t||t.dispatch(i(this.trackIdentifier,o))}},t.prototype.getTimestampOffset=function(e){var t,n,i=-this.segmentController.getMPDHandler().getTimestampOffset();if(this.isHls){var r=e.getSegmentInfo().discontinuitySequenceNumber,o=this.getPtoForHlsDiscoFromStore(r);void 0!==o?i=-o:this.context.logger.warn("FragmentedSubtitleHandler: Failed to get PTO for disco sequence ".concat(r," from store, falling back to MPDHandler value of ").concat(-i),e.getSegmentInfo())}return i+=null!==(n=null===(t=this.getTransmuxer())||void 0===t?void 0:t.getTimestampMappingOffset(e))&&void 0!==n?n:0},t.prototype.addCues=function(e){var n=e.getMetadata().subtitles;if(Array.isArray(n)&&0!==n.length){var i=x(e),r=this.getTimestampOffset(e),o=i||_(e)||k(e)?this.parseCues(n,r):n,a=i?this.applyVttOffset(o):o,s=t.ensureCueInSegmentRange(a,e.getPlaybackTime(),e.getDuration(),r,this.isTsContainer());this.fireOnCueParsedEvents(s,this.periodId),this.timeline.addCues(s)}},t.prototype.parseCues=function(e,t){var n=this;return e.flatMap((function(e){return n.parse(e.text,t)}))},t.ensureCueInSegmentRange=function(e,t,n,i,o){if(void 0===o&&(o=!1),!t&&0!==t||t<0||!n)return e;if(e.length>0){var a=n+i;if(e.every((function(e){return e.end<=a})))return e.map((function(e){return o&&null!=e.offset?r(r({},e),{start:e.start+t-i-e.offset,end:e.end+t-i-e.offset}):r(r({},e),{start:e.start+t-i,end:e.end+t-i})}))}return e},t.prototype.parse=function(e,t){var n=M.SubtitleParserFactory.createInstance(this.context,e);return t?n.parse(e,t):n.parse(e)},t.prototype.activate=function(){var t=this;return e.prototype.activate.call(this),this.prepareActivation().then((function(){if(t.isActive()){var e=O(t.context.renderer,t.context.store);t.segmentController.activateSubtitleSegmentController(e),t.segmentController.getNext(e),t.eventHandler.on(s.PlayerEvent.TimeChanged,t.onTimeUpdate)}}))},t.prototype.prepareActivation=function(){var e=this.segmentController.getCurrentAdaptationSet().Representation[0],t=e._internalId,n=e._mimeType,i=b.ModuleManager.get(S.ModuleName.EngineBitmovin).actions,r=i.setRepresentationIdAction,o=i.setMediaTypeAction;return this.sourceStoreService.dispatch(r(t)),this.sourceStoreService.dispatch(o(t,v.MimeTypeHelper.getMediaType(n))),this.manifestUpdateScheduler.waitForRepUpdate(t)},t.prototype.deactivate=function(){var t;this.timeline.removeCues();var n=b.ModuleManager.get(S.ModuleName.EngineBitmovin).actions,i=n.resetStreamTimeline,r=n.resetLoadedRanges;this.eventHandler.off(s.PlayerEvent.TimeChanged,this.onTimeUpdate),this.sourceStoreService.dispatch(i(this.trackIdentifier)),this.sourceStoreService.dispatch(r(this.trackIdentifier));var o=null===(t=this.segmentController.getCurrentAdaptationSet())||void 0===t?void 0:t._internalId;return o&&this.sourceStoreService.dispatch(b.ModuleManager.get(S.ModuleName.EngineBitmovin).actions.removeActiveTrackAction(o)),e.prototype.deactivate.call(this)},t.prototype.getSubtitleType=function(){return E.SubtitleType.FRAGMENTED},t.prototype.clearBuffersUntil=function(t){e.prototype.clearBuffersUntil.call(this,t);var n=b.ModuleManager.get(S.ModuleName.EngineBitmovin).actions.removeLoadedRange;this.sourceStoreService.dispatch(n(this.trackIdentifier,{start:0,end:t,bufferBlockId:P}))},t.prototype.isInBufferSeek=function(e){var t,n=null===(t=this.sourceStoreService)||void 0===t?void 0:t.getState(),i=(0,c.getBufferState)(n),r=i?(0,c.getLoadedRangesForMimeType)(i,this.trackIdentifier):[];return(0,o.isInTimeRanges)(r,e)},t.prototype.onSeek=function(e,t){this.isActive()&&(this.isInBufferSeek(t)||this.segmentController.cancelLoading())},t.prototype.onSeeked=function(e,t){if(this.isActive()&&!this.isInBufferSeek(t)){this.timeline.removeCues();var n=b.ModuleManager.get(S.ModuleName.EngineBitmovin).actions.resetLoadedRanges;this.sourceStoreService.dispatch(n(this.trackIdentifier)),this.segmentController.seekTo(t),this.segmentController.getNext(t)}},t.prototype.dispose=function(){e.prototype.dispose.call(this),this.unsubscribeFromManifestChanges(),this.segmentController.dispose()},t}(T.AbstractSubtitleHandler);function H(e){return h.TextSegmentAnalyzer.isPlainTextPayload(e.getData())}function x(e){var t=e.getCodec();return("wvtt"===t||"vtt"===t||""===t&&e.getMimeType().includes("vtt"))&&H(e)}function k(e){return"application/mp4"===e.getMimeType()&&"wvtt"===e.getCodec()&&!H(e)}function _(e){return"stpp"===f.CodecStringHelper.extractCodec(e.getCodec())}function F(e){return v.MimeTypeHelper.isApplication(e.getMimeType())&&!H(e)}function O(e,t){var n=(0,l.getPlayerState)(t.getState());return(0,l.getIsSeekingOrTimeshifting)(n)?n.targetPlaybackTime:e.getCurrentTime(!0)}function N(e,t){var n=b.ModuleManager.get(S.ModuleName.EngineBitmovin,!1);if(void 0!==t&&void 0!==n)return n.selectors.getContainerFormat(t,e)}function A(e,t,n){var i=N(n,t);return void 0!==i?Promise.resolve(i):(0,d.waitForStateChangeThen)(e)((function(e){return N(n,e)}),(function(e){return void 0!==e}))}function w(e){var t,n=null!==(t=e.activeTracks)&&void 0!==t?t:{};return Object.keys(n).flatMap((function(e){var t;return null!==(t=n[e].mediaTypes)&&void 0!==t?t:[]})).filter((function(e){return e===u.MediaType.Video||e===u.MediaType.Audio}))}function I(e){var t=null==e?void 0:e.getState();return void 0!==t&&void 0!==e?Promise.race(w(t).map((function(n){return A(e,t,n)}))):Promise.reject("SourceState unavailable")}function j(e){return"discontinuitySequenceNumber"in e}function L(e){var t=[null==e?void 0:e.getAudioBufferLength(),null==e?void 0:e.getVideoBufferLength()].filter(g.isDefined);return Math.min.apply(Math,t)}t.FragmentedSubtitleHandler=C},24855:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.isSubtitleTrack=r,t.isFragmentedAdaptationSet=o;var i=n(79814);function r(e,t){return(i.MimeTypeHelper.isText(e)||i.MimeTypeHelper.isTtmlXml(e))&&!t||i.MimeTypeHelper.isApplication(e)}function o(e){var t=[e];return e.Representation&&e.Representation.length>0&&t.push(e.Representation[0]),t.some((function(e){return e.SegmentList||e.SegmentTemplate||e.SegmentBase}))}},27338:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SubtitleParserFactory=void 0;var i=n(62510),r=n(91520),o=n(16368),a=n(57042),s=function(){function e(){}return e.createInstance=function(e,t){var n;t.indexOf("<?")>-1||t.indexOf("<tt")>-1?r.ModuleManager.has(o.ModuleName.SubtitlesTTML)?n=new(0,r.ModuleManager.get(o.ModuleName.SubtitlesTTML).TTMLParser)(e):e.eventHandler.dispatchEvent(i.PlayerEvent.Warning,new a.PlayerModuleMissingWarning(o.ModuleName.SubtitlesTTML,"The ".concat(o.ModuleName.SubtitlesTTML," module is required to parse TTML subtitles."))):r.ModuleManager.has(o.ModuleName.SubtitlesWebVTT)?n=new(r.ModuleManager.get(o.ModuleName.SubtitlesWebVTT).WebVttParser):e.eventHandler.dispatchEvent(i.PlayerEvent.Warning,new a.PlayerModuleMissingWarning(o.ModuleName.SubtitlesWebVTT,"The ".concat(o.ModuleName.SubtitlesWebVTT," module is required to parse WebVTT subtitles.")));return n},e}();t.SubtitleParserFactory=s},29495:function(e,t){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.EnabledSubtitlesTracker=void 0;var i=function(){function e(){this.enabledSubtitles={},this.idCounter=0}return e.prototype.addSubtitle=function(e){this.removeSubtitle(e.id),this.enabledSubtitles[this.getNextId()]=n({isFragmented:!1},e)},e.prototype.removeSubtitle=function(e){var t=this;Object.keys(this.enabledSubtitles).forEach((function(n){t.enabledSubtitles[n].id===e&&delete t.enabledSubtitles[n]}))},e.prototype.shouldEnableSubtitle=function(e,t){var n=this,i=Object.keys(this.enabledSubtitles).find((function(i){return n.getTargetSubtitleIdForPeriod(n.enabledSubtitles[i],t)===e.id}));return Boolean(i)},e.prototype.getTargetSubtitleIdForPeriod=function(e,t){var n=e.id,i=e.kind,o=e.lang,a=e.isFragmented;return r({lang:o,kind:i,isFragmented:a,id:n},t)||r({lang:o,kind:i,isFragmented:a},t)||r({lang:o,kind:i},t)},e.prototype.getNextId=function(){return this.idCounter++,this.idCounter},e.prototype.dispose=function(){this.enabledSubtitles={},this.idCounter=0},e}();function r(e,t){return Object.keys(t).find((function(n){var i=t[n].getSubtitle(),r=i.id,o=i.lang,a=i.isFragmented,s=i.kind,u=r===e.id,l=!e.isFragmented||a===e.isFragmented,c=o===e.lang;e.isPreferred&&e.lang&&(c=o.includes(e.lang)||e.lang.includes(o));var d=s===e.kind&&c&&l;return u||d}))}t.EnabledSubtitlesTracker=i},45684:function(e,t,n){var i=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.ExternalFileSubtitleHandler=void 0;var r=n(63546),o=n(35148),a=n(60997),s=n(62510),u=n(88005),l=n(67345),c=n(27338),d=n(49625),f=n(8679),p=function(e){function t(n,i,r){var o=e.call(this,n,i,r&&r._internalId.periodId)||this;return o.onFileLoaded=function(e){if(o.timeline.removeCues(),null==e?void 0:e.body){o.isFileLoaded=!0;var t=o.parse(e.body.trim()),n=o.applyVttOffset(t);o.fireOnCueParsedEvents(n,null),o.timeline.addCues(n)}},r&&(i.id=r.Representation[0]._id),i.url?(o.externalFileUrl=t.enforceHTTPSForSecureOrigins(i.url.trim()),o.isFileLoaded=!1,o.subtitleLoader=new u.DefaultContentLoader(o.context,{maxRetries:o.context.settings.MAX_RETRIES,retryDelay:o.context.settings.RETRY_DELAY,requestType:l.HttpRequestType.MEDIA_SUBTITLES})):(o.context.logger.log("No url was provided for an external subtitle, nothing will be loaded."),o.isFileLoaded=!0,o.loadPromise=Promise.resolve()),o}return i(t,e),t.prototype.activate=function(){var t=this;return this.context.logger.debug("loading "+this.externalFileUrl),e.prototype.activate.call(this).then((function(){return t.loadPromise?t.loadPromise:t.isFileLoaded?Promise.resolve():(t.loadPromise=t.subtitleLoader.load(t.externalFileUrl).then(t.onFileLoaded).catch((function(e){var n,i;if(t.loadPromise=void 0,!t.isActive())return Promise.resolve();throw t.context.eventHandler&&t.context.eventHandler.dispatchEvent(s.PlayerEvent.Warning,new r.PlayerWarning(o.WarningCode.NETWORK_COULD_NOT_LOAD_SUBTITLE)),t.context.logger.debug("Could not load subtitles/captions, got HTTP status code ".concat(null!==(i=null===(n=null==e?void 0:e.status)||void 0===n?void 0:n.toString())&&void 0!==i?i:""),e),t.deactivate(),"loading failed"})),t.loadPromise)}))},t.prototype.parse=function(e){return c.SubtitleParserFactory.createInstance(this.context,e).parse(e)},t.enforceHTTPSForSecureOrigins=function(e){var t=new RegExp("^(?:[a-z]+:)?//","i");return"https:"===location.protocol?e.replace(t,"//"):e},t.prototype.getSubtitleType=function(){return f.SubtitleType.EXTERNAL_FILE},t.prototype.clearBuffersUntil=function(){},t.prototype.onSeek=function(){},t.prototype.onSeeked=function(){},t.prototype.dispose=function(){e.prototype.dispose.call(this),this.subtitleLoader=(0,a.dispose)(this.subtitleLoader)},t}(d.AbstractSubtitleHandler);t.ExternalFileSubtitleHandler=p},49625:function(e,t,n){var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__decorate||function(e,t,n,i){var r,o=arguments.length,a=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,i);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(o<3?r(a):o>3?r(t,n,a):r(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractSubtitleHandler=void 0;var o=n(18665),a=n(62510),s=n(99378),u=n(16937),l=n(33696),c=n(91520),d=n(16368),f=function(){function e(e,t,n){void 0===n&&(n=null);var i=this;this.onCueEnter=function(e){i.context.eventHandler.dispatchEvent(a.PlayerEvent.CueEnter,p(e,i.getSubtitle().id))},this.onCueExit=function(e){i.context.eventHandler&&i.context.eventHandler.dispatchEvent(a.PlayerEvent.CueExit,p(e,i.getSubtitle().id))},this.onCueUpdate=function(e){i.context.eventHandler.dispatchEvent(a.PlayerEvent.CueUpdate,p(e,i.getSubtitle().id))},this.context=e,this.subtitle=t,this.subtitle.enabled=this.subtitle.enabled||!1,this.timeline=new s.Timeline(e,{onCueEnter:this.onCueEnter,onCueExit:this.onCueExit,onCueUpdate:this.onCueUpdate}),this.timeline.stopListening(),this.periodId=n,this.sourceStore=e.serviceManager.get(o.ServiceName.SourceStoreService,e.sourceContext.sourceIdentifier)}return e.prototype.fireOnCueParsedEvents=function(e,t){var n=this;e&&this.context.eventHandler&&e.forEach((function(e){n.context.eventHandler.dispatchEvent(a.PlayerEvent.CueParsed,i(i({subtitleId:n.getSubtitle().id},e),{periodId:t}))}))},e.prototype.isActive=function(){return this.subtitle.enabled},e.prototype.activate=function(){return this.subtitle.enabled=!0,this.timeline.startListening(),this.timeline.show(),Promise.resolve()},e.prototype.deactivate=function(){return this.subtitle.enabled=!1,this.timeline.stopListening(),this.timeline.hide(),Promise.resolve()},e.prototype.refresh=function(){this.timeline.show()},e.prototype.getSubtitle=function(){return this.subtitle},e.prototype.clearBuffersUntil=function(e){this.timeline.removeCuesUntil(e)},e.prototype.dispose=function(){this.sourceStore=null,this.deactivate(),this.timeline.dispose()},e.prototype.isTsContainer=function(){var e,t=c.ModuleManager.get(d.ModuleName.EngineBitmovin),n=t.selectors,i=t.mseModuleTypes;return(null===(e=n.getContainerFormat(this.sourceStore.getState(),l.MediaType.Video))||void 0===e?void 0:e.source)===i.containerFormat.TS},e.prototype.applyVttOffset=function(e){return this.isTsContainer()?e.map((function(e){return null!=e.offset?i(i({},e),{start:e.start+e.offset,end:e.end+e.offset,updates:e.updates.map((function(t){return i(i({},t),{updateTime:t.updateTime+e.offset})}))}):e})):e},r([(0,u.trackPerformance)("SubtitleHandler.activate")],e.prototype,"activate",null),e}();function p(e,t){return i({subtitleId:t,start:e.start,end:e.end},e.content)}t.AbstractSubtitleHandler=f},49896:function(e,t){function n(e){var t=0;if(0===e.length)return t;for(var n=0;n<e.length;n++){t=(t<<5)-t+e.charCodeAt(n),t|=0}return t}Object.defineProperty(t,"__esModule",{value:!0}),t.hashCode=n},59272:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.ForcedSubtitleController=void 0;var i=n(70412),r=function(){function e(){this.subtitleHandlers={}}return e.prototype.addSubtitleHandler=function(e){this.subtitleHandlers[e.getSubtitle().id]=e},e.prototype.deactivate=function(){return this.hasForcedSubtitles()?Promise.all(Object.values(this.subtitleHandlers).map((function(e){return e.deactivate()}))).then((function(){})):Promise.resolve()},e.prototype.activate=function(e){var t=this;return void 0===e&&(e=this.currentLang),this.hasForcedSubtitles()?(this.currentLang=e,this.deactivate().then((function(){var e=Object.values(t.subtitleHandlers).find((function(e){return(0,i.isEqualIsoLanguage)(e.getSubtitle().lang,t.currentLang)}));if((e=null!=e?e:Object.values(t.subtitleHandlers)[0])&&!e.isActive())return e.activate().catch((function(){}))}))):Promise.resolve()},e.prototype.onSeek=function(e,t){Object.values(this.subtitleHandlers).forEach((function(n){return n.onSeek(e,t)}))},e.prototype.onSeeked=function(e,t){Object.values(this.subtitleHandlers).forEach((function(n){return n.onSeeked(e,t)}))},e.prototype.dispose=function(){Object.values(this.subtitleHandlers).forEach((function(e){return e.dispose()})),this.subtitleHandlers=null},e.prototype.hasForcedSubtitles=function(){return Object.keys(this.subtitleHandlers).length>0},e.prototype.getForcedSubtitleHandler=function(e){if(this.subtitleHandlers)return Object.values(this.subtitleHandlers).find((function(t){return e(t)}))},e}();t.ForcedSubtitleController=r},64397:function(e,t){function n(e){return i(o(e))}function i(e){return e.replace(/<[^>]*>?/g,"")}function r(e){return e.replace(/(\r\n|\r|\n)/g,"<br />")}function o(e){return e.replace(/<br\s*\/?>/g,"\n")}function a(e){return e.replace(/<br\s*\/?>/g,"")}Object.defineProperty(t,"__esModule",{value:!0}),t.htmlToText=n,t.stripHtmlTags=i,t.prepareHtml=r,t.replaceHtmlBreakPointsWithNewLine=o,t.stripAwayHtmlBreakPoints=a},66774:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.parseMP4WrappedSubtitles=u;var i=n(81361),r=n(42283),o=n(34586),a=n(91520),s=n(16368);function u(e,t){var n=e.getTimescale();if(void 0===n||!a.ModuleManager.has(s.ModuleName.ContainerMP4))return[];var i=a.ModuleManager.get(s.ModuleName.ContainerMP4);return l(i.getMp4Fragments(e).flatMap((function(n){return i.getTrackSamples(n.moof,n.mdat,e,t)})),e,n)}function l(e,t,n){var i=0,o=[],a=0;return e.forEach((function(e){var s=r.CodecStringHelper.extractCodec(t.getCodec()),u=e.duration/n,l=t.getPlaybackTime()+i,f=l+u;if(i+=u,"wvtt"===s||"vtt"===s){var p=d(e,l,f,a);a+=p.length,p.forEach((function(e){o.push({start:l,end:f,text:e})}))}else"stpp"===s&&o.push({start:l,end:f,text:c(e)})})),o}function c(e){var t=new Uint8Array(e.data.buffer,e.data.byteOffset,e.data.byteLength);return i.ArrayHelper.convertBufferSourceToUTF8(t,o.TextSegmentAnalyzer.detectEncoding(e.data))}function d(e,t,n,r){for(var o=e.data.buffer,a=e.data.byteOffset,s=0,u=[],l="",c="";s<e.data.byteLength;){var d=e.data.getUint32(s),p=a+s,v=i.ArrayHelper.convertBufferSourceToUTF8(new Uint8Array(o,p+4,4));if("vttc"===v){if(l){var h=f(t,n,r,c,l);u.push(h),r+=1,l="",c=""}s+=8}else"payl"===v?(l=i.ArrayHelper.convertBufferSourceToUTF8(new Uint8Array(o,p+8,d-8)),s+=d):"sttg"===v?(c=i.ArrayHelper.convertBufferSourceToUTF8(new Uint8Array(o,p+8,d-8)),s+=d):s+=d}if(l){h=f(t,n,r,c,l);u.push(h)}return u}function f(e,t,n,i,r){return["[EMBEDDEDWEBVTT]",e,t,n,i,r].join("\n")}},68466:function(e,t,n){var i=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.Cea608Handler=void 0;var r=n(49625),o=n(8679),a=function(e){function t(t,n,i){return void 0===i&&(i=null),e.call(this,t,n,i)||this}return i(t,e),t.prototype.onSeek=function(){},t.prototype.onSeeked=function(e,t){var n=t<e,i=this.context.settings.CLEAR_BUFFERS_ON_SEEKING_BACKWARDS;n&&i&&this.clearBuffersUntil(this.context.renderer.getDuration())},t.prototype.addCaption=function(e,t){e&&(this.fireOnCueParsedEvents([e],t),this.timeline.addCues([e],0))},t.prototype.getSubtitleType=function(){return o.SubtitleType.IN_STREAM},t}(r.AbstractSubtitleHandler);t.Cea608Handler=a},70412:function(e,t){function n(e,t){var n;if(void 0===t)return!1;if(null===(n=window.Intl)||void 0===n?void 0:n.DisplayNames){var i=new window.Intl.DisplayNames(["en"],{type:"language"});try{return i.of(e.split("-")[0])===i.of(t.split("-")[0])}catch(e){}}return e===t}Object.defineProperty(t,"__esModule",{value:!0}),t.isEqualIsoLanguage=n},75225:function(e,t,n){var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerSubtitlesApiFactory=void 0;var r=n(62510),o=function(){function e(){}return e.build=function(e){return new(function(){function t(){}return t.prototype.add=function(t){e.addSubtitle(t)},t.prototype.remove=function(t){e.removeSubtitle(t)},t.prototype.list=function(){return e.listSubtitles()},t.prototype.enable=function(t,n){void 0===n&&(n=!0),e.enableSubtitle(t,n)},t.prototype.disable=function(t){e.disableSubtitle(t)},t.prototype.cueEnter=function(t){e.fireEvent(r.PlayerEvent.CueEnter,i(i({},t),{type:r.PlayerEvent.CueEnter}))},t.prototype.cueExit=function(t){e.fireEvent(r.PlayerEvent.CueExit,i(i({},t),{type:r.PlayerEvent.CueExit}))},t}())},e}();t.PlayerSubtitlesApiFactory=o},81253:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.parsePlainTextSubtitles=o;var i=n(81361),r=n(34586);function o(e){var t,n,o=r.TextSegmentAnalyzer.detectEncoding(e.getData()),a=i.ArrayHelper.convertBufferSourceToUTF8(e.getData(),o),s=null!==(t=e.getPlaybackTime())&&void 0!==t?t:0;return[{start:s,end:s+(null!==(n=e.getDuration())&&void 0!==n?n:0),text:a}]}},84380:function(e,t,n){var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.SubtitleModuleDefinition=void 0;var o=n(16368),a=n(49896),s=n(64397),u=n(70412),l=n(75225),c=n(27338),d=n(10842),f=n(96892);t.SubtitleModuleDefinition={name:o.ModuleName.Subtitles,module:function(){return{SubtitleService:d.SubtitleService,SubtitleParserFactory:c.SubtitleParserFactory,PlayerSubtitlesApiFactory:l.PlayerSubtitlesApiFactory,HtmlTransformer:s,LanguageComparer:u,hashCode:a.hashCode,createHtmlElement:f.createHtmlElement}},dependencies:[o.ModuleName.EngineBitmovin]},t.default=t.SubtitleModuleDefinition,r(n(86685),t)},86685:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},96892:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.createHtmlElement=a;var i,r=n(13533),o=void 0;function a(e){var t;if(s()){var n=u().parseFromString("<".concat(e,"></").concat(e,">"),"text/html");return(null===(t=n.body)||void 0===t?void 0:t.firstChild)||n.firstChild}return document.createElement(e)}function s(){if(void 0===o){var e=(0,r.getCapabilities)().isReactNative,t=window&&window.document&&"function"==typeof window.document.createElement;o=!t||e}return o}function u(){return null!=i||(i=new DOMParser),i}}},function(e){return function(t){return e(e.s=t)}(84380)}])}));
})();
