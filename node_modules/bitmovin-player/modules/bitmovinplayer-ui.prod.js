/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(e,i){"object"==typeof exports&&"object"==typeof module?module.exports=i():"function"==typeof define&&define.amd?define([],i):"object"==typeof exports?exports.ui=i():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player.ui=i())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[209],{24099:function(e,i,n){Object.defineProperty(i,"__esModule",{value:!0}),i.UIModuleDefinition=void 0;var t=n(33964),o=n(86246),u=n(58362);i.UIModuleDefinition={name:o.ModuleName.UI,module:{},hooks:{setup:function(e,i){var n=new u.UiControls(i);i.on(t.PlayerEvent.Destroy,(function(){n&&n.dispose()}))}}},i.default=i.UIModuleDefinition},58362:function(e,i,n){Object.defineProperty(i,"__esModule",{value:!0}),i.UiControls=void 0;var t=n(46324),o=n(11385),u=n(89336),r=function(){function e(i){var n=this;this.isDestroyed=!1,this.uiInstance=null;var u=i.getConfig(!0);!1!==u.ui&&(u.ui&&(this.uiConfig=i.getConfig().ui),o.FileLoader.getInstance().loadCSS(u.location.ui_css,u.style),e.isUIAvailable()?this.createUI(i):o.FileLoader.getInstance().loadScript(u.location.ui,!0).then((function(e){t.Environment.modules.playerui=e||window.bitmovin.playerui,n.createUI(i)})).catch((function(e){console.warn("Could not load UI",e),n.uiInstance=null})))}return e.prototype.createUI=function(e){this.isDestroyed||(this.uiInstance=t.Environment.modules.playerui.UIFactory.buildDefaultUI(e,this.uiConfig))},e.isUIAvailable=function(){if(!t.Environment.modules.playerui||!t.Environment.modules.playerui.UIFactory)return!1;var e=t.Environment.modules.playerui.UIFactory;return e&&u.Util.isFunction(e.buildDefaultUI)},e.prototype.dispose=function(){this.isDestroyed=!0,this.uiInstance&&this.uiInstance.release()},e}();i.UiControls=r}},function(e){return function(i){return e(e.s=i)}(24099)}])}));
})();
