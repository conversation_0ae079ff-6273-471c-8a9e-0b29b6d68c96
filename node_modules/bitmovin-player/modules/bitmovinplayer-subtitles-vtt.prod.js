/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports["subtitles-vtt"]=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player["subtitles-vtt"]=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[831],{6355:function(e,t){function n(e){var t=[],n=/::cue(\((.+)\))?(?= |{)/,r=/([a-zA-Z0-9-]+):(.+?(?=;|}|$))/g,i=n.exec(e[1]);if(!i)return t;for(var u=1;u<e.length;u++){for(var o=n.exec(e[u]),l=null!==o,a=(i=l?o:i)[2],s={},c=r.exec(e[u]);null!==c;){var f=c[1],p=c[2];s[f]=p.trim(),c=r.exec(e[u])}(l||Object.keys(s).length>0)&&t.push({identifier:a,style:s})}return t}function r(){return[{identifier:":future",style:{visibility:"hidden"}}]}Object.defineProperty(t,"__esModule",{value:!0}),t.parseStyles=n,t.getPlaceholderVttStyleProperties=r},11572:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),i(n(38581),t),i(n(36419),t),i(n(49017),t),i(n(21187),t),i(n(6355),t)},21187:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.getDefaultVttProperties=l,t.extractVttProperties=a,t.getDefaultVttRegionProperties=c,t.parseTime=f,t.ensureArray=p,t.stringifyCssProperties=d,t.stringifyCueHtml=v;var r=n(58974),i=n(47122),u=n(86246),o=n(49017);function l(){return{vertical:"",align:"center",size:100,line:"auto",lineAlign:"start",position:"auto",positionAlign:"center",snapToLines:!1}}function a(e){var t=l(),n=c();return Object.keys(t).forEach((function(r){return(0,o.setOptionProperty)(s(r,e,t),[n],t)})),t}function s(e,t,n){var i=[e,t[e]?"".concat(t[e]):n[e]],u="line"===e;return("position"===e&&(0,r.isNumber)(t[e])||u&&!1===t.snapToLines)&&(i[1]+="%"),i}function c(){return{id:null,width:100,lines:3,regionAnchorX:0,regionAnchorY:100,viewportAnchorX:0,viewportAnchorY:100,scroll:""}}function f(e){var t=/(?:(\d{1,}):)?(\d{2}):(\d{2})[.,](\d{3})/g.exec(e);if(null==t)return null;var n=t[1],r=t[2],i=t[3],u=t[4];return Number(u)/1e3+Number(i)+60*Number(r)+3600*(Number(n)||0)}function p(e){return Array.isArray(e)?e:[]}function d(e){return e?Object.entries(e).map((function(e){return"".concat(e[0],":").concat(e[1],";")})).join(""):null}function v(e){return(0,i.ModuleManager.get(u.ModuleName.Subtitles).HtmlTransformer.prepareHtml)((null==e?void 0:e.outerHTML)||"")}},32439:function(e,t,n){var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.WebVttParser=void 0;var i=n(11572),u=9e4,o=function(){function e(){}return e.prototype.parse=function(e,t){return void 0===t&&(t=0),(e=e.replace(/\r\n/gm,"\n")).startsWith("[EMBEDDEDWEBVTT]")?this.parseEmbeddedWebVtt(e):this.parseExternalWebVtt(e,t)},e.prototype.parseEmbeddedWebVtt=function(e){var t,n,r=e.split("\n"),u=r[1],o=r[2],l=r[3],s=r[4],c=r.slice(5).join("\n"),f=a();f.start=Number(u),f.end=Number(o),f.i=Number(l);var p=i.parseContents(c,0,i.getPlaceholderVttStyleProperties());f.text=null!==(n=null!==(t=null==p?void 0:p.textContent)&&void 0!==t?t:null==p?void 0:p.innerText)&&void 0!==n?n:"";var d=i.stringifyCueHtml(p);if(f.html=d,s){var v=i.parseOptions(s,[]);f.vtt=v}return f.vtt.id=l,[f]},e.prototype.parseExternalWebVtt=function(e,t){var n=e.split(/\n{2,}|^[WEBVTT\n+]?X-TIMESTAMP-MAP=.*\n+|^WEBVTT\n+/m).filter((function(e){return null!=e&&""!==e})).map((function(e){return e.split("\n")})),r=this.parseOffset(e),i=this.parseRegionsAndStyles(n),u={vtt:r,base:t};return this.parseCues(n,i,u)},e.prototype.parseOffset=function(e){var t=/X-TIMESTAMP-MAP=(.*)\n/.exec(e);if(null==(null==t?void 0:t[1]))return 0;var n=t[0],r=/LOCAL:((?:(\d{1,}):)?(\d{2}):(\d{2})\.(\d{3}))/m.exec(n),o=/MPEGTS:(\d+)/m.exec(n),l=0;if(null==r?void 0:r[1]){var a=i.parseTime(r[1]);null!=a&&(l=a)}var s=0;return null!=o&&(s=Number(o[1])),s/u-l},e.prototype.parseRegionsAndStyles=function(e){for(var t=[],n=[],r=0,u=e;r<u.length;r++){var o=u[r],l=o[0];/^STYLE/.test(l.trim())&&n.push.apply(n,i.parseStyles(o)),/^region:?/i.test(l.trim())&&t.push.apply(t,i.parseRegions(o))}return{regions:t,styles:n}},e.prototype.shouldParseCueBlock=function(e){var t=/^NOTE/.test(e),n=/^STYLE/.test(e),r=/^region:?/i.test(e);return!t&&!n&&!r},e.prototype.isLineWithCueTime=function(e){return null!=e&&""!==e&&!!e.includes("--\x3e")},e.prototype.getVttStyles=function(e){var t={},n=[];return e.forEach((function(e){null==e.identifier&&(t=r(r({},t),e.style));var i=n.findIndex((function(t){return t.identifier===e.identifier}));-1!==i?n[i]={identifier:e.identifier,style:r(r({},n[i].style),e.style)}:n.push(e)})),i.getPlaceholderVttStyleProperties().forEach((function(e){n.find((function(t){return t.identifier===e.identifier}))||n.push(e)})),{globalStyle:t,contentStyles:n}},e.prototype.applyRootCueStyle=function(e,t){var n=i.stringifyCssProperties(t);n&&(e.html.setAttribute("style",n),e.subCues.forEach((function(e){e.html.setAttribute("style",n)})))},e.prototype.getParsedCueData=function(e,t,n){var r=/(<\d+:\d{2}:\d{2}[.,]\d{3}>)/g,u=[],o=s(e),l=o.start,a=o.end;if(null!==l&&null!==a){for(var p=c(e),d=p.split(r),v="",y=p.replace(r,""),b=l;d.length>0;){var g=d.shift(),h=d.shift(),m=h?i.parseTime(h):a,P=null!=m?m:a;if(b!==P){var x=f(g,v,y=y.substring(g.length));v+=g;var A=i.parseContents(x,n,t);u.push({updateTime:b,html:A}),b=P}}var C=u.shift();if(C)return{start:l,end:a,html:C.html,subCues:u}}},e.prototype.getCueStyle=function(e,t,n){var i={};return n&&(i=e.filter((function(e){return e.identifier==="#"+n})).reduce((function(e,t){return r(r({},e),t.style)}),{})),r(r({},t),i)},e.prototype.parseCues=function(e,t,n){var r=this,u=[],o=0,a=t.regions,s=t.styles,c=this.getVttStyles(s),f=c.globalStyle,p=c.contentStyles;return e.filter((function(e){return r.shouldParseCueBlock(e[0])})).forEach((function(e){var t=void 0;e[0].includes("--\x3e")||(t=e[0].trim(),e.splice(0,1));var s=r.getCueIndex(t,o);if(o=s,r.isLineWithCueTime(e[0])){var c=i.parseOptions(e[0],a);c.id=t;var d=r.getParsedCueData(e,p,n.base);if(void 0!==d){var v=r.getCueStyle(p,f,t);r.applyRootCueStyle(d,v);var y=l(d,s,n,c);u.push(y)}}})),u},e.prototype.getCueIndex=function(e,t){var n=Number(e);return null==e||isNaN(n)?t+1:n},e}();function l(e,t,n,u){var o,l,s=a();return s.i=t,s.start=e.start+n.base,s.end=e.end+n.base,s.text=(null===(o=e.html)||void 0===o?void 0:o.textContent)||(null===(l=e.html)||void 0===l?void 0:l.innerText)||"",s.html=i.stringifyCueHtml(e.html),s.vtt=r({},u),0!==n.vtt&&(s.offset=n.vtt),s.updates=e.subCues.map((function(e){return{updateTime:e.updateTime+n.base,html:i.stringifyCueHtml(e.html)}})),s}function a(){return{i:null,start:null,end:null,text:null,html:null,vtt:i.getDefaultVttProperties(),offset:null,updates:[]}}function s(e){var t=e[0].split(/[ \t]+-->[ \t]+/);return{start:i.parseTime(t[0]),end:i.parseTime(t[1])}}function c(e){return e.slice(1).join("\n").trim()}function f(e,t,n){return(t?p(t,"cue-past"):"")+e+(n?p(n,"cue-future"):"")}function p(e,t){return"<c.".concat(t,">").concat(e,"</c>")}t.WebVttParser=o,t.default=o},36419:function(e,t,n){var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},r.apply(this,arguments)},i=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,i=0,u=t.length;i<u;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.parseContents=y;var u=n(47122),o=n(86246),l=n(96201),a=n(21187),s={"&amp;":"&","&lt;":"<","&gt;":">","&lrm;":"‎","&rlm;":"‏","&nbsp;":" "},c={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},f={v:"title",lang:"lang"},p={rt:"ruby"},d={"cue-future":":future","cue-past":":past"},v=2;function y(e,t,n){var r=u.ModuleManager.get(o.ModuleName.Subtitles).createHtmlElement("span");return b({input:e,root:r,current:r,tagStack:[],offset:t,styles:n})}function b(e){var t=P(e.input);return null==t?e.root:(e.input=e.input.substring(t.length),m(t,e))}function g(e,t){var n,u,o,s,c;e=i(i([],e,!0),l.webVttDefaultClassStyles,!0);var f="",p="",v={};if(t.type&&"v"!==t.type&&(v=null!==(u=null===(n=e.find((function(e){return e.identifier===t.type})))||void 0===n?void 0:n.style)&&void 0!==u?u:v),t.contents){var y=t.contents.substring(1).split(".").map((function(e){return d[e]||".".concat(e)})),b=[];y.forEach((function(t){var n,r=null===(n=e.find((function(e){return e.identifier===t})))||void 0===n?void 0:n.style;r&&b.push(r)})),v=b.reduce((function(e,t){return r(r({},e),t)}),{}),f=t.contents.split(".").join(" ").trim()}return"v"===t.type&&t.annotation&&(p=t.annotation.substring(1),v=null!==(s=null===(o=e.find((function(e){return e.identifier==="".concat(t.type,'[voice="').concat(p,'"]')})))||void 0===o?void 0:o.style)&&void 0!==s?s:v),{className:f,title:p,styleString:null!==(c=(0,a.stringifyCssProperties)(v))&&void 0!==c?c:""}}function h(e,t,n){var r=g(t,n),i=r.className,u=r.title,o=r.styleString;u&&(e.title=u),i&&(e.className=i),o&&e.setAttribute("style",o)}function m(e,t){if("<"!==e[0]){var n=t.current.ownerDocument.createTextNode(x(e));return t.current.appendChild(n),b(t)}if("/"===e[1])return S(e,t.tagStack)&&(t.tagStack.pop(),t.current=t.current.parentNode),b(t);var r=(0,a.parseTime)(e.substring(1,e.length-1));if(null!=r){var i=t.current.ownerDocument.createComment("time: ".concat(r+t.offset));return t.current.appendChild(i),b(t)}var u=/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/.exec(e);if(!u)return b(t);var o=u[0],l=u[1],s=u[2],c=u[3],f=C(l,c,t.current.ownerDocument);return f&&A(t.current,f)?(h(f,t.styles,{type:l,contents:s,annotation:c}),t.tagStack.push(o[1]),t.current.appendChild(f),t.current=f,b(t)):b(t)}function P(e){if(!e)return null;var t=/^([^<]*)(<[^>]+>?)?/.exec(e);if(!t)return null;var n=t[1],r=t[2];return n||r}function x(e){return Object.keys(s).forEach((function(t){e=e.replace(new RegExp(t,"g"),s[t])})),e}function A(e,t){return!p[t.localName]||p[t.localName]===e.localName}function C(e,t,n){var r=c[e];if(!r)return null;var i=n.createElement(r),u=f[e];return u&&t&&(i[u]=t.trim()),i}function S(e,t){return t.length>0&&t[t.length-1]===e.substring(v).replace(">","")}},38581:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.parseRegions=i;var r=n(21187);function i(e){for(var t=[],n=0,r=e;n<r.length;n++){var i=r[n];if(i=i.trim(),/^REGION$/i.test(i)){t.push(u(e));break}/^Region:/i.test(i)&&t.push(o(i))}return t}function u(e){return l(e.slice(1),":")}function o(e){return l(e.split(/[ \t]+/).slice(1),"=")}function l(e,t){var n=(0,r.getDefaultVttRegionProperties)();return e.forEach((function(e){a(e.split(t),n)})),n}function a(e,t){var n=e[0],r=e[1];switch(n){case"id":s(r,t);break;case"width":c(r,t);break;case"lines":f(r,t);break;case"regionanchor":p(r,t);break;case"viewportanchor":d(r,t);break;case"scroll":v(r,t)}}function s(e,t){null!=e&&(t.id=e)}function c(e,t){var n=/^(\d{1,2}|100)%$/,i=(0,r.ensureArray)(n.exec(e))[1];null!=i&&(t.width=Number(i))}function f(e,t){var n=/^(\d+)$/,i=(0,r.ensureArray)(n.exec(e))[1];null!=i&&(t.lines=Number(i))}function p(e,t){var n=/^(\d{1,2}|100)%,(\d{1,2}|100)%$/,i=(0,r.ensureArray)(n.exec(e)),u=i[1],o=i[2];null!=u&&(t.regionAnchorX=Number(u)),null!=o&&(t.regionAnchorY=Number(o))}function d(e,t){var n=/^(\d{1,2}|100)%,(\d{1,2}|100)%$/,i=(0,r.ensureArray)(n.exec(e)),u=i[1],o=i[2];null!=u&&(t.viewportAnchorX=Number(u)),null!=o&&(t.viewportAnchorY=Number(o))}function v(e,t){var n=/^(up)$/,i=(0,r.ensureArray)(n.exec(e))[1];null!=i&&(t.scroll=i)}},49017:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.parseOptions=u,t.setOptionProperty=o;var r=n(58974),i=n(21187);function u(e,t){var n=e.split(/\s/),r=(0,i.getDefaultVttProperties)();return n.forEach((function(e){o(e.split(":"),t,r)})),v(r),r}function o(e,t,n){var r=e[0],i=e[1];switch(r){case"region":l(i,t,n);break;case"vertical":a(i,n);break;case"align":s(i,n);break;case"size":c(i,n);break;case"position":p(i,n);break;case"line":d(i,n)}}function l(e,t,n){var r=t.find((function(t){return t.id===e}));null!=r&&(n.region=r)}function a(e,t){var n=/^(lr|rl)$/,r=(0,i.ensureArray)(n.exec(e))[1];null!=r&&(t.vertical=r)}function s(e,t){var n=/^(start|middle|center|end|left|right)$/,r=(0,i.ensureArray)(n.exec(e))[1];null!=r&&(t.align=r),t.positionAlign=f(void 0,t)}function c(e,t){var n=/^([\d.]+)%$/,r=(0,i.ensureArray)(n.exec(e))[1];null!=r&&(t.size=Number(r))}function f(e,t){if(null!=e)return e;switch(t.align){case"start":case"left":return"line-left";case"end":case"right":return"line-right";default:return"center"}}function p(e,t){var n=/^([\d.]+|auto)%?(?:,(line-left|line-right|center))?$/,r=(0,i.ensureArray)(n.exec(e)),u=r[1],o=r[2];"auto"===u?t.position="auto":void 0!==u&&(t.position=Number(u)),t.positionAlign=f(o,t)}function d(e,t){var n=/^(-?[\d.]+|auto)(%)?(?:,(start|end|center|auto))?$/,u=(0,i.ensureArray)(n.exec(e)),o=u[1],l=u[2],a=u[3],s="%"===l;"auto"===o?t.line="auto":s?t.line="".concat(o,"%"):null!=e&&(t.line=Number(o)),null!=a&&(t.lineAlign=a),t.snapToLines=(0,r.isNumber)(t.line)}function v(e){var t=(0,i.getDefaultVttProperties)();null!=e.region&&(e.vertical!==t.vertical||e.size!==t.size||e.line!==t.line)&&delete e.region}},51461:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.WebVttSubtitlesModuleDefinition=void 0;var r=n(86246),i=n(21187),u=n(32439);t.WebVttSubtitlesModuleDefinition={name:r.ModuleName.SubtitlesWebVTT,module:function(){return{WebVttParser:u.WebVttParser,extractVttProperties:i.extractVttProperties}},dependencies:[r.ModuleName.Subtitles]},t.default=t.WebVttSubtitlesModuleDefinition},96201:function(e,t){var n=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,i=0,u=t.length;i<u;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.webVttDefaultClassStyles=void 0;var r=["white","lime","cyan","red","yellow","magenta","blue","black"];function i(e){return{identifier:".".concat(e),style:{color:e}}}function u(e){return{identifier:".bg_".concat(e),style:{background:e}}}t.webVttDefaultClassStyles=n(n([],r.map(i),!0),r.map(u),!0)}},function(e){return function(t){return e(e.s=t)}(51461)}])}));
})();
