/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports["container-mp4"]=e():(t.bitmovin=t.bitmovin||{},t.bitmovin.player=t.bitmovin.player||{},t.bitmovin.player["container-mp4"]=e())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[270],{10398:function(t,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.CmafChunkParser=void 0;var r=i(12291),s=function(){function t(t){this.context=t,this.buffer=new ArrayBuffer(0)}return t.parseIsoBoxType=function(t,e){return String.fromCharCode(t[e])+String.fromCharCode(t[e+1])+String.fromCharCode(t[e+2])+String.fromCharCode(t[e+3])},t.prototype.appendData=function(t){this.buffer=r.ArrayHelper.concatBuffers(this.buffer,t)},t.prototype.getCompleteChunks=function(){var e=t.findCompleteCmafChunks(this.buffer);if(0===e.length)return null;var i=e[e.length-1].end,r=this.buffer.slice(0,i);return this.buffer=this.buffer.slice(i),r},t.findCompleteCmafChunks=function(e){if(!e||e.byteLength<8)return[];for(var i=new DataView(e),r=[],s=["moof","mdat"],n=0,a=[],o=0;o+8<e.byteLength;){var h=i.getUint32(o),f=t.parseIsoBoxType(new Uint8Array(e),o+4);if(0===h)break;o+h<=e.byteLength&&(a.push(f),f===s[0]&&s.shift(),0===s.length&&(r.push({start:n,end:o+h,boxes:a}),s=["moof","mdat"],n=o+h+1,a=[])),o+=h}return r},t.prototype.reset=function(){this.buffer.byteLength,this.buffer=new ArrayBuffer(0)},t}();e.CmafChunkParser=s},16032:function(t,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.ContainerMp4ModuleDefinition=void 0;var r=i(86246),s=i(10398),n=i(40026),a=i(78672),o=i(59965);e.ContainerMp4ModuleDefinition={name:r.ModuleName.ContainerMP4,module:{MP4Parser:o.MP4Parser,MP4Decrypter:n.MP4Decrypter,MP4EncryptionParser:a.MP4EncryptionParser,CmafChunkParser:s.CmafChunkParser,getMp4Fragments:o.getMp4Fragments,getAvcSamples:o.getAvcSamples,parseTfdtBoxOffsets:o.parseTfdtBoxOffsets,getTrackId:o.getTrackId,parseBuffer:o.parseBuffer,parsePlaybackTime:o.parsePlaybackTime,isValidMp4:o.isValidMp4,getCodecFromSegment:o.getCodecFromSegment,getTrackSamples:o.getTrackSamples}},e.default=e.ContainerMp4ModuleDefinition},17600:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.ntpToUnix=r;var i=new Date(Date.UTC(1900,0,1)).getTime();function r(t){return t+i}},40026:function(t,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.MP4Decrypter=void 0;var r=i(80691),s=i(12291),n=i(88599),a=i(4742),o=i(47122),h=i(86246),f=8,_=function(){function t(t,e,i){this.context=t,this.keys=e,this.isDataEncrypted=!1,this.mimeType=i,this.keyIds=[]}return t.prototype.parseInitSegment=function(t){var e=this;this.isDataEncrypted=!1;for(var i=r.ISOBoxer.parseBuffer(t),o=i._raw.buffer,h=0,f=u(i);h<f.length;h++){var _=f[h];this.storeKeyId(_)}var d=i.get("moov.trak.mdia.minf.stbl.stsd").entries[0];return d.list("sinf").forEach((function(t){var r=t.get("frma"),o=t.get("schm"),h=t.get("schi.tenc");if(r&&h&&o){for(var f=0;f<4;++f)i._raw.setUint8(d._offset+4+f,r.codingname.charCodeAt(f));e.defaultIsEncrypted=h.default_is_encrypted,e.defaultIVSize=h.default_iv_size,e.defaultKid=h.default_kid,e.isDataEncrypted=!0;var _=s.ArrayHelper.arrayBufferToAsciiString(n.FormatHelper.hexToBytes(e.defaultKid).buffer,!0);e.storeKeyId(_)}(0,a.rewriteMp4BoxType)(t,"free")})),Promise.resolve(o)},t.prototype.storeKeyId=function(t){this.keyIds.includes(t)||this.keyIds.push(t)},t.prototype.getKeyIds=function(){return this.keyIds},t.prototype.addKeys=function(t){for(var e=function(t){var e=i.keys.find((function(e){return e.kid===t.kid}));e?e.key=t.key:i.keys.push(t)},i=this,r=0,s=t;r<s.length;r++){e(s[r])}},t.prototype.getKey=function(){if(this.key)return this.key;var t;for(var e in this.keys){if(this.keys.hasOwnProperty(e))if(this.keys[e].hasOwnProperty("key")&&this.keys[e].hasOwnProperty("kid")&&this.keys[e].kid===this.defaultKid){t=this.keys[e].key;break}}return void 0===t&&(t=this.keys[0].key),"string"==typeof t?this.key=n.FormatHelper.hexToBytes(t).buffer:t instanceof Uint8Array?this.key=t.buffer:this.key=t,this.key},t.prototype.decryptDataSegment=function(t){if(!this.getKey())return Promise.reject("Error decrypting AES-128 encrypted content");if(!1===this.isEncrypted())return Promise.reject("NO ENCRYPTED CONTENT");for(var e=r.ISOBoxer.parseBuffer(t,{iv_size:this.defaultIVSize}),i=new y(e),s=[],n=0;n<i.getSampleCount();++n)s.push(this.decryptSample(t,i,n));return Promise.all(s).then((function(){return g(i),t}))},t.prototype.decryptSample=function(t,e,i){var r=e.sampleToOffset(i),s=e.sampleToSize(i,1),n=e.getSampleEncodeInfo(i);return n.iv?p(new(0,o.ModuleManager.get(h.ModuleName.Crypto).CryptoApi)(this.context,"AES-CTR",this.getKey(),n.iv,this.mimeType),t,r,s,n):Promise.reject("NO INITIAL VECTOR PROVIDED")},t.prototype.isEncrypted=function(){return this.isDataEncrypted},t}();function u(t){var e=[],i=[];i.push.apply(i,t.list("moov.pssh")),i.push.apply(i,t.list("moof.pssh"));for(var r=0,s=i;r<s.length;r++){var n=s[r];n.kids&&n.kids.length>0&&e.push.apply(e,n.kids)}return e}function d(t,e,i,r){var s=e.slice(i,i+r);return t.decrypt(s).then((function(t){new Uint8Array(e).set(new Uint8Array(t),i)}))}function p(t,e,i,r,s){var n;return(null!==(n=s.subsample_count)&&void 0!==n?n:0)>0&&s.subsamples?c(t,l(e,i,s)):d(t,e,i,r)}function c(t,e){var i=m(e);return t.decrypt(i).then((function(t){for(var i=0,r=0,s=e;r<s.length;r++){var n=s[r],a=new Uint8Array(t,i,n.byteLength);n.set(a),i+=n.byteLength}}))}function l(t,e,i){if(!i.subsamples)return[];for(var r=[],s=0,n=i.subsamples;s<n.length;s++){var a=n[s];e+=a.bytes_of_clear_data;var o=a.bytes_of_encrypted_data;if(o>0){var h=new Uint8Array(t,e,o);r.push(h),e+=o}}return r}function m(t){for(var e=t.reduce((function(t,e){return t+e.byteLength}),0),i=new Uint8Array(e),r=0,s=0,n=t;s<n.length;s++){var a=n[s];i.set(a,r),r+=a.byteLength}return i.buffer}e.MP4Decrypter=_;var y=function(){function t(t){var e=t.get("moof.traf");this.trunBox=e.get("trun"),this.sencBox=e.get("senc"),this.uuidBox=e.get("uuid"),this.saizBox=e.get("saiz"),this.saioBox=e.get("saio"),this.seigBox=e.get("seig"),this.sbgpBox=e.get("sbgp"),this.mdatOffset=t.get("mdat")._offset+f}return t.prototype.getSampleSizeTable=function(){return this.trunBox.samples},t.prototype.getSampleCount=function(){return this.getSampleSizeTable().length},t.prototype.getSampleEncodeInfo=function(t){return this.sencBox.samples[t]},t.prototype.sampleToOffset=function(t){return this.mdatOffset+this.sampleToSize(0,t)},t.prototype.sampleToSize=function(t,e){for(var i,r=this.getSampleSizeTable(),s=0,n=t;n<t+e;n++)s+=null!==(i=r[n].sample_size)&&void 0!==i?i:0;return s},t}();function g(t){[t.uuidBox,t.sencBox,t.saioBox,t.saizBox,t.seigBox,t.sbgpBox].forEach((function(t){t&&(0,a.rewriteMp4BoxType)(t,"free")}))}},59965:function(t,e,i){var r=this&&this.__spreadArray||function(t,e,i){if(i||2===arguments.length)for(var r,s=0,n=e.length;s<n;s++)!r&&s in e||(r||(r=Array.prototype.slice.call(e,0,s)),r[s]=e[s]);return t.concat(r||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.MP4Parser=void 0,e.getAvcSamples=S,e.getTrackSamples=b,e.removeEmulationPreventionThreeBytes=B,e.getMp4Fragments=I,e.parseTfdtBoxOffsets=T,e.getTrackId=w,e.parseBuffer=D,e.parsePlaybackTime=P,e.parseSegmentData=F,e.isValidMp4=E,e.getCodecFromSegment=M;var s=i(80691),n=i(81668),a=i(17854),o=(i(90681),i(6231)),h=i(70008),f=i(12291),_=i(39713),u=i(88599),d=i(40060),p=i(4742),c=i(58974),l=i(47122),m=i(86246),y=i(17600),g=8,v=function(){function t(t,e,i){void 0===i&&(i=s.ISOBoxer),this.context=t,this.mp4EncryptionParser=e,this.isoBoxer=i,this.CryptoModule=l.ModuleManager.get(m.ModuleName.Crypto,!1)}return t.prototype.parseSegment=function(t){"mp4"===d.MimeTypeHelper.extractContainerType(t.getMimeType())&&(t.isInit()?this.parseInitSegment(t):this.parseDataSegment(t),t.setDrmInitData(this.extractDrmInitData(t)))},t.prototype.extractDrmInitData=function(t){var e=this.mp4EncryptionParser.parsePsshBox(t);return Array.isArray(e)&&e.length>0?e:[]},t.prototype.parseTrackDefaultParameters=function(t){var e=t.getParsedData().get("moov.mvex.trex");if(e){var i={defaultSampleSize:e.default_sample_size,defaultSampleDuration:e.default_sample_duration};t.updateParserMetadata(i)}},t.prototype.parseSampleGroupDescriptionBox=function(t){var e=t.getParsedData().get("moof.traf.sgpd");if(e&&("seig"===G(e)&&!(R(e)<1))){j(e),K(e),Y(e);var i=Z(e);t.setDrmKid(i)}},t.prototype.parseTrackEncryptionBox=function(t){var e=this.mp4EncryptionParser.parseDefaultKIDFromBuffer(t.getData(),t.getMimeType()),i=(null==e?void 0:e.length)?e.pop():void 0,r=t.getParsedData().get("moov.trak.mdia.minf.stbl.stsd.encv.sinf.schi.tenc")||t.getParsedData().get("moov.trak.mdia.minf.stbl.stsd.enca.sinf.schi.tenc");r&&(t.updateParserMetadata({ivSize:r.default_iv_size}),t.setEncrypted(!0)),i&&t.setDrmKid(u.FormatHelper.beautifyUUID(i))},t.prototype.isSupportedEdtsBox=function(t){var e=t.get("elst");return!(e&&e.entries.length>0&&e.entries[0].media_time<0)},t.prototype.parseInitSegment=function(t){var e=F(t,{},this.isoBoxer);e?(this.parseTrackEncryptionBox(t),H(t,this.context.logger),this.parseTrackDefaultParameters(t),this.context.settings.IGNORE_MP4_EDTS_BOX&&this.removeUnsupportedEdtsBox(e)):this.context.eventHandler.fireError(new a.PlayerError(n.ErrorCode.SOURCE_EMPTY_SEGMENT,{mimeType:t.getMimeType(),segmentUrl:t.getUrl()},"The player has encountered an init segment that is unexpectedly empty."))},t.prototype.removeUnsupportedEdtsBox=function(t){var e=t.get("moov.trak.edts");e&&!this.isSupportedEdtsBox(e)&&(0,p.rewriteMp4BoxType)(e,"free")},t.prototype.parseDataSegment=function(e){var i,r,s=0,n=e.getSegmentInfo();(null==n?void 0:n.presentationTimeOffset)&&(s=n.presentationTimeOffset);var a=this.getParsedSegmentIsoFile(e);e.isSelfInit()&&this.parseTrackDefaultParameters(e),this.parseSampleGroupDescriptionBox(e),this.parseTrackEncryptionBox(e),e.setEncrypted(null!==(r=null===(i=e.getInitSegment())||void 0===i?void 0:i.isEncrypted())&&void 0!==r&&r),N(e),H(e,this.context.logger),this.parseSegmentTimingInformation(e,s,a),this.addDuration(e),this.context.settings.PARSE_EMSG_BOXES&&e.setInbandEvents(this.parseInbandEvents(e,s)),e.setLastSegment(this.parseIsLastSegment(e));var o=t.getProducerReferenceTime(e);o&&(n.wallClockTime=o)},t.prototype.addDuration=function(t){var e=this.parseSegmentDuration(t);if(e){Math.abs(t.getDuration()-e);t.setDuration(e)}},t.prototype.parseSegmentTimingInformation=function(t,e,i){var r=i.fetchAll("tfdt"),s=r[0],n=r.map((function(t){return t._offset}));if(s){var a=t.getTimescale();t.setTFDTBoxOffsets(n),t.setBaseMediaDecodeTime(s.baseMediaDecodeTime),t.setPresentationTimeOffset(e),void 0===a||t.setPlaybackTime(s.baseMediaDecodeTime/a-e)}},t.prototype.getParsedSegmentIsoFile=function(t){return F(t,{iv_size:t.getIvSize()||g},this.isoBoxer)},t.prototype.parseSegmentDuration=function(t){var e=this,i=t.getTimescale();return d.MimeTypeHelper.isAV(t.getMimeType())?void 0===i?null:t.getParsedData().list("moof.traf").reduce((function(i,r){return i+e.calculateTrackFragmentDuration(r,t.getDefaultSampleDuration())}),0)/i:null},t.prototype.calculateTrackFragmentDuration=function(t,e){var i,r=t.get("trun");if(!r)return 0;var s=t.get("tfhd"),n=s?s.default_sample_duration:null,a=null===(i=r.samples)||void 0===i?void 0:i.reduce((function(t,i){return t+(i.sample_duration||n||e||0)}),0);return null!=a?a:0},t.prototype.parseInbandEvents=function(t,e){var i=this.getParsedSegmentIsoFile(t),r=U(t,i,e);return i.fetchAll("emsg").map((function(t){return{schemeIdUri:t.scheme_id_uri,value:t.value,presentationTime:q(t,r,e),eventDuration:t.event_duration/t.timescale,id:String(t.id),messageData:f.ArrayHelper.convertBufferSourceToUTF8(t.message_data)}}))},t.parseFourCharsText=function(t,e){for(var i="",r=0;r<4;r++){var s=t.getInt8(e+r);i+=String.fromCharCode(s)}return i},t.prototype.parseIsLastSegment=function(e){var i=e.getData();if(!i||i.byteLength<4)return!1;for(var r=new DataView(i),s=0;s+3<r.byteLength;){var n=r.getUint32(s);if(n<=0)return!1;if("styp"===t.parseFourCharsText(r,s+4)){s+=8,s+=8;for(var a=void 0;s<n;)if(a=t.parseFourCharsText(r,s),s+=4,"lmsg"===a)return!0;break}s+=n}return!1},t.getProducerReferenceTime=function(t){var e=t.getParsedData().fetch("prft");if(!e)return null;var i=1e3*e.ntp_timestamp_seconds;return i+=Math.round(e.ntp_timestamp_seconds_fraction/Math.pow(2,32)*1e3),(0,y.ntpToUnix)(i)},t}();function U(t,e,i){var r=e.fetch("sidx"),s=0,n=t.getTimescale();if(void 0===n)return 0;if(r&&r.earliest_presentation_time)s=r.earliest_presentation_time/n;else{var a=e.fetch("tfdt");a&&a.baseMediaDecodeTime&&(s=a.baseMediaDecodeTime/n)}return(0,c.isNumber)(s)?s-=i:s=0,s}function S(t,e,i,r){return b(t.moof,t.mdat,e,r).map((function(t){return t.nalus=x(t.data,i),t}))}function b(t,e,i,r){var s,n,a,o=t.get("traf"),h=o.get("tfhd"),f=o.get("trun"),_=null!==(s=h.base_data_offset)&&void 0!==s?s:t._offset,u=null!==(n=f.data_offset)&&void 0!==n?n:0;return(null===(a=null==f?void 0:f.samples)||void 0===a?void 0:a.length)?f.samples.map((function(t){var r=t.sample_size||h.default_sample_size||i.getDefaultSampleSize();r>f._raw.buffer.byteLength&&(r=f._raw.buffer.byteLength-(_+u)),1!==f.sample_count||t.sample_size||(r=e.size-O(e));var s={duration:t.sample_duration||h.default_sample_duration||i.getDefaultSampleDuration(),size:r,compositionTimeOffset:t.sample_composition_time_offset||0,data:new DataView(f._raw.buffer,_+u,r)};return u+=r,s})):[]}function x(t,e){void 0===e&&(e=[]);for(var i=5,r=[],s=0;s+i<t.byteLength;){var n=Math.min(t.getUint32(s),t.byteLength-s-i);if(s+=4,0!==n){var a=31&t.getUint8(s);if(s++,0===e.length||e.includes(a)){var o={type:a,data:new DataView(t.buffer,t.byteOffset+s,n-1)};B(o),r.push(o)}s+=n-1}}return r}function B(t){for(var e=[],i=1;i<t.data.byteLength-2;)0===t.data.getUint8(i)&&0===t.data.getUint8(i+1)&&3===t.data.getUint8(i+2)?(e.push(i+2),i+=3):i++;if(0!==e.length){for(var r=new DataView(new ArrayBuffer(t.data.byteLength-e.length)),s=0,n=0;s<r.byteLength;s++,n++)e[0]===n&&(e.shift(),n++),r.setUint8(s,t.data.getInt8(n));t.data=r}}function I(t){var e=t.getParsedData().fetchAll("moof"),i=t.getParsedData().fetchAll("mdat");return e.length===i.length?e.map((function(t,e){return{moof:t,mdat:i[e]}})):[]}function O(t){return t.hasOwnProperty("largesize")?h.ISOBMFFConstants.BOX_HEADER_LENGTH_LARGE:h.ISOBMFFConstants.BOX_HEADER_LENGTH_REGULAR}function T(t){var e=D(t.getData()).fetchAll("tfdt");return e?e.map((function(t){return t._raw.byteOffset})):[]}function w(t,e){var i=e.get("moof.traf.tfhd");return i&&i.track_ID?i.track_ID:-1}function D(t,e,i){return void 0===e&&(e={}),void 0===i&&(i=s.ISOBoxer),i.parseBuffer(t,e)}function P(t,e){var i=z(t).get("moof.traf.tfdt");H(t,e);var r=t.getTimescale();return i&&void 0!==r?i.baseMediaDecodeTime/r:void 0===r?NaN:void 0}function F(t,e,i){if(void 0===e&&(e={}),void 0===i&&(i=s.ISOBoxer),!t.getParsedData()){var r=D(t.getData(),e,i);t.setParsedData(r)}return t.getParsedData()}function E(t){var e=z(t),i=(t.isInit()?["ftyp","moov"]:["moof","mdat"]).every((function(t){return Boolean(e.fetch(t))})),r=e.boxes[e.boxes.length-1],s=(r?r.size+r._offset:0)===t.getData().byteLength,n=!e._incomplete;return i&&s&&n}function C(t){var e=L(t);if(!e)return null;var i=e.entries[0],r=(i.get("esds")||{}).decoderConfig;return r?i.type+"."+r.objectProfileIndication.toString(16)+"."+r.decoderConfigDescriptor.audioObjectType:null}function k(t){var e=L(t);if(!e)return null;var i=e.entries.find((function(t){return r(r([],o.VisualSampleEntryTypes.Avc,!0),o.VisualSampleEntryTypes.Hvc,!0).includes(t.type)}));return i?o.VisualSampleEntryTypes.Avc.includes(i.type)?A(i):o.VisualSampleEntryTypes.Hvc.includes(i.type)?(0,_.assembleHvcCodecString)(i.type,i.get("hvcC")):null:null}function M(t){var e=F(t);return k(e)||C(e)}function L(t){return t.get("moov.trak.mdia.minf.stbl.stsd")}function A(t){var e=t.get("avcC");return e&&0!==e.sps.length?t.type+"."+u.FormatHelper.bytesToHex(new Uint8Array(e.sps[0].splice(1,3))):null}function z(t,e){return void 0===e&&(e=s.ISOBoxer),F(t,{iv_size:t.getIvSize()||g},e)}function H(t,e){var i=t.getParsedData().get("moov.trak.mdia.mdhd");if(i){var r=i.timescale;t.setTimescale(r)}}function N(t){t.getParsedData().fetch("senc")&&t.setEncrypted(!0)}function V(t){return t._raw.getUint8(8)}function G(t){return String.fromCharCode(t._raw.getUint8(12),t._raw.getUint8(13),t._raw.getUint8(14),t._raw.getUint8(15))}function X(t){return 1===V(t)?t._raw.getUint32(16):0}function R(t){var e=1===V(t)?20:16;return t._raw.getUint32(e)}function j(t){var e=V(t),i=X(t),r=1===e?24:20;return 1===e&&0===i?t._raw.getUint32(r):i}function K(t){var e=V(t),i=X(t),r=1===e&&0===i?28:24;return(t._raw.getUint16(r)<<16)+t._raw.getUint8(r+2)}function Y(t){var e=V(t),i=X(t),r=1===e&&0===i?31:27;return t._raw.getUint8(r)}function Z(t){for(var e=V(t),i=X(t),r=1===e&&0===i?32:28,s=new Uint8Array(16),n=0;n<16;n++)s[n]=t._raw.getUint8(r+n);return u.FormatHelper.bytesToUUID(s)}function q(t,e,i){return 1===t.version?t.presentation_time/t.timescale-i:e+t.presentation_time_delta/t.timescale}e.MP4Parser=v},78672:function(t,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.MP4EncryptionParser=void 0;var r=i(81668),s=i(17854),n=(i(90681),i(63093)),a=i(70008),o=i(12291),h=i(88599),f=i(40060),_=i(69837),u=function(){function t(t){var e=this;this.FULL_BOX_TYPES={stsd:function(t){var i;switch(t+=a.ISOBMFFConstants.FULL_BOX_VERSION_LENGTH+a.ISOBMFFConstants.FULL_BOX_FLAGS_LENGTH,e.boxTypes[0]){case"vide":i=e.FULL_BOX_TYPES.encv;break;case"soun":i=e.FULL_BOX_TYPES.enca}return i?t+=4:-1},encv:function(t){return t+=6+2+2+2+12+2+2+4+4+4+2+32+2+2},enca:function(t){return t+=6+2+8+2+2+2+2+4}},this.context=t}return t.prototype.readBoxSize=function(t,e){var i=t.getUint32(e);return i<=0?null:i},t.prototype.readBoxType=function(t,e){for(var i="",r=0;r<a.ISOBMFFConstants.BOX_TYPE_LENGTH;r++){var s=t.getInt8(e+r);i+=String.fromCharCode(s)}return i},t.prototype.parse=function(t,e){for(var i,r,s=0,n=e,o=[],h=new DataView(t);s+3<h.byteLength;){if(r=this.readBoxSize(h,s),i=this.readBoxType(h,s+a.ISOBMFFConstants.BOX_SIZE_LENGTH),null===r)return[];if(i!==n[0])s+=r;else{if(n.length<=1)o.push(t.slice(s,s+r));else{var f=s;if(s+=a.ISOBMFFConstants.BOX_SIZE_LENGTH+a.ISOBMFFConstants.BOX_TYPE_LENGTH,this.FULL_BOX_TYPES.hasOwnProperty(i)){var _=this.FULL_BOX_TYPES[i](s);-1!==_&&(s=_)}var u=s-f;u=r-u;var d=t.slice(s,s+u),p=this.parse(d,e.slice(1));p&&(o=o.concat(p)),s=f}s+=r}}return o.length>0?o:[]},t.prototype.parseHandlerType=function(t){for(var e=["moov","trak","mdia","hdlr"],i=this.parse(t,e),r=[],s=4,n=0;n<i.length;n++){var o=new DataView(i[n]),h=a.ISOBMFFConstants.BOX_SIZE_LENGTH+a.ISOBMFFConstants.BOX_TYPE_LENGTH;h+=a.ISOBMFFConstants.FULL_BOX_VERSION_LENGTH+a.ISOBMFFConstants.FULL_BOX_FLAGS_LENGTH,h+=s,r.push(this.readBoxType(o,h))}return r},t.prototype.extractDefaultKIDFromTenc=function(t){if(!t||!(t instanceof ArrayBuffer)||t.byteLength<1)return null;var e=new DataView(t),i=3,r=1,s=16,n=a.ISOBMFFConstants.BOX_SIZE_LENGTH+a.ISOBMFFConstants.BOX_TYPE_LENGTH;n+=a.ISOBMFFConstants.FULL_BOX_VERSION_LENGTH+a.ISOBMFFConstants.FULL_BOX_FLAGS_LENGTH+i+r;for(var o="",f=0;f<s;f++)o+=h.FormatHelper.intToHex(e.getUint8(n+f));return o},t.prototype.parseDefaultKIDFromBuffer=function(t,e){var i;this.boxTypes=this.parseHandlerType(t),i=f.MimeTypeHelper.isVideo(e)?["moov","trak","mdia","minf","stbl","stsd","encv","sinf","schi","tenc"]:["moov","trak","mdia","minf","stbl","stsd","enca","sinf","schi","tenc"];var r=this.parse(t,i);if(!r||r.length<1)return null;for(var s=[],n=0;n<r.length;n++){var a=this.extractDefaultKIDFromTenc(r[n]);null!==a&&s.push(a)}return s},t.prototype.arrayBufferToString=function(t){return o.ArrayHelper.arrayBufferToAsciiString(t,!0)},t.prototype.getDataFromPsshBox=function(t){if(!t||!(t instanceof ArrayBuffer)||t.byteLength<3)return null;var e={},i=new DataView(t),r=0;r+=a.ISOBMFFConstants.BOX_SIZE_LENGTH+a.ISOBMFFConstants.BOX_TYPE_LENGTH;var s=i.getUint8(r);r+=a.ISOBMFFConstants.FULL_BOX_VERSION_LENGTH+a.ISOBMFFConstants.FULL_BOX_FLAGS_LENGTH;for(var f="",_=0;_<a.ISOBMFFConstants.PSSH_SYSTEM_ID_LENGTH;_++)f+=h.FormatHelper.intToHex(i.getUint8(r)),r++;if(e.systemIDraw=f,e.systemID="urn:uuid:".concat(h.FormatHelper.beautifyUUID(f)),n.DRMSchemeIDURIs.hasOwnProperty(e.systemID)&&(e.systemName=n.DRMSchemeIDURIs[e.systemID]),s>0){var u=i.getUint32(r);r+=4,e.kid=[];for(_=0;_<u;_++){var d=o.ArrayHelper.extractHexString(i,r,a.ISOBMFFConstants.PSSH_KID_LEN);r+=a.ISOBMFFConstants.PSSH_KID_LEN,e.kid.push(d)}}return e.initData=t.slice(0),e.initDataStr=this.arrayBufferToString(e.initData),e},t.prototype.parsePsshBox=function(t){if(t.getMimeType().indexOf("mp4")<0)return null;var e=t.getData();if(!(e&&e instanceof ArrayBuffer))return null;var i=[];if(i=(i=i.concat(this.parse(e,["moov","pssh"]))).concat(this.parse(e,["moof","pssh"])),t.isEncrypted()&&!i.length&&t.isInit()&&(0,_.isTizen2016)())return this.context.eventHandler.fireError(new s.PlayerError(r.ErrorCode.SEGMENT_PSSH_DATA_MISSING)),[];var n=[];if(Array.isArray(i))for(var a=0;a<i.length;a++){var o=this.getDataFromPsshBox(i[a]);null!==o&&n.push(o)}return n},t}();e.MP4EncryptionParser=u},80691:function(t,e){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,i=1,r=arguments.length;i<r;i++)for(var s in e=arguments[i])Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.ISOBox=e.ISOFile=e.ISOBoxer=void 0;var r=["moov","trak","tref","mdia","minf","stbl","edts","dinf","sinf","mvex","moof","traf","mfra","udta","meco","strk","vttc","schi"];e.ISOBoxer={parseBuffer:function(t,i){return void 0===i&&(i={}),new e.ISOFile(t,i).parse()}};var s=8;function n(t){return void 0===t&&(t=0),{offset:t}}var a=function(t,e){void 0===e&&(e={}),this.options=i({},e),this.options.iv_size=e.iv_size||s,t&&(this._raw=new DataView(t)),this._cursor=n(),this.boxes=[]};function o(t){return 0===t.length?null:t[0]}function h(t,e){var i=e.indexOf("."),r=[];if(-1===i)for(var s=0;s<t.length;s++)t[s].type===e&&r.push(t[s]);else{var n=e.substring(0,i),a=e.substring(i+1);for(s=0;s<t.length;s++)t[s].type===n&&(r=r.concat(t[s].list(a)))}return r}e.ISOFile=a,e.ISOFile.prototype.fetch=function(t){var e=this.fetchAll(t,!0);return e.length?e[0]:null},e.ISOFile.prototype.fetchAll=function(t,i){var r=[];return e.ISOFile._sweep.call(this,t,r,i),r},e.ISOFile.prototype.parse=function(){for(this._cursor.offset=0,this.boxes=[];this._cursor.offset<this._raw.byteLength;){var t=e.ISOBox.parse(this);if(void 0===t.type)break;this.boxes.push(t)}return this},e.ISOFile._sweep=function(t,i,r){for(var s in this.type&&this.type===t&&i.push(this),this.boxes){if(i.length&&r)return;e.ISOFile._sweep.call(this.boxes[s],t,i,r)}},e.ISOFile.prototype.get=function(t){return o(this.list(t))},e.ISOFile.prototype.list=function(t){return h(this.boxes,t)};var f=function(){this._cursor=n()};function _(t,i){t.forEach((function(t){e.ISOBox.prototype._boxParsers[t]=i}))}function u(t,e){return t._cursor.offset-t._raw.byteOffset+e<=t.size}function d(t){return u(t,6)?{bytes_of_clear_data:t._readUint(16),bytes_of_encrypted_data:t._readUint(32)}:{bytes_of_clear_data:0,bytes_of_encrypted_data:0}}function p(t,e){return u(t,2)?(e.subsample_count=t._readUint(16),e.subsamples=Array.apply(null,Array(e.subsample_count)).map((function(e){return d(t)})),e):i(i({},e),{subsample_count:0,subsamples:[]})}function c(t){if(u(t,t._root.options.iv_size)){var e={iv:t._readHex(t._root.options.iv_size)};return t.flags&&(e=p(t,e)),e}t.samples=[]}e.ISOBox=f,e.ISOBox.parse=function(t){var i=new e.ISOBox;return i._offset=t._cursor.offset,i._root=t._root?t._root:t,i._raw=t._raw,i._parent=t,i._parseBox(),t._cursor.offset=i._raw.byteOffset+i._raw.byteLength,i},e.ISOBox.prototype.list=function(t){return h(this.boxes||this.entries||[],t)},e.ISOBox.prototype.get=function(t){return o(this.list(t))},e.ISOBox.prototype._readUint64=function(){var t=this._raw.getUint32(this._cursor.offset-this._raw.byteOffset),e=this._raw.getUint32(this._cursor.offset-this._raw.byteOffset+4);return t*Math.pow(2,32)+e},e.ISOBox.prototype._readInt64=function(){var t=this._raw.getInt32(this._cursor.offset-this._raw.byteOffset),e=this._raw.getInt32(this._cursor.offset-this._raw.byteOffset+4);return t*Math.pow(2,32)+e},e.ISOBox.prototype._readInt=function(t){var e=null;switch(t){case 8:e=this._raw.getInt8(this._cursor.offset-this._raw.byteOffset);break;case 16:e=this._raw.getInt16(this._cursor.offset-this._raw.byteOffset);break;case 32:e=this._raw.getInt32(this._cursor.offset-this._raw.byteOffset);break;case 64:e=this._readInt64()}return this._cursor.offset+=t>>3,e},e.ISOBox.prototype._readUint=function(t){var e=null;switch(t){case 8:e=this._raw.getUint8(this._cursor.offset-this._raw.byteOffset);break;case 16:e=this._raw.getUint16(this._cursor.offset-this._raw.byteOffset);break;case 24:e=(this._raw.getUint16(this._cursor.offset-this._raw.byteOffset)<<8)+this._raw.getUint8(this._cursor.offset-this._raw.byteOffset+2);break;case 32:e=this._raw.getUint32(this._cursor.offset-this._raw.byteOffset);break;case 64:e=this._readUint64()}return this._cursor.offset+=t>>3,e},e.ISOBox.prototype._readUintArray=function(t,e){for(var i=new Array(e),r=0;r<e;++r)i[r]=this._readUint(t);return i},e.ISOBox.prototype._readFP=function(t){return this._readInt(2*t)/Math.pow(2,t)},e.ISOBox.prototype._readString=function(t){for(var e="",i=0;i<t;i++){var r=this._readUint(8);e+=String.fromCharCode(r)}return e},e.ISOBox.prototype._readHex=function(t){for(var e=[],i=0;i<t;i++){var r=this._readUint(8);e.push((r>>>4).toString(16)),e.push((15&r).toString(16))}return e.join("")},e.ISOBox.prototype._readUUID=function(){return this._readUintArray(8,16)},e.ISOBox.prototype._readTerminatedString=function(){for(var t="";;){var e=this._readUint(8);if(0==e)break;t+=String.fromCharCode(e)}return t},e.ISOBox.prototype._readTemplate=function(t){return this._readUint(t/2)+this._readUint(t/2)/Math.pow(2,t/2)},e.ISOBox.prototype._parseBox=function(){if(this._cursor.offset=this._offset,this._offset+8>this._raw.buffer.byteLength)this._root._incomplete=!0;else{switch(this.size=this._readUint(32),this.type=this._readString(4),1===this.size&&(this.largesize=this._readUint(64)),"uuid"===this.type&&(this.usertype=this._readUUID()),this.size){case 0:this._raw=new DataView(this._raw.buffer,this._offset,this._raw.byteLength-this._cursor.offset);break;case 1:this._offset+this.size>this._raw.buffer.byteLength?(this._incomplete=!0,this._root._incomplete=!0):this._raw=new DataView(this._raw.buffer,this._offset,this.largesize);break;default:this._offset+this.size>this._raw.buffer.byteLength?(this._incomplete=!0,this._root._incomplete=!0):this._raw=new DataView(this._raw.buffer,this._offset,this.size)}!this._incomplete&&this._boxParsers[this.type]&&this._boxParsers[this.type].call(this)}},e.ISOBox.prototype._parseFullBox=function(){this.version=this._readUint(8),this.flags=this._readUint(24)},e.ISOBox.prototype._createDataView=function(){return new DataView(this._raw.buffer,this._cursor.offset,this._raw.byteLength-(this._cursor.offset-this._offset))},e.ISOBox.prototype._boxParsers={},_(r,(function(){for(this.boxes=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.boxes.push(e.ISOBox.parse(this))})),e.ISOBox.prototype._boxParsers.elst=function(){this._parseFullBox(),this.entry_count=this._readUint(32),this.entries=[];for(var t=1;t<=this.entry_count;t++){var i=new e.ISOBox;1===this.version?(i.segment_duration=this._readUint(64),i.media_time=this._readInt(64)):(i.segment_duration=this._readUint(32),i.media_time=this._readInt(32)),i.media_rate_integer=this._readInt(16),i.media_rate_fraction=this._readInt(16),this.entries.push(i)}},e.ISOBox.prototype._boxParsers.emsg=function(){this._parseFullBox(),0===this.version?(this.scheme_id_uri=this._readTerminatedString(),this.value=this._readTerminatedString(),this.timescale=this._readUint(32),this.presentation_time_delta=this._readUint(32),this.event_duration=this._readUint(32),this.id=this._readUint(32)):(this.timescale=this._readUint(32),this.presentation_time=this._readUint(64),this.event_duration=this._readUint(32),this.id=this._readUint(32),this.scheme_id_uri=this._readTerminatedString(),this.value=this._readTerminatedString()),this.message_data=this._createDataView()},_(["free","mdat"],(function(){this.data=this._createDataView()})),_(["ftyp","styp"],(function(){for(this.major_brand=this._readString(4),this.minor_versions=this._readUint(32),this.compatible_brands=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.compatible_brands.push(this._readString(4))})),e.ISOBox.prototype._boxParsers.hdlr=function(){this._parseFullBox(),this.pre_defined=this._readUint(32),this.handler_type=this._readString(4),this.reserved=[this._readUint(32),this._readUint(32),this._readUint(32)],this.name=this._readTerminatedString()},e.ISOBox.prototype._boxParsers.mdhd=function(){this._parseFullBox(),1==this.version?(this.creation_time=this._readUint(64),this.modification_time=this._readUint(64),this.timescale=this._readUint(32),this.duration=this._readUint(64)):(this.creation_time=this._readUint(32),this.modification_time=this._readUint(32),this.timescale=this._readUint(32),this.duration=this._readUint(32));var t=this._readUint(16);this.pad=t>>15,this.language=String.fromCharCode(96+(t>>10&31),96+(t>>5&31),96+(31&t)),this.pre_defined=this._readUint(16)},e.ISOBox.prototype._boxParsers.mfhd=function(){this._parseFullBox(),this.sequence_number=this._readUint(32)},e.ISOBox.prototype._boxParsers.mvhd=function(){this._parseFullBox(),1===this.version?(this.creation_time=this._readUint(64),this.modification_time=this._readUint(64),this.timescale=this._readUint(32),this.duration=this._readUint(64)):(this.creation_time=this._readUint(32),this.modification_time=this._readUint(32),this.timescale=this._readUint(32),this.duration=this._readUint(32)),this.rate=this._readTemplate(32),this.volume=this._readTemplate(16),this.reserved1=this._readUint(16),this.reserved2=[this._readUint(32),this._readUint(32)],this.matrix=[];for(var t=0;t<9;t++)this.matrix.push(this._readTemplate(32));for(this.pre_defined=[],t=0;t<6;t++)this.pre_defined.push(this._readUint(32));this.next_track_ID=this._readUint(32)},e.ISOBox.prototype._boxParsers.sidx=function(){this._parseFullBox(),this.reference_ID=this._readUint(32),this.timescale=this._readUint(32),0===this.version?(this.earliest_presentation_time=this._readUint(32),this.first_offset=this._readUint(32)):(this.earliest_presentation_time=this._readUint(64),this.first_offset=this._readUint(64)),this.reserved=this._readUint(16),this.reference_count=this._readUint(16),this.references=[];for(var t=0;t<this.reference_count;t++){var e={},i=this._readUint(32);e.reference_type=i>>31&1,e.referenced_size=2147483647&i,e.subsegment_duration=this._readUint(32);var r=this._readUint(32);e.starts_with_SAP=r>>31&1,e.SAP_type=r>>28&7,e.SAP_delta_time=268435455&r,this.references.push(e)}},e.ISOBox.prototype._boxParsers.ssix=function(){this._parseFullBox(),this.subsegment_count=this._readUint(32),this.subsegments=[];for(var t=0;t<this.subsegment_count;t++){var e={};e.ranges_count=this._readUint(32),e.ranges=[];for(var i=0;i<e.ranges_count;i++){var r={};r.level=this._readUint(8),r.range_size=this._readUint(24),e.ranges.push(r)}this.subsegments.push(e)}},e.ISOBox.prototype._boxParsers.tfdt=function(){this._parseFullBox(),1===this.version?this.baseMediaDecodeTime=this._readUint(64):this.baseMediaDecodeTime=this._readUint(32)},e.ISOBox.prototype._boxParsers.tfhd=function(){this._parseFullBox(),this.track_ID=this._readUint(32),1&this.flags?(this.base_data_offset_present=!0,this.base_data_offset=this._readUint(64)):131072&this.flags&&(this.default_base_is_moof=!0),2&this.flags&&(this.sample_description_offset=this._readUint(32)),8&this.flags&&(this.default_sample_duration=this._readUint(32)),16&this.flags&&(this.default_sample_size=this._readUint(32)),32&this.flags&&(this.default_sample_flags=this._readUint(32))},e.ISOBox.prototype._boxParsers.tkhd=function(){this._parseFullBox(),1===this.version?(this.creation_time=this._readUint(64),this.modification_time=this._readUint(64),this.track_ID=this._readUint(32),this.reserved1=this._readUint(32),this.duration=this._readUint(64)):(this.creation_time=this._readUint(32),this.modification_time=this._readUint(32),this.track_ID=this._readUint(32),this.reserved1=this._readUint(32),this.duration=this._readUint(32)),this.reserved2=[this._readUint(32),this._readUint(32)],this.layer=this._readUint(16),this.alternate_group=this._readUint(16),this.volume=this._readTemplate(16),this.reserved3=this._readUint(16),this.matrix=[];for(var t=0;t<9;t++)this.matrix.push(this._readTemplate(32));this.width=this._readTemplate(32),this.height=this._readTemplate(32)},e.ISOBox.prototype._boxParsers.trun=function(){this._parseFullBox(),this.sample_count=this._readUint(32),1&this.flags&&(this.data_offset_present=!0,this.data_offset=this._readInt(32)),4&this.flags&&(this.first_sample_flags=this._readUint(32)),this.samples=[];for(var t=0;t<this.sample_count;t++){var e={};256&this.flags&&(e.sample_duration=this._readUint(32)),512&this.flags&&(e.sample_size=this._readUint(32)),1024&this.flags&&(e.sample_flags=this._readUint(32)),2048&this.flags&&(0==this.version?e.sample_composition_time_offset=this._readUint(32):e.sample_composition_time_offset=this._readInt(32)),this.samples.push(e)}},e.ISOBox.prototype._boxParsers.trex=function(){this._parseFullBox(),this.track_ID=this._readUint(32),this.default_sample_description_index=this._readUint(32),this.default_sample_duration=this._readUint(32),this.default_sample_size=this._readUint(32),this.default_sample_flags=this._readUint(32)},e.ISOBox.prototype._boxParsers.stsd=function(){for(this._parseFullBox(),this.entry_count=this._readUint(32),this.entries=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.entries.push(e.ISOBox.parse(this))},e.ISOBox.prototype._boxParsers.encv=function(){for(this._readHex(6),this.data_reference_index=this._readUint(16),this._readUint(16),this._readUint(16),this._readUint(32),this._readUint(32),this._readUint(32),this.width=this._readUint(16),this.height=this._readUint(16),this.horizresolution=this._readTemplate(32),this.vertresolution=this._readTemplate(32),this._readUint(32),this.frame_count=this._readUint(16),this._readUint(8),this.compressor_name=this._readString(4),this.depth=this._readUint(16),this._readUint(16),this._readHex(27),this.boxes=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.boxes.push(e.ISOBox.parse(this))},_(["enca","mp4a"],(function(){for(this._readHex(6),this.data_reference_index=this._readUint(16),this._readUint(32),this._readUint(32),this.channel_count=this._readUint(16),this.sample_size=this._readUint(16),this._readUint(16),this._readUint(16),this.sample_rate=this._readUint(32)>>>16,this.boxes=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.boxes.push(e.ISOBox.parse(this))})),_(["avc1","hvc1","hev1"],(function(){this._readUint(32),this._readUint(16),this.data_reference_index=this._readUint(16),this.version=this._readUint(16),this.revision_level=this._readUint(16),this.vendor=this._readUint(32),this.temporal_quality=this._readUint(32),this.spatial_quality=this._readUint(32),this.width=this._readUint(16),this.height=this._readUint(16),this.horizontal_resolution=this._readTemplate(32),this.vertical_resolution=this._readTemplate(32),this.data_size=this._readUint(32),this.frame_count=this._readUint(16);var t=this._readUint(8);this.compressor_name="";for(var i=0;i<31;i++){var r=this._readUint(8);i<t&&(this.compressor_name+=String.fromCharCode(r))}for(this.depth=this._readUint(16),this._readInt(16),this.boxes=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.boxes.push(e.ISOBox.parse(this))})),e.ISOBox.prototype._boxParsers.avcC=function(){this.configuration_version=this._readUint(8),this.avc_profile_indication=this._readUint(8),this.profile_compatibility=this._readUint(8),this.avc_level_indication=this._readUint(8);var t=this._readUint(8);this.complete_representation=t>>>7&1,this.length_size_minus_one=3&t,this.num_sps=31&this._readUint(8),this.sps=[];for(var e=0;e<this.num_sps;++e)this.sps.push(this._readUintArray(8,this._readUint(16)));this.num_pps=31&this._readUint(8),this.pps=[];for(e=0;e<this.num_pps;++e)this.pps.push(this._readUintArray(8,this._readUint(16)))},e.ISOBox.prototype._boxParsers.hvcC=function(){var t;this.configurationVersion=this._readUint(8),t=this._readUint(8),this.general_profile_space=t>>>6,this.general_tier_flag=t>>>5&1,this.general_profile_idc=31&t,this.general_profile_compatibility_flags=this._readUint(32),this.general_constraint_indicator_flags_bytes=[];for(var e=0;e<6;e++)this.general_constraint_indicator_flags_bytes[e]=this._readUint(8);this.general_level_idc=this._readUint(8),this.min_spatial_segmentation_idc=4095&this._readUint(16),this.parallelismType=3&this._readUint(8),this.chroma_format_idc=3&this._readUint(8),this.bit_depth_luma_minus8=7&this._readUint(8),this.bit_depth_chroma_minus8=7&this._readUint(8),this.avgFrameRate=this._readUint(16),t=this._readUint(8),this.constantFrameRate=t>>>6,this.numTemporalLayers=t>>>3&7,this.temporalIdNested=t>>>2&1,this.lengthSizeMinusOne=3&t,this.numOfArrays=this._readUint(8)},e.ISOBox.prototype._boxParsers.esds=function(){this._parseFullBox(),this._readUint(16),this.esId=this._readUint(16),this.streamPriority=31&this._readUint(8),this._readUint(16),this.decoderConfig={},this.decoderConfig.objectProfileIndication=this._readUint(8),this.decoderConfig.streamType=this._readUint(8)>>>2&63,this.decoderConfig.bufferSize=this._readUint(24),this.decoderConfig.maxBitrate=this._readUint(32),this.decoderConfig.avgBitrate=this._readUint(32),this.decoderConfig.decoderConfigDescriptor={},this.decoderConfig.decoderConfigDescriptor.tag=this._readUint(8),this.decoderConfig.decoderConfigDescriptor.length=this._readUint(8);var t=this._readUint(8),e=this._readUint(8),i=this.decoderConfig.decoderConfigDescriptor;i.audioObjectType=t>>>3&31,i.samplingFrequencyIndex=(7&t)<<1|e>>>7&1,i.channelConfiguration=e>>>3&15},e.ISOBox.prototype._boxParsers.tenc=function(){this._parseFullBox(),this.default_is_encrypted=1===this._readUint(24),this.default_iv_size=this._readUint(8),this.default_kid=this._readHex(16)},e.ISOBox.prototype._boxParsers.senc=function(){var t=this;this._parseFullBox(),this.sample_count=this._readUint(32),this.samples=Array.apply(null,Array(this.sample_count)).map((function(e){return c(t)}))},e.ISOBox.prototype._boxParsers.frma=function(){this.codingname=this._readString(4)},e.ISOBox.prototype._boxParsers.schm=function(){this._parseFullBox(),this.scheme_type=this._readString(4),this.scheme_version=this._readUint(32),1&this.flags&&(this.scheme_uri=this._readUint(8))},e.ISOBox.prototype._boxParsers.saiz=function(){if(this._parseFullBox(),1&this.flags&&(this.aux_info_type=this._readUint(32),this.aux_info_type_parameter=this._readUint(32)),this.default_sample_info_size=this._readUint(8),this.sample_count=this._readUint(32),0===this.default_sample_info_size){this.sample_info_size=[];for(var t=0;t<this.sample_count;++t)this.sample_info_size.push(this._readUint(8))}},e.ISOBox.prototype._boxParsers.pssh=function(){if(this._parseFullBox(),this.system_id=this._readUUID(),this.version>0){this.kid_count=this._readUint(32),this.kids=[];for(var t=0;t<this.kid_count;++t)this.kids.push(this._readString(16))}this.data_size=this._readUint(32),this.data=new DataView(this._raw.buffer,this._cursor.offset,this.data_size)},e.ISOBox.prototype._boxParsers.prft=function(){this._parseFullBox(),this.reference_track_ID=this._readUint(32),this.ntp_timestamp_seconds=this._readUint(32),this.ntp_timestamp_seconds_fraction=this._readUint(32),0===this.version?this.media_time=this._readUint(32):this.media_time=this._readUint(64)}}},function(t){return function(e){return t(t.s=e)}(16032)}])}));
})();
