/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.polyfill=e():(t.bitmovin=t.bitmovin||{},t.bitmovin.player=t.bitmovin.player||{},t.bitmovin.player.polyfill=e())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[538],{5029:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.ArrayFindPolyfill=void 0,e.ArrayFindPolyfill=function(){var t=function(){Array.prototype.find=function(t){if(null==this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var e,n=Object(this),r=n.length>>>0,o=arguments[1],i=0;i<r;i++)if(e=n[i],t.call(o,e,i,n))return e}};return{polyfill:t}}()},11585:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.ArrayFlatMapPolyfill=void 0;var n=function(){function t(){}return t.polyfill=function(){Object.defineProperty(Array.prototype,"flatMap",{value:function(t){return Array.prototype.concat.apply([],this.map(t))}})},t}();e.ArrayFlatMapPolyfill=n},13801:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.StringIncludesPolyfill=void 0,e.StringIncludesPolyfill=function(){return{polyfill:function(){String.prototype.includes=function(t,e){return"number"!=typeof e&&(e=0),!(e+t.length>this.length)&&-1!==this.indexOf(t,e)}}}}()},16051:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.ArrayFindIndexPolyfill=void 0,e.ArrayFindIndexPolyfill=function(){var t=function(){Object.defineProperty(Array.prototype,"findIndex",{value:function(t){if(null==this)throw new TypeError('"this" is null or not defined');var e=Object(this),n=e.length>>>0;if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var r=arguments[1],o=0;o<n;){var i=e[o];if(t.call(r,i,o,e))return o;o++}return-1},configurable:!0,writable:!0})};return{polyfill:t}}()},23523:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.Polyfills=void 0;var r=n(51620),o=n(16051),i=n(5029),l=n(11585),u=n(68367),f=n(31630),a=n(54917),c=n(37038),s=n(74005),y=n(64211),p=n(97986),d=n(70520),h=n(94678),v=n(74338),_=n(13801),b=n(41921),w=n(87129);e.Polyfills={installPolyfills:function(){window.bitmovin=window.bitmovin||{},window&&window.Promise&&"function"==typeof window.Promise||f.PromisePolyfill.polyfill(),h.default.polyfill(),window&&window.ArrayBuffer&&!window.ArrayBuffer.prototype.slice&&r.ArrayBufferPolyfill.polyfill(),window.WeakMap&&"function"==typeof window.WeakMap||w.WeakMapPolyfill.polyfill(),window.Map&&"function"==typeof window.Map||a.MapPolyfill.polyfill(),Array.prototype.find||i.ArrayFindPolyfill.polyfill(),Array.prototype.includes||u.ArrayIncludesPolyfill.polyfill(),Array.prototype.findIndex||o.ArrayFindIndexPolyfill.polyfill(),Array.prototype.flatMap||l.ArrayFlatMapPolyfill.polyfill(),String.prototype.includes||_.StringIncludesPolyfill.polyfill(),String.prototype.startsWith||b.StringStartsWithPolyfill.polyfill(),String.prototype.endsWith||v.StringEndsWithPolyfill.polyfill(),window.NodeList&&!NodeList.prototype.forEach&&s.NodeListPolyfill.polyfill(),(window.Node&&!Node.prototype.hasOwnProperty("children")||window.Element&&!Element.prototype.hasOwnProperty("children"))&&c.NodeChildrenPolyfill.polyfill(),Object.values||d.ObjectValuesPolyfill.polyfill(),Object.entries||p.ObjectEntriesPolyfill.polyfill(),Object.assign||y.ObjectAssignPolyfill.polyfill(),(window.MediaSource||window.WebKitMediaSource)&&(window.MediaSource=window.MediaSource||window.WebKitMediaSource),Number.isNaN||(Number.isNaN=function(t){return"number"==typeof t&&isNaN(t)})}}},31630:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.PromisePolyfill=void 0,e.PromisePolyfill=function(){function t(t){return"function"==typeof t||"object"==typeof t&&null!==t}function e(t){return"function"==typeof t}function n(t){return"object"==typeof t&&null!==t}var r,o=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},i=0,l=function(t,e){v[i]=t,v[i+1]=e,2===(i+=2)&&(r?r(_):h())};function u(t){r=t}function f(t){l=t}var a=("undefined"!=typeof window?window:void 0)||{},c=a.MutationObserver||a.WebKitMutationObserver,s="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function y(){var t=0,e=new c(_),n=document.createTextNode("");return e.observe(n,{characterData:!0}),function(){n.data=t=++t%2}}function p(){var t=new MessageChannel;return t.port1.onmessage=_,function(){t.port2.postMessage(0)}}function d(){return function(){setTimeout(_,1)}}var h,v=new Array(1e3);function _(){for(var t=0;t<i;t+=2){(0,v[t])(v[t+1]),v[t]=void 0,v[t+1]=void 0}i=0}function b(){}h=c?y():s?p():d();var w=void 0,P=1,g=2,j=new F;function m(){return new TypeError("You cannot resolve a promise with itself")}function O(){return new TypeError("A promises callback cannot return that same promise.")}function M(t){try{return t.then}catch(t){return j.error=t,j}}function A(t,e,n,r){try{t.call(e,n,r)}catch(t){return t}}function k(t,e,n){l((function(t){var r=!1,o=A(n,e,(function(n){r||(r=!0,e!==n?S(t,n):W(t,n))}),(function(e){r||(r=!0,T(t,e))}),"Settle: "+(t._label||" unknown promise"));!r&&o&&(r=!0,T(t,o))}),t)}function E(t,e){e._state===P?W(t,e._result):e._state===g?T(t,e._result):I(e,void 0,(function(e){S(t,e)}),(function(e){T(t,e)}))}function N(t,n){if(n.constructor===t.constructor)E(t,n);else{var r=M(n);r===j?T(t,j.error):void 0===r?W(t,n):e(r)?k(t,n,r):W(t,n)}}function S(e,n){e===n?T(e,m()):t(n)?N(e,n):W(e,n)}function x(t){t._onerror&&t._onerror(t._result),C(t)}function W(t,e){t._state===w&&(t._result=e,t._state=P,0!==t._subscribers.length&&l(C,t))}function T(t,e){t._state===w&&(t._state=g,t._result=e,l(x,t))}function I(t,e,n,r){var o=t._subscribers,i=o.length;t._onerror=null,o[i]=e,o[i+P]=n,o[i+g]=r,0===i&&t._state&&l(C,t)}function C(t){var e=t._subscribers,n=t._state;if(0!==e.length){for(var r,o,i=t._result,l=0;l<e.length;l+=3)r=e[l],o=e[l+n],r?z(n,r,o,i):o(i);t._subscribers.length=0}}function F(){this.error=null}var B=new F;function L(t,e){try{return t(e)}catch(t){return B.error=t,B}}function z(t,n,r,o){var i,l,u,f,a=e(r);if(a){if((i=L(r,o))===B?(f=!0,l=i.error,i=null):u=!0,n===i)return void T(n,O())}else i=o,u=!0;n._state!==w||(a&&u?S(n,i):f?T(n,l):t===P?W(n,i):t===g&&T(n,i))}function U(t,e){try{e((function(e){S(t,e)}),(function(e){T(t,e)}))}catch(e){T(t,e)}}function V(t,e){var n=this;n._instanceConstructor=t,n.promise=new t(b),n._validateInput(e)?(n._input=e,n.length=e.length,n._remaining=e.length,n._init(),0===n.length?W(n.promise,n._result):(n.length=n.length||0,n._enumerate(),0===n._remaining&&W(n.promise,n._result))):T(n.promise,n._validationError())}V.prototype._validateInput=function(t){return o(t)},V.prototype._validationError=function(){return new Error("Array Methods must be provided an Array")},V.prototype._init=function(){this._result=new Array(this.length)};var D=V;function H(t){return new D(this,t).promise}V.prototype._enumerate=function(){for(var t=this,e=t.length,n=t.promise,r=t._input,o=0;n._state===w&&o<e;o++)t._eachEntry(r[o],o)},V.prototype._eachEntry=function(t,e){var r=this,o=r._instanceConstructor;n(t)?t.constructor===o&&t._state!==w?(t._onerror=null,r._settledAt(t._state,e,t._result)):r._willSettleAt(o.resolve(t),e):(r._remaining--,r._result[e]=t)},V.prototype._settledAt=function(t,e,n){var r=this,o=r.promise;o._state===w&&(r._remaining--,t===g?T(o,n):r._result[e]=n),0===r._remaining&&W(o,r._result)},V.prototype._willSettleAt=function(t,e){var n=this;I(t,void 0,(function(t){n._settledAt(P,e,t)}),(function(t){n._settledAt(g,e,t)}))};var K=H;function Y(t){var e=this,n=new e(b);if(!o(t))return T(n,new TypeError("You must pass an array to race.")),n;var r=t.length;function i(t){S(n,t)}function l(t){T(n,t)}for(var u=0;n._state===w&&u<r;u++)I(e.resolve(t[u]),void 0,i,l);return n}var R=Y;function q(t){var e=this;if(t&&"object"==typeof t&&t.constructor===e)return t;var n=new e(b);return S(n,t),n}var G=q;function J(t){var e=new this(b);return T(e,t),e}var Q=J,X=0;function Z(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function $(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}var tt=et;function et(t){this._id=X++,this._state=void 0,this._result=void 0,this._subscribers=[],b!==t&&(e(t)||Z(),this instanceof et||$(),U(this,t))}function nt(){var t=window.Promise;t&&"[object Promise]"===Object.prototype.toString.call(t.resolve())&&!t.cast||(window.Promise=tt)}return et.all=K,et.race=R,et.resolve=G,et.reject=Q,et._setScheduler=u,et._setAsap=f,et._asap=l,et.prototype={constructor:et,then:function(t,e){var n=this,r=n._state;if(r===P&&!t||r===g&&!e)return this;var o=new this.constructor(b),i=n._result;if(r){var u=arguments[r-1];l((function(){z(r,o,u,i)}))}else I(n,o,t,e);return o},catch:function(t){return this.then(null,t)}},{Promise:tt,polyfill:nt}}.call(this)},37038:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.NodeChildrenPolyfill=void 0,e.NodeChildrenPolyfill=function(){return{polyfill:function(){!function(t){t&&t.prototype&&null==t.prototype.children&&Object.defineProperty(t.prototype,"children",{get:function(){var t,e=0,n=this.childNodes,r=[];if(!n)return r;for(;t=n[e++];)1===t.nodeType&&r.push(t);return r}})}(window.Node||window.Element)}}}()},41921:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.StringStartsWithPolyfill=void 0,e.StringStartsWithPolyfill=function(){return{polyfill:function(){String.prototype.startsWith=function(t,e){var n=e>0?0|e:0;return this.substring(n,n+t.length)===t}}}}()},42638:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.PolyfillModuleDefinition=void 0;var r=n(16368),o=n(23523);e.PolyfillModuleDefinition={name:r.ModuleName.Polyfill,module:{Polyfills:o.Polyfills},hooks:{add:function(t){t.Polyfills.installPolyfills()}}},e.default=e.PolyfillModuleDefinition},51620:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.ArrayBufferPolyfill=void 0,e.ArrayBufferPolyfill=function(){function t(t,e){return(t=0|t||0)<0?Math.max(t+e,0):Math.min(t,e)}return{polyfill:function(){ArrayBuffer.prototype.slice=function(e,n){var r=this.byteLength,o=t(e,r),i=r;if(void 0!==n&&(i=t(n,r)),o>i)return new ArrayBuffer(0);var l=i-o,u=new ArrayBuffer(l),f=new Uint8Array(u),a=new Uint8Array(this,o,l);return f.set(a),u}}}}()},54917:function(t,e){function n(){var t,e=Object.defineProperty,n=function(t,e){return t===e||t!=t&&e!=e};function r(t){function n(t){if(!this||this.constructor!==n)return new(n(t));this._keys=[],this._values=[],this._itp=[],t&&o.call(this,t)}return e(t,"size",{get:h}),t.constructor=n,n.prototype=t,n}function o(t){this.add?t.forEach(this.add,this):t.forEach((function(t){this.set(t[0],t[1])}),this)}function i(e){return this.has(e)&&(this._keys.splice(t,1),this._values.splice(t,1),this._itp.forEach((function(e){t<e[0]&&e[0]--}))),-1<t}function l(e){return this.has(e)?this._values[t]:void 0}function u(e,r){if(r!=r||0===r)for(t=e.length;t--&&!n(e[t],r););else t=e.indexOf(r);return-1<t}function f(t){return u.call(this,this._keys,t)}function a(e,n){return this.has(e)?this._values[t]=n:this._values[this._keys.push(e)-1]=n,this}function c(){(this._keys||0).length=this._values.length=0}function s(){return d(this._itp,this._keys)}function y(){return d(this._itp,this._values)}function p(){return d(this._itp,this._keys,this._values)}function d(t,e,n){var r=[0],o=!1;return t.push(r),{next:function(){var i,l=r[0];return!o&&l<e.length?(i=n?[e[l],n[l]]:e[l],r[0]++):(o=!0,t.splice(t.indexOf(r),1)),{done:o,value:i}}}}function h(){return this._values.length}function v(t,e){for(var n=this.entries();;){var r=n.next();if(r.done)break;t.call(e,r.value[1],r.value[0],this)}}window.Map=r({delete:i,has:f,get:l,set:a,keys:s,values:y,entries:p,forEach:v,clear:c})}Object.defineProperty(e,"__esModule",{value:!0}),e.MapPolyfill=void 0,e.MapPolyfill={polyfill:n}},64211:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.ObjectAssignPolyfill=void 0,e.ObjectAssignPolyfill=function(){var t=function(){Object.defineProperty(Object,"assign",{value:function(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(t),r=1;r<arguments.length;r++){var o=arguments[r];if(null!=o)for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])}return n},writable:!0,configurable:!0})};return{polyfill:t}}()},68367:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.ArrayIncludesPolyfill=void 0,e.ArrayIncludesPolyfill=function(){return{polyfill:function(){Object.defineProperty(Array.prototype,"includes",{value:function(t,e){if(null==this)throw new TypeError('"this" is null or not defined');var n=Object(this),r=n.length>>>0;if(0===r)return!1;var o=0|e,i=Math.max(o>=0?o:r-Math.abs(o),0);function l(t,e){return t===e||"number"==typeof t&&"number"==typeof e&&isNaN(t)&&isNaN(e)}for(;i<r;){if(l(n[i],t))return!0;i++}return!1}})}}}()},70520:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.ObjectValuesPolyfill=void 0,e.ObjectValuesPolyfill=function(){return{polyfill:function(){Object.values=function(t){return Object.keys(t).map((function(e){return t[e]}))}}}}()},74005:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.NodeListPolyfill=void 0,e.NodeListPolyfill=function(){return{polyfill:function(){NodeList.prototype.forEach=function(t,e){e=e||window;for(var n=0;n<this.length;n++)t.call(e,this[n],n,this)}}}}()},74338:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.StringEndsWithPolyfill=void 0,e.StringEndsWithPolyfill=function(){return{polyfill:function(){String.prototype.endsWith=function(t,e){if(null==t)return!1;var n=this.length;return null!=e&&(n=Math.min(e,this.length)),this.substring(n-t.length,n)===t}}}}()},87129:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.WeakMapPolyfill=void 0;var n=function(){};!function(){function t(e){e.permitHostObjects___&&e.permitHostObjects___(t)}var e,r=!1;if("function"==typeof WeakMap)if(e=WeakMap,"undefined"!=typeof navigator&&/Firefox/.test(navigator.userAgent));else{var o=new e,i=Object.freeze({});if(o.set(i,1),1===o.get(i))return void(n=function(){});r=!0}var l=Object.getOwnPropertyNames,u=Object.defineProperty,f=Object.isExtensible,a="weakmap:",c=a+"ident:"+Math.random()+"___";if("undefined"!=typeof crypto&&"function"==typeof crypto.getRandomValues&&"function"==typeof ArrayBuffer&&"function"==typeof Uint8Array){var s=new ArrayBuffer(25),y=new Uint8Array(s);crypto.getRandomValues(y),c=a+"rand:"+Array.prototype.map.call(y,(function(t){return(t%36).toString(36)})).join("")+"___"}function p(t){return!(t.substr(0,a.length)==a&&"___"===t.substr(t.length-3))}var d="object"==typeof window?Object.getOwnPropertyNames(window):[];if(u(Object,"getOwnPropertyNames",{value:function(t){if("[object Window]"===Object.prototype.toString.call(t))try{return l(t)}catch(t){return[].concat([],d)}return l(t).filter(p)}}),"getPropertyNames"in Object){var h=Object.getPropertyNames;u(Object,"getPropertyNames",{value:function(t){return h(t).filter(p)}})}function v(t){if(t!==Object(t))throw new TypeError("Not an object: "+t);var e=t[c];if(e&&e.key===t)return e;if(f(t)){e={key:t};try{return u(t,c,{value:e,writable:!1,enumerable:!1,configurable:!1}),e}catch(t){return}}}function _(t){return t.prototype=null,Object.freeze(t)}!function(){var t=Object.freeze;u(Object,"freeze",{value:function(e){return v(e),t(e)}});var e=Object.seal;u(Object,"seal",{value:function(t){return v(t),e(t)}});var n=Object.preventExtensions;u(Object,"preventExtensions",{value:function(t){return v(t),n(t)}})}();var b=!1;function w(){b||"undefined"==typeof console||(b=!0,console.warn("WeakMap should be invoked as new WeakMap(), not WeakMap(). This will be an error in the future."))}var P=0,g=function(){this instanceof g||w();var t=[],e=[],n=P++;function r(r,o){var i,l=v(r);return l?n in l?l[n]:o:(i=t.indexOf(r))>=0?e[i]:o}function o(e){var r=v(e);return r?n in r:t.indexOf(e)>=0}function i(r,o){var i,l=v(r);return l?l[n]=o:(i=t.indexOf(r))>=0?e[i]=o:(i=t.length,e[i]=o,t[i]=r),this}function l(r){var o,i,l=v(r);return l?n in l&&delete l[n]:!((o=t.indexOf(r))<0)&&(i=t.length-1,t[o]=void 0,e[o]=e[i],t[o]=t[i],t.length=i,e.length=i,!0)}return Object.create(g.prototype,{get___:{value:_(r)},has___:{value:_(o)},set___:{value:_(i)},delete___:{value:_(l)}})};g.prototype=Object.create(Object.prototype,{get:{value:function(t,e){return this.get___(t,e)},writable:!0,configurable:!0},has:{value:function(t){return this.has___(t)},writable:!0,configurable:!0},set:{value:function(t,e){return this.set___(t,e)},writable:!0,configurable:!0},delete:{value:function(t){return this.delete___(t)},writable:!0,configurable:!0}}),"function"==typeof e?function(){function o(){this instanceof g||w();var n,o=new e,i=void 0,l=!1;function u(t,e){return i?o.has(t)?o.get(t):i.get___(t,e):o.get(t,e)}function f(t){return o.has(t)||!!i&&i.has___(t)}function a(t){var e=!!o.delete(t);return i&&i.delete___(t)||e}return n=r?function(t,e){return o.set(t,e),o.has(t)||(i||(i=g()),i.set(t,e)),this}:function(t,e){if(l)try{o.set(t,e)}catch(n){i||(i=g()),i.set___(t,e)}else o.set(t,e);return this},Object.create(g.prototype,{get___:{value:_(u)},has___:{value:_(f)},set___:{value:_(n)},delete___:{value:_(a)},permitHostObjects___:{value:_((function(e){if(e!==t)throw new Error("bogus call to permitHostObjects___");l=!0}))}})}r&&"undefined"!=typeof Proxy&&(Proxy=void 0),o.prototype=g.prototype,n=function(){window.WeakMap=o},Object.defineProperty(WeakMap.prototype,"constructor",{value:WeakMap,enumerable:!1,configurable:!0,writable:!0})}():("undefined"!=typeof Proxy&&(Proxy=void 0),n=function(){window.WeakMap=g})}(),e.WeakMapPolyfill={polyfill:n}},94678:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default={polyfill:function(){window.Promise.prototype.finally&&"function"==typeof window.Promise.prototype.finally||(window.Promise.prototype.finally=function(t){var e=this.constructor;return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){throw n}))}))})}}},97986:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.ObjectEntriesPolyfill=void 0,e.ObjectEntriesPolyfill=function(){return{polyfill:function(){Object.entries=function(t){for(var e=Object.keys(t),n=e.length,r=new Array(n);n--;)r[n]=[e[n],t[e[n]]];return r}}}}()}},function(t){return function(e){return t(t.s=e)}(42638)}])}));
})();
