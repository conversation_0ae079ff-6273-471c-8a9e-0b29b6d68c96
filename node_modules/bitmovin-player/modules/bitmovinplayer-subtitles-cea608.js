/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports["subtitles-cea608"]=e():(t.bitmovin=t.bitmovin||{},t.bitmovin.player=t.bitmovin.player||{},t.bitmovin.player["subtitles-cea608"]=e())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[886],{8824:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Cea608Processor=void 0;var s=i(30251),n=i(52473),o=function(){function t(t){var e=this;this.captions=[],this.pushCaptions=function(t){e.flushedCaptions=!1,t.forEach((function(t){return e.captionStream.push(t)}))},this.setupCaptionStreamEventHandlers=function(){e.captionStream.on("data",e.onCaptionStreamData),e.captionStream.on("done",e.onCaptionStreamDone)},this.onCaptionStreamData=function(t){e.captions.push((0,n.assembleCaption)(t))},this.onCaptionStreamDone=function(){if(!e.flushedCaptions)if(e.flushedCaptions=!0,e.onCaptionsAvailable(e.captions),e.captions=[],e.jobQueue.length>0){var t=e.jobQueue[0];e.process(t.captionPackets)}else e.isProcessing=!1},this.parseCea708Caption=t,this.captions=[],this.jobQueue=[],this.isProcessing=!1,this.captionStream=new s.CaptionStream({parse708captions:t}),this.setupCaptionStreamEventHandlers()}return t.prototype.getCaptions=function(t){var e=this;return new Promise((function(i,s){(!t||!Array.isArray(t)||t.length<1)&&s(new Error("Could not get captions, no SeiNal provided")),e.jobQueue.push({captionPackets:t,resolve:i,reject:s}),e.isProcessing||(e.isProcessing=!0,e.process(t))}))},t.prototype.process=function(t){this.pushCaptions(t),this.flush()},t.prototype.flush=function(){this.captionStream.flush()},t.prototype.reset=function(){this.captionStream.reset()},t.prototype.onCaptionsAvailable=function(t){var e=this,i=t.filter((function(t){return e.parseCea708Caption?t.channel.includes("708"):!t.channel.includes("708")})),s=this.jobQueue.shift();null==s||s.resolve(i)},t.prototype.dispose=function(){this.captionStream.reset()},t}();e.Cea608Processor=o},30251:function(t){!function(e,i){t.exports=i()}(0,(function(){"use strict";var t=function(){this.init=function(){var t={};this.on=function(e,i){t[e]||(t[e]=[]),t[e]=t[e].concat(i)},this.off=function(e,i){var s;return!!t[e]&&(s=t[e].indexOf(i),t[e]=t[e].slice(),t[e].splice(s,1),s>-1)},this.trigger=function(e){var i,s,n,o;if(i=t[e])if(2===arguments.length)for(n=i.length,s=0;s<n;++s)i[s].call(this,arguments[1]);else{for(o=[],s=arguments.length,s=1;s<arguments.length;++s)o.push(arguments[s]);for(n=i.length,s=0;s<n;++s)i[s].apply(this,o)}},this.dispose=function(){t={}}}};t.prototype.pipe=function(t){return this.on("data",(function(e){t.push(e)})),this.on("done",(function(e){t.flush(e)})),this.on("partialdone",(function(e){t.partialFlush(e)})),this.on("endedtimeline",(function(e){t.endTimeline(e)})),this.on("reset",(function(e){t.reset(e)})),t},t.prototype.push=function(t){this.trigger("data",t)},t.prototype.flush=function(t){this.trigger("done",t)},t.prototype.partialFlush=function(t){this.trigger("partialdone",t)},t.prototype.endTimeline=function(t){this.trigger("endedtimeline",t)},t.prototype.reset=function(t){this.trigger("reset",t)};var e=t,i=4,s=128,n={parseSei:function(t){for(var e=0,n=[],o=0,r=0;e<t.byteLength&&t[e]!==s;){for(;255===t[e];)o+=255,e++;for(o+=t[e++];255===t[e];)r+=255,e++;r+=t[e++];var a=String.fromCharCode(t[e+3],t[e+4],t[e+5],t[e+6]);o===i&&"GA94"===a&&n.push({payloadType:o,payloadSize:r,payload:t.subarray(e,e+r)}),e+=r,o=0,r=0}return n},parseUserData:function(t){return 181!==t.payload[0]||49!=(t.payload[1]<<8|t.payload[2])||"GA94"!==String.fromCharCode(t.payload[3],t.payload[4],t.payload[5],t.payload[6])||3!==t.payload[7]?null:t.payload.subarray(8,t.payload.length-1)},parseCaptionPackets:function(t,e){var i,s,n,o,r=[];if(!(64&e[0]))return r;for(s=31&e[0],i=0;i<s;i++)o={type:3&e[(n=3*i)+2],pts:t},4&e[n+2]&&(o.ccData=e[n+3]<<8|e[n+4],r.push(o));return r},discardEmulationPreventionBytes:function(t){for(var e,i,s=t.byteLength,n=[],o=1;o<s-2;)0===t[o]&&0===t[o+1]&&3===t[o+2]?(n.push(o+2),o+=2):o++;if(0===n.length)return t;e=s-n.length,i=new Uint8Array(e);var r=0;for(o=0;o<e;r++,o++)r===n[0]&&(r++,n.shift()),i[o]=t[r];return i},USER_DATA_REGISTERED_ITU_T_T35:i},o=function t(e){e=e||{},t.prototype.init.call(this),this.parse708captions_="boolean"!=typeof e.parse708captions||e.parse708captions,this.captionPackets_=[],this.ccStreams_=[new C(0,0),new C(0,1),new C(1,0),new C(1,1)],this.parse708captions_&&(this.cc708Stream_=new l({captionServices:e.captionServices})),this.reset(),this.ccStreams_.forEach((function(t){t.on("data",this.trigger.bind(this,"data")),t.on("partialdone",this.trigger.bind(this,"partialdone")),t.on("done",this.trigger.bind(this,"done"))}),this),this.parse708captions_&&(this.cc708Stream_.on("data",this.trigger.bind(this,"data")),this.cc708Stream_.on("partialdone",this.trigger.bind(this,"partialdone")),this.cc708Stream_.on("done",this.trigger.bind(this,"done")))};o.prototype=new e,o.prototype.push=function(t){var e=this;"sei_rbsp"===t.nalUnitType&&n.parseSei(t.escapedRBSP).filter((function(t){return t.payloadType===n.USER_DATA_REGISTERED_ITU_T_T35})).map(n.parseUserData).forEach((function(i){i&&(e.captionPackets_=e.captionPackets_.concat(n.parseCaptionPackets(t.pts,i)))}))},o.prototype.flushCCStreams=function(t){this.ccStreams_.forEach((function(e){return"flush"===t?e.flush():e.partialFlush()}),this)},o.prototype.flushStream=function(t){this.captionPackets_.length?(this.captionPackets_.forEach((function(t,e){t.presortIndex=e})),this.captionPackets_.sort((function(t,e){return t.pts===e.pts?t.presortIndex-e.presortIndex:t.pts-e.pts})),this.captionPackets_.forEach((function(t){t.type<2?this.dispatchCea608Packet(t):this.dispatchCea708Packet(t)}),this),this.captionPackets_.length=0,this.flushCCStreams(t)):this.flushCCStreams(t)},o.prototype.flush=function(){return this.flushStream("flush")},o.prototype.partialFlush=function(){return this.flushStream("partialFlush")},o.prototype.reset=function(){this.latestDts_=null,this.ignoreNextEqualDts_=!1,this.numSameDts_=0,this.activeCea608Channel_=[null,null],this.ccStreams_.forEach((function(t){t.reset()}))},o.prototype.dispatchCea608Packet=function(t){this.setsTextOrXDSActive(t)?this.activeCea608Channel_[t.type]=null:this.setsChannel1Active(t)?this.activeCea608Channel_[t.type]=0:this.setsChannel2Active(t)&&(this.activeCea608Channel_[t.type]=1),null!==this.activeCea608Channel_[t.type]&&this.ccStreams_[(t.type<<1)+this.activeCea608Channel_[t.type]].push(t)},o.prototype.setsChannel1Active=function(t){return 4096==(30720&t.ccData)},o.prototype.setsChannel2Active=function(t){return 6144==(30720&t.ccData)},o.prototype.setsTextOrXDSActive=function(t){return 256==(28928&t.ccData)||4138==(30974&t.ccData)||6186==(30974&t.ccData)},o.prototype.dispatchCea708Packet=function(t){this.parse708captions_&&this.cc708Stream_.push(t)};var r={127:9834,4128:32,4129:160,4133:8230,4138:352,4140:338,4144:9608,4145:8216,4146:8217,4147:8220,4148:8221,4149:8226,4153:8482,4154:353,4156:339,4157:8480,4159:376,4214:8539,4215:8540,4216:8541,4217:8542,4218:9168,4219:9124,4220:9123,4221:9135,4222:9126,4223:9121,4256:12600},a=function(t){var e=r[t]||t;return 4096&t&&t===e?"":String.fromCharCode(e)},h=function(t){return 32<=t&&t<=127||160<=t&&t<=255},p=function(t){this.windowNum=t,this.reset()};p.prototype.reset=function(){this.clearText(),this.pendingNewLine=!1,this.winAttr={},this.penAttr={},this.penLoc={},this.penColor={},this.visible=0,this.rowLock=0,this.columnLock=0,this.priority=0,this.relativePositioning=0,this.anchorVertical=0,this.anchorHorizontal=0,this.anchorPoint=0,this.rowCount=1,this.virtualRowCount=this.rowCount+1,this.columnCount=41,this.windowStyle=0,this.penStyle=0},p.prototype.getText=function(){return this.rows.join("\n")},p.prototype.clearText=function(){this.rows=[""],this.rowIdx=0},p.prototype.newLine=function(t){for(this.rows.length>=this.virtualRowCount&&"function"==typeof this.beforeRowOverflow&&this.beforeRowOverflow(t),this.rows.length>0&&(this.rows.push(""),this.rowIdx++);this.rows.length>this.virtualRowCount;)this.rows.shift(),this.rowIdx--},p.prototype.isEmpty=function(){return 0===this.rows.length||1===this.rows.length&&""===this.rows[0]},p.prototype.addText=function(t){this.rows[this.rowIdx]+=t},p.prototype.backspace=function(){if(!this.isEmpty()){var t=this.rows[this.rowIdx];this.rows[this.rowIdx]=t.substr(0,t.length-1)}};var c=function(t,e,i){this.serviceNum=t,this.text="",this.currentWindow=new p(-1),this.windows=[],this.stream=i,"string"==typeof e&&this.createTextDecoder(e)};c.prototype.init=function(t,e){this.startPts=t;for(var i=0;i<8;i++)this.windows[i]=new p(i),"function"==typeof e&&(this.windows[i].beforeRowOverflow=e)},c.prototype.setCurrentWindow=function(t){this.currentWindow=this.windows[t]},c.prototype.createTextDecoder=function(t){if("undefined"==typeof TextDecoder)this.stream.trigger("log",{level:"warn",message:"The `encoding` option is unsupported without TextDecoder support"});else try{this.textDecoder_=new TextDecoder(t)}catch(e){this.stream.trigger("log",{level:"warn",message:"TextDecoder could not be created with "+t+" encoding. "+e})}};var l=function t(e){e=e||{},t.prototype.init.call(this);var i,s=this,n=e.captionServices||{},o={};Object.keys(n).forEach((function(t){i=n[t],/^SERVICE/.test(t)&&(o[t]=i.encoding)})),this.serviceEncodings=o,this.current708Packet=null,this.services={},this.push=function(t){3===t.type?(s.new708Packet(),s.add708Bytes(t)):(null===s.current708Packet&&s.new708Packet(),s.add708Bytes(t))}};l.prototype=new e,l.prototype.new708Packet=function(){null!==this.current708Packet&&this.push708Packet(),this.current708Packet={data:[],ptsVals:[]}},l.prototype.add708Bytes=function(t){var e=t.ccData,i=e>>>8,s=255&e;this.current708Packet.ptsVals.push(t.pts),this.current708Packet.data.push(i),this.current708Packet.data.push(s)},l.prototype.push708Packet=function(){var t=this.current708Packet,e=t.data,i=null,s=null,n=0,o=e[n++];for(t.seq=o>>6,t.sizeCode=63&o;n<e.length;n++)s=31&(o=e[n++]),7===(i=o>>5)&&s>0&&(i=o=e[n++]),this.pushServiceBlock(i,n,s),s>0&&(n+=s-1)},l.prototype.pushServiceBlock=function(t,e,i){var s,n=e,o=this.current708Packet.data,r=this.services[t];for(r||(r=this.initService(t,n));n<e+i&&n<o.length;n++)s=o[n],h(s)?n=this.handleText(n,r):24===s?n=this.multiByteCharacter(n,r):16===s?n=this.extendedCommands(n,r):128<=s&&s<=135?n=this.setCurrentWindow(n,r):152<=s&&s<=159?n=this.defineWindow(n,r):136===s?n=this.clearWindows(n,r):140===s?n=this.deleteWindows(n,r):137===s?n=this.displayWindows(n,r):138===s?n=this.hideWindows(n,r):139===s?n=this.toggleWindows(n,r):151===s?n=this.setWindowAttributes(n,r):144===s?n=this.setPenAttributes(n,r):145===s?n=this.setPenColor(n,r):146===s?n=this.setPenLocation(n,r):143===s?r=this.reset(n,r):8===s?r.currentWindow.backspace():12===s?r.currentWindow.clearText():13===s?r.currentWindow.pendingNewLine=!0:14===s?r.currentWindow.clearText():141===s&&n++},l.prototype.extendedCommands=function(t,e){var i=this.current708Packet.data[++t];return h(i)&&(t=this.handleText(t,e,{isExtended:!0})),t},l.prototype.getPts=function(t){return this.current708Packet.ptsVals[Math.floor(t/2)]},l.prototype.initService=function(t,e){var i,s,n=this;return(i="SERVICE"+t)in this.serviceEncodings&&(s=this.serviceEncodings[i]),this.services[t]=new c(t,s,n),this.services[t].init(this.getPts(e),(function(e){n.flushDisplayed(e,n.services[t])})),this.services[t]},l.prototype.handleText=function(t,e,i){var s,n,o=i&&i.isExtended,r=i&&i.isMultiByte,h=this.current708Packet.data,p=o?4096:0,c=h[t],l=h[t+1],d=e.currentWindow;return e.textDecoder_&&!o?(r?(n=[c,l],t++):n=[c],s=e.textDecoder_.decode(new Uint8Array(n))):s=a(p|c),d.pendingNewLine&&!d.isEmpty()&&d.newLine(this.getPts(t)),d.pendingNewLine=!1,d.addText(s),t},l.prototype.multiByteCharacter=function(t,e){var i=this.current708Packet.data,s=i[t+1],n=i[t+2];return h(s)&&h(n)&&(t=this.handleText(++t,e,{isMultiByte:!0})),t},l.prototype.setCurrentWindow=function(t,e){var i=7&this.current708Packet.data[t];return e.setCurrentWindow(i),t},l.prototype.defineWindow=function(t,e){var i=this.current708Packet.data,s=i[t],n=7&s;e.setCurrentWindow(n);var o=e.currentWindow;return s=i[++t],o.visible=(32&s)>>5,o.rowLock=(16&s)>>4,o.columnLock=(8&s)>>3,o.priority=7&s,s=i[++t],o.relativePositioning=(128&s)>>7,o.anchorVertical=127&s,s=i[++t],o.anchorHorizontal=s,s=i[++t],o.anchorPoint=(240&s)>>4,o.rowCount=15&s,s=i[++t],o.columnCount=63&s,s=i[++t],o.windowStyle=(56&s)>>3,o.penStyle=7&s,o.virtualRowCount=o.rowCount+1,t},l.prototype.setWindowAttributes=function(t,e){var i=this.current708Packet.data,s=i[t],n=e.currentWindow.winAttr;return s=i[++t],n.fillOpacity=(192&s)>>6,n.fillRed=(48&s)>>4,n.fillGreen=(12&s)>>2,n.fillBlue=3&s,s=i[++t],n.borderType=(192&s)>>6,n.borderRed=(48&s)>>4,n.borderGreen=(12&s)>>2,n.borderBlue=3&s,s=i[++t],n.borderType+=(128&s)>>5,n.wordWrap=(64&s)>>6,n.printDirection=(48&s)>>4,n.scrollDirection=(12&s)>>2,n.justify=3&s,s=i[++t],n.effectSpeed=(240&s)>>4,n.effectDirection=(12&s)>>2,n.displayEffect=3&s,t},l.prototype.flushDisplayed=function(t,e){for(var i=[],s=0;s<8;s++)e.windows[s].visible&&!e.windows[s].isEmpty()&&i.push(e.windows[s].getText());e.endPts=t,e.text=i.join("\n\n"),this.pushCaption(e),e.startPts=t},l.prototype.pushCaption=function(t){""!==t.text&&(this.trigger("data",{startPts:t.startPts,endPts:t.endPts,text:t.text,stream:"cc708_"+t.serviceNum}),t.text="",t.startPts=t.endPts)},l.prototype.displayWindows=function(t,e){var i=this.current708Packet.data[++t],s=this.getPts(t);this.flushDisplayed(s,e);for(var n=0;n<8;n++)i&1<<n&&(e.windows[n].visible=1);return t},l.prototype.hideWindows=function(t,e){var i=this.current708Packet.data[++t],s=this.getPts(t);this.flushDisplayed(s,e);for(var n=0;n<8;n++)i&1<<n&&(e.windows[n].visible=0);return t},l.prototype.toggleWindows=function(t,e){var i=this.current708Packet.data[++t],s=this.getPts(t);this.flushDisplayed(s,e);for(var n=0;n<8;n++)i&1<<n&&(e.windows[n].visible^=1);return t},l.prototype.clearWindows=function(t,e){var i=this.current708Packet.data[++t],s=this.getPts(t);this.flushDisplayed(s,e);for(var n=0;n<8;n++)i&1<<n&&e.windows[n].clearText();return t},l.prototype.deleteWindows=function(t,e){var i=this.current708Packet.data[++t],s=this.getPts(t);this.flushDisplayed(s,e);for(var n=0;n<8;n++)i&1<<n&&e.windows[n].reset();return t},l.prototype.setPenAttributes=function(t,e){var i=this.current708Packet.data,s=i[t],n=e.currentWindow.penAttr;return s=i[++t],n.textTag=(240&s)>>4,n.offset=(12&s)>>2,n.penSize=3&s,s=i[++t],n.italics=(128&s)>>7,n.underline=(64&s)>>6,n.edgeType=(56&s)>>3,n.fontStyle=7&s,t},l.prototype.setPenColor=function(t,e){var i=this.current708Packet.data,s=i[t],n=e.currentWindow.penColor;return s=i[++t],n.fgOpacity=(192&s)>>6,n.fgRed=(48&s)>>4,n.fgGreen=(12&s)>>2,n.fgBlue=3&s,s=i[++t],n.bgOpacity=(192&s)>>6,n.bgRed=(48&s)>>4,n.bgGreen=(12&s)>>2,n.bgBlue=3&s,s=i[++t],n.edgeRed=(48&s)>>4,n.edgeGreen=(12&s)>>2,n.edgeBlue=3&s,t},l.prototype.setPenLocation=function(t,e){var i=this.current708Packet.data,s=i[t],n=e.currentWindow.penLoc;return e.currentWindow.pendingNewLine=!0,s=i[++t],n.row=15&s,s=i[++t],n.column=63&s,t},l.prototype.reset=function(t,e){var i=this.getPts(t);return this.flushDisplayed(i,e),this.initService(e.serviceNum,t)};var d={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,304:174,305:176,306:189,307:191,308:8482,309:162,310:163,311:9834,312:224,313:160,314:232,315:226,316:234,317:238,318:244,319:251,544:193,545:201,546:211,547:218,548:220,549:252,550:8216,551:161,552:42,553:39,554:8212,555:169,556:8480,557:8226,558:8220,559:8221,560:192,561:194,562:199,563:200,564:202,565:203,566:235,567:206,568:207,569:239,570:212,571:217,572:249,573:219,574:171,575:187,800:195,801:227,802:205,803:204,804:236,805:210,806:242,807:213,808:245,809:123,810:125,811:92,812:94,813:95,814:124,815:126,816:196,817:228,818:214,819:246,820:223,821:165,822:164,823:9474,824:197,825:229,826:216,827:248,828:9484,829:9488,830:9492,831:9496},u=function(t){return t=d[t]||t,String.fromCharCode(t).replace(/[\x00]/g,"")},f=14,_=[4352,4384,4608,4640,5376,5408,5632,5664,5888,5920,4096,4864,4896,5120,5152],y=function(){for(var t=[],e=f+1;e--;)t.push("");return t},w=function(){for(var t=[],e=f+1;e--;)t[e]=0;return t},C=function t(e,i,s){t.prototype.init.call(this),this.field_=e||0,this.dataChannel_=i||0,this.name_="CC"+(1+(this.field_<<1|this.dataChannel_)),this.setConstants(),this.reset(),this.push=function(t){var e,i,s,n,o;if((e=32639&t.ccData)!==this.lastControlCode_){if(4096==(61440&e)?this.lastControlCode_=e:e!==this.PADDING_&&(this.lastControlCode_=null),s=e>>>8,n=255&e,e!==this.PADDING_)if(e===this.RESUME_CAPTION_LOADING_)this.mode_="popOn";else if(e===this.END_OF_CAPTION_)this.mode_="popOn",this.clearFormatting(t.pts),this.flushDisplayed(t.pts),i=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=i,i=this.displayedIndent_,this.displayedIndent_=this.nonDisplayedIndent_,this.nonDisplayedIndent_=i,this.startPts_=t.pts;else if(e===this.ROLL_UP_2_ROWS_)this.rollUpRows_=2,this.setRollUp(t.pts);else if(e===this.ROLL_UP_3_ROWS_)this.rollUpRows_=3,this.setRollUp(t.pts);else if(e===this.ROLL_UP_4_ROWS_)this.rollUpRows_=4,this.setRollUp(t.pts);else if(e===this.CARRIAGE_RETURN_)this.clearFormatting(t.pts),this.flushDisplayed(t.pts),this.shiftRowsUp_(),this.startPts_=t.pts;else if(e===this.BACKSPACE_)"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1);else if(e===this.ERASE_DISPLAYED_MEMORY_)this.flushDisplayed(t.pts),this.displayed_=y(),this.displayedIndent_=w();else if(e===this.ERASE_NON_DISPLAYED_MEMORY_)this.nonDisplayed_=y(),this.nonDisplayedIndent_=w();else if(e===this.RESUME_DIRECT_CAPTIONING_)"paintOn"!==this.mode_&&(this.flushDisplayed(t.pts),this.displayed_=y(),this.displayedIndent_=w()),this.mode_="paintOn",this.startPts_=t.pts;else{if(!Boolean(this.mode_))return;if(this.isSpecialCharacter(s,n))o=u((s=(3&s)<<8)|n),this[this.mode_](t.pts,o),this.column_++;else if(this.isExtCharacter(s,n))"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1),o=u((s=(3&s)<<8)|n),this[this.mode_](t.pts,o),this.column_++;else if(this.isMidRowCode(s,n))this.clearFormatting(t.pts),this[this.mode_](t.pts," "),this.column_++,14&~n||this.addFormatting(t.pts,["i"]),1&~n||this.addFormatting(t.pts,["u"]);else if(this.isOffsetControlCode(s,n)){var r=3&n;this.column_+=r,this.incrementIdentation(r)}else if(this.isPAC(s,n)){var a=_.indexOf(7968&e);"rollUp"===this.mode_&&(a-this.rollUpRows_+1<0&&(a=this.rollUpRows_-1),this.setRollUp(t.pts,a)),a!==this.row_&&(this.clearFormatting(t.pts),this.changeRow(a)),1&n&&-1===this.formatting_.indexOf("u")&&this.addFormatting(t.pts,["u"]),16&~e||(this.column_=4*((14&e)>>1),"popOn"===this.mode_?this.nonDisplayedIndent_[this.row_]=this.column_:"rollUp"===this.mode_&&(this.displayedIndent_[this.row_]=this.column_)),this.isColorPAC(n)&&(14&~n||this.addFormatting(t.pts,["i"]))}else this.isNormalChar(s)&&(o=u(s),0!==n&&(o+=u(n)),this[this.mode_](t.pts,o),this.column_+=o.length)}}else this.lastControlCode_=null}};return C.prototype=new e,C.prototype.flushDisplayed=function(t){for(var e=0;e<this.displayed_.length;e++){var i=this.displayed_[e];i&&i.length&&this.trigger("data",{startPts:this.startPts_,endPts:t,text:i,stream:this.name_,position:{row:e,column:this.displayedIndent_[e]}})}},C.prototype.reset=function(){this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=y(),this.nonDisplayed_=y(),this.displayedIndent_=w(),this.nonDisplayedIndent_=w(),this.lastControlCode_=null,this.column_=0,this.row_=f,this.rollUpRows_=2,this.formatting_=[]},C.prototype.setConstants=function(){0===this.dataChannel_?(this.BASE_=16,this.EXT_=17,this.CONTROL_=(20|this.field_)<<8,this.OFFSET_=23):1===this.dataChannel_&&(this.BASE_=24,this.EXT_=25,this.CONTROL_=(28|this.field_)<<8,this.OFFSET_=31),this.PADDING_=0,this.RESUME_CAPTION_LOADING_=32|this.CONTROL_,this.END_OF_CAPTION_=47|this.CONTROL_,this.ROLL_UP_2_ROWS_=37|this.CONTROL_,this.ROLL_UP_3_ROWS_=38|this.CONTROL_,this.ROLL_UP_4_ROWS_=39|this.CONTROL_,this.CARRIAGE_RETURN_=45|this.CONTROL_,this.RESUME_DIRECT_CAPTIONING_=41|this.CONTROL_,this.BACKSPACE_=33|this.CONTROL_,this.ERASE_DISPLAYED_MEMORY_=44|this.CONTROL_,this.ERASE_NON_DISPLAYED_MEMORY_=46|this.CONTROL_,this.TAB_OFFSET_1_=33,this.TAB_OFFSET_2_=34,this.TAB_OFFSET_3_=35},C.prototype.isSpecialCharacter=function(t,e){return t===this.EXT_&&e>=48&&e<=63},C.prototype.isExtCharacter=function(t,e){return(t===this.EXT_+1||t===this.EXT_+2)&&e>=32&&e<=63},C.prototype.isMidRowCode=function(t,e){return t===this.EXT_&&e>=32&&e<=47},C.prototype.isOffsetControlCode=function(t,e){return t===this.OFFSET_&&e>=this.TAB_OFFSET_1_&&e<=this.TAB_OFFSET_3_},C.prototype.isPAC=function(t,e){return t>=this.BASE_&&t<this.BASE_+8&&e>=64&&e<=127},C.prototype.isColorPAC=function(t){return t>=64&&t<=79||t>=96&&t<=127},C.prototype.isNormalChar=function(t){return t>=32&&t<=127},C.prototype.setRollUp=function(t,e){if("rollUp"!==this.mode_&&(this.row_=f,this.mode_="rollUp",this.flushDisplayed(t),this.nonDisplayed_=y(),this.displayed_=y(),this.displayedIndent_=w(),this.nonDisplayedIndent_=w()),void 0!==e&&e!==this.row_)for(var i=0;i<this.rollUpRows_;i++)this.displayed_[e-i]=this.displayed_[this.row_-i],this.displayed_[this.row_-i]="";void 0===e&&(e=this.row_),this.topRow_=e-this.rollUpRows_+1},C.prototype.addFormatting=function(t,e){this.formatting_=this.formatting_.concat(e);var i=e.reduce((function(t,e){return t+"<"+e+">"}),"");this[this.mode_](t,i)},C.prototype.clearFormatting=function(t){if(this.formatting_.length){var e=this.formatting_.reverse().reduce((function(t,e){return t+"</"+e+">"}),"");this.formatting_=[],this[this.mode_](t,e)}},C.prototype.popOn=function(t,e){var i=this.nonDisplayed_[this.row_];i+=e,this.nonDisplayed_[this.row_]=i},C.prototype.rollUp=function(t,e){var i=this.displayed_[this.row_];i+=e,this.displayed_[this.row_]=i},C.prototype.shiftRowsUp_=function(){var t;for(t=0;t<this.topRow_;t++)this.displayed_[t]="";for(t=this.row_+1;t<f+1;t++)this.displayed_[t]="";for(t=this.topRow_;t<this.row_;t++)this.displayed_[t]=this.displayed_[t+1];this.displayed_[this.row_]=""},C.prototype.paintOn=function(t,e){var i=this.displayed_[this.row_];i+=e,this.displayed_[this.row_]=i},C.prototype.changeRow=function(t){if("rollUp"===this.mode_){var e=this.row_-this.topRow_+1;this.topRow_=t-e+1;var i=this.displayed_,s=this.displayedIndent_;this.displayed_=y(),this.displayedIndent_=w();for(var n=0;n<e;n++)t-n>=0&&(this.displayed_[t-n]=i[this.row_-n],this.displayedIndent_[t-n]=s[this.row_-n])}this.row_=t},C.prototype.incrementIdentation=function(t){"popOn"===this.mode_?this.nonDisplayedIndent_[this.row_]+=t:"rollUp"===this.mode_&&(this.displayedIndent_[this.row_]+=t)},{CaptionStream:{CaptionStream:o,Cea608Stream:C,Cea708Stream:l}.CaptionStream}}))},52473:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.assembleCaption=s;var i=9e4;function s(t){return{start:t.startPts/i,end:t.endPts/i,text:t.text,channel:t.stream,position:t.position}}},57708:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CeaSubtitlesModuleDefinition=void 0;var s=i(16368),n=i(65e3);e.CeaSubtitlesModuleDefinition={name:s.ModuleName.SubtitlesCEA608,module:function(){return{Cea608Extractor:n.Cea608Extractor}},dependencies:[s.ModuleName.Subtitles]},e.default=e.CeaSubtitlesModuleDefinition},65e3:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Cea608Extractor=e.MPEG_TS_TIMESCALE=void 0;var s=i(72181),n=i(8824);e.MPEG_TS_TIMESCALE=9e4;var o=function(){function t(t,e,i){void 0===i&&(i=!1),this.logger=t,this.MP4Module=e,this.parseCea708Caption=i,this.timeScale=1,this.defaultSampleDuration=0}return t.prototype.extract=function(t){var e,i,n=this,o=(null!==(i=null===(e=t.getInitSegment())||void 0===e?void 0:e.getParsedData())&&void 0!==i?i:t.getParsedData()).get("moov.trak.mdia.mdhd");o&&(this.timeScale=o.timescale);var a=t.getParsedData().get("moov.mvex.trex");a&&(this.defaultSampleDuration=a.default_sample_duration);var h=this.MP4Module.getMp4Fragments(t).flatMap((function(e){var i=e.moof.get("traf.tfdt"),o=n.MP4Module.getAvcSamples(e,t,[s.NalUnitType.SEI],n.logger),a=i?n.toSeconds(i.baseMediaDecodeTime):0;return o.flatMap((function(t){var e=t.nalus.map((function(e){return r(e,a+n.toSeconds(t.compositionTimeOffset))}));return a+=Math.floor(n.toSeconds(t.duration||n.defaultSampleDuration)),e}))}));return this.parseCaptions(h)},t.prototype.toSeconds=function(t){return t/this.timeScale*e.MPEG_TS_TIMESCALE},t.prototype.parseCaptions=function(t){return 0===t.length?Promise.resolve([]):(this.cea608Processor||(this.cea608Processor=new n.Cea608Processor(this.parseCea708Caption)),this.cea608Processor.getCaptions(t).catch((function(){return Promise.resolve([])})))},t.prototype.reset=function(){this.cea608Processor&&this.cea608Processor.reset()},t.prototype.dispose=function(){this.cea608Processor&&this.cea608Processor.dispose()},t}();function r(t,e){return{nalUnitType:"sei_rbsp",escapedRBSP:new Uint8Array(t.data.buffer,t.data.byteOffset,t.data.byteLength),pts:e}}e.Cea608Extractor=o}},function(t){return function(e){return t(t.s=e)}(57708)}])}));
})();
