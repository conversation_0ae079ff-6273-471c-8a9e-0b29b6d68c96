/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports["engine-native"]=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player["engine-native"]=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[855],{3257:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.HlsDownloadService=void 0;var n=i(4792),r=i(65622),a=i(33964),o=i(51243),s=i(55423),l=function(){function e(e){var t=this;this.useCredentialsForManifestRequests=!1,this.downloadFailureHandler=function(e){t.context.eventHandler.dispatchEvent(a.PlayerEvent.Warning,new n.PlayerWarning(r.WarningCode.NETWORK_COULD_NOT_LOAD_MANIFEST))},this.context=e,this.init()}return e.prototype.init=function(){this.initCache(),this.initUseCredentialsForManifest()},e.prototype.initCache=function(){this.requestCache={}},e.prototype.initUseCredentialsForManifest=function(){var e=this.context.sourceContext.source&&this.context.sourceContext.source.options||{};this.useCredentialsForManifestRequests="boolean"==typeof e.hlsManifestWithCredentials?e.hlsManifestWithCredentials:!0===e.manifestWithCredentials},e.prototype.downloadPlaylist=function(e,t,i){return void 0===i&&(i=!0),i&&this.requestCache.hasOwnProperty(e)?Promise.resolve(this.requestCache[e]):this.download(t,e)},e.prototype.download=function(e,t){var i=this,n=this.getContentLoader(e);return n.load(t,s.HttpRequestMethod.GET,s.HttpResponseType.TEXT,null,{},this.useCredentialsForManifestRequests).then((function(e){return n.dispose(),i.requestCache[t]=e,e})).catch((function(e){return n.dispose(),Promise.reject(e)}))},e.prototype.getContentLoader=function(e){return new o.DefaultContentLoader(this.context,{onFailure:this.downloadFailureHandler,maxRetries:this.context.settings.MAX_MPD_RETRIES,requestType:e})},e.prototype.dispose=function(){},e}();t.HlsDownloadService=l},9280:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.NativePlayerManifestApiFactory=void 0;var n=i(39713),r=function(){function e(){}return e.create=function(t){var i=t.getParsedManifest();return t.isValidMasterPlaylist(i)?new(function(){function i(){this.hls=e.createHlsApi(t)}return i}()):null},e.createHlsApi=function(t){var i=t.getParsedManifest(),n=i.media.filter((function(e){return"AUDIO"===e.attributes.TYPE})),r=i.media.filter((function(e){return"SUBTITLES"===e.attributes.TYPE||"CLOSED-CAPTIONS"===e.attributes.TYPE}));return new(function(){function a(){this.properties=t.getParsedManifest().tags}return a.prototype.getVideoTracks=function(){return e.getVideoTracks(i.playlists)},a.prototype.getAudioTracks=function(){return e.getAudioTracks(n,i.playlists)},a.prototype.getTextTracks=function(){return e.getTextTracks(r)},a}())},e.getAudioTracks=function(e,t){var i=e.reduce((function(e,t){return e[t.attributes.LANGUAGE]||(e[t.attributes.LANGUAGE]=t.attributes.NAME),e}),{}),r=Object.keys(i).map((function(t,n){return{id:"audio-".concat(n),label:i[t],lang:t,getQualities:function(){return e.filter((function(e){return e.attributes.LANGUAGE===t})).map((function(e,t){return{id:t+"",bitrate:0,label:e.attributes.NAME}}))}}})),a=function(e){return e.attributes.CODECS&&n.CodecStringHelper.getMimeTypeForCodecString(e.attributes.CODECS).includes("audio")};return t.some(a)&&r.push({id:"audio-".concat(r.length),label:"und",lang:"",getQualities:function(){return t.filter(a).map((function(e,t){return{id:t+"",bitrate:e.attributes.BANDWIDTH,codec:n.CodecStringHelper.getExtractedCodecStrings(e.attributes.CODECS).audio}}))}}),r},e.getVideoTracks=function(t){var i=function(e){return!e.attributes.CODECS||n.CodecStringHelper.getMimeTypeForCodecString(e.attributes.CODECS).includes("video")},r=t.filter(i);return[{id:"video-0",label:"und",getQualities:function(){return e.getVideoQualities(r)}}]},e.getTextTracks=function(e){return e.map((function(e,t){return{id:"text-".concat(t),label:e.attributes.NAME,lang:e.attributes.LANGUAGE,isFragmented:!0,kind:"CLOSED-CAPTIONS"===e.attributes.TYPE?"caption":"subtitle",url:e.attributes.URI}}))},e.getVideoQualities=function(e){return e.map((function(e){var t=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width,i=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.height,r={id:i?"".concat(i,"_").concat(e.attributes.BANDWIDTH):"".concat(e.attributes.BANDWIDTH),bitrate:e.attributes.BANDWIDTH,width:t||0,height:i||0};if(e.attributes.CODECS){var a=n.CodecStringHelper.getExtractedCodecStrings(e.attributes.CODECS);a.video&&(r.codec=a.video)}return r}))},e}();t.NativePlayerManifestApiFactory=r},30986:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.HlsLiveNativeTimeTracker=void 0;var n=i(82049),r=i(74749),a=i(89336),o=i(98892),s=2e3,l=function(){function e(e,t){this.nativePlayer=e,this.context=t,this.timeAdjustment=0,this.unloadTimestamp=null,this.startDate=null}return e.prototype.onUnload=function(e){e===n.ADVERTISING_ISSUER_NAME&&this.context.settings.RESUME_LIVE_CONTENT_AT_PREVIOUS_POSITION_AFTER_AD_BREAK&&null==this.unloadTimestamp&&(this.nativePlayer.isLive()?(this.unloadTimestamp=Date.now(),this.timeAdjustment=this.nativePlayer.getSeekableRange().start,this.startDate=this.nativePlayer.getStartDate()):this.reset())},e.prototype.reset=function(){this.unloadTimestamp=null,this.timeAdjustment=0,this.startDate=null},e.prototype.adjustTargetTime=function(e,t){var i=this;return t!==n.ADVERTISING_RESTORE_ISSUER_NAME||null==this.unloadTimestamp?Promise.resolve(e):(null!=this.startDate?this.waitForStartDate():Promise.resolve(null)).then((function(t){if(null!=t){var n=a.Util.timeInSeconds(t)-a.Util.timeInSeconds(i.startDate);i.timeAdjustment=n}else{var o=(0,r.toSeconds)(Date.now()-i.unloadTimestamp);i.timeAdjustment+=o}var s=e-i.timeAdjustment;return i.reset(),s}))},e.prototype.waitForStartDate=function(){var e=this,t=this.getValidDateTime();return t?Promise.resolve(t):new Promise((function(t){var i=null,n=function(){t(e.getValidDateTime()),e.context.videoElement.removeEventListener(o.MediaElementEvent.loadeddata,n),clearTimeout(i)};e.context.videoElement.addEventListener(o.MediaElementEvent.loadeddata,n),i=setTimeout(n,s)}))},e.prototype.getValidDateTime=function(){var e=this.nativePlayer.getStartDate();return e&&!isNaN(e.getTime())?e:null},e}();t.HlsLiveNativeTimeTracker=l},37191:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractHlsManifestController=void 0;var n,r,a=i(9851),o=i(33964),s=i(64461),l=i(45347),d=i(29095),u=i(24195),h=i(74749),c=i(83275),v=i(89336),p=i(47122),m=i(86246);!function(e){e.CueTag="cueTag",e.Scte35="scte35",e.DateRange="dateRange",e.CustomTags="customTags"}(n||(n={}));var f="#EXTM3U\n#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=100000\n$VARIANT$\n",g=function(){function e(e){var t,i=this;this.context=e,this.discontinuitySequences=[],this.onManifestMetadata=function(e){if(e.hasOwnProperty("metadata")){var t={metadataType:e.metadataType.toUpperCase(),metadata:e.metadata};(e.start||0===e.start)&&(t.start=e.start),e.end&&(t.end=e.end),i.context.eventHandler.dispatchEvent(o.PlayerEvent.Metadata,t)}},this.onTimeChangedHandler=function(e){var t=i.segmentTimelineController.getPlayingSegment(e.relativeTime||e.time);t&&!t.consumed&&(t.consumed=!0,i.triggerMetadata(t),i.triggerSegmentPlayback(t))},this.onTimeShiftedHandler=function(){i.segmentTimelineController.reset()},this.onSeeked=function(){i.segmentTimelineController.reset()},this.registerVariantPlaylist=function(e){var t,n,a=i.parsePlaylist(e.body),o=null===(n=null===(t=i.parsedManifest)||void 0===t?void 0:t.playlists)||void 0===n?void 0:n.find((function(t){var i=c.URLHelper.removeDotSegments(t.uri);return c.URLHelper.removeDotSegments(e.url).includes(i)}));if(i.isValidVariantPlaylist(a)&&o){var l=function(e){var t,n;return null!==(n=null===(t=i.parsedManifest.media)||void 0===t?void 0:t.some((function(t){var i;return null===(i=t.attributes.URI)||void 0===i?void 0:i.includes(e)})))&&void 0!==n&&n},d=function(e){var t,i;return null!==(i=null===(t=e.playlists)||void 0===t?void 0:t.every((function(e){return l(e.uri)})))&&void 0!==i&&i},u=function(e){var t,n;return null!==(n=null===(t=i.parsedManifest.playlists)||void 0===t?void 0:t.some((function(t){return e.uri.includes(t.uri)&&!l(e.uri)})))&&void 0!==n&&n};if(u(o)||d(i.parsedManifest)){i.segmentTimelineController.switchTimeline(o.id),r.extrapolateProgramDateTime(a.segments),i.discontinuitySequences=r.createDiscontinuitySequences(a,i.discontinuitySequences);var h=S(a,o.id,i.config);i.metadataParsedService.expirationTimeInSeconds=i.segmentTimelineController.updateTimeline(o.id,h);var p=i.segmentTimelineController.getCurrentTimeline();if(Boolean(h[0].dateTime)){var m=v.Util.timeInSeconds(h[0].dateTime);i.metadataService.removeUntil(m)}(0,s.extractDateRangeMetadata)(a.dateRange,a.endList?a.programDateTime:void 0,(function(e){i.addDateRangeMetadata(e)}));var f=!(null==a?void 0:a.endList);return i.extractMetadataFromTimeline(p,f),a}}},r=p.ModuleManager.get(m.ModuleName.HLS).PlaylistUtils,this.settings=e.settings,this.config=e.config,this.eventHandler=e.eventHandler,this.originalManifest=null,this.metadataParsedService=e.serviceManager.get(a.ServiceName.MetadataParsedService),this.eventHandler.on(o.PlayerEvent.TimeChanged,this.onTimeChangedHandler),this.eventHandler.on(o.PlayerEvent.TimeShifted,this.onTimeShiftedHandler),this.eventHandler.on(o.PlayerEvent.Seeked,this.onSeeked),this.metadataService=new l.MetadataService(this.context,((t={})[d.TimedMetadataType.Manifest]=this.onManifestMetadata,t[d.TimedMetadataType.DateRange]=this.onManifestMetadata,t))}return e.prototype.triggerSegmentPlayback=function(e){var t={url:e.uri,uid:null,mimeType:"video/mp4",playbackTime:e.playbackTime,duration:e.duration,mediaInfo:{},dateTime:null};e.dateTime&&(t.dateTime=e.dateTime.toISOString()),e.customTags&&e.customTags.length>0&&(t.EXPERIMENTAL=t.EXPERIMENTAL||{},t.EXPERIMENTAL.hlsAttributes=e.customTags.map((function(e){return e.attributes}))),this.context.eventHandler.dispatchEvent(o.PlayerEvent.SegmentPlayback,t)},e.prototype.triggerMetadata=function(e){var t=this;e.metadata&&e.metadata.forEach((function(e){T(e)?t.context.eventHandler.dispatchEvent(o.PlayerEvent.Metadata,{metadataType:o.MetadataType.CUETAG,metadata:e}):t.context.eventHandler.dispatchEvent(o.PlayerEvent.Metadata,{metadataType:e.type,metadata:e})}))},e.prototype.resolveMasterManifest=function(e){return this.parsedManifest?Promise.resolve(this.manifestUrl):(this.manifestUrl=e,this.downloadMasterManifest(e))},e.prototype.registerMasterManifest=function(e,t){var i;this.originalManifest=e.body;var n=this.processMasterManifest(null!==(i=e.body)&&void 0!==i?i:"",t);if(this.parsedManifest=this.parsePlaylist(n),this.isValidMasterPlaylist(this.parsedManifest))return n;if(this.isValidVariantPlaylist(this.parsedManifest)){var r=f.replace("$VARIANT$",t);n=this.processMasterManifest(r,t),this.originalManifest=r,this.parsedManifest=this.parsePlaylist(n)}else this.parsedManifest=null;return n},e.prototype.isValidMasterPlaylist=function(e){return null!=e&&"playlists"in e&&e.playlists.length>0},e.prototype.isValidVariantPlaylist=function(e){return null!=e&&"segments"in e&&e.segments.length>0},e.prototype.parsePlaylist=function(e){var t=p.ModuleManager.get(m.ModuleName.HLS).parsePlaylist;try{return t(e)}catch(e){return null}},e.prototype.addDateRangeMetadata=function(e){var t={metadataType:o.MetadataType.DATERANGE,metadata:e.data,start:e.startTime,end:e.endTime};this.addMetadata(t)},e.prototype.metadataToMetadataParsedEvent=function(e){return{metadataType:e.metadataType,metadata:e.metadata,data:e.metadata,start:e.start,end:e.end}},e.prototype.addMetadata=function(e){var t="DATERANGE"===e.metadataType?d.TimedMetadataType.DateRange:d.TimedMetadataType.Manifest;this.metadataService.addToMetadataParsedService(e.start,this.metadataToMetadataParsedEvent(e),u.DEFAULT_PERIOD_ID),this.metadataService.addToTimeline(t,e.start,e)},e.prototype.extractMetadataFromTimeline=function(e,t){var i=this;e.forEach((function(e){Object.values(n).forEach((function(n){e.hasOwnProperty(n)&&y(n,e,t).forEach((function(e){return i.addMetadata(e)}))}))}))},e.prototype.parseTimelineEntries=function(e){var t=this.parsePlaylist(e),i=function(e){var t;return{uri:e.uri,duration:e.duration,discontinuity:null!==(t=e.discontinuity)&&void 0!==t&&t,dateTime:null,metadata:[],consumed:!1,playbackTime:-1,keys:[]}};return this.isValidVariantPlaylist(t)?t.segments.map(i):[]},e.prototype.getPlaybackTimeOfFirstSegmentInTimeline=function(e){for(var t=0;t<e.length;t++)for(var i=e[t],n=0,r=0;r<i.timeline.length;r++){for(var a=i.timeline[r],o=this.segmentTimelineController.getCurrentTimeline(),s=0;s<o.length;s++){var l=o[s];if(l.uri===a.uri)return l.playbackTime-n}n+=a.duration}return-1},e.prototype.getParsedManifest=function(){return this.parsedManifest},e.prototype.getManifest=function(){return this.originalManifest},e.prototype.detachEventHandlers=function(){this.eventHandler&&(this.eventHandler.off(o.PlayerEvent.TimeChanged,this.onTimeChangedHandler),this.eventHandler.off(o.PlayerEvent.TimeShifted,this.onTimeShiftedHandler),this.eventHandler.off(o.PlayerEvent.Seeked,this.onSeeked))},e.prototype.dispose=function(){this.detachEventHandlers(),this.segmentTimelineController&&(this.segmentTimelineController.dispose(),this.segmentTimelineController=null),this.metadataService&&(this.metadataService.dispose(),this.metadataService=null),this.parsedManifest=null,this.originalManifest=null,this.manifestUrl=null,this.settings=null,this.config=null,this.eventHandler=null},e}();function y(e,t,i){var r=i&&t.dateTime?(0,h.toSeconds)(t.dateTime.getTime()):t.playbackTime;switch(e){case n.CueTag:return[{metadataType:o.MetadataType.CUETAG,metadata:t[n.CueTag],start:r}];case n.Scte35:return[{metadataType:o.MetadataType.SCTE,metadata:t[n.Scte35],start:r}];case n.CustomTags:return[{metadataType:o.MetadataType.CUSTOM,metadata:t[n.CustomTags],start:r,end:r+t.duration}];default:return[]}}function T(e){return e.type.includes("CUE")}function S(e,t,i){for(var n,a=e.segments,o=a.some((function(e){return void 0!==e.dateTime})),s=0;s<a.length;s++){var l=a[s];if(l.variantPlaylistId=t,l.mediaSequenceNumber=e.mediaSequence+s,!o&&(null===(n=i.tweaks)||void 0===n?void 0:n.akamai_datetime_parsing)){var d=r.getProgramDateTimeFromSegmentUrl(l.uri,l.duration);d&&(l.dateTime=d)}}return a}t.AbstractHlsManifestController=g},39652:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.TechnologyChecker=void 0;var n=i(50930),r=function(){function e(){}return e.prototype.getSupportedTechnologies=function(){return[{player:n.PlayerType.Native,streaming:n.StreamType.Progressive},{player:n.PlayerType.Native,streaming:n.StreamType.Hls},{player:n.PlayerType.Native,streaming:n.StreamType.Dash}]},e}();t.TechnologyChecker=r},47783:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.AssetAvailabilityChecker=void 0;var n=i(51243),r=i(55423),a=i(74749),o=i(98892),s=function(){function e(e,t,i){var s=this;this.context=e,this.source=t,this.mediaElement=i,this.scheduleAvailabilityCheck=function(){s.mediaElement.removeEventListener(o.MediaElementEvent.loadstart,s.scheduleAvailabilityCheck);var e=(0,a.toMilliSeconds)(s.context.settings.XHR_TIMEOUT)/2;s.assetAvailabilityCheckTimeoutID=window.setTimeout((function(){var e=!1,t=function(t){var i;t.loadedBytes>0&&(e=!0,null===(i=s.contentLoader)||void 0===i||i.cancel())};s.contentLoader=new n.DefaultContentLoader(s.context,{onProgress:t,requestType:r.HttpRequestType.INTERNAL}),s.contentLoader.load(s.source.src).catch((function(t){if(!e){var i=new Event(o.MediaElementEvent.error,{bubbles:!1,cancelable:!1});s.source.dispatchEvent(i)}}))}),e)},i.addEventListener(o.MediaElementEvent.loadstart,this.scheduleAvailabilityCheck)}return e.prototype.dispose=function(){this.assetAvailabilityCheckTimeoutID&&window.clearTimeout(this.assetAvailabilityCheckTimeoutID),this.mediaElement.removeEventListener(o.MediaElementEvent.loadstart,this.scheduleAvailabilityCheck),this.contentLoader&&(this.contentLoader.cancel(),this.contentLoader.dispose())},e}();t.AssetAvailabilityChecker=s},49688:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.NativeModuleDefinition=void 0;var n=i(86246),r=i(97483),a=i(39652);t.NativeModuleDefinition={name:n.ModuleName.EngineNative,module:{NativePlayer:r.NativePlayer,technologyChecker:new a.TechnologyChecker}},t.default=t.NativeModuleDefinition},53095:function(e,t,i){var n=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.FallbackHlsManifestController=void 0;var r,a=i(4792),o=i(65622),s=i(33964),l=i(55423),d=i(74749),u=i(47122),h=i(86246),c=i(37191),v=i(62253),p=i(3257),m=i(68404),f=function(e){function t(t){var i=e.call(this,t)||this;return i.onLoadPlaylistError=function(e){return null},r=u.ModuleManager.get(h.ModuleName.HLS).PlaylistUtils,i.hlsDownloadService=new p.HlsDownloadService(i.context),i.segmentTimelineController=new v.FallbackSegmentTimelineController(t.videoElement),i}return n(t,e),t.prototype.init=function(){return Promise.resolve()},t.prototype.getDownloadedVideoData=function(){return{id:"not available",bitrate:0,height:0,width:0,isAuto:!0}},t.prototype.getPlayingVideoData=function(){return null},t.prototype.downloadMasterManifest=function(e){var t=this;return this.hlsDownloadService.downloadPlaylist(e,l.HttpRequestType.MANIFEST_HLS_MASTER).then((function(e){var i=t.registerMasterManifest(e,e.url),n=t.parsedManifest.playlists[0].uri;return t.registerFirstVariantPlaylist(n).then((function(n){return t.overrideManifestUrl(i,e.url,n)}))}))},t.prototype.processMasterManifest=function(e,t){return m.replaceManifestUrls(t,e,this.settings.QUERY_PARAMETERS)},t.prototype.registerFirstVariantPlaylist=function(e){var t=this;return this.hlsDownloadService.downloadPlaylist(e,l.HttpRequestType.MANIFEST_HLS_VARIANT).then(this.registerVariantPlaylist).then((function(i){return r.isLive(i)&&t.scheduleReload(i,e),i}))},t.prototype.overrideManifestUrl=function(e,t,i){return"createObjectURL"in window.URL?this.getManifestUrl(e,i):(this.context.eventHandler.dispatchEvent(s.PlayerEvent.Warning,new a.PlayerWarning(o.WarningCode.SETUP_CREATE_OBJECT_URL_MISSING)),Promise.resolve(t))},t.prototype.getManifestUrl=function(e,t){var i=this;return r.isLive(t)?Promise.resolve(this.createDataUrlForManifest(e)):Promise.all(this.getMediaPlaylistPromises(e)).then((function(t){return t.filter((function(e){return Boolean(e)})).forEach((function(t){e=e.replace(t.mediaUrl,t.blobUrl)})),i.createDataUrlForManifest(e)}))},t.prototype.getMediaPlaylistPromises=function(e){var t=this;return e.split("\n").map((function(e){return m.parseUrlFromLine(e)})).filter((function(e){return null!==e})).map((function(e){return t.getMediaPlaylistBlobUrlMapping(e)}))},t.prototype.getMediaPlaylistBlobUrlMapping=function(e){var t=this;return this.hlsDownloadService.downloadPlaylist(e,l.HttpRequestType.MANIFEST_HLS_VARIANT).then((function(i){var n=m.replaceManifestUrls(e,i.body,t.settings.QUERY_PARAMETERS);return{blobUrl:t.createBlobForManifest(n),mediaUrl:e}})).catch(this.onLoadPlaylistError)},t.prototype.loadPlaylist=function(e){var t=this;return this.hlsDownloadService.downloadPlaylist(e.uri,l.HttpRequestType.MANIFEST_HLS_VARIANT).then((function(i){return{width:e.width,height:e.height,timeline:t.parseTimelineEntries(i.body)}})).catch(this.onLoadPlaylistError)},t.prototype.reload=function(e){var t=this;return function(){if(!t.hlsDownloadService)return function(){};t.hlsDownloadService.downloadPlaylist(e,l.HttpRequestType.MANIFEST_HLS_VARIANT,!1).then(t.registerVariantPlaylist).then((function(e){return r.isLive(e)&&t.scheduleReload(e,t.parsedManifest.playlists[0].uri),e})).catch(t.onLoadPlaylistError)}},t.prototype.scheduleReload=function(e,t){this.reloadTimerId=setTimeout(this.reload(t),(0,d.toMilliSeconds)(e.targetDuration))},t.prototype.createBlobForManifest=function(e){var t=new Blob([e],{type:"application/x-mpegURL"});return window.URL.createObjectURL(t)},t.prototype.createDataUrlForManifest=function(e){return"data:application/x-mpegURL,"+encodeURIComponent(e)},t.prototype.getVariantPlaylistInfos=function(){return this.parsedManifest.playlists.map((function(e){return{uri:e.uri,width:e.attributes&&e.attributes.RESOLUTION?e.attributes.RESOLUTION.width:-1,height:e.attributes&&e.attributes.RESOLUTION?e.attributes.RESOLUTION.height:-1}}))},t.prototype.loadAllVariantPlaylists=function(){var e=this,t=this.getVariantPlaylistInfos();t.reverse();var i=t.map((function(t){return e.loadPlaylist(t)}));return Promise.all(i).then((function(t){var i=e.getPlaybackTimeOfFirstSegmentInTimeline(t);return i>=0&&t.forEach((function(e){e.timeline.reduce((function(e,t){return t.playbackTime=e,e+t.duration}),i)})),t}))},t.prototype.getCurrentSegments=function(e,t,i,n){return void 0===n&&(n=1),this.loadAllVariantPlaylists().then((function(r){var a=r.find((function(e){return e.width===t&&e.height===i}));return g(a?[a]:r,n,e)}))},t.prototype.dispose=function(){e.prototype.dispose.call(this),clearTimeout(this.reloadTimerId),this.reloadTimerId=null},t}(c.AbstractHlsManifestController);function g(e,t,i){for(var n=[],r=0,a=0;a<e.length;a++){var o=e[a];r=0;for(var s=0;s<o.timeline.length;s++){var l=o.timeline[s];if(l.playbackTime+l.duration>=i&&(n.push(l.uri),r++),r>t)break}}return n}t.FallbackHlsManifestController=f},57547:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.createHlsManifestController=s,t.shouldUseServiceWorker=l;var n=i(47122),r=i(86246),a=i(37191),o=i(53095);function s(e){if(l(e)){var t=new(n.ModuleManager.get(r.ModuleName.ServiceWorkerClient).createServiceWorkerHlsManifestController(a.AbstractHlsManifestController))(e);return t.init().then((function(){return t})).catch((function(){return d(e)}))}return d(e)}function l(e){var t=e.config.location&&e.config.location.serviceworker;return"serviceWorker"in navigator&&t&&n.ModuleManager.has(r.ModuleName.ServiceWorkerClient)}function d(e){var t=new o.FallbackHlsManifestController(e);return t.init().then((function(){return t}))}},62253:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.FallbackSegmentTimelineController=void 0;var n=i(80523),r=function(){function e(e){this.videoElement=e,this.shouldCalculatePlaybackTimeForLive=!0,this.timelines={}}return e.prototype.isLiveStream=function(){return this.videoElement.duration===1/0},e.prototype.switchTimeline=function(e){this.timelines[e]||(this.currentActiveTimelineId=e,this.timelines[e]=[])},e.prototype.updateTimeline=function(e,t){var i=this,r=this.timelines[e],a=n.updateTimeline(r,t),o=a.findIndex((function(e){return e.hasOwnProperty("playbackTime")}));-1!==o&&n.populateCurrentTimelineWithPlaybackTime(a,o,a[o].playbackTime),this.timelines[e]=a;var s=a.reduce((function(e,t){return i.isLiveStream()&&!i.shouldCalculatePlaybackTimeForLive||(t.playbackTime=e),e+t.duration}),0);return this.shouldCalculatePlaybackTimeForLive&&(this.shouldCalculatePlaybackTimeForLive=!1),s},e.prototype.getPlayingSegment=function(e){for(var t=this.getCurrentTimeline(),i=0;i<t.length;i++){var n=t[i];if(e>=n.playbackTime&&e<=n.playbackTime+n.duration)return t[i]}return null},e.prototype.addDownloadedSegments=function(e){},e.prototype.getCurrentTimeline=function(){return this.timelines[this.currentActiveTimelineId]},e.prototype.getAllTimelines=function(){return this.timelines},e.prototype.reset=function(){this.timelines[this.currentActiveTimelineId].forEach((function(e){return e.consumed=!1}))},e.prototype.dispose=function(){this.timelines={},this.videoElement=null},e}();t.FallbackSegmentTimelineController=r},68404:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.replaceManifestUrls=a,t.parseUrlFromLine=o,t.replaceUrlAndAddUriParams=s;var n=i(83275),r=/URI="([^"]+)"/;function a(e,t,i){var n=t.split(/\r?\n/);return n.forEach((function(t,a){var l=o(t);if(l&&t.match(r)){var d=s(e,l,i);n[a]=t.replace(r,(function(){return'URI="'.concat(d,'"')}))}else l&&(n[a]=s(e,l,i))})),n.join("\n")}function o(e){if("#"!==e[0]&&e.trim().length>0)return e;if(e.indexOf("URI")>-1){var t=e.match(r);if(t&&t[1].indexOf("skd://")<0&&t[1].indexOf("skds://")<0)return t[1]}return null}function s(e,t,i){var r=n.URLHelper.concatBaseUrlWithPartial(e,t);return n.URLHelper.appendQueryParametersToUrl(r,i)}},73762:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.HlsDownloadErrorHandler=void 0;var n=i(81668),r=i(17854),a=i(51243),o=i(16692),s=i(55423),l=i(84815),d=i(98892),u=function(){function e(e,t){var i=this;this.fakeProgress=function(){for(var e=i.video.buffered,t="",n=0;n<e.length;n++)t+="".concat(e.start(n)," - ").concat(e.end(n),", ");t===i.lastProgressString?i.numProgressNotChanged++:i.numProgressNotChanged=0,i.numProgressNotChanged>=10&&i.onStalled(),i.lastProgressString=t},this.fakeWaiting=function(){var e=o.TimingUtil.getHiResTimestamp(),t=!i.video.paused&&!i.video.seeking&&!i.video.ended;e-i.lastTimeUpdateTimeStamp>5&&t&&i.onWaiting()},this.onStalled=function(){i.stalledTimeStamp=o.TimingUtil.getHiResTimestamp(),i.checkStalledState()},this.onProgress=function(){i.stalledTimeStamp=-1/0,clearTimeout(i.downloadErrorCheckTimeoutId)},this.onWaiting=function(){i.waitingEventTimeStamp=o.TimingUtil.getHiResTimestamp(),i.checkStalledState()},this.onTimeUpdate=function(){i.waitingEventTimeStamp=-1/0,i.lastTimeUpdateTimeStamp=o.TimingUtil.getHiResTimestamp(),clearTimeout(i.downloadErrorCheckTimeoutId)},this.checkForDownloadError=function(){if(0!==i.video.buffered.length){var e=i.video.currentTime,t=i.video.buffered.end(i.video.buffered.length-1),o=e<=t&&e+.5>t,l=i.video.paused,d=i.video.networkState===HTMLMediaElement.NETWORK_LOADING,u=i.video.videoWidth,h=i.video.videoHeight;!l&&o&&d&&i.manifestController.getCurrentSegments(t,u,h,2).then((function(e){var t=new a.DefaultContentLoader(i.context),o=function(i,n){return void 0===i&&(i=!0),e.length>0&&i?t.load(e.pop(),s.HttpRequestMethod.HEAD).then((function(e){return o(e.status>=200&&e.status<300,e)})):i?Promise.resolve():Promise.reject(n)};o().catch((function(e){var t=e.url,a=e.status,o=e.statusText;i.context.eventHandler.fireError(new r.PlayerError(n.ErrorCode.NETWORK_ERROR,{url:t,statusCode:a,statusText:o},"Failed to load segment ".concat(t,", statusCode: ").concat(a,", statusText: ").concat(o)))}))}))}},this.context=e,this.video=e.videoElement,this.manifestController=t,this.playbackAdvancing=!1,this.stalledTimeStamp=-1/0,this.waitingEventTimeStamp=-1/0,this.downloadErrorCheckTimeoutId=-1,this.lastProgressString="",this.numProgressNotChanged=0,this.lastTimeUpdateTimeStamp=-1/0,this.video.addEventListener(d.MediaElementEvent.waiting,this.onWaiting),this.video.addEventListener(d.MediaElementEvent.stalled,this.onStalled),this.video.addEventListener(d.MediaElementEvent.progress,this.onProgress),this.video.addEventListener(d.MediaElementEvent.timeupdate,this.onTimeUpdate),(0,l.getCapabilities)().isIOS&&(this.fakeEventsIntervalId=window.setInterval((function(){i.fakeProgress(),i.fakeWaiting()}),500))}return e.prototype.checkStalledState=function(){var e=Math.abs(this.waitingEventTimeStamp-this.stalledTimeStamp);clearTimeout(this.downloadErrorCheckTimeoutId),isFinite(e)&&(this.downloadErrorCheckTimeoutId=window.setTimeout(this.checkForDownloadError,1e3*(5-Math.min(5,e))))},e.prototype.dispose=function(){clearTimeout(this.fakeEventsIntervalId),clearTimeout(this.downloadErrorCheckTimeoutId),this.video.removeEventListener(d.MediaElementEvent.waiting,this.onWaiting),this.video.removeEventListener(d.MediaElementEvent.stalled,this.onStalled),this.video.removeEventListener(d.MediaElementEvent.progress,this.onProgress),this.video.removeEventListener(d.MediaElementEvent.timeupdate,this.onTimeUpdate),this.video=null,this.manifestController=null},e}();t.HlsDownloadErrorHandler=u},91988:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.AppleAirplayService=t.isAirplaySupported=void 0;var n,r=i(33964),a=i(98892);!function(e){e.Available="available",e.NotAvailable="not-available"}(n||(n={}));var o=function(){return Boolean(window.WebKitPlaybackTargetAvailabilityEvent)};t.isAirplaySupported=o;var s=function(){function e(e,t){var i=this;this.videoElement=e,this.eventHandler=t,this.airplayAvailable=!1,this.onWebkitPlaybackTargetAvailabilityChanged=function(e){switch(e.availability){case n.Available:i.airplayAvailable=!0,i.eventHandler.dispatchEvent(r.PlayerEvent.AirplayAvailable);break;case n.NotAvailable:i.airplayAvailable=!1}},this.onWebkitPlaybackTargetIsWirelessChanged=function(){i.eventHandler.dispatchEvent(r.PlayerEvent.AirplayChanged,{airplayEnabled:i.isAirplayActive(),time:i.videoElement.currentTime})},this.initAirplay()}return e.prototype.initAirplay=function(){this.videoElement.setAttribute("x-webkit-airplay","allow"),this.videoElement.addEventListener(a.MediaElementEvent.webkitplaybacktargetavailabilitychanged,this.onWebkitPlaybackTargetAvailabilityChanged),this.videoElement.addEventListener(a.MediaElementEvent.webkitcurrentplaybacktargetiswirelesschanged,this.onWebkitPlaybackTargetIsWirelessChanged)},e.prototype.isAirplayAvailable=function(){return this.airplayAvailable},e.prototype.isAirplayActive=function(){return this.videoElement.isAirplayActive()},e.prototype.showAirplayTargetPicker=function(){this.isAirplayAvailable()&&(this.videoElement.webkitShowPlaybackTargetPicker(),this.eventHandler.dispatchEvent(r.PlayerEvent.ShowAirplayTargetPicker))},e.prototype.dispose=function(){this.videoElement.removeEventListener(a.MediaElementEvent.webkitplaybacktargetavailabilitychanged,this.onWebkitPlaybackTargetAvailabilityChanged),this.videoElement.removeEventListener(a.MediaElementEvent.webkitcurrentplaybacktargetiswirelesschanged,this.onWebkitPlaybackTargetIsWirelessChanged)},e}();t.AppleAirplayService=s},97483:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};this&&this.__decorate;Object.defineProperty(t,"__esModule",{value:!0}),t.NativePlayer=void 0;var r=i(50238),a=i(81668),o=i(17854),s=i(4792),l=i(65622),d=i(9851),u=i(75671),h=i(33964),c=i(10478),v=i(82049),p=i(51243),m=i(50930),f=i(844),g=i(41497),y=i(84815),T=i(87402),S=i(12291),E=i(10395),b=i(40060),k=i(69837),P=i(74749),M=i(58974),C=i(83275),A=i(89336),L=i(54668),D=i(98892),I=i(47122),w=i(86246),R=i(55423),H=i(91988),O=i(47783),N=i(73762),_=i(30986),V=i(57547),x=i(9280),U=2,F=.1,W=.25,B="not available",j=.75,Q=2,G=0,q=function(){function e(e){var t=this;this.numTimeChanged=0,this.loaded=!1,this.audio=null,this.ended=!1,this.ignoreNextVideoError=!1,this.controlPermitted=!0,this.seekingTimeoutId=-1,this.nativeSeekCalled=!1,this.isIllicitlySeeking=!1,this.ignoreNextDelta=!1,this.numSeekingCalled=0,this.timeShiftJumpStartPosition=-1,this.seekTargetTime=-1,this.textTrackController=null,this.lastPlayIssuer="api",this.lastPauseIssuer="api",this.lastSeekIssuer="api",this.sourceLoadRetryCount=0,this.lastVerifiedPlaybackPosition=-1,this.lastLegalPlaybackPosition=0,this.restrictedInlinePlayback=!1,this.lastTime=0,this.availableVideoStreams=[],this.allowedToThrowEvents=!0,this.hasSourceBeenLoaded=!1,this.hasDataBeenLoaded=!1,this.sourceElementEventListeners=[],this.restoringPlaybackPosAfterQualitySwitch=!1,this.isInitialSeek=!1,this.lastDelayedSeekHandlers={},this.isProgressiveQualityChange=!1,this.isSourceBeingRedirected=!1,this.isInNativeFullscreen=!1,this.onAddTrack=function(e){if(t.isReady()){var i=e.track;t.eventCallback(h.PlayerEvent.AudioAdded,{track:t.convertAudioTrackNativeToApi(i)}),i.enabled&&t.loaded&&t.dispatchAudioChangedEvent(null,i)}},this.onRemoveTrack=function(e){t.isReady()&&t.eventCallback(h.PlayerEvent.AudioRemoved,{track:t.convertAudioTrackNativeToApi(e.track)})},this.onWebkitPresentationModeChanged=function(){var e,i,n=t.video.getViewModeFromPresentationMode();n===m.ViewMode.PictureInPicture||n===m.ViewMode.Fullscreen&&(0,y.getCapabilities)().isIOS?null===(e=t.textTrackController)||void 0===e||e.enableNative():null===(i=t.textTrackController)||void 0===i||i.disableNative(),t.context.store.dispatch((0,f.setPlayerViewMode)(n))},this.onDurationChange=function(){(0,c.maybeFireDurationChangedEvent)(t.lastDuration,t.getDuration(),t.eventHandler),t.lastDuration=t.getDuration(),t.hasDataBeenLoaded&&t.maybeResolveDeferredLoading()},this.videoOnPlay=function(){t.stateManager.stallExit(),t.restrictedInlinePlayback&&t.audio&&t.setVideoTime(),t.ended=!1,t.stateManager.transitionToPlayState(!0,t.lastPlayIssuer)},this.videoOnPlaying=function(){t.stateManager.isStalled?(t.stateManager.stallExit(),t.stateManager.transitionToPlayingState(t.lastPlayIssuer)):t.stateManager.isStopped()?t.onPlaybackStarted():t.stateManager.isPlaying()||(t.stateManager.transitionToPlayState(!0,t.lastPlayIssuer),t.stateManager.transitionToPlayingState(t.lastPlayIssuer))},this.loadedDataHandler=function(){var e;clearTimeout(t.loadedDataFallbackTimeoutID),null===(e=t.assetAvailabilityChecker)||void 0===e||e.dispose(),t.hasSourceBeenLoaded||clearTimeout(t.loadTimeoutID),t.hasSourceBeenLoaded=!0,t.isProgressiveQualityChange=!1,t.allowedToThrowEvents=!0,t.hasDataBeenLoaded=!0,t.maybeResolveDeferredLoading()},this.eventCallback=function(e,i,n,r){void 0===i&&(i={}),void 0===n&&(n=!1),void 0===r&&(r=!1),t.eventHandler&&(t.allowedToThrowEvents||n)&&t.eventHandler.dispatchEvent(e,i,r)},this.onTimeUpdate=function(e){var i=t.getElement(),n=i.currentTime;(-1!==t.lastVerifiedPlaybackPosition||n!==t.lastVerifiedPlaybackPosition)&&!t.isIllicitlySeeking&&(t.isIllegalSeek(n)?(Promise.resolve(t.video.play()).catch((function(e){})),t.ignoreNextDelta=!0,i.currentTime=t.lastLegalPlaybackPosition):t.handleTimeUpdateEvent(i,n),t.nativeSeekCalled=!1,t.currentVideoStream&&(t.currentVideoStream.width=t.video.videoWidth,t.currentVideoStream.height=t.video.videoHeight))},this.onPlaybackStarted=function(){t.isPlaying()||(t.ended=!1,t.startManifestTimeoutTimer(),t.stateManager.transitionToPlayingState(t.lastPlayIssuer))},this.onContentPlaybackFinished=function(){t.ended=!0},this.getCurrentTime=function(e){void 0===e&&(e=m.TimeMode.AbsoluteTime);var i=t.getStartDate();return e===m.TimeMode.AbsoluteTime&&i?A.Util.timeInSeconds(i)+t.getElement().currentTime:t.getElement().currentTime},this.getAudioQuality=this.getPlaybackAudioData,this.getVideoQuality=this.getPlaybackVideoData,this.videoOnEnded=function(){var e,i=t.getElement(),n=Math.abs(i.currentTime-t.lastLegalPlaybackPosition),r=t.settings.GLOBAL_DISABLE_SEEKING,a=null!==(e=t.currentDuration)&&void 0!==e?e:i.duration;r&&(n>j||i.currentTime<a-j)?(Promise.resolve(t.video.play()).catch((function(e){})),t.ignoreNextDelta=!0,t.video.currentTime=t.lastLegalPlaybackPosition):(t.lastLegalPlaybackPosition=0,t.stateManager.isPlaying()&&t.stateManager.transitionToPausedState(!0,t.lastPauseIssuer),t.stateManager.transitionToStoppedState())},this.videoOnSeeked=function(){t.resetTimeShift(),t.numSeekingCalled=0,!t.settings.GLOBAL_DISABLE_SEEKING||t.restoringPlaybackPosAfterQualitySwitch||t.isInitialSeekToStartTime?(t.lastVerifiedPlaybackPosition=t.getElement().currentTime,t.isInitialSeekToStartTime=!1,t.stateManager.isSeeking()&&(t.textTrackController&&t.textTrackController.seek(),t.maybeHandleSeekTargetChangeDuringStartup())):t.isIllicitlySeeking=!1,t.finishedSeekedTransitionState()},this.videoOnSeeking=function(){if(t.settings.GLOBAL_DISABLE_SEEKING&&!t.isInitialSeekToStartTime)t.isIllicitlySeeking=!t.restoringPlaybackPosAfterQualitySwitch,t.numSeekingCalled++,t.numSeekingCalled>1&&(t.ignoreNextDelta=!1);else if(!t.stateManager.isSeeking()&&!t.stateManager.isTimeShifting()){var e=t.getElement().currentTime;t.stateManager.transitionToSeekingIOSState(e,v.INTERNAL_ISSUER_NAME),t.lastVerifiedPlaybackPosition=e}},this.onTimeChanged=function(){void 0===t.maxTimeShift&&(t.maxTimeShift=t.getElement().currentTime)},this.delayedSeek=function(){t.numTimeChanged++,t.numTimeChanged<t.settings.IOS_MIN_TIMEUPDATES_AFTER_AD||(t.eventHandler.off(h.PlayerEvent.TimeChanged,t.delayedSeek),t.restoringPlaybackPosAfterQualitySwitch=!0,t.triggerSeek({time:t.currentTimeWas,issuer:"api",force:!0}))},this.context=e,this.isAtLiveEdge=!1,this.logger=e.logger,this.video=e.videoElement,this.config=e.config,this.eventHandler=e.eventHandler,this.settings=e.settings,this.hlsSeekingGlitchEventHandlers=[],this.downloadErrorHandler=null,this.currentVol=this.video.volume,this.originalCrossOriginAttributeValue=this.video.getAttribute("crossOrigin"),this.currentTimeShift=-1/0,(0,H.isAirplaySupported)()&&(this.airplayService=new H.AppleAirplayService(this.video,this.eventHandler)),this.hlsLiveTimeTracker=new _.HlsLiveNativeTimeTracker(this,e),this.elementEvents=[{event:D.MediaElementEvent.pause,handler:function(){t.resetTimeShift(),t.ended||t.stateManager.transitionToPausedState(!0,t.lastPauseIssuer,!0)}},{event:D.MediaElementEvent.ended,handler:function(){t.videoOnEnded()}},{event:D.MediaElementEvent.play,handler:this.videoOnPlay},{event:D.MediaElementEvent.canplay,handler:function(){t.stateManager.stallExit()}},{event:D.MediaElementEvent.seeking,handler:function(){t.nativeSeekCalled=!0,t.videoOnSeeking(),t.getCurrentTime()>=t.getDuration()&&(t.videoOnSeeked(),t.videoOnEnded())}},{event:D.MediaElementEvent.seeked,handler:function(){t.videoOnSeeked(),t.restrictedInlinePlayback&&t.audio&&t.setVideoTime()}},{event:D.MediaElementEvent.durationchange,handler:this.onDurationChange},{event:D.MediaElementEvent.loadeddata,handler:this.loadedDataHandler},{event:D.MediaElementEvent.loadedmetadata,handler:function(){t.loadedDataFallbackTimeoutID=window.setTimeout(t.loadedDataHandler,200)}},{event:D.MediaElementEvent.playing,handler:this.videoOnPlaying},{event:D.MediaElementEvent.waiting,handler:function(){t.resetTimeShift(),t.stateManager.stallEnter()}},{event:D.MediaElementEvent.timeupdate,handler:this.onTimeUpdate},{event:D.MediaElementEvent.error,handler:function(e){t.hasSourceBeenLoaded||1!==t.availableVideoStreams.length||t.type!==m.StreamType.Progressive?(t.loadingDeferred.reject(),t.ignoreNextVideoError?t.ignoreNextVideoError=!1:(clearTimeout(t.loadTimeoutID),t.eventHandler.fireError((0,c.createPlayerErrorFromMediaError)(e,!0)))):t.handleStreamErrorEvent(a.ErrorCode.SOURCE_PROGRESSIVE_STREAM_ERROR,t.video.getWrappedElement().src,e)}},{event:D.MediaElementEvent.webkitpresentationmodechanged,handler:this.onWebkitPresentationModeChanged},{elementSelector:function(e){return e.audioTracks},event:"addtrack",handler:this.onAddTrack},{elementSelector:function(e){return e.audioTracks},event:"removetrack",handler:this.onRemoveTrack}]}return e.prototype.shouldAwaitDurationAvailability=function(){return!(0,k.isWebOS)()&&!this.getDuration()},e.prototype.shouldAwaitSeekableRange=function(){var e,t;if(!this.isHlsLive()||(null===(t=null===(e=this.config)||void 0===e?void 0:e.tweaks)||void 0===t?void 0:t.prevent_video_element_preloading)||(0,k.isWebOS)()||this.isSourceBeingRedirected)return!1;var i=this.video.readyState>=HTMLMediaElement.HAVE_CURRENT_DATA;return!$(this.getElement().seekable)&&!i},e.prototype.maybeResolveDeferredLoading=function(){this.shouldAwaitDurationAvailability()||this.shouldAwaitSeekableRange()||this.loadingDeferred.resolve()},e.prototype.handleTimeUpdateEvent=function(e,t){(this.ignoreNextDelta=!1,this.currentDuration=e.duration,t>this.currentDuration+Q)?e.currentTime=this.currentDuration-.1:(this.lastLegalPlaybackPosition=t,this.stateManager.stallExit(),this.stateManager.isSeeking()||this.stateManager.isTimeShifting()||!this.isPlaying()||(null!=this.getStartDate()?this.eventCallback(h.PlayerEvent.TimeChanged,{time:this.getCurrentTime(),relativeTime:t}):this.eventCallback(h.PlayerEvent.TimeChanged,{time:this.getCurrentTime()})))},e.prototype.isIllegalSeek=function(e){if(!this.settings.GLOBAL_DISABLE_SEEKING||!this.nativeSeekCalled)return!1;var t=-1!==this.lastVerifiedPlaybackPosition&&e>this.lastVerifiedPlaybackPosition+j,i=Math.abs(e-this.lastLegalPlaybackPosition);return(!this.ignoreNextDelta||t)&&i>j},e.prototype.getElement=function(){return this.restrictedInlinePlayback&&this.audio?this.audio:this.video},e.prototype.setVideoTime=function(){var e=Date.now();(0,P.toSeconds)(e-this.lastTime)>=1/30&&(this.video.currentTime=this.audio.currentTime,this.lastTime=e),this.isPlaying()&&window.requestAnimationFrame(this.setVideoTime)},e.prototype.maybeGoToLiveEdgeOnPlay=function(){if(this.isLive()&&!this.isTimeShiftingAllowed()){var e=this.getSeekableRangeInternal();e.end>=0&&this.seekForLive(e.end,v.STARTUP_ISSUER_NAME)}},e.prototype.resolveDelayedSeek=function(){void 0!==this.delayedSeekTarget&&(this.clearDelayedSeekHandlers(),this.seek(this.delayedSeekTarget,"api-play"))},e.prototype.play=function(e){return this.controlPermitted?(this.lastPlayIssuer=e,this.resolveDelayedSeek(),this.maybeGoToLiveEdgeOnPlay(),this.getElement().play()):Promise.reject("Play control not permitted")},e.prototype.getStartDate=function(){if(!this.isLive())return null;var e=this.getElement().getWrappedElement(),t="function"==typeof e.getStartDate?e.getStartDate():null;return t&&!isNaN(t.getTime())?t:null},e.prototype.preload=function(){this.getElement().preload="auto"},e.prototype.pause=function(e){this.lastPauseIssuer=e;var t=this.getElement();this.controlPermitted&&this.loaded&&(this.stateManager.transitionToPausedState(!1,this.lastPauseIssuer),t.pause())},e.prototype.mute=function(e){this.getElement().muted||(this.getElement().muted=!0,this.eventCallback(h.PlayerEvent.Muted,{issuer:e||"api"}))},e.prototype.unmute=function(e){this.getElement().muted&&(this.getElement().muted=!1,this.eventCallback(h.PlayerEvent.Unmuted,{issuer:e||"api"}))},e.prototype.getActualSeekTarget=function(e){var t=this.getStartDate();return t?e-(0,P.toSeconds)(t.getTime()):e},e.prototype.seek=function(e,t){var i=this;return!(!this.isSeekingAllowed(e)||this.getCurrentTime()===e)&&(this.hlsLiveTimeTracker.adjustTargetTime(e,t).then((function(e){i.lastSeekIssuer=t,i.triggerSeekEvent(e,t);var n=i.getActualSeekTarget(e);return i.triggerSeek({time:n,issuer:t})})),this.hasDataBeenLoaded&&this.getDuration()>0)},e.prototype.seekForLive=function(e,t){this.timeShiftJumpStartPosition=this.getCurrentTime(),this.lastSeekIssuer=t,this.triggerTimeShiftEvent(e,t),this.triggerSeekForLive({time:e,issuer:t})},e.prototype.isSeekingAllowed=function(e){this.isInitialSeekToStartTime=this.isInitialSeek&&this.hasStartTime(e);var t=!this.settings.GLOBAL_DISABLE_SEEKING&&this.isSeekingEnabled();return(this.isInitialSeekToStartTime||t)&&this.controlPermitted},e.prototype.isTimeShiftingAllowed=function(){var e=!(this.config.playback&&this.config.playback.hasOwnProperty("timeShift"))||Boolean(this.config.playback.timeShift);return this.isLive()&&e},e.prototype.triggerSeek=function(e){return!this.maybeDelaySeek(e)&&(this.stateManager.transitionToSeekingState(e.time,e.issuer,!1),this.finishSeek(e),!0)},e.prototype.triggerSeekForLive=function(e){this.maybeDelaySeek(e,!0)||(this.stateManager.transitionToTimeShiftingState(this.getCurrentTime(),e.time,e.issuer,!1),e.time<0&&(e.time=this.getSeekableRangeInternal().end+e.time,this.resetTimeShift()),this.finishSeek(e))},e.prototype.maybeDelaySeek=function(e,t){return void 0===t&&(t=!1),this.stateManager.isSeeking()&&"api-play"!==e.issuer?(this.scheduleDelayedSeek(e,t,[D.MediaElementEvent.seeked]),!0):this.hasDataBeenLoaded?0===this.getDuration()?(this.scheduleDelayedSeek(e,t,[D.MediaElementEvent.durationchange]),!0):this.video.seekable.length<1&&(this.scheduleDelayedSeek(e,t,[D.MediaElementEvent.timeupdate,D.MediaElementEvent.canplay]),!0):(this.scheduleDelayedSeek(e,t,[D.MediaElementEvent.loadeddata]),!0)},e.prototype.finishSeek=function(e){var t=e.issuer===v.ADVERTISING_ISSUER_NAME,i=this.getSeekableRangeInternal(),n=this.adjustSeekTimeForBoundaries(e.time,i,t);this.isInitialSeek=!1,this.ignoreNextDelta=this.ignoreNextDelta||this.isInitialSeekToStartTime||e.force;var r=parseFloat(n.toFixed(U));this.seekTargetTime=r,this.getElement().currentTime=r},e.prototype.adjustSeekTimeForBoundaries=function(e,t,i){void 0===i&&(i=!1);var n=J(e,t);return i?e>=t.end&&(n=t.end-F):n=Math.min(n,t.end-this.settings.SEEK_TO_END_OFFSET),n},e.prototype.isSeekingEnabled=function(){var e=this.config&&this.config.hasOwnProperty("playback")?this.config.playback:{};return!e.hasOwnProperty("seeking")||e.seeking},e.prototype.hasStartTime=function(e){var t=this.context.sourceContext.source?this.context.sourceContext.source:{};return t.hasOwnProperty("options")&&t.options.hasOwnProperty("startTime")&&e===t.options.startTime},e.prototype.triggerSeekEvent=function(e,t){t!==v.STARTUP_ISSUER_NAME&&this.eventCallback(h.PlayerEvent.Seek,{position:this.getCurrentTime(),seekTarget:e,issuer:t||"api"})},e.prototype.triggerTimeShiftEvent=function(e,t){t!==v.STARTUP_ISSUER_NAME&&this.eventCallback(h.PlayerEvent.TimeShift,{target:e<0?this.timeShiftJumpStartPosition+e:e,position:this.timeShiftJumpStartPosition,issuer:t||"api"})},e.prototype.waitForOnSeeked=function(e){var t=this;return new Promise((function(i){var n=function(){e.removeEventListener(D.MediaElementEvent.seeked,n),i()};t.hlsSeekingGlitchEventHandlers.push({el:e,ev:D.MediaElementEvent.seeked,fn:n}),e.addEventListener(D.MediaElementEvent.seeked,n)}))},e.prototype.waitForOnProgress=function(e,t){var i=this;return new Promise((function(n,r){var a=0,o=function(){var s=e.buffered,l=s.length>0?s.end(s.length-1):-1/0;(a+=isFinite(l)?1:0)>i.settings.SAFARI_NUM_PROGRESS_EVENTS_AFTER_SEEK_GLITCH&&l<t?(e.removeEventListener(D.MediaElementEvent.progress,o),r(null)):l>=t&&(e.removeEventListener(D.MediaElementEvent.progress,o),n())};i.hlsSeekingGlitchEventHandlers.push({el:e,ev:D.MediaElementEvent.progress,fn:o}),e.addEventListener(D.MediaElementEvent.progress,o)}))},e.prototype.verifyHLSPlaybackBuffer=function(e,t){var i=this;return this.type!==m.StreamType.Hls?Promise.resolve():this.waitForOnSeeked(e).then((function(){return i.waitForOnProgress(e,t)})).catch((function(){throw null}))},e.prototype.clearDelayedSeekHandlers=function(){var e=this;Object.keys(this.lastDelayedSeekHandlers).forEach((function(t){e.getElement().removeEventListener(t,e.lastDelayedSeekHandlers[t])})),this.lastDelayedSeekHandlers={}},e.prototype.scheduleDelayedSeek=function(e,t,i){var n=this,r=this.getElement();this.clearDelayedSeekHandlers(),this.delayedSeekTarget=e.time;var a=function(i){n.clearDelayedSeekHandlers();var o={time:(0,M.isNumber)(i)?i:e.time,issuer:e.issuer,force:e.force};n.verifyHLSPlaybackBuffer(r,e.time).catch((function(){return a(e.time+1)})),n.delayedSeekTarget=void 0,t?n.triggerSeekForLive(o):n.triggerSeek(o)};i.forEach((function(e){r.addEventListener(e,a),n.lastDelayedSeekHandlers[e]=a}))},e.prototype.setAudio=function(e){var t=S.ArrayHelper.toArray(this.video.audioTracks).find((function(t){return t.id===e}));t&&this.enableAudioTrack(t)},e.prototype.dispatchAudioChangedEvent=function(e,t){this.eventCallback(h.PlayerEvent.AudioChanged,{targetAudio:{id:t.id,label:t.label,lang:t.language},sourceAudio:e,time:this.video.currentTime})},e.prototype.enableAudioTrack=function(e){if(!e.enabled){var t=S.ArrayHelper.toArray(this.video.audioTracks).find((function(e){return e.enabled})),i=null;t&&(t.enabled=!1,i=this.convertAudioTrackNativeToApi(t)),e.enabled=!0,this.dispatchAudioChangedEvent(i,e)}},e.prototype.setVolume=function(e,t){var i=this.getElement(),n=this.currentVol,r=Math.min(e/100,1);n!==r&&(this.currentVol=r,i.volume=r,this.eventCallback(h.PlayerEvent.VolumeChanged,{targetVolume:100*r,sourceVolume:100*n,issuer:t||"api"}))},e.prototype.startManifestTimeoutTimer=function(){var e=this;clearTimeout(this.loadTimeoutID),this.hasSourceBeenLoaded||(this.loadTimeoutID=setTimeout((function(){e.loadTimeoutID=null,!e.hasSourceBeenLoaded&&e.eventHandler&&(e.type===m.StreamType.Hls?e.eventHandler.fireError(new o.PlayerError(a.ErrorCode.NETWORK_MANIFEST_DOWNLOAD_TIMEOUT,{sourceUrl:e.sourceConfig.url},"Failed to load the HLS playlist: the request timed out.")):e.type===m.StreamType.Progressive&&e.eventHandler.fireError(new o.PlayerError(a.ErrorCode.NETWORK_PROGRESSIVE_STREAM_DOWNLOAD_TIMEOUT,{sourceUrl:e.sourceConfig.url},"Failed to load the progressive source: the request timed out.")))}),(0,P.toMilliSeconds)(this.settings.XHR_TIMEOUT/2)))},e.getContentDomain=function(e){var t=document.createElement("a");return t.href=e,""===t.host&&(t.href=t.href),t.hostname},e.prototype.trackLoading=function(e){var t;(0,k.isTizen)()&&(null===(t=this.assetAvailabilityChecker)||void 0===t||t.dispose(),this.assetAvailabilityChecker=new O.AssetAvailabilityChecker(this.context,e,this.video))},e.prototype.prepareLoad=function(){return Promise.resolve()},e.prototype.load=function(t,i,n){var r=this;this.stateManager=this.context.serviceManager.get(d.ServiceName.PlayerStateService),this.stateManager.transitionToSeekedState(!1),this.video.setAttribute("preload","metadata"),this.type=this.context.sourceContext.technology.streaming,this.sourceConfig=t,t.type&&(this.type=t.type),!0===this.loaded&&this.unload().catch((function(){})),this.hasSourceBeenLoaded=!1,this.hasDataBeenLoaded=!1,this.getElement().nativeMediaEventListener.reset(),this.isInitialSeek=!0,this.ignoreNextVideoError=!1,this.sourceLoadRetryCount=0,this.delayedSeekTarget=void 0,this.isSourceBeingRedirected=!1,t.hasOwnProperty("vr")&&t.vr&&Boolean(t.vr.restrictedInlinePlayback)?(this.restrictedInlinePlayback=!0,this.audio=A.Util.getVRAudioElement(),this.addEventListeners(this.audio)):(this.audio=null,this.restrictedInlinePlayback=!1,this.addEventListeners(this.video)),this.eventHandler.on(h.PlayerEvent.TimeChanged,this.onTimeChanged),this.unsubscribeFromStoreStoppedListener=(0,T.subscribe)(this.context.store)((function(e){return(0,g.getIsStopped)((0,g.getPlayerState)(e))}),this.onContentPlaybackFinished,(function(e){return!0===e})),this.settings.GLOBAL_DISABLE_SEEKING=i||!1,this.seekingTimeoutId>-1&&(clearTimeout(this.seekingTimeoutId),this.seekingTimeoutId=-1);var a="";t&&t.url&&("string"==typeof t.url?a=t.url:t.url.length>0&&(a=t.url[0].url));var o=window.location.hostname===e.getContentDomain(a);t.hasOwnProperty("vr")&&Boolean(t.vr)&&!o&&(this.originalCrossOriginAttributeValue=this.video.getAttribute("crossOrigin"),this.video.setAttribute("crossOrigin","anonymous")),this.video.eventHandler.reset(),this.isProgressiveQualityChange||(this.textTrackController=this.createTextTrackController()),this.loadingDeferred=new E.Deferred;var s=this.loadSource(t,n);return this.loadingDeferred.promise.then((function(){r.context.sourceContext.source=t.config})).then((function(){return s})).then((function(){return r.initializeLoadedState(t)}))},e.prototype.maybeHandleRedirectedSource=function(e){var t=this;(0,k.isSafari17orIOS17)()&&fetch(e.url,{method:R.HttpRequestMethod.GET,headers:{Range:"bytes=0-0"}}).then((function(e){e.redirected&&(t.hasDataBeenLoaded&&t.loadingDeferred.resolve(),t.isSourceBeingRedirected=!0)})).catch((function(){}))},e.prototype.loadSource=function(e,t){var i=this;return this.type===m.StreamType.Hls?(this.maybeHandleRedirectedSource(e),this.loadHls(e)):this.type===m.StreamType.Progressive?this.loadProgressive(e,t).then((function(){return i.loadVideoElement()})):Promise.resolve()},e.prototype.initializeLoadedState=function(e){this.currentSource=e,this.loaded=!0,this.lastLegalPlaybackPosition=0,this.ignoreNextDelta=!1},e.prototype.loadVideoElement=function(){this.config.tweaks.prevent_video_element_preloading||this.video.load()},e.prototype.setupVideoElement=function(t,i){var n=this;if(t.config.drm&&t.config.drm.fairplay){var r=I.ModuleManager.get(w.ModuleName.DRM).FairplayHandler;this.fpsHandler=new r(this.context,this.video,t.config.drm.fairplay)}var o=e.createSourceElement(i,null);this.appendAndSetVideoStream(o,"0",t),this.trackLoading(o);var s={element:o,handler:function(e){return n.handleStreamErrorEvent(a.ErrorCode.SOURCE_HLS_STREAM_ERROR,o.src,e)}};this.sourceElementEventListeners.push(s),s.element.addEventListener(D.MediaElementEvent.error,s.handler)},e.prototype.setSourceAndLoadVideoElement=function(e,t){this.setupVideoElement(e,t),this.loadVideoElement()},e.prototype.setVideoForServiceWorker=function(e,t){var i=C.URLHelper.appendQueryParametersToUrl(t,{"bitmovin-player-id":this.context.internalPlayerID});return this.setSourceAndLoadVideoElement(e,i),this.hlsManifestController.resolveMasterManifest(e.url).then((function(){})).catch((function(e){}))},e.prototype.setVideoForNativeHLSParsing=function(e,t){var i=this;return V.createHlsManifestController(this.context).then((function(n){return i.hlsManifestController=n,i.settings.NATIVE_HLS_DOWNLOAD_ERROR_HANDLING&&(i.downloadErrorHandler=new N.HlsDownloadErrorHandler(i.context,n)),V.shouldUseServiceWorker(i.context)?i.setVideoForServiceWorker(e,t):i.hlsManifestController.resolveMasterManifest(e.url).then((function(t){0!==t.indexOf("data")&&0!==t.indexOf("blob")||i.setSourceAndLoadVideoElement(e,t)})).catch((function(e){}))}))},e.prototype.loadHls=function(e){var t;if("string"!=typeof e.url){var i=new o.PlayerError(a.ErrorCode.SOURCE_INVALID,{given:e.url},"Cannot load the HLS playlist: the source URL should be a string.");return null===(t=this.eventHandler)||void 0===t||t.fireError(i),this.loadingDeferred.reject(i),Promise.resolve()}var n=this.adjustToFullUrlWithQueryParams(e.url);return this.settings.NATIVE_HLS_PARSING?this.setVideoForNativeHLSParsing(e,n):(this.setSourceAndLoadVideoElement(e,n),Promise.resolve())},e.prototype.loadProgressive=function(e,t){if(this.availableVideoStreams=[],Array.isArray(e.url))this.loadProgressiveMultiSource(e.url,t);else{var i={url:e.url,bitrate:0};this.video.src=this.adjustToFullUrlWithQueryParams(i.url),this.addStreamToAvailableOnes("0",i),this.setCurrentVideoStream("0",i),this.trackLoading(this.video.getWrappedElement())}return Promise.resolve()},e.prototype.loadProgressiveMultiSource=function(e,t){var i=this,n=!1;t=t||Y(e);var r=this.loadAllSupportedProgressiveSources(e,t,!n);if(r){var o={element:r,handler:function(e){return i.handleStreamErrorEvent(a.ErrorCode.SOURCE_PROGRESSIVE_STREAM_ERROR,r.src,e)}};this.sourceElementEventListeners.push(o),o.element.addEventListener(D.MediaElementEvent.error,o.handler),this.restrictedInlinePlayback&&this.audio&&this.audio.load(),this.trackLoading(r)}},e.prototype.handleStreamErrorEvent=function(e,t,i){var n,r=this;clearTimeout(this.loadTimeoutID),null===(n=this.assetAvailabilityChecker)||void 0===n||n.dispose(),this.loaded=!0,new p.DefaultContentLoader(this.context).load(t).then((function(e){return{status:e.status,statusText:e.statusText,error:void 0}})).catch((function(e){return{error:e,status:(null==e?void 0:e.status)||0,statusText:(null==e?void 0:e.statusText)||""}})).then((function(n){var a,s=n.error,l=n.status,d=n.statusText,u=(null==i?void 0:i.message)||i,h=function(e,t){r.loadingDeferred.reject(new o.PlayerError(e,t,"The video element has thrown an error while loading the stream."))};l!==200?h(e,a={event:i,error:s,status:l,statusText:d,src:t,extraMessage:u}):(a={event:i,error:s,src:t,extraMessage:u},r.sourceConfig&&r.sourceLoadRetryCount<r.settings.MAX_RETRIES?(r.sourceLoadRetryCount++,r.loadSource(r.sourceConfig).catch((function(){return h(e,a)}))):h(e,a))}))},e.prototype.loadAllSupportedProgressiveSources=function(t,i,n){for(var r,a=[],o=-1,s=null,l=null,d=0;d<t.length;d++){var u=t[d];if(!this.shouldIgnoreSourceObject(u,l,n)){if(r=e.createSourceElement(this.adjustToFullUrlWithQueryParams(u.url),u.type),l=l||u.type,this.addStreamToAvailableOnes(String(d),u),n){if(a.indexOf(u.type)>-1)continue;i===String(d)?(s=r,o=u.bitrate,this.appendAndSetVideoStream(r,String(d),u),a.push(u.type)):o===u.bitrate&&(s=r,this.appendVideoStream(r),a.push(u.type))}else s=r,this.appendAndSetVideoStream(r,String(d),u);this.restrictedInlinePlayback&&this.audio&&(this.audio.innerHTML=this.video.innerHTML)}}return s},e.prototype.shouldIgnoreSourceObject=function(t,i,n){return!e.isValidProgressiveSourceObject(t)||!!n&&(!this.isMimeTypeSupported(t.type)||!!i&&i!==t.type)},e.isValidProgressiveSourceObject=function(e){return"object"==typeof e&&(e&&e.url&&""!==e.url)},e.prototype.appendAndSetVideoStream=function(e,t,i){this.video.appendChild(e),this.setCurrentVideoStream(t,i)},e.prototype.appendVideoStream=function(e){this.video.appendChild(e)},e.prototype.addStreamToAvailableOnes=function(e,t){var i={id:e};t.bitrate&&(i.bitrate=t.bitrate),i.label=String(t.label||t.bitrate||"unknown"),this.availableVideoStreams.push(i)},e.prototype.setCurrentVideoStream=function(e,t){this.currentVideoStream={id:e},t.bitrate&&(this.currentVideoStream.bitrate=t.bitrate),this.currentVideoStream.label=""+(t.label||t.bitrate||"unknown")},e.prototype.isMimeTypeSupported=function(e){return!(this.video.canPlayType(e).length<=0)},e.createSourceElement=function(e,t){var i=document.createElement("source");return i.src=e,t&&(t.indexOf("/")<0&&(t="video/"+t),i.type=t),i},e.prototype.unload=function(e,t){var i,n;return void 0===e&&(e="api"),void 0===t&&(t=!1),this.hlsLiveTimeTracker.onUnload(e),this.clearDelayedSeekHandlers(),this.loadingDeferred&&!t&&this.loadingDeferred.reject(),clearTimeout(this.loadedDataFallbackTimeoutID),clearTimeout(this.loadTimeoutID),null===(i=this.assetAvailabilityChecker)||void 0===i||i.dispose(),this.ended=!1,this.fpsHandler=(0,u.dispose)(this.fpsHandler),this.downloadErrorHandler=(0,u.dispose)(this.downloadErrorHandler),this.hlsManifestController=(0,u.dispose)(this.hlsManifestController),this.hlsSeekingGlitchEventHandlers.forEach((function(e){return e.el.removeEventListener(e.ev,e.fn)})),this.hlsSeekingGlitchEventHandlers=[],this.isProgressiveQualityChange||(null===(n=this.textTrackController)||void 0===n||n.signalSourceChange(),this.textTrackController=(0,u.dispose)(this.textTrackController)),this.clearVideoElement(),this.clearAudioElement(),this.pause(),this.ignoreNextVideoError=!0,this.clearEventListeners(),this.unsubscribeFromStoreStoppedListener&&this.unsubscribeFromStoreStoppedListener(),this.allowedToThrowEvents&&(this.currentVideoStream=null),this.loaded&&(this.loaded=!1,t||(this.eventCallback(h.PlayerEvent.SourceUnloaded,{oldSource:this.currentSource}),this.lastDuration=null)),Promise.resolve()},e.prototype.clearAudioElement=function(){if(this.restrictedInlinePlayback&&this.audio){for(this.audio.removeAttribute("src");this.audio.firstChild;)this.audio.removeChild(this.audio.firstChild);this.audio.load()}},e.prototype.clearEventListeners=function(){this.restrictedInlinePlayback?this.audio&&this.removeEventListeners(this.audio):this.removeEventListeners(this.video),this.clearDelayedSeekHandlers(),this.eventHandler.off(h.PlayerEvent.TimeChanged,this.onTimeChanged),this.eventHandler.off(h.PlayerEvent.TimeChanged,this.delayedSeek),this.sourceElementEventListeners.forEach((function(e){try{e.element.removeEventListener(D.MediaElementEvent.error,e.handler)}catch(e){}}))},e.prototype.clearVideoElement=function(){L.VideoElementUtil.removeSource(this.video),null!=this.originalCrossOriginAttributeValue?this.video.setAttribute("crossOrigin",this.originalCrossOriginAttributeValue):this.video.removeAttribute("crossOrigin"),this.video.load()},e.prototype.isReady=function(){return this.restrictedInlinePlayback&&this.audio&&"readyState"in this.audio?this.audio.readyState!==HTMLMediaElement.HAVE_NOTHING:!("readyState"in this.video)||this.video.readyState!==HTMLMediaElement.HAVE_NOTHING},e.prototype.isLive=function(){return this.getElement().duration===1/0},e.prototype.isPlaying=function(){return this.stateManager.isPlaying()},e.prototype.isPaused=function(){return this.stateManager.isPaused()},e.prototype.hasEnded=function(){return this.ended},e.prototype.isMuted=function(){return this.getElement().muted},e.prototype.isStalled=function(){return this.stateManager.isStalled},e.prototype.getVolume=function(){return 100*this.getElement().volume},e.prototype.getDuration=function(){return this.getElement().duration},e.prototype.convertAudioTrackNativeToApi=function(e){var t={id:e.id||String(S.ArrayHelper.toArray(this.video.audioTracks).indexOf(e)),label:e.label,lang:e.language,getQualities:null},i=this.getLabelForAudio(t);return i&&"string"==typeof i&&(t.label=i),t},e.prototype.getAudio=function(){var e=S.ArrayHelper.toArray(this.video.audioTracks).find((function(e){return e.enabled}));return e?this.convertAudioTrackNativeToApi(e):null},e.prototype.getAvailableAudio=function(){var e=this;return S.ArrayHelper.toArray(this.video.audioTracks).map((function(t){return e.convertAudioTrackNativeToApi(t)}))},e.prototype.getLabelForAudio=function(e){var t={id:e.id,lang:e.lang,mimeType:b.MimeType.AudioMp4};return this.getLabelingFunctionForAudio()(t)},e.prototype.getLabelingFunctionForAudio=function(){var t=this.context.sourceContext,i=t&&t.source&&t.source.labeling;return i&&i[this.type]&&i[this.type].tracks?"function"!=typeof i[this.type].tracks?e.getDefaultLabelForAudio:i[this.type].tracks:e.getDefaultLabelForAudio},e.getDefaultLabelForAudio=function(){return null},e.prototype.getDownloadedVideoData=function(){return this.type===m.StreamType.Progressive?this.currentVideoStream:this.hlsManifestController?this.hlsManifestController.getDownloadedVideoData():{id:B,bitrate:0,height:0,width:0,isAuto:!0}},e.prototype.getDownloadedAudioData=function(){return{id:B,bitrate:0,isAuto:this.type===m.StreamType.Progressive}},e.prototype.createVideoQualityObject=function(e){var t={};return t.id=e.id,t.label=e.label,e.width>1?t.width=e.width:this.currentVideoStream&&this.currentVideoStream.id===e.id&&(this.currentVideoStream.width>1?t.width=this.currentVideoStream.width:!isNaN(this.video.videoWidth)&&this.video.videoWidth>1&&(t.width=this.video.videoWidth)),e.height>1?t.height=e.height:this.currentVideoStream&&this.currentVideoStream.id===e.id&&(this.currentVideoStream.height>1?t.height=this.currentVideoStream.height:!isNaN(this.video.videoHeight)&&this.video.videoHeight>1&&(t.height=this.video.videoHeight)),void 0!==e.bitrate&&"unknown"!==e.bitrate&&(t.bitrate=e.bitrate),t},e.prototype.getPlaybackVideoData=function(){if(this.hlsManifestController){var e=this.hlsManifestController.getPlayingVideoData();if(e)return e}if(this.currentVideoStream){var t=this.createVideoQualityObject(this.currentVideoStream);return"progressive"!==this.type&&(t.id=B),t}return{id:B,bitrate:0,height:this.video.videoHeight,width:this.video.videoWidth}},e.prototype.getPlaybackAudioData=function(){return{id:B,bitrate:0}},e.prototype.getMaxTimeShift=function(){if(!this.isTimeShiftingAllowed())return 0;var e=this.getSeekableRangeInternal();return this.isHlsLive()&&e.start>=0?e.start-e.end:0},e.prototype.isHlsLive=function(){return this.type===m.StreamType.Hls&&this.isLive()},e.prototype.timeShift=function(e,t){if(this.isTimeShiftingAllowed()&&this.isTimeShiftNecessary(e,t))if(0===e){var i=this.getSeekableRangeInternal();i.end>=0&&this.seekForLive(i.end)}else this.seekForLive(e,t)},e.prototype.isTimeShiftNecessary=function(e,t){if(t===v.STARTUP_ISSUER_NAME&&!X(this.context.sourceContext.source.options))return!1;var i=Math.abs(Math.abs(e)-Math.abs(this.getTimeShift())),n=this.isOffsetConsideredAsLiveEdge(e);return i>W&&!(this.isAtLiveEdge&&n)},e.prototype.getTimeShift=function(){if(this.shouldReturnDefaultTimeshiftValue())return G;var e=this.getSeekableRangeInternal();if(e.start>=0&&this.needToUpdateTimeshiftValue()){var t=J(this.getElement().currentTime-e.end,this.getTimeshiftRange());this.isAtLiveEdge=this.isOffsetConsideredAsLiveEdge(t)&&!this.isStalledOrPaused(),this.currentTimeShift=this.isAtLiveEdge?G:t}return isFinite(this.currentTimeShift)?this.currentTimeShift:G},e.prototype.shouldReturnDefaultTimeshiftValue=function(){return!this.isLive()||!this.stateManager.hasBeenPlaying&&!this.stateManager.seekedOrTimeshifted||this.isAtLiveEdge},e.prototype.isStalledOrPaused=function(){return this.isStalled()||this.isPaused()},e.prototype.needToUpdateTimeshiftValue=function(){return!isFinite(this.currentTimeShift)||this.isStalledOrPaused()},e.prototype.getManifest=function(){return this.hlsManifestController?this.hlsManifestController.getManifest():null},e.prototype.getSnapshot=function(e,t){if(this.isPlaying()||this.isPaused()){var i=this.video.videoWidth,n=this.video.videoHeight;if(this.snapshotCanvas||(this.snapshotCanvas=document.createElement("canvas"),this.snapshotCanvas.id="snapshotHiddenCanvas"),!this.snapshotCanvas||!this.snapshotCanvas.getContext||!this.snapshotCanvas.getContext("2d"))return;this.snapshotCanvas.height=n,this.snapshotCanvas.width=i,this.snapshotCanvasContext=this.snapshotCanvas.getContext("2d"),this.snapshotCanvasContext.drawImage(this.video.getWrappedElement(),0,0,i,n);var r=void 0;try{"image/jpeg"===e||"image/webp"===e?(Number(t)===t?(t<0||t>1)&&(t=1):t=1,r=this.snapshotCanvas.toDataURL(e,t)):r=this.snapshotCanvas.toDataURL(e,t)}catch(e){return void(e&&e.message)}return{height:n,width:i,data:r}}},e.prototype.setPlaybackSpeed=function(e){this.getElement().playbackRate=e},e.prototype.getPlaybackSpeed=function(){return this.getElement().playbackRate},e.prototype.permitControl=function(e){this.controlPermitted=e},e.prototype.resetTimeShift=function(){this.currentTimeShift=-1/0,this.isTimeShiftingAllowed()&&(this.isAtLiveEdge=!1)},e.prototype.isOffsetConsideredAsLiveEdge=function(e){return Math.abs(e)<2*this.settings.SEEK_TO_END_OFFSET},e.prototype.isSeekTargetChangedDuringStartup=function(){return this.seekTargetTime!==this.lastVerifiedPlaybackPosition&&this.lastSeekIssuer===v.STARTUP_ISSUER_NAME},e.prototype.maybeHandleSeekTargetChangeDuringStartup=function(){this.isSeekTargetChangedDuringStartup()&&(this.triggerSeekEvent(this.seekTargetTime),this.lastSeekIssuer="api",this.seekTargetTime=-1)},e.prototype.finishedSeekedTransitionState=function(){this.stateManager.isSeeking()&&(this.stateManager.transitionToSeekedState(this.lastSeekIssuer!==v.STARTUP_ISSUER_NAME),this.lastSeekIssuer="api"),this.stateManager.isTimeShifting()&&this.stateManager.transitionToTimeShiftedState(this.lastSeekIssuer!==v.STARTUP_ISSUER_NAME)},e.prototype.addEventListeners=function(e){for(var t=0;t<this.elementEvents.length;t++){var i=this.elementEvents[t].elementSelector?this.elementEvents[t].elementSelector(e):e;i&&i.addEventListener(this.elementEvents[t].event,this.elementEvents[t].handler)}},e.prototype.removeEventListeners=function(e){for(var t=0;t<this.elementEvents.length;t++){var i=this.elementEvents[t].elementSelector?this.elementEvents[t].elementSelector(e):e;i&&i.removeEventListener&&i.removeEventListener(this.elementEvents[t].event,this.elementEvents[t].handler)}},e.prototype.getBufferLevel=function(e){var t={level:null,targetLevel:null,type:e,media:m.MediaType.Video},i=this.getElement();if(!i.buffered||i.buffered.length<1)return t;var n=function(n){for(var r=0;r<i.buffered.length;r++){var a=i.buffered.start(r),o=i.buffered.end(r);a>n||o<=n||(t.level=o-n,e===m.BufferType.BackwardDuration&&(t.level=n-a))}},r=i.currentTime;if(n(r),null==t.level){var a=K(i,r),o=a.distanceToCurrentTime,s=a.closestBufferStart;o<1&&n(s)}return t},e.prototype.createTextTrackController=function(){if(I.ModuleManager.has(w.ModuleName.SubtitlesNative))return new(0,I.ModuleManager.get(w.ModuleName.SubtitlesNative).TextTrackController)(this.context,this.video,this.config,this.type)},e.prototype.removeSubtitle=function(e){e&&this.textTrackController&&this.textTrackController.removeSubtitle(e)},e.prototype.addSubtitle=function(e){return this.textTrackController?(this.removeSubtitle(e.id),e.kind="subtitles",this.textTrackController.addSubtitle(e)):Promise.reject()},e.prototype.listSubtitles=function(){return this.textTrackController?this.textTrackController.getAvailableSubtitles():[]},e.prototype.enableSubtitle=function(e){return this.textTrackController?this.textTrackController.enableSubtitle(e):Promise.resolve(!1)},e.prototype.disableSubtitle=function(e){return this.textTrackController?Promise.resolve(this.textTrackController.disableSubtitle(e)):Promise.resolve(!1)},e.prototype.adjustToFullUrlWithQueryParams=function(e){var t=C.URLHelper.toFullUrl(e);return C.URLHelper.appendQueryParametersToUrl(t,this.settings.QUERY_PARAMETERS)},e.prototype.onFullscreenEnter=function(){this.isInNativeFullscreen=this.video.webkitDisplayingFullscreen,this.textTrackController&&this.isInNativeFullscreen&&this.textTrackController.enableNative()},e.prototype.onFullscreenExit=function(){this.textTrackController&&this.isInNativeFullscreen&&this.textTrackController.disableNative(),this.isInNativeFullscreen=!1},e.prototype.getAvailableVideoQualities=function(){var e=this;return"progressive"!==this.type?[]:this.availableVideoStreams.map((function(t){return e.createVideoQualityObject(t)}))},e.prototype.getBufferedRanges=function(){var e=this;return(0,r.getBufferedRanges)(this.getElement()).map((function(t){return(0,r.convertRangeToTimeMode)(t,e.getStartDate(),m.TimeMode.AbsoluteTime)}))},e.prototype.setVideoQuality=function(e){var t,i=this,r=this.getPlaybackSpeed(),a=this.availableVideoStreams.find((function(t){return t.id===e}));if("progressive"===this.type&&a&&(!this.currentVideoStream||this.currentVideoStream.id!==e)){this.isProgressiveQualityChange=!0,this.currentTimeWas=this.getCurrentTime(),this.availableVideoStreams=[],this.allowedToThrowEvents=!1;var o=this.stateManager.isPlaying()||this.stateManager.isPlay();this.stateManager.stallEnter(),this.unload("api",!0).catch((function(){}));var d=this.createVideoQualityObject(a),u={sourceQuality:this.currentVideoStream,sourceQualityId:this.currentVideoStream?this.currentVideoStream.id:null,targetQuality:d,targetQualityId:d.id};this.eventHandler.one(h.PlayerEvent.StallEnded,(function(){i.allowedToThrowEvents=!0,(0,y.getCapabilities)().isIOS||(0,y.getCapabilities)().isSafari?(i.numTimeChanged=0,i.eventHandler.on(h.PlayerEvent.TimeChanged,i.delayedSeek)):(i.numTimeChanged=1/0,i.delayedSeek()),o&&(i.play(v.INTERNAL_ISSUER_NAME).catch((function(){i.eventCallback(h.PlayerEvent.Warning,new s.PlayerWarning(l.WarningCode.PLAYBACK_COULD_NOT_BE_STARTED))})),i.stateManager.transitionToPlayingState(v.INTERNAL_ISSUER_NAME,!1))})),this.eventCallback(h.PlayerEvent.VideoQualityChanged,n({},u),!0),null===(t=this.airplayService)||void 0===t||t.initAirplay(),this.load(this.sourceConfig,this.settings.GLOBAL_DISABLE_SEEKING,e).then((function(){i.setPlaybackSpeed(r),i.eventCallback(h.PlayerEvent.VideoDownloadQualityChange,n({},u),!0),i.eventCallback(h.PlayerEvent.VideoDownloadQualityChanged,n({},u),!0),i.eventCallback(h.PlayerEvent.VideoPlaybackQualityChanged,{sourceQuality:u.sourceQuality,targetQuality:u.targetQuality},!0)}))}},e.prototype.isAirplayAvailable=function(){var e,t;return null!==(t=null===(e=this.airplayService)||void 0===e?void 0:e.isAirplayAvailable())&&void 0!==t&&t},e.prototype.isAirplayActive=function(){var e,t;return null!==(t=null===(e=this.airplayService)||void 0===e?void 0:e.isAirplayActive())&&void 0!==t&&t},e.prototype.showAirplayTargetPicker=function(){var e;null===(e=this.airplayService)||void 0===e||e.showAirplayTargetPicker()},e.prototype.getTimeshiftRange=function(){return this.isLive()?{start:this.getMaxTimeShift(),end:0}:{start:-1,end:-1}},e.prototype.getSeekableRangeInternal=function(){var e=this.video.seekable;if(e.length>0){var t={start:e.start(0),end:e.end(0)};return t.start=Math.max(0,t.start),t}return{start:-1,end:-1}},e.prototype.getSeekableRange=function(){if(!this.isLive()||this.settings.ENABLE_SEEK_FOR_LIVE){var e=this.getStartDate(),t=this.getSeekableRangeInternal();return e?(0,r.convertRangeToTimeMode)(t,e,m.TimeMode.AbsoluteTime):t}return{start:-1,end:-1}},e.prototype.createManifestApi=function(){return this.hlsManifestController?x.NativePlayerManifestApiFactory.create(this.hlsManifestController):null},e.prototype.release=function(){var e,t=this,i=this.unload("api").catch((function(){}));return clearTimeout(this.loadTimeoutID),i=i.then((function(){t.type=null,t.config=null,t.eventHandler=null,t.settings=null,t.currentTimeWas=null,t.snapshotCanvas=null,t.snapshotCanvasContext=null,t.audio=null,t.video=null,t.lastPlayIssuer=null,t.lastPauseIssuer=null,t.lastSeekIssuer=null,t.currentSource=null,t.sourceConfig=null,t.currentVideoStream=null,t.availableVideoStreams=null,t.sourceElementEventListeners=null,t.delayedSeekTarget=void 0})),null===(e=this.airplayService)||void 0===e||e.dispose(),this.textTrackController&&this.textTrackController.dispose(),this.textTrackController=null,this.fpsHandler&&this.fpsHandler.dispose(),this.fpsHandler=null,this.downloadErrorHandler&&this.downloadErrorHandler.dispose(),this.downloadErrorHandler=null,this.hlsManifestController&&this.hlsManifestController.dispose(),this.hlsManifestController=null,i},e.prototype.setQueryParameters=function(e){this.settings.QUERY_PARAMETERS=e},e.prototype.clearQueryParameters=function(){this.settings.QUERY_PARAMETERS=void 0},e.prototype.setAudioQuality=function(e){},e.prototype.setTargetBufferLevel=function(){},e.prototype.getAvailableAudioQualities=function(){return[]},e.prototype.getDroppedVideoFrames=function(){return 0},e.prototype.getTotalStalledTime=function(){return 0},e.prototype.updateCallback=function(e){},e.prototype.getAvailableSegments=function(){return{}},e.prototype.getLatency=function(){var e=this.video.getWrappedElement();if(!e.getStartDate)return 1/0;var t=e.getStartDate().getTime();if(isNaN(t))return 1/0;var i=(new Date).getTime();return(0,P.toSeconds)(i-t)-this.video.currentTime},e.prototype.setTargetLatency=function(e){},e.prototype.getTargetLatency=function(){return null},e.prototype.setCatchupConfig=function(e){},e.prototype.getCatchupConfig=function(){return null},e.prototype.setFallbackConfig=function(e){},e.prototype.getFallbackConfig=function(){return null},e}();function K(e,t){for(var i=e.buffered.start(0),n=Math.abs(i-t),r=1;r<e.buffered.length;r++){var a=Math.abs(e.buffered.start(r)-t);a<n&&(n=a,i=e.buffered.start(r))}return{distanceToCurrentTime:n,closestBufferStart:i}}function X(e){return null!=(null==e?void 0:e.startTime)||null!=(null==e?void 0:e.startOffset)}function Y(e){var t=e.findIndex((function(e){return!0===e.preferred}));return t>-1?String(t):"0"}function J(e,t){return(e<t.start||e>t.end)&&(e=Math.max(e,t.start),e=Math.min(e,t.end)),e}function $(e){return e.length>0&&e.end(e.length-1)!==1/0}t.NativePlayer=q}},function(e){return function(t){return e(e.s=t)}(49688)}])}));
})();
