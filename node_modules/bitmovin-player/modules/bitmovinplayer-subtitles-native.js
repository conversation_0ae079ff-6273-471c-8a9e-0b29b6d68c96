/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports["subtitles-native"]=e():(t.bitmovin=t.bitmovin||{},t.bitmovin.player=t.bitmovin.player||{},t.bitmovin.player["subtitles-native"]=e())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[826],{21999:function(t,e,i){var a=this&&this.__assign||function(){return a=Object.assign||function(t){for(var e,i=1,a=arguments.length;i<a;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},a.apply(this,arguments)},r=this&&this.__spreadArray||function(t,e,i){if(i||2===arguments.length)for(var a,r=0,n=e.length;r<n;r++)!a&&r in e||(a||(a=Array.prototype.slice.call(e,0,r)),a[r]=e[r]);return t.concat(a||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.TextTrackController=void 0;var n,o,s,l=i(63546),u=i(35148),d=i(62510),c=i(88005),h=i(67345),v=i(81361),b=i(76885),f=i(91520),T=i(16368),p=i(31448);!function(t){t.Showing="showing",t.Hidden="hidden",t.Disabled="disabled"}(n||(n={})),function(t){t.Subtitle="subtitles",t.ForcedSubtitle="forced",t.Caption="captions",t.Metadata="metadata"}(o||(o={})),function(t){t.ID3="org.id3",t.DATERANGE="com.apple.quicktime.HLS",t.EMSG="https://aomedia.org/emsg/ID3"}(s||(s={}));var g=function(t){var e=80/15,i=2.5,a=t.line-10,r=t.position-10;return a-=a%e,r-=r%i,{row:a=Math.round(a/e),column:r=Math.round(r/i)}},k=new RegExp("^(?:[a-z]+:)?//","i"),m=function(t){return"https:"===location.protocol?t.replace(k,"//"):t},x=function(t){var e={id:t.id,lang:t.lang,label:t.label,kind:t.kind,isFragmented:t.isFragmented,isSideloaded:t.isSideloaded,enabled:t.textTrack&&t.textTrack.mode!==n.Disabled,forced:t.kind===o.ForcedSubtitle};return t.url&&(e.url=t.url),e},y=function(){function t(t,e,i,r){var o=this;this.onFileLoaded=function(t,e,i){var a=t.body;a&&o.add(a.trim(),i)},this.onFileLoadError=function(t,e){o.context.logger.debug("Error loading subtitle file: "+JSON.stringify(e)),o.removeSubtitle(o.getSubtitleIDFromUrl(t.url)),o.eventHandler.dispatchEvent(d.PlayerEvent.Warning,new l.PlayerWarning(u.WarningCode.NETWORK_COULD_NOT_LOAD_SUBTITLE)),o.context.logger.debug("Could not load subtitles/captions, got HTTP status code "+t.status)},this.onMetadataCueChangeHandler=function(t){var e=t.currentTarget.activeCues,i={frames:[]},r=[];for(var n in e)if(e[n].type===s.ID3&&void 0!==e[n].value){var l=e[n].value;v.ArrayHelper.isArrayBuffer(l.data)&&((l=a({},l)).data=v.ArrayHelper.getArray(new Uint8Array(l.data))),i[l.key]=l.data,i.frames.push(l),r.push(e[n])}i.frames.length&&o.eventHandler.dispatchEvent(d.PlayerEvent.Metadata,{metadataType:d.MetadataType.ID3,metadata:i}),r.forEach((function(e){return t.currentTarget.removeCue(e)}))},this.onCueChange=function(t){var e=t.target;if(e.activeCues&&e.activeCues.length>0){var i=p.TextTrackCueHelper.cueListToArray(e.activeCues),a=o.getIdFromTextTrack(e);p.TextTrackCueHelper.sort(i).forEach((function(t){return o.processCue(a,t)}))}},this.addTrack=function(t){var e=window.setTimeout((function(){return o.asyncAddTrack(t)}),0);o.timeoutsToClear.push(e)},this.getDefaultLabelForSubtitle=function(){return null},this.setForcedSubtitles=function(){var t;o.activeSubtitleId?o.activeForcedSubtitleId&&o.disableSubtitle(o.activeForcedSubtitleId):(o.activeForcedSubtitleId=null===(t=o.getForcedSubtitleForSelectedLanguage())||void 0===t?void 0:t.id,o.activeForcedSubtitleId&&o.enableSubtitle(o.activeForcedSubtitleId,!0))},this.audioChangeHandler=function(){o.disableSubtitle(o.activeForcedSubtitleId),o.setForcedSubtitles()},this.textTracksChangeHandler=function(){var t=o.getTextTrackAndSubtitleId(),e=t[0],i=t[1];if(o.shouldIgnoreTextTrackChange(i))return o.disableSubtitle(i),void o.setForcedSubtitles();i||(o.activeSubtitleId=void 0),o.setForcedSubtitles(),o.isNative&&o.syncTracksSelectedThroughNativeUI(),i&&e&&e.mode!==n.Hidden&&!o.recentlyAddedTracks.includes(e)&&o.enableSubtitle(i)},this.context=t,this.eventHandler=t.eventHandler,this.video=e,this.type=r,this.metadataTracks=[],this.availableTracks={},this.currentCCIdx=1,this.currentSubIdx=0,this.showNative=!1,this.timeoutsToClear=[],this.recentlyAddedTracks=[],this.activeCues=[],this.subtitleLoader=null,i.hasOwnProperty("style")&&i.style.hasOwnProperty("nativeSubtitles")?this.showNativeAlways=!!i.style.nativeSubtitles:this.showNativeAlways=!1,e.audioTracks&&e.audioTracks.addEventListener("change",this.audioChangeHandler),e.textTracks&&"function"==typeof e.textTracks.addEventListener&&(e.textTracks.addEventListener("addtrack",this.addTrack),e.textTracks.addEventListener("change",this.textTracksChangeHandler))}return Object.defineProperty(t.prototype,"isNative",{get:function(){return this.showNative||this.showNativeAlways},enumerable:!1,configurable:!0}),t.prototype.getAvailableSubtitles=function(){return Object.values(this.availableTracks||{}).filter((function(t){return t.kind!==o.ForcedSubtitle})).map(x)},t.prototype.enableSubtitle=function(t,e){void 0===e&&(e=!1);var i=this.availableTracks[t];return i?(e||(this.activeSubtitleId=t),i.textTrack&&(this.showNativeAlways||this.showNative?i.textTrack.mode=n.Showing:i.textTrack.mode=n.Hidden),i.enabled?Promise.resolve(!1):(i.enabled=!0,i.textTrack?i.loadPromise.then((function(){return!0}),(function(){return!1})):Promise.resolve(!0))):Promise.resolve(!1)},t.prototype.disableSubtitle=function(t){var e=this,i=this.availableTracks[t];return!!i&&(i.textTrack&&(i.textTrack.mode=n.Disabled),i.enabled=!1,r([],this.activeCues,!0).forEach((function(i){return i.subtitleId===t&&e.deactivateCue(t,i)})),!0)},t.prototype.toTextTrackCue=function(t){var e,i;return window.VTTCue?new window.VTTCue(t.start,t.end,null!==(e=t.html)&&void 0!==e?e:t.text):window.TextTrackCue?new TextTrackCue(t.start,t.end,null!==(i=t.html)&&void 0!==i?i:t.text):void 0},t.prototype.getSubtitleIDFromUrl=function(t){if(t&&this.availableTracks)for(var e in this.availableTracks)if(this.availableTracks.hasOwnProperty(e)&&this.availableTracks[e].url===t)return e},t.prototype.add=function(t,e){var i=this.getSubtitleIDFromUrl(e);if(i&&t){for(var r=f.ModuleManager.get(T.ModuleName.Subtitles).SubtitleParserFactory.createInstance(this.context,t).parse(t),n=0;n<r.length;n++)this.eventHandler.dispatchEvent(d.PlayerEvent.CueParsed,a(a({subtitleId:i},r[n]),{periodId:null})),this.availableTracks[i].textTrack.addCue(this.toTextTrackCue(r[n]));this.eventHandler.dispatchEvent(d.PlayerEvent.SubtitleAdded,{subtitle:x(this.availableTracks[i])})}},t.prototype.loadFile=function(t){var e=this;if(t&&t.trim())return this.context.logger.debug("loading "+t),this.subtitleLoader=new c.DefaultContentLoader(this.context,{maxRetries:this.context.settings.MAX_RETRIES,retryDelay:this.context.settings.RETRY_DELAY,onSuccess:this.onFileLoaded,onFailure:this.onFileLoadError,requestType:h.HttpRequestType.MEDIA_SUBTITLES}),this.subtitleLoader.load(t).catch((function(t){e.context.logger.debug("Error while loading subtitles ",t)}))},t.prototype.createCueEvents=function(t,e){var i=f.ModuleManager.get(T.ModuleName.Subtitles).HtmlTransformer,a=i.htmlToText,r=i.stripHtmlTags,n=(f.ModuleManager.get(T.ModuleName.SubtitlesWebVTT,!1)||{}).extractVttProperties,o={subtitleId:t,text:a(e.text),start:e.startTime,end:e.endTime},s=[o];if("subtitles"===e.track.kind)o.html=E(e.text),window.VTTCue&&(e instanceof VTTCue&&n?o.vtt=n(e):e instanceof VTTCue&&this.context.logger.debug("Could not interpret VTT properties from native VTTCue Event as ".concat(T.ModuleName.SubtitlesWebVTT," is missing")));else if("captions"===e.track.kind){o.position=g(e);var l=r(o.text);o.position.column+l.length>p.CEA_608_MAX_LINE_LENGTH-1&&(s=p.TextTrackCueHelper.splitCea608Cue(o))}return s},t.prototype.processCue=function(t,e){var i=this;if(e){var a=Object.values(this.availableTracks).find((function(t){return t.textTrack===e.track}));if(C(this.showNativeAlways||this.showNative,e,a),!this.showNativeAlways&&!this.showNative){var r=this.createCueEvents(t,e);r.forEach((function(a){i.updateCue(a)?i.eventHandler.dispatchEvent(d.PlayerEvent.CueUpdate,a):i.activateCue(t,e,a)})),this.removeFloatingCues(t,r)}}},t.prototype.removeFloatingCues=function(t,e){var i=this,a=this.activeCues.filter((function(i){return!e.find((function(t){return i.start===t.start&&i.text===t.text&&i.subtitleId===t.subtitleId}))&&i.end===1/0&&i.subtitleId===t}));a.length>0&&a.forEach((function(e){i.deactivateCue(t,e)}))},t.prototype.updateCue=function(t){var e=this,i=this.activeCues.find((function(i){var a=i.start===t.start,r=i.text===t.text,n=!t.end||i.end===1/0||i.end===e.video.duration||i.end===t.end;return a&&r&&n}));return!!i&&(i.end=t.end,!0)},t.prototype.activateCue=function(t,e,i){var a=this;this.activeCues.push(i),e.onexit=function(){a.createCueEvents(t,e).forEach((function(e){return a.deactivateCue(t,e)}))},this.eventHandler.dispatchEvent(d.PlayerEvent.CueEnter,i)},t.prototype.deactivateCue=function(t,e){var i=this.activeCues.find((function(t){return t.start===e.start&&t.end===e.end&&t.text===e.text}));if(i){this.activeCues=this.activeCues.filter((function(t){return t!==i}));var a={subtitleId:t,text:i.text,start:i.start,end:i.end};i.position&&(a.position=i.position),this.eventHandler.dispatchEvent(d.PlayerEvent.CueExit,a)}},t.prototype.removeSubtitle=function(t){if(this.availableTracks.hasOwnProperty(t)){this.availableTracks[t].textTrack.mode!==n.Disabled&&this.disableSubtitle(t),this.availableTracks[t].textTrack.mode=n.Disabled;var e=this.availableTracks[t];delete this.availableTracks[t],this.eventHandler.dispatchEvent(d.PlayerEvent.SubtitleRemoved,{subtitle:x(e)})}},t.prototype.signalSourceChange=function(){this.video&&this.video.textTracks&&"function"==typeof this.video.textTracks.addEventListener&&this.video.textTracks.removeEventListener("addtrack",this.addTrack),this.removeAll()},t.prototype.removeAll=function(){for(var t in this.context.logger.debug("removing all subtitle tracks"),this.timeoutsToClear.forEach((function(t){return clearTimeout(t)})),this.timeoutsToClear=[],this.availableTracks)this.availableTracks.hasOwnProperty(t)&&(this.availableTracks[t].textTrack.mode=n.Disabled,this.availableTracks[t].textTrack.removeEventListener("cuechange",this.onCueChange));this.availableTracks={},this.activeCues=[],this.currentCCIdx=1,this.currentSubIdx=0},t.prototype.addSubtitle=function(t){if("function"!=typeof this.video.addTextTrack)return this.context.logger.warn("adding subtitles not supported!"),Promise.reject();this.availableTracks.hasOwnProperty(t.id)&&(this.context.logger.debug("re-adding subtitle "+t.id),this.removeSubtitle(t.id));var e=t.url?b.URLHelper.toFullUrl(m(t.url.trim())):null,i={id:t.id,url:e,kind:t.kind,lang:t.lang,label:t.label,textTrack:null,isFragmented:!1,isSideloaded:!0,loadPromise:null};return this.availableTracks[t.id]=i,this.availableTracks[t.id].textTrack=this.video.addTextTrack(t.kind,t.label),this.availableTracks[t.id].textTrack.mode=n.Disabled,this.availableTracks[t.id].textTrack.addEventListener("cuechange",this.onCueChange),this.loadSubtitleFile(i)},t.prototype.loadSubtitleFile=function(t){var e=this;return t.url?t.loadPromise=this.loadFile(t.url).then((function(){if(!e.getAvailableSubtitles().find((function(e){return e.id===t.id})))throw"subtitle adding failed"})):(this.context.logger.log("No url was provided for an external subtitle, nothing will be loaded."),t.loadPromise=Promise.resolve(),this.eventHandler.dispatchEvent(d.PlayerEvent.SubtitleAdded,{subtitle:x(t)})),t.loadPromise},t.prototype.asyncAddTrack=function(t){for(var e,i,a=this,r=t.track,s=t.isFragmented&&r.kind===o.Subtitle||!1,l=0,u=Object.keys(this.availableTracks);l<u.length;l++){var c=u[l],h=this.availableTracks[c];if(h.textTrack===r&&h.isSideloaded)return void this.context.logger.insane("track "+r.label+" has already been added externally")}switch(r.kind){case o.Subtitle:e=r.id||"sub".concat(this.currentSubIdx),i=r.label||"Subtitles (".concat(this.currentSubIdx,")"),this.currentSubIdx++;break;case o.ForcedSubtitle:e=r.id||"fsub".concat(this.currentSubIdx),i=r.label||"Subtitles (".concat(this.currentSubIdx,")) (Forced)"),this.currentSubIdx++;break;case o.Caption:e="CC".concat(this.currentCCIdx),i=r.label||"Captions (CC ".concat(this.currentCCIdx,")"),this.currentCCIdx++;break;default:return void this.addMetadataTrack(r)}this.context.logger.debug("adding ".concat(r.kind," text track ").concat(e)),this.availableTracks[e]={id:e,url:null,kind:r.kind,lang:r.language||"unknown",label:i,textTrack:r,isFragmented:s,isSideloaded:!1,loadPromise:Promise.resolve()},(i=this.getLabelForSubtitle(this.availableTracks[e]))&&"string"==typeof i&&(this.availableTracks[e].label=i),this.recentlyAddedTracks.push(r),r.addEventListener("cuechange",this.onCueChange),r.kind!==o.ForcedSubtitle&&this.eventHandler.dispatchEvent(d.PlayerEvent.SubtitleAdded,{subtitle:x(this.availableTracks[e])}),r.mode=n.Disabled;var v=window.setTimeout((function(){a.recentlyAddedTracks=a.recentlyAddedTracks.filter((function(t){return t!==r})),Object.keys(a.availableTracks).map((function(t){return a.availableTracks[t]})).filter((function(t){return t.enabled&&Boolean(t.textTrack)&&t.textTrack.mode===n.Disabled})).forEach((function(t){a.isNative?t.textTrack.mode=n.Showing:t.textTrack.mode=n.Hidden}))}),0);this.timeoutsToClear.push(v),this.hasStoredAllTracks()&&this.setForcedSubtitles()},t.prototype.addMetadataTrack=function(t){this.context.settings.HLS_PARSE_NATIVE_METADATA&&(this.metadataTracks.push(t),t.mode=n.Hidden,t.addEventListener("cuechange",this.onMetadataCueChangeHandler))},t.prototype.hasStoredAllTracks=function(){var t=Object.keys(this.availableTracks).length;return this.video.textTracks.length===t},t.prototype.getLabelForSubtitle=function(t){var e={kind:t.kind,id:t.id,label:t.label,lang:t.lang};return this.getLabelingFunctionForSubtitle()(e)},t.prototype.getLabelingFunctionForSubtitle=function(){var t=this.context.sourceContext,e=t&&t.source&&t.source.labeling;return e&&e[this.type]&&e[this.type].subtitles?"function"!=typeof e[this.type].subtitles?this.getDefaultLabelForSubtitle:e[this.type].subtitles:this.getDefaultLabelForSubtitle},t.prototype.getIdFromTextTrack=function(t){var e;return(null===(e=Object.values(this.availableTracks).find((function(e){return e.textTrack===t})))||void 0===e?void 0:e.id)||null},t.prototype.getTextTrackAndSubtitleId=function(){for(var t=0,e=this.video.textTracks;t<e.length;t++){var i=e[t];if((i.mode===n.Showing||i.mode===n.Hidden)&&i.kind!==o.Metadata&&i.kind!==o.ForcedSubtitle)return[i,this.getIdFromTextTrack(i)]}return[void 0,void 0]},t.prototype.enableNative=function(){var t=this;Object.keys(this.availableTracks).map((function(e){return t.availableTracks[e]})).filter((function(t){return t.enabled&&Boolean(t.textTrack)})).forEach((function(t){return t.textTrack.mode=n.Showing})),this.showNative=!0},t.prototype.disableNative=function(){var t=this;Object.keys(this.availableTracks).map((function(e){return t.availableTracks[e]})).filter((function(t){return t.enabled&&Boolean(t.textTrack)})).forEach((function(t){return t.textTrack.mode=n.Hidden})),this.showNative=!1},t.prototype.seek=function(){var t=this;Object.keys(this.availableTracks).map((function(e){return t.availableTracks[e]})).filter((function(t){return t.enabled&&Boolean(t.textTrack)})).forEach((function(t){var e=t.textTrack.mode;t.textTrack.mode=n.Disabled,t.textTrack.mode=e}))},t.prototype.getForcedSubtitleForSelectedLanguage=function(){var t,e,i,a=this.video.audioTracks,r=f.ModuleManager.get(T.ModuleName.Subtitles).LanguageComparer.isEqualIsoLanguage;if(this.activeSubtitleId)i=null===(t=this.availableTracks[this.activeSubtitleId])||void 0===t?void 0:t.lang;else{if(!a)return;var n=v.ArrayHelper.toArray(a).find((function(t){return t.enabled}));i=null==n?void 0:n.language}i=null!==(e=null!=i?i:navigator.language)&&void 0!==e?e:navigator.userLanguage;var s=Object.values(this.availableTracks).filter((function(t){return t.kind===o.ForcedSubtitle})),l=s.find((function(t){return r(t.lang,i)}));return null!=l?l:s[0]},t.prototype.shouldIgnoreTextTrackChange=function(t){var e=this,i=Object.keys(this.availableTracks).some((function(i){return i===t&&e.availableTracks[i].enabled}));return t&&!this.isNative&&!i},t.prototype.syncTracksSelectedThroughNativeUI=function(){var t=this,e=function(t){var e=a({},t);return delete e.loadPromise,delete e.textTrack,a({},e)};this.getUnsyncedDisabledTracks().forEach((function(i){t.disableSubtitle(i.id),t.eventHandler.dispatchEvent(d.PlayerEvent.SubtitleDisabled,{subtitle:e(i)})})),this.getUnsyncedEnabledTracks().forEach((function(i){t.eventHandler.dispatchEvent(d.PlayerEvent.SubtitleEnabled,{subtitle:e(i)})}))},t.prototype.getUnsyncedDisabledTracks=function(){var t=this.video.textTracks;return Object.values(this.availableTracks).filter((function(t){return t.enabled})).filter((function(e){var i=S(t,e);return i&&i.mode===n.Disabled}))},t.prototype.getUnsyncedEnabledTracks=function(){var t=this.video.textTracks;return Object.values(this.availableTracks).filter((function(t){return!t.enabled})).filter((function(e){var i=S(t,e);return i&&i.mode!==n.Disabled}))},t.prototype.dispose=function(){var t;this.removeAll(),this.eventHandler=null,this.activeCues=null,this.availableTracks=null,this.metadataTracks=null,null===(t=this.video.audioTracks)||void 0===t||t.removeEventListener("change",this.audioChangeHandler),this.video&&this.video.textTracks&&"function"==typeof this.video.textTracks.addEventListener&&(this.video.textTracks.removeEventListener("addtrack",this.addTrack),this.video.textTracks.removeEventListener("change",this.textTracksChangeHandler)),this.subtitleLoader&&this.subtitleLoader.dispose(),this.subtitleLoader=null},t}();function S(t,e){for(var i=0,a=t;i<a.length;i++){var r=a[i];if(r===e.textTrack)return r}}function C(t,e,i){if(i){var a=f.ModuleManager.get(T.ModuleName.Subtitles).HtmlTransformer,r=a.stripAwayHtmlBreakPoints,n=a.replaceHtmlBreakPointsWithNewLine;t&&i.isSideloaded&&(e.text=n(e.text)),t||i.isSideloaded||(e.text=r(e.text))}}function E(t){var e=(0,f.ModuleManager.get(T.ModuleName.Subtitles).HtmlTransformer.prepareHtml)(t);return e.startsWith("<span>")?e:"<span>".concat(e,"</span>")}e.TextTrackController=y},26976:function(t,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.NativeSubtitlesModuleDefinition=void 0;var a=i(16368),r=i(21999);e.NativeSubtitlesModuleDefinition={name:a.ModuleName.SubtitlesNative,module:function(){return{TextTrackController:r.TextTrackController}},dependencies:[a.ModuleName.Subtitles]},e.default=e.NativeSubtitlesModuleDefinition},31448:function(t,e){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,i=1,a=arguments.length;i<a;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.TextTrackCueHelper=e.CEA_608_MAX_LINE_LENGTH=void 0,e.CEA_608_MAX_LINE_LENGTH=32;var a=function(){function t(){}return t.cueListToArray=function(t){for(var e=[],i=0;i<t.length;i++)e.push(t[i]);return e},t.sortEqualStartTimeByLineFn=function(t,e){if("line"in t&&"line"in e&&t.startTime===e.startTime){if(t.line<e.line)return-1;if(t.line>e.line)return 1}return 0},t.sortByStartTimeFn=function(t,e){return t.startTime<e.startTime?-1:t.startTime>e.startTime?1:0},t.sort=function(e){return e.sort(t.sortByStartTimeFn),e.sort(t.sortEqualStartTimeByLineFn),e},t.getTagIndexRanges=function(t){for(var e,i=RegExp("<[^>]*>","g"),a=[];e=i.exec(t);){var r=e[0];a.push({start:e.index,end:e.index+r.length-1})}return a},t.splitCea608Cue=function(i){for(var a=e.CEA_608_MAX_LINE_LENGTH-i.position.column,r=i.text,n=t.getTagIndexRanges(r),o=[],s=0,l=0,u=!1,d=0,c=0;c<r.length;){if(c===r.length-1){o.push(r.substring(s));break}if(n.length>0&&c===n[0].start&&(u=!0),u);else if(l++," "===r[c]&&(d=c),l>=a&&d>s){c=d+1;var h=r.substring(s,c);h&&o.push(h),s=c,l=1}u&&n.length>0&&c===n[0].end&&(u=!1,n.shift()),c++}var v=t.createCueEvents(o,i);return t.adjustCea608CaptionPositioning(v),v},t.createCueEvents=function(t,e){var a=t.length-1;return t.map((function(t){var r=i({},e);return r.text=t,r.position={column:r.position.column,row:r.position.row-a},a--,r}))},t.adjustCea608CaptionPositioning=function(t){var e=14,i=Math.max.apply(Math,t.map((function(t){return t.position.row}))),a=Math.min.apply(Math,t.map((function(t){return t.position.row})));if(i>e){var r=e-i;t.forEach((function(t){return t.position.row+=r}))}if(a<0){var n=0-a;t.forEach((function(t){return t.position.row+=n}))}},t}();e.TextTrackCueHelper=a}},function(t){return function(e){return t(t.s=e)}(26976)}])}));
})();
