/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports["engine-bitmovin"]=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player["engine-bitmovin"]=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[238],{1590:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.StreamTimelineActionType=void 0,function(e){e.StreamTimelineReset="@instance/source/stream/streamtimelinereset",e.StreamTimeRangeRemove="@instance/source/stream/streamtimerangeremove",e.StreamTimeRangeAdd="@instance/source/stream/streamtimerangeadd"}(i||(t.StreamTimelineActionType=i={}))},3337:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.createManifestLoadingService=u;var n=i(25550),r=i(28764),o=i(33696),a=i(91520),s=i(16368);function u(e,t,i){switch(e.type){case o.StreamType.Dash:return new(0,a.ModuleManager.get(s.ModuleName.DASH).MPDLoader)(t,i);case o.StreamType.Hls:return new(0,a.ModuleManager.get(s.ModuleName.HLS).M3u8Loader)(t,i);case o.StreamType.Smooth:return new(0,a.ModuleManager.get(s.ModuleName.Smooth).SmoothStreamingLoader)(t,i);default:throw new r.PlayerError(n.ErrorCode.SOURCE_INVALID,{given:e.type,supported:"dash, hls, smooth"},"The provided stream type is not supported by the player.")}}},4053:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.setRepresentationIdAction=a,t.setMediaTypeAction=s,t.setContainerFormatAction=u,t.removeActiveTrackAction=d,t.clearActiveTracksAction=c;var n=i(15231),r=i(81361),o=i(73731);function a(e){return(0,n.createAction)(o.ActiveTracksActionType.SetSelectedRepresentationId,{selectedRepresentationId:e})}function s(e,t){var i=(0,r.forceArray)(t);return(0,n.createAction)(o.ActiveTracksActionType.SetMediaType,{trackId:e,mediaTypes:i})}function u(e,t){return(0,n.createAction)(o.ActiveTracksActionType.SetContainerFormat,{trackId:e,containerFormat:t})}function d(e){return(0,n.createAction)(o.ActiveTracksActionType.RemoveActiveTrack,{adaptationSetId:e})}function c(){return(0,n.createAction)(o.ActiveTracksActionType.ClearActiveTracks)}},4141:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.getLoaderConfig=a,t.getLoader=u;var n=i(88005),r=i(67345),o=i(79814);function a(e,t){var i;return(null===(i=e.config.tweaks)||void 0===i?void 0:i.segmentLoaderArgs)?e.config.tweaks.segmentLoaderArgs:{maxRetries:e.settings.MAX_CDN_RETRIES,requestType:s(t),disableDownloadTimeout:!0,resetRetriesWhenOffline:!0}}function s(e){return o.MimeTypeHelper.isVideo(e)?r.HttpRequestType.MEDIA_VIDEO:o.MimeTypeHelper.isAudio(e)?r.HttpRequestType.MEDIA_AUDIO:o.MimeTypeHelper.isSubtitle(e)?r.HttpRequestType.MEDIA_SUBTITLES:o.MimeTypeHelper.isUnknown(e)?r.HttpRequestType.INTERNAL:r.HttpRequestType.UNKNOWN}function u(e,t){var i,r=null===(i=e.config.tweaks)||void 0===i?void 0:i.segmentLoader;return r&&"function"==typeof r?new r(t):new n.DefaultContentLoader(e,t)}},5206:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.KeyLoader=void 0;var n=i(25550),r=i(28764),o=i(88005),a=i(90637),s=i(67345),u=function(){function e(e){this.context=e,this.settings=e.settings,this.sourceIdentifier=e.sourceContext.sourceIdentifier,this.loader=new o.DefaultContentLoader(this.context,{maxRetries:this.settings.MAX_RETRIES,retryDelay:this.settings.RETRY_DELAY,requestType:s.HttpRequestType.KEY_HLS_AES})}return e.prototype.hasWithCredentials=function(){var e=this.context.sourceContext.source&&this.context.sourceContext.source.hasOwnProperty("options")&&this.context.sourceContext.source.options,t=e&&e.hasOwnProperty("withCredentials")&&e.withCredentials,i=e&&e.hasOwnProperty("hlsWithCredentials")&&e.hlsWithCredentials;return Boolean(t)||Boolean(i)},e.prototype.load=function(e){var t=this;return this.loader?this.loader.load(e,s.HttpRequestMethod.GET,s.HttpResponseType.ARRAYBUFFER,void 0,void 0,this.hasWithCredentials()).then((function(e){return e.body})).catch((function(i){if(i.message===a.RequestError.Canceled)return Promise.reject(i);var o=new r.PlayerError(n.ErrorCode.DRM_FAILED_LICENSE_REQUEST,{statusCode:i.status,statusText:i.statusText,keyUrl:e},"Failed to load the DRM key: ".concat(i.status," ").concat(i.statusText,"."),t.sourceIdentifier);return t.context.eventHandler.fireError(o),Promise.reject(o)})):Promise.reject("Could not load: No loader.")},e.prototype.isLoading=function(){return!!this.loader&&this.loader.isLoading()},e.prototype.dispose=function(){this.loader&&"function"==typeof this.loader.dispose&&this.loader.dispose(),this.loader=void 0,this.settings=void 0},e}();t.KeyLoader=u},6082:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.StreamState=void 0,function(e){e.Open="open",e.Ended="ended",e.Aborted="aborted"}(i||(t.StreamState=i={}))},6476:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.HeartbeatService=void 0;var n=i(18665),r=250,o=function(){function e(e,t){this.serviceManager=e,this.mediaPlayerController=t,this.heartbeatTimeoutId=-1}return Object.defineProperty(e.prototype,"streamTimeService",{get:function(){return this.serviceManager.get(n.ServiceName.StreamTimeService)},enumerable:!1,configurable:!0}),e.prototype.resetHeartbeatTimeout=function(){var e=this;clearTimeout(this.heartbeatTimeoutId),this.heartbeatTimeoutId=window.setTimeout((function(){return e.beat()}),r)},e.prototype.beat=function(){this.resetHeartbeatTimeout(),d(this.mediaPlayerController,this.streamTimeService)},Object.defineProperty(e.prototype,"started",{get:function(){return-1!==this.heartbeatTimeoutId},enumerable:!1,configurable:!0}),e.prototype.start=function(){this.beat()},e.prototype.stop=function(){clearTimeout(this.heartbeatTimeoutId),this.heartbeatTimeoutId=-1},e.prototype.dispose=function(){this.stop()},e}();function a(e){e.shouldClearSubtitleServiceBuffers()&&e.clearSubtitleServiceBuffers()}function s(e){e.areMediaTypesFinalForPlayingPeriod()?e.maybeStop():e.isAVCompletelyLoaded()&&e.signalEndOfStream(),a(e)}function u(e,t){var i=e.getActiveSegmentControllers(),n=i.length>0;n&&i.forEach((function(i){var n=t.getTimeForNextSegment(i.getMimeType());e.getNextSegment(i,n)})),e.areAllLoadersReady()&&(n?a(e):e.isLoadingLastPeriod()?s(e):(e.switchLoadingPeriod(),d(e,t)))}function d(e,t){e.shouldSuspendHeartbeat()||u(e,t)}t.HeartbeatService=o},9140:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.resolveContainerFormat=u;var n=i(79814),r=i(91520),o=i(16368),a=i(73543),s=i(58423);function u(e,t,i){return i?d(e,t):c(e)}function d(e,t){var i,s=r.ModuleManager.has(o.ModuleName.ContainerMP4),u=null===(i=r.ModuleManager.get(o.ModuleName.ContainerMP4,!1))||void 0===i?void 0:i.isValidMp4;return s&&u(t)?{source:a.ContainerFormat.MP4,target:a.ContainerFormat.MP4}:n.MimeTypeHelper.isSubtitle(e)?c(e):{source:a.ContainerFormat.TS,target:a.ContainerFormat.MP4}}function c(e){var t=(0,s.extractContainerFormat)(e);return t?{source:t,target:t}:void 0}},9378:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentInfoProvider=void 0;var n=i(76885),r=i(9827),o=function(){function e(){}return e.findSegmentInfoOfRepresentation=function(t,i){if(i.SegmentList){var n=i.SegmentList[0].SegmentURL,r=t.getUrl(),o=n.find((function(t){return e.getSegmentInfoUrl(t,i)===r}));if(o)return o}return null},e.findHlsRepresentationForSegment=function(e,t){var i,n,o=t.Representation.find((function(t){return e.getRepresentationId().representationId===t._internalId.representationId}));if(o){var a=(0,r.findSegmentUrlIndexWithinSegmentList)(e.getSegmentInfo(),null!==(n=null===(i=o.SegmentList)||void 0===i?void 0:i[0].SegmentURL)&&void 0!==n?n:[]);if(-1!==a)return{representation:o,index:a}}},e.getSegmentInfoUrl=function(e,t){var i=e._media;return n.URLHelper.isUrlAbsolute(i)||(i=n.URLHelper.concatUrlParts(t.BaseURL[0],i)),i},e}();t.SegmentInfoProvider=o},9827:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.findSegmentUrlIndexWithinSegmentList=r,t.matchesMediaUrl=o;var n=i(86865);function r(e,t){return t.findIndex((function(t){var i=e.discontinuitySequenceNumber===t._discontinuitySequenceNumber,r=o(e.url,t._media),a=(0,n.compareValues)(e.byteRange,t._byteRange);return i&&r&&a}))}function o(e,t){return e.endsWith(t)||e.split("?")[0].endsWith(t)}},10997:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.AdRestorationOptimizationService=void 0;var n=i(62510),r=i(76650),o=i(28819),a=i(27177),s=i(94938),u=i(18665),d=i(3872),c=function(){function e(){var e=this;this.startOptimization=function(){(0,a.isContextAvailable)(e.context)&&e.context.eventHandler.off(n.PlayerEvent.AdStarted,e.startOptimization),e.prefetchContentSegments(e.segmentInfosToPrefetch),e.segmentInfosToPrefetch=[]}}return e.prototype.updatePlayerContext=function(e){this.mimeTypes=[],this.context=e,this.segmentInfoService=e.segmentInfoService,this.segmentPrefetchingService=e.segmentPrefetchingService},e.prototype.optimizeRestorationAfterUpcomingAdBreak=function(){var e,t,i=this,r=null===(t=null===(e=this.getSourceState())||void 0===e?void 0:e.getState())||void 0===t?void 0:t.playback;if(!(0,a.isContextAvailable)(this.context)||!this.segmentInfoService||!r)return Promise.reject("Could not get playback state and/or SegmentInfoService");var o=(0,d.getPlaybackPosition)(r);return isNaN(o)?Promise.reject("Could not get playback position"):this.segmentInfoService.getSegmentInfos(o,this.context.settings.AD_RESTORATION_SEGMENT_PREFETCHING_DURATION).then((function(e){i.addCodecInfo(e),i.segmentInfosToPrefetch=e,(0,a.isContextAvailable)(i.context)&&i.context.eventHandler&&i.context.eventHandler.on(n.PlayerEvent.AdStarted,i.startOptimization)}))},e.prototype.getSourceState=function(){return(0,a.isContextAvailable)(this.context)?this.context.serviceManager.get(u.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier):void 0},e.prototype.addCodecInfo=function(e){var t=(0,a.isContextAvailable)(this.context)?this.context.sourceContext.sourceIdentifier:void 0,i=t?this.context.serviceManager.get(u.ServiceName.ManifestService,t):void 0;i&&e.forEach((function(e){var t=i.getAdaptationSet(e.internalRepresentationId);t&&(e.codecs=(0,s.getCodecsFromAdaptationSet)(t))}))},e.prototype.prefetchContentSegments=function(e){var t;this.mimeTypes=e.reduce((function(e,t){return t.mimeType&&!e.includes(t.mimeType)&&e.push(t.mimeType),e}),[]),this.maybeCreateMetrics(),null===(t=this.segmentPrefetchingService)||void 0===t||t.fetchAll(e)},e.prototype.findMatchingSegmentInfo=function(e,t){var i,n=null===(i=this.segmentPrefetchingService)||void 0===i?void 0:i.getPrefetchedSegments().get(e);if(n)return l(n,(function(e){return e.segmentInfo})).find((function(e){if(!t||null==t.segmentNumber||null==t.startTime||null==t.duration)return!0;var i=t.segmentNumber+1,n=Math.round(t.startTime+t.duration),r=e.segmentNumber===i,o=null!=e.startTime&&Math.round(e.startTime)===n;return r||o}))},e.prototype.hasPrefetchedSegment=function(e,t){var i;if((0,a.isContextAvailable)(this.context)&&this.context.sourceContext.isAd)return!1;var n=e;return"string"==typeof e&&(n=this.findMatchingSegmentInfo(e,t)),!(!n||"string"==typeof n)&&Boolean(null===(i=this.segmentPrefetchingService)||void 0===i?void 0:i.getPrefetchedSegment(n))},e.prototype.getPrefetchedSegment=function(e,t){var i=this;if(!this.segmentPrefetchingService||!e.mimeType)return null;var n=this.segmentPrefetchingService.getPrefetchedSegment(e);return this.segmentPrefetchingService.clearPrefetchingQueue(e.mimeType),n&&(this.segmentPrefetchingService.removePrefetchedSegment(e),this.segmentPrefetchingService.setShouldDownloadBeCancelledCallback(e.mimeType,t),n.finally((function(){i.segmentPrefetchingService&&e.mimeType&&i.segmentPrefetchingService.removeShouldDownloadBeCancelledCallback(e.mimeType)}))),n},e.prototype.getRepresentationForPrefetchedSegment=function(e){var t,i=null===(t=this.segmentPrefetchingService)||void 0===t?void 0:t.getPrefetchedSegments().get(e);if(i)return l(i,(function(e){return e.segmentInfo.internalRepresentationId})).find(Boolean)},e.prototype.maybeCreateMetrics=function(){var e=this;if((0,a.isContextAvailable)(this.context)){var t=this.context.store.getState();if(0!==this.mimeTypes.length&&t){var i=(0,o.getMetricsState)(t);this.mimeTypes.forEach((function(t){i[t]||e.context.store.dispatch((0,r.initializeMetricsForMimeType)(t,e.context.settings))}))}}},e.prototype.dispose=function(){(0,a.isContextAvailable)(this.context)&&this.context.eventHandler.off(n.PlayerEvent.AdStarted,this.startOptimization),this.segmentInfosToPrefetch=[],this.segmentInfoService=void 0,this.segmentPrefetchingService=void 0},e}();function l(e,t){var i=[];return e.forEach((function(e){return i.push(t(e))})),i}t.AdRestorationOptimizationService=c},12482:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.TimestampOffsetService=void 0;var n=i(18665),r=i(79814),o=i(34586),a=i(91520),s=i(16368),u=function(){function e(e,t){this.context=e,this.mimeType=t,this.discontinuityNumbers=new Map;var i=e.sourceContext.sourceIdentifier;this.sourceStore=e.serviceManager.get(n.ServiceName.SourceStoreService,i)}return Object.defineProperty(e.prototype,"logger",{get:function(){return this.context.logger},enumerable:!1,configurable:!0}),e.prototype.maybeAdjustTimestampOffsetAtDiscontinuityChange=function(e,t){var i=e.getSegmentInfo().discontinuitySequenceNumber;if(null==i||e.isInit())return!1;var n=this.discontinuityNumbers.get(e.getMimeType())!==i,r=isNaN(t.getTimestampOffset());if(n||r)this.adjustTimestampOffset(e,i,t);else if(e.getSegmentInfo().wasPrefetched){var o=this.getPresentationTimeOffsetForDisco(e,i);this.setSegmentPresentationTimeOffset(e,o)}return this.discontinuityNumbers.set(e.getMimeType(),i),!0},e.prototype.adjustTimestampOffset=function(e,t,i){var n=this.getPresentationTimeOffsetForDisco(e,t);this.maybeExtrapolateStartTimes(e,t,i),this.logger.debug("[".concat(this.mimeType,"] Initialized PTO for new discontinuity ").concat(t," to ").concat(n," on segment ").concat(e.getUrl())),i.setTimestampOffset(n),this.setSegmentPresentationTimeOffset(e,n)},e.prototype.getPresentationTimeOffsetForDisco=function(e,t){var i,n,r=this.calculateEncodedPlaybackTime(e).encodedPlaybackTime,o=a.ModuleManager.get(s.ModuleName.HLS),u=null!==(i=null==o?void 0:o.selectors)&&void 0!==i?i:{},d=u.getHlsState,c=u.getPresentationTimeOffset,l=null==d?void 0:d(this.sourceStore.getState());return l&&null!==(n=c(l,String(t)))&&void 0!==n?n:this.calculatePresentationTimeOffset(e,r,t)},e.prototype.setSegmentPresentationTimeOffset=function(e,t){var i=this.calculateEncodedPlaybackTime(e),n=i.encodedPlaybackTime,r=i.shouldUpdatePlaybackTime;e.getSegmentInfo().presentationTimeOffset=t,e.setPresentationTimeOffset(t),r&&e.setPlaybackTime(n-t)},e.prototype.maybeExtrapolateStartTimes=function(e,t,i){var n=function(e){return"extrapolateStartTimesFromDiscontinuityStarts"in e};null==e.getSegmentInfo().startTime&&n(i)&&(this.logger.debug("[".concat(e.getMimeType(),"] Missing segment startTime when trying to calculate PTO for discontinuity ").concat(t)),i.extrapolateStartTimesFromDiscontinuityStarts(e))},e.prototype.calculatePresentationTimeOffset=function(e,t,i){var n=t-e.getSegmentInfo().startTime,u=a.ModuleManager.get(s.ModuleName.HLS);return r.MimeTypeHelper.isSubtitle(e.getMimeType())&&o.TextSegmentAnalyzer.isPlainTextPayload(e.getData())||(this.sourceStore.dispatch(u.actions.setPresentationTimeOffset(String(i),n)),this.logger.debug("[".concat(e.getMimeType(),"] Setting PTO for discontinuity ").concat(i," to ").concat(n))),n},e.prototype.calculateEncodedPlaybackTime=function(e){var t,i=a.ModuleManager.get(s.ModuleName.ContainerMP4,!1),n=!0,r=i?i.parsePlaybackTime(e,this.logger):e.getBaseMediaDecodeTime()/e.getTimescale();return isNaN(r)&&(r=null!==(t=e.getPlaybackTime())&&void 0!==t?t:e.getSegmentInfo().startTime,n=!1),{encodedPlaybackTime:r,shouldUpdatePlaybackTime:n}},e.prototype.dispose=function(){this.discontinuityNumbers=null,this.sourceStore=null},e}();t.TimestampOffsetService=u},13034:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.findAdaptationSetOfMimeType=a,t.getHlsDefaultLanguage=s,t.getPreferredLanguage=u,t.findAdaptationSetsForMimeType=l;var n=i(79814),r=i(91520),o=i(16368);function a(e,t,i){void 0===i&&(i={});var n,r=i.sourceState,o=i.langObj,a=i.periodSwitched,c=u(e,r),p=l(t,e);(c&&(n=d(p,c,o,a)),!n&&r)&&(n=d(p,s(e,r),o,a));return n||p[0]}function s(e,t){var i;if(r.ModuleManager.has(o.ModuleName.HLS)){var a=r.ModuleManager.get(o.ModuleName.HLS).selectors,s=a.getHlsState,u=a.getDefaultLanguages,d=s(t),c=d?u(d):void 0;if(c){var l=null===(i=n.MimeTypeHelper.getMediaType(e))||void 0===i?void 0:i.toUpperCase();return l?c[l]:void 0}}}function u(e,t){var i=t&&s(e,t),n={language:navigator.language||navigator.userLanguage||(null==i?void 0:i.language)};return n.language&&i&&c(n.language,i.language)&&(n.name=i.name),n}function d(e,t,i,n){if(void 0===n&&(n=!1),0!==e.length){var r=void 0;if(i&&(r=p(e,i,n)),r)return r;if(t&&t.language){var o=e.filter((function(e){return!(!Boolean(e._lang)||null==e._lang)&&c(e._lang,t.language)}));return o.find((function(e){return e.Representation.find((function(e){return e._name===t.name}))}))||o[0]}}}function c(e,t){return Boolean((null==e?void 0:e.includes(t))||(null==t?void 0:t.includes(e)))}function l(e,t){return e.AdaptationSet.filter((function(e){return e._mimeType===t||e.Representation.find((function(e){return e._mimeType===t}))}))}function p(e,t,i){var n=e.find((function(e){return e._lang&&t.lang&&e._lang===t.lang})),r=e.find((function(e){return t.adaptationSetId&&e._internalId.equals(t.adaptationSetId)}));return r=r||e.find((function(e){return t.id===e._internalId.adaptationSetId})),i?n||r:r||n}},13127:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.XSDateTimeDecoder=t.ISO8601TimeDecoder=void 0;var i=function(){function e(){}return e.prototype.decode=function(e){return Date.parse(e)},e}();t.ISO8601TimeDecoder=i;var n=function(){function e(){}return e.prototype.decode=function(t){var i=Date.parse(t);if(isNaN(i)){var n=e.DATETIME_REGEX.exec(t);if(!n)return NaN;var r=Date.UTC(parseInt(n[1],10),parseInt(n[2],10)-1,parseInt(n[3],10),parseInt(n[4],10),parseInt(n[5],10),n[6]&&parseInt(n[6],10)||0,n[7]&&1e3*parseFloat(n[7])||0);if(n[9]&&n[10]){var o=60*parseInt(n[9],10)+parseInt(n[10],10);r+=("+"===n[8]?-1:1)*o*60*1e3}return new Date(r).getTime()}return i},e.DATETIME_REGEX=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2})(?::(\d*)(\.\d*)?)?(?:([+-])(\d{2}):(\d{2}))?/,e}();t.XSDateTimeDecoder=n},13788:function(e,t,i){var n=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentInfoService=void 0,t.getSegmentInfoId=c;var r=i(18665),o=i(27177),a=function(){function e(){this.segmentControllers=[]}return e.prototype.updatePlayerContext=function(e){this.context=e},e.prototype.setSegmentControllers=function(e){this.segmentControllers=e},e.prototype.getSegmentControllerMimeTypes=function(){return this.segmentControllers.map((function(e){return e.getMimeType()}))},e.prototype.getSegmentInfos=function(e,t){if(!(0,o.isContextAvailable)(this.context))return Promise.resolve([]);var i=this.context.serviceManager.get(r.ServiceName.ManifestService,this.context.sourceContext.sourceIdentifier).getPeriodIdForTime(e),n=this.segmentControllers.map((function(n){return s(n,{startTime:e,duration:t,targetPeriodId:i})}));return Promise.all(n).then((function(e){return e.flatMap((function(e){return e}))}))},e.prototype.dispose=function(){this.segmentControllers=[]},e}();function s(e,t){return e.getCurrentPeriodId()!==t.targetPeriodId&&e.switchPeriod(t.targetPeriodId),e.seekTo(t.startTime),e.getCurrentAdaptationSet()?u(e,t):Promise.resolve([])}function u(e,t){return d(e,t.duration).then((function(t){if(t.length>0){var i=e.getInitSegmentInfoForDataSegmentInfo(t[0]);if(i)return n([i],t,!0)}return t}))}function d(e,t,i){void 0===i&&(i=[]);var n=i[i.length-1],r=n?n.startTime+n.duration:0;return t<=0||!e.hasNext(r)?Promise.resolve(i):e.getDataSegmentInfo(r).then((function(n){return n.duration&&(t-=n.duration),i.push(n),d(e,t,i)}))}function c(e){return e.url+(e.byteRange?","+JSON.stringify(e.byteRange):"")}t.SegmentInfoService=a},14764:function(e,t,i){var n,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},o=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.getStreamTimeline=t.StreamTimelineReducer=void 0;var a=i(92712),s=i(21829),u=i(22645),d=i(1590),c={};function l(e,t,i){var n,a=e[t]?o([],e[t],!0):[];return r(r({},e),((n={})[t]=(0,u.addStreamTimeRangeToTimeline)(a,i),n))}function p(e,t,i,n){var s;if(!e[t])return e;var d=o([],e[t],!0);if(n){var c=r(r({},i),{type:n});d=(0,u.removeStreamTimeRangeFromTimeline)(d,c)}else d=a.BufferRangeHelper.excludeTimeRange(d,i);return r(r({},e),((s={})[t]=d,s))}function f(e,t){var i;return t?r(r({},e),((i={})[t]=[],i)):c}t.StreamTimelineReducer=(0,s.default)(c,((n={})[d.StreamTimelineActionType.StreamTimeRangeAdd]=function(e,t){return l(e,t.payload.mimeType,t.payload.streamTimeRange)},n[d.StreamTimelineActionType.StreamTimeRangeRemove]=function(e,t){return p(e,t.payload.mimeType,t.payload.range,t.payload.type)},n[d.StreamTimelineActionType.StreamTimelineReset]=function(e,t){return f(e,t.payload)},n));var h=function(e){var t;return null!==(t=null==e?void 0:e.streamTimeline)&&void 0!==t?t:{}};t.getStreamTimeline=h},15109:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.BufferStallingService=void 0;var n=i(76420),r=i(26905),o=function(){function e(e,t,i){this.playerStateService=e,this.logger=t,this.renderer=i}return e.prototype.startStalling=function(){this.isRendererStalling()||(this.logger.debug("Stalling playback at ".concat(this.renderer.getCurrentTime(!0))),this.logger.insane("Video element stall started in state: ".concat(this.playerStateService.playbackState)),this.playerStateService.setIsRendererStalling(!0),this.playerStateService.isPlaying()&&(this.logger.insane("Call pause on renderer"),this.renderer.pause()))},e.prototype.endStalling=function(){var e=this;if(this.isRendererStalling()){this.playerStateService.setIsRendererStalling(!1);var t=[n.PlaybackState.Play,n.PlaybackState.Playing].includes(this.playerStateService.playbackState);if(this.logger.debug("Unstalling and ".concat(t?"":"not ","restarting playback")),t)!this.playerStateService.seekingOrTimeshifting&&(0,r.shouldResetCurrentTimeAfterBufferUnderrun)()&&(this.logger.debug("Resetting current time before unstalling"),this.renderer.setCurrentTime(this.renderer.getCurrentTime())),this.renderer.play().catch((function(t){e.logger&&e.logger.debug("Play after unstalling failed with reason: ".concat(t))}))}},e.prototype.isRendererStalling=function(){return this.playerStateService.getIsRendererStalling()},e.prototype.dispose=function(){this.logger=null,this.playerStateService=null,this.renderer=null},e}();t.BufferStallingService=o},16280:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.mergeBufferBlockTimeRanges=a,t.excludeBufferBlockTimeRange=c,t.segmentToBufferBlockTimeRange=l;var n=i(92712),r=.1;function o(e,t,i){return{start:(0,n.rangeFixedValue)(e),end:(0,n.rangeFixedValue)(t),bufferBlockId:i}}function a(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var i=p(e);return Object.keys(i).flatMap((function(e){return n.BufferRangeHelper.mergeRanges(i[e],r)}))}function s(e,t){return[o(e.start,t.start,e.bufferBlockId),o(t.end,e.end,e.bufferBlockId)]}function u(e,t){return o(e.start>=t.start?t.end:e.start,e.end>t.end?e.end:t.start,e.bufferBlockId)}function d(e,t){var i=[];return e.forEach((function(e){(0,n.areDisjoint)(e,t)?i.push(e):(0,n.isSurrounding)(e,t)?i.push.apply(i,s(e,t)):(0,n.isFullyIncluded)(e,t)||i.push(u(e,t))})),i.filter((function(e){return e.end-e.start>=r}))}function c(e,t){var i=t.bufferBlockId,a=p(e.map((function(e){return o(e.start,e.end,e.bufferBlockId)}))),s=a[i];if(!(null==s?void 0:s.length))return e;var u=d(s,t);return a[i]=n.BufferRangeHelper.mergeRanges(u,r),Object.keys(a).flatMap((function(e){return a[e]}))}function l(e){var t,i=null!==(t=e.getBufferBlockId())&&void 0!==t?t:1/0,n=e.getPlaybackTime();return o(n,n+e.getDuration(),i)}function p(e){var t={};return e.forEach((function(e){t[e.bufferBlockId]||(t[e.bufferBlockId]=[]),t[e.bufferBlockId].push(e)})),t}},16492:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.BufferRangesCache=void 0;var n=i(18665),r=i(33669),o=i(3464),a=i(28337),s=function(){function e(e,t){var i=this;this.context=e,this.segmentStore=t,this.sourceStore=this.getSourceStore(),this.unsubscribeToStoreChange=(0,o.subscribe)(e.store)(a.sourceIdentifiersSelector,(function(){return i.onSourceIdentifierChanged()}),(function(e,t){return!!e.includes(i.context.sourceContext.sourceIdentifier)&&(0,a.hasASourceStoreIdentifierChanged)(e,t)})),this.subscribeToBufferChange(),this.setNewCache((0,r.getBufferState)(this.sourceStore.getState()))}return e.prototype.onSourceIdentifierChanged=function(){this.sourceStore=this.getSourceStore(),this.sourceStore?(this.subscribeToBufferChange(),this.setNewCache((0,r.getBufferState)(this.sourceStore.getState()))):this.unsubscribeToBufferChange()},e.prototype.getSourceStore=function(){return this.context.serviceManager.get(n.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier)},e.prototype.unsubscribeToBufferChange=function(){this.unsubscribeToBufferRangeChange&&this.unsubscribeToBufferRangeChange(),this.unsubscribeToBufferRangeChange=void 0,this.unsubscribeToStoreChange=void 0},e.prototype.subscribeToBufferChange=function(){var e=this;this.unsubscribeToBufferChange(),this.unsubscribeToBufferRangeChange=(0,o.subscribe)(this.sourceStore)((function(e){return(0,r.getBufferState)(e)}),(function(t){return e.setNewCache(t)}),r.hasBufferRangesChanged)},e.prototype.setNewCache=function(e){this.cachedBufferRanges={overallBufferRanges:(0,r.getBufferRangesMap)(this.segmentStore,e,this.context.settings.GAP_TOLERANCE)}},e.prototype.getRanges=function(e){return void 0!==this.unsubscribeToBufferRangeChange&&void 0!==this.cachedBufferRanges&&0!==Object.keys(this.cachedBufferRanges.overallBufferRanges).length||this.setNewCache(e),this.cachedBufferRanges.overallBufferRanges},e.prototype.dispose=function(){this.unsubscribeToBufferChange(),this.unsubscribeToStoreChange&&(this.unsubscribeToStoreChange(),this.unsubscribeToStoreChange=void 0),this.cachedBufferRanges=void 0},e}();t.BufferRangesCache=s},19300:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.DiscontinuityMonitor=void 0;var n=i(62510),r=function(){function e(e){var t=this;this.context=e,this.onSeek=function(){t.seekStarted=!0},this.onSeeked=function(){var e=t.context.internalPlayer.getCurrentTime();Object.values(t.delayedSegmentPlaybackData).forEach((function(i){e>=i.playbackTime&&i.playbackTime+i.duration>=e&&t.maybeFirePeriodSwitchEvents(i)})),t.delayedSegmentPlaybackData={}},this.currentDiscontinuityNumbers={},this.delayedSegmentPlaybackData={},this.seekStarted=!1,this.context.eventHandler.on(n.PlayerEvent.Seek,this.onSeek,!0),this.context.eventHandler.on(n.PlayerEvent.Seeked,this.onSeeked,!0)}return e.prototype.onSegmentAvailable=function(e){if(!e.isInit()){var t=e.getMimeType(),i=e.getSegmentInfo();this.currentDiscontinuityNumbers[t]?this.seekStarted&&(this.delayedSegmentPlaybackData[e.getMimeType()]={mimeType:e.getMimeType(),discontinuitySequenceNumber:i.discontinuitySequenceNumber,playbackTime:i.startTime,duration:e.getDuration()},this.seekStarted=!1):this.currentDiscontinuityNumbers[t]={discontinuitySequenceNumber:i.discontinuitySequenceNumber,startTime:i.startTime||0}}},e.prototype.maybeFirePeriodSwitchEvents=function(e){var t=e.mimeType,i=e.discontinuitySequenceNumber,r=e.playbackTime;if(null!=i&&this.currentDiscontinuityNumbers[t]){var o=this.currentDiscontinuityNumbers[t].discontinuitySequenceNumber;o!==i&&(this.shouldFirePeriodSwitchEvent(t,i,r)&&(this.context.eventHandler.dispatchEvent(n.PlayerEvent.PeriodSwitch),this.context.eventHandler.dispatchEvent(n.PlayerEvent.PeriodSwitched,{sourcePeriod:{periodId:String(o)},targetPeriod:{periodId:String(i)}})),this.currentDiscontinuityNumbers[t]={discontinuitySequenceNumber:i,startTime:r})}},e.prototype.shouldFirePeriodSwitchEvent=function(e,t,i){var n=this,r=Object.keys(this.currentDiscontinuityNumbers).filter((function(t){return t!==e})),o=!0;return r.forEach((function(e){var r=n.currentDiscontinuityNumbers[e];r.discontinuitySequenceNumber===t&&(Math.abs(i-r.startTime)<1&&(o=!1))})),o},e.prototype.dispose=function(){this.context.eventHandler.off(n.PlayerEvent.Seek,this.onSeek),this.context.eventHandler.off(n.PlayerEvent.Seeked,this.onSeeked)},e}();t.DiscontinuityMonitor=r},19724:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.Segregation=void 0,t.areSegregationCriteriaEqual=c,t.canReuseSourceBuffer=p;var n=i(11399),r=i(13533),o=i(42283),a=i(79814),s=i(56435),u=i(55937),d=function(){function e(e,t){this.requiredMediaTypes=e,this.settings=t,this.requiredMediaTypes=e}return e.prototype.isRequiredMimeType=function(e){var t;return Boolean(null===(t=this.requiredMediaTypes[e.getPeriodId()])||void 0===t?void 0:t[e.getMimeType()])},e.prototype.hasSameRequiredMediaTypes=function(e,t){var i=this;if(void 0!==t.getSegmentInfo().discontinuitySequenceNumber)return!0;if(!this.isRequiredMimeType(t))return!1;var n=t.getPeriodId(),r=this.requiredMediaTypes[n]||{},o=Object.keys(r).filter((function(e){return a.MimeTypeHelper.isAV(e)}));return e.length===o.length&&e.every((function(e){var t;return null===(t=i.requiredMediaTypes[n])||void 0===t?void 0:t[e.mimeType]}))},e.prototype.getSegregationCriteria=function(e){var t,i={encryption:l(e),codec:o.CodecStringHelper.extractCodec(e.getCodec())};if(this.settings.HLS_CLEAR_BUFFERS_ON_DISCONTINUITY_SWITCHES){var n=null===(t=e.getSegmentInfo())||void 0===t?void 0:t.discontinuitySequenceNumber;null!=n&&(i.discontinuityNumber=n)}return(this.settings.DASH_CLEAR_BUFFERS_ON_PERIOD_SWITCHES||(0,s.isTizen2016)())&&(i.periodId=e.getPeriodId()),i},e.prototype.canAddSegmentToBufferBlock=function(e,t,i){var n,r=t.getMediaTypes();if(0===r.length)return!0;if(!this.hasSameRequiredMediaTypes(r,e))return!1;var o=e.getMimeType(),a=t.getSegregationCriteria(o);if(a)return c(i,a);var s=null===(n=r.find((function(e){return e.mimeType!==o})))||void 0===n?void 0:n.mimeType;return!s||c(i,t.getSegregationCriteria(s),["codec","encryption"])},e}();function c(e,t,i){return void 0===i&&(i=[]),Object.keys(e).filter((function(e){return!i.includes(e)})).every((function(i){return t&&t[i]===e[i]}))}function l(e){return e.isEncrypted()?u.EncryptionState.Encrypted:u.EncryptionState.Clear}function p(e,t,i){var n=h();return i.every((function(i){var r=i.mimeType;return n.canReuseSourceBuffers(e,t,r)}))}function f(e,t){var i=e.getMediaTypes().find((function(e){return e.mimeType===t}));return i?i.timescale:0}function h(){return(0,r.getCapabilities)().isTizen?T:(0,r.getCapabilities)().isLegacyEdge?M:(0,r.getCapabilities)()[n.CapabilityKey.isEdge]?y:(0,r.getCapabilities)()[n.CapabilityKey.isWebOS]?b:(0,r.getCapabilities)()[n.CapabilityKey.isPlayStation4]?v:(0,r.getCapabilities)()[n.CapabilityKey.isPlayStation5]?S:m}function g(e,t,i){return{currentCriteria:e.getSegregationCriteria(i),nextCriteria:t.getSegregationCriteria(i)}}t.Segregation=d;var m={canReuseSourceBuffers:function(e,t,i){var n=g(e,t,i);return P(n.currentCriteria,n.nextCriteria)}},v={canReuseSourceBuffers:function(e,t,i){var n=g(e,t,i),r=n.currentCriteria,o=n.nextCriteria;return P(r,o)&&R(r,o)&&E(r,o)&&C(r,o)&&A(e,t,i)}},S={canReuseSourceBuffers:function(e,t,i){var n=g(e,t,i),r=n.currentCriteria,o=n.nextCriteria;return P(r,o)&&R(r,o)&&A(e,t,i)}},y={canReuseSourceBuffers:function(e,t,i){var n=g(e,t,i),r=n.currentCriteria,o=n.nextCriteria;return P(r,o)&&!I(r,o)}},T={canReuseSourceBuffers:function(e,t,i){var n=g(e,t,i),r=n.currentCriteria,o=n.nextCriteria;return!(0,s.isTizen2017)()&&(P(r,o)&&R(r,o)&&A(e,t,i))}},b={canReuseSourceBuffers:function(e,t,i){var n=g(e,t,i),r=n.currentCriteria,o=n.nextCriteria,a=P(r,o)&&A(e,t,i)&&!E(r,o);return((0,s.isWebOs2017)()||(0,s.isWebOs2016)())&&(a=a&&R(r,o)),a}},M={canReuseSourceBuffers:function(e,t,i){return!1}};function I(e,t){var i=(null==e?void 0:e.encryption)===u.EncryptionState.Clear,n=(null==t?void 0:t.encryption)===u.EncryptionState.Encrypted;return i&&n}function P(e,t){return(null==e?void 0:e.codec)===(null==t?void 0:t.codec)}function R(e,t){return(null==e?void 0:e.encryption)===(null==t?void 0:t.encryption)}function C(e,t){return(null==e?void 0:e.periodId)===(null==t?void 0:t.periodId)}function E(e,t){return(null==e?void 0:e.discontinuityNumber)===(null==t?void 0:t.discontinuityNumber)}function A(e,t,i){return f(e,i)===f(t,i)}},20417:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.throwDownloadError=c;var n=i(25550),r=i(28764),o=i(63546),a=i(35148),s=i(62510),u=i(90637),d=i(66864);function c(e,t,i,n,r){e.eventHandler.dispatchEvent(s.PlayerEvent.Warning,new o.PlayerWarning(a.WarningCode.PLAYBACK_DECODE_RETRIES_EXCEEDED)),e.eventHandler.fireError(l(e,t,i,n,r))}function l(e,t,i,o,a){if(0===i)return t&&t===u.RequestError.TimedOut||a===d.SegmentLoadingErrorReason.TIMEOUT?new r.PlayerError(n.ErrorCode.NETWORK_SEGMENT_DOWNLOAD_TIMEOUT,{segmentUrl:o.url,mimeType:o.mimeType},"Failed to load the segment: the request timed out."):new r.PlayerError(n.ErrorCode.NETWORK_ERROR,{url:o.url,statusCode:i},t);e.logger.debug(t);var s={message:t};return o.url&&(s.segment=p(o)),a===d.SegmentLoadingErrorReason.TIMEOUT?new r.PlayerError(n.ErrorCode.NETWORK_SEGMENT_DOWNLOAD_TIMEOUT,s,t):new r.PlayerError(n.ErrorCode.NETWORK_ERROR,s,t)}function p(e){return{url:e.url,mimeType:e.mimeType,codecs:e.codecs}}},21918:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.TimeShiftHandler=void 0;var n=i(18665),r=i(62510),o=i(58975),a=i(27279),s=i(3941),u=i(331),d=i(70016),c=i(54838),l=i(91520),p=i(16368),f=i(72788),h=i(59839),g=i(22645),m=i(64729),v=i(87062),S=i(93109),y=function(){function e(e,t,i,r){var o=this;this.onSeek=function(){o.updateInitialTimeShiftContext(!0)},this.onSeeked=function(e){var t=o.context.renderer.getCurrentTime(!0),i=t-e;i=c.Util.roundValue(i,1),o.updateTimeShiftStateAfterSeek(i,t)},this.context=e,this.mediaPlayerController=t,this.bufferController=i,this.segmentControllers=Object.keys(r).map((function(e){return r[e]})),this.manifestService=e.serviceManager.get(n.ServiceName.ManifestService,e.sourceContext.sourceIdentifier),this.logger=e.logger,this.timeShiftCounter=0,this.sourceIdentifier=e.sourceContext.sourceIdentifier,this.setupStoreSubscriptions()}return e.prototype.isLowLatency=function(){var e=(0,v.isLowLatencyConfigured)(this.context),t=(0,S.isPlayingLowLatencyHls)(this.context);return e||t},e.prototype.calculateLiveEdgeInVideoTime=function(){var e;if(this.initialTimeShiftContext&&!this.initialTimeShiftContext.isOngoing){var t=(0,u.toSeconds)(Date.now()-this.initialTimeShiftContext.timestamp);e=this.initialTimeShiftContext.liveEdge+t}else e=this.getInitialLiveEdge();return isFinite(e)||(e=(0,u.toSeconds)(Date.now()-this.manifestService.getAvailabilityStartTime())),e},e.prototype.getInitialLiveEdge=function(){var e=this.calculateMpdHandlerLiveEdge();if(!this.isLowLatency())return e;var t=this.segmentControllers.reduce((function(e,t){return Math.min(e,t.getMPDHandler().getSegmentDuration())}),1/0),i=this.getWallclockTimeInSeconds();return i-e<t?i:e},e.prototype.getWallclockTimeInSeconds=function(){var e=this.context.serviceManager.maybeCall(n.ServiceName.SynchronizedTimeService,(function(e){return e.getTimeDifference()}),0,this.context.sourceContext.sourceIdentifier);return(0,u.toSeconds)(Date.now()+e)},e.prototype.calculateMpdHandlerLiveEdge=function(){return this.segmentControllers.map((function(e){return e.getLiveEdgeTime()})).reduce((function(e,t){return t<e&&t>=0?t:e}),1/0)},e.prototype.updateSegmentControllers=function(e){this.segmentControllers=Object.values(e)},e.prototype.getMaxTimeShift=function(){if(this.manifestService.getManifest()){var e=this.manifestService.getTimeShiftBufferDepthSeconds();return Math.min(e+this.manifestService.getDesiredDistanceToLiveEdge(),0)}return 0},e.prototype.getTimeShiftLiveEdge=function(){return this.calculateLiveEdgeInVideoTime()-this.manifestService.getDesiredDistanceToLiveEdge()},e.prototype.getTimeShift=function(){var e=this.getActualTimeShift();if(this.lastTimeShiftStatus){var t=(0,u.toSeconds)(Date.now()-this.lastTimeShiftStatus.completionDate)-(this.mediaPlayerController.getCurrentTime()-this.lastTimeShiftStatus.reachedTime);if(Math.abs(t)<this.context.settings.ACCEPTABLE_TIMESHIFT_INACCURACY)return this.lastTimeShiftStatus.offset}var i=this.getMaxTimeShift();return e<i&&(e=i),e>0&&(e=0),e},e.prototype.getActualTimeShift=function(e){return void 0===e&&(e=!1),!this.initialTimeShiftContext||this.initialTimeShiftContext.isOngoing?0:(e?this.mediaPlayerController.getBufferRelevantTime():this.mediaPlayerController.getCurrentTime())-this.getTimeShiftLiveEdge()},e.prototype.getDistanceToRealLiveEdge=function(){return this.calculateLiveEdgeInVideoTime()-this.mediaPlayerController.getBufferRelevantTime()},e.prototype.timeShift=function(e,t,i){var n,r=this;void 0===i&&(i=!1);var o=Boolean(this.initialTimeShiftContext);o||this.adjustTargetBufferLevel(),e=Math.max(this.getMaxTimeShift(),e);var u=this.mediaPlayerController.getCurrentTime(),c=this.calculatePlaybackTimeForTimeShiftOffset(e),l=null!==(n=this.manifestService.getPeriodIdForTime(c))&&void 0!==n?n:s.DEFAULT_PERIOD_ID;this.timeShiftCounter++;var p=this.timeShiftCounter,h=i||this.bufferController.shouldBuffersBeClearedOnTimeshift(c);return this.logger.debug("Performing timeShift to offset ".concat(e,", time: ").concat(u," -> ").concat(c,", period: ").concat(l)),this.bufferController.setTimeshiftCancelState(!1),(0,d.isDefined)(l)&&this.mediaPlayerController.updatePeriodForTimeshift(l,!o,h),h&&this.segmentControllers.forEach((function(e){return e.seekTo(c)})),o&&this.maybeResetTransmuxerForPtsRollover(c),this.updateInitialTimeShiftContext(!0),this.bufferController.setCurrentTime(c,!0,i).then((function(i){var n;r.updateInitialTimeShiftContext(!1),r.lastTimeShiftStatus={offset:e,completionDate:Date.now(),reachedTime:i},r.timeShiftCounter===p&&t?(r.logger.debug("Successfully timeShifted to ".concat(i,", hit target time with a diff of ").concat(i-c)),null===(n=(0,a.getSourceStore)(r.context))||void 0===n||n.dispatch((0,f.periodSwitchFinished)(l)),t()):t&&r.context.logger.debug("Timeshift to ".concat(e," has finished but a newer operation has been started"))})).catch((function(e){r.logger.debug("Failed to set currentTime on timeShift",e)})).finally((function(){return r.bufferController.setTimeshiftCancelState(!1)}))},e.prototype.maybeResetTransmuxerForPtsRollover=function(e){var t;if(this.manifestService.isHlsManifest()){var i=this.context.serviceManager.get(n.ServiceName.SourceStoreService,this.sourceIdentifier);T(null===(t=l.ModuleManager.get(p.ModuleName.HLS))||void 0===t?void 0:t.selectors.getHlsState(null==i?void 0:i.getState()),e)&&(this.context.logger.debug("Resetting transmuxer because target is beyond a timestamp rollover"),this.mediaPlayerController.resetTransmuxer())}},e.prototype.cancel=function(){this.context.logger.debug("Cancelling ongoing time shift operation..."),this.bufferController.setTimeshiftCancelState(!0)},e.prototype.calculatePlaybackTimeForTimeShiftOffset=function(e){var t=this.manifestService.getDesiredDistanceToLiveEdge(),i=this.calculateLiveEdgeInVideoTime(),n=i+(e-t);return this.manifestService.hasMultiplePeriods()?(0,m.adjustTargetTimeToPeriodStart)(n,this.context.settings.END_OF_BUFFER_TOLERANCE,this.manifestService,this.context.logger):0===e&&this.isLowLatency()?Math.min(i,n+this.context.settings.LOW_LATENCY_SEEK_AHEAD):n},e.prototype.onManifestUpdate=function(){(this.adjustTargetBufferLevel(),this.manifestService.isLive())&&(this.getActualTimeShift(!0)<this.getMaxTimeShift()-this.getMinSegmentDuration()&&this.context.eventHandler.dispatchEvent(r.PlayerEvent.DVRWindowExceeded),this.removeDroppedOutFailedBufferRanges())},e.prototype.removeDroppedOutFailedBufferRanges=function(){var e=this;this.context.serviceManager.maybeCall(n.ServiceName.SourceStoreService,(function(t){var i,n=e.manifestService.getTimeShiftBufferDepthSeconds(),r=e.calculateLiveEdgeInVideoTime()+n,o=null===(i=t.getState())||void 0===i?void 0:i.streamTimeline;void 0!==o&&Object.keys(o).forEach((function(e){t.dispatch((0,h.removeStreamTimeRange)(e,{start:0,end:r},g.StreamTimeRangeType.Failed))}))}))},e.prototype.isTimeInBufferedRange=function(e){return this.bufferController.isInBufferedRange(e)},e.prototype.getMinSegmentDuration=function(){return this.segmentControllers.reduce((function(e,t){return Math.min(e,t.getMPDHandler().getSegmentDuration())}),1/0)},e.prototype.adjustTargetBufferLevel=function(){var e=this.manifestService.isLive(),t=e?this.getDistanceToRealLiveEdge():1/0,i=e?Math.abs(this.manifestService.getTimeShiftBufferDepthSeconds()):this.manifestService.getDuration();this.context.bufferSettings.setTargetBufferLevelConstraints({minimumBufferLength:this.getMinSegmentDuration(),currentDistanceToLiveEdge:t,streamDuration:i})},e.prototype.updateTimeShiftStateAfterSeek=function(e,t){this.lastTimeShiftStatus={completionDate:Date.now(),reachedTime:t,offset:e},this.updateInitialTimeShiftContext(!1)},e.prototype.updateInitialTimeShiftContext=function(e){(!this.initialTimeShiftContext||!e&&this.initialTimeShiftContext.isOngoing)&&(this.initialTimeShiftContext={timestamp:Date.now(),liveEdge:this.calculateLiveEdgeInVideoTime(),isOngoing:e})},e.prototype.setupStoreSubscriptions=function(){var e=this;this.unsubscribeOnSeek=(0,o.subscribeToSeek)(this.context.store,this.onSeek),this.unsubscribeOnSeeked=(0,o.subscribeToSeeked)(this.context.store,(function(t,i){return e.onSeeked(i.seekingProcess.seekableRange.end)}))},e.prototype.removeStoreSubscriptions=function(){var e,t;null===(e=this.unsubscribeOnSeek)||void 0===e||e.call(this),null===(t=this.unsubscribeOnSeeked)||void 0===t||t.call(this)},e.prototype.dispose=function(){this.removeStoreSubscriptions(),this.bufferController.setTimeshiftCancelState(!0)},e}();function T(e,t){var i,n,r=null!==(n=null===(i=null==e?void 0:e.timestampRolloverPositions)||void 0===i?void 0:i.next)&&void 0!==n?n:-1;return-1!==r&&(t>=r||t<=e.timestampRolloverPositions.previous)}t.TimeShiftHandler=y},22645:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.StreamTimeRangeType=void 0,t.addStreamTimeRangeToTimeline=s,t.removeStreamTimeRangeFromTimeline=u,t.getTrackIdentifier=c;var n,r=i(92712),o=i(87326),a=i(79814);function s(e,t){var i=e.filter((function(e){return e.type===t.type})).concat(t),n=r.BufferRangeHelper.mergeRanges(i,o.DefaultSettings.GAP_TOLERANCE);return e=e.filter((function(e){return e.type!==t.type})),n.forEach((function(t){e=d(e,t,(function(e){return e.start>t.start}))})),e}function u(e,t){var i=e.filter((function(e){return e.type===t.type})),n=r.BufferRangeHelper.excludeTimeRange(i,t);return e=e.filter((function(e){return e.type!==t.type})),n.forEach((function(t){t.end-t.start>o.DefaultSettings.GAP_TOLERANCE&&(e=d(e,t,(function(e){return e.start>t.start})))})),e}function d(e,t,i){var n=e.findIndex(i);return n<0?e.push(t):e.splice(n,0,t),e}function c(e){var t=e.mimeType,i=e.internalRepresentationId;return a.MimeTypeHelper.isSubtitle(t)?"".concat(t,"/").concat(i.adaptationSetId):t}!function(e){e.Loading="loading",e.Failed="failed"}(n||(t.StreamTimeRangeType=n={}))},22916:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.resolveMediaTypes=o;var n=i(42283),r=i(94938);function o(e){return(0,r.getCodecsFromAdaptationSet)(e).split(",").map((function(e){return(0,n.getMediaTypeFromCodec)(e)}))}},23414:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.TechnologyChecker=void 0;var n=i(91520),r=i(16368),o=function(){function e(){}return e.prototype.getSupportedTechnologies=function(){return n.ModuleManager.has(r.ModuleName.RendererMse)?n.ModuleManager.get(r.ModuleName.RendererMse).technologyChecker.getSupportedTechnologies():[]},e}();t.TechnologyChecker=o},25040:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getEncryptionKey=t.getAllEncryptionKeys=void 0;var i=function(e){return null==e?void 0:e.encryptionKeys};t.getAllEncryptionKeys=i;var n=function(e,t){var i;return null===(i=null==e?void 0:e.encryptionKeys)||void 0===i?void 0:i.find((function(e){return e.uri===t}))};t.getEncryptionKey=n},25996:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerConfigEnhancer=void 0;var n=i(25226),r=i(54838),o=i(87062),a=function(){function e(e){this.originalConfig={},r.Util.deepCopy(this.originalConfig,e.config),this.originalSettings={},r.Util.deepCopy(this.originalSettings,e.settings)}return e.prototype.adjustToCurrentSource=function(e,t){var i,r,a,s,u=e.config;if(u.adaptation&&(u.adaptation.logic=null===(i=this.originalConfig.adaptation)||void 0===i?void 0:i.logic),e.settings.CHUNKED_CMAF_STREAMING=this.originalSettings.CHUNKED_CMAF_STREAMING,t.isDashManifest()&&t.isLive()&&(0,o.isLowLatencyConfigured)(e)){e.logger.debug("Detected DASH Live stream and LL config, enabling 'low-latency-v1' adaptation logic and CHUNKED_CMAF_STREAMING tweak"),u.adaptation=null!==(r=u.adaptation)&&void 0!==r?r:{},u.adaptation.logic=null!==(a=u.adaptation.logic)&&void 0!==a?a:n.AdaptationLogicType.LOW_LATENCY;var d=e.internalPlayer.getConfig().tweaks||{};e.settings.CHUNKED_CMAF_STREAMING=null===(s=d.CHUNKED_CMAF_STREAMING)||void 0===s||s}},e}();t.PlayerConfigEnhancer=a},26382:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.FetchController=void 0;var r,o=i(90637),a=i(23234),s=i(67345),u=i(10981),d=i(72207);!function(e){e.User="user",e.Timeout="timeout"}(r||(r={}));var c=function(){function e(e,t,i,r){var o=this;this.useStreamResponse=r,this.setProgressListener=function(e){o.progressListener=e},this.onProgress=function(e){o.progressListener&&(o.timing.progressTimestamp=a.TimingUtil.getHiResTimestamp(),o.progress.loadedBytes=e,o.progressListener(n({},o.progress)))},this.onSuccess=function(){clearTimeout(o.cancelTimeout)},this.logger=e.logger,this.timeout=i,this.request=t,this.fetching=new u.Deferred,this.bytesReceivedCount=0,this.timing={sendTimestamp:-1,openedTimestamp:-1,headersReceivedTimestamp:-1,progressTimestamp:-1,doneTimestamp:-1},this.abortController=new AbortController,this.progress={loadedBytes:0,url:t.url,responseTiming:this.timing,chunks:[],elapsedTime:0}}return e.prototype.getResponse=function(){return this.load(this.request),this.fetching.promise},e.prototype.cancel=function(e){void 0===e&&(e=r.User),clearTimeout(this.cancelTimeout),this.cancellationReason=e,this.abortController.abort()},e.prototype.load=function(e){var t,i=this,s={method:e.method,headers:new Headers(e.headers),credentials:e.credentials,signal:this.abortController?this.abortController.signal:void 0,body:l(e.method)?e.body:void 0};this.timing.sendTimestamp=a.TimingUtil.getHiResTimestamp(),fetch(e.url,s).then((function(e){var t;if(i.timing.headersReceivedTimestamp=a.TimingUtil.getHiResTimestamp(),null===(t=i.progressListener)||void 0===t||t.call(i,n({},i.progress)),e.ok){var r=i.createHttpResponse(e);i.readResponse(e).then((function(e){r.body=e,r.length=i.bytesReceivedCount,r.timeToFirstByte=i.timing.headersReceivedTimestamp-i.timing.sendTimestamp,i.timing.doneTimestamp>0&&(r.elapsedTime=i.timing.doneTimestamp-i.timing.sendTimestamp),i.fetching.resolve(r)})).catch((function(t){i.onError({error:t.message,response:i.createHttpResponse(e)})}))}else i.onError({error:o.RequestError.Failed,response:i.createHttpResponse(e)})})).catch((function(e){i.onError({error:i.getRequestError(e)})})),this.timing.openedTimestamp=a.TimingUtil.getHiResTimestamp(),null===(t=this.progressListener)||void 0===t||t.call(this,n({},this.progress)),this.cancelTimeout=setTimeout((function(){return i.cancel(r.Timeout)}),this.timeout)},e.prototype.getResponseHeaders=function(e){var t={};return e.headers.forEach((function(e,i){return t[i]=e})),t},e.prototype.createHttpResponse=function(e){return{request:this.request,url:e.url,headers:this.getResponseHeaders(e),status:e.status,statusText:e.statusText}},e.prototype.getRequestError=function(e){return this.logger.debug("fetch error",e),e instanceof Error&&"AbortError"===e.name?this.cancellationReason===r.User?o.RequestError.Canceled:o.RequestError.TimedOut:o.RequestError.Failed},e.prototype.readResponse=function(e){var t=this;if(e.body&&this.useStreamResponse){var i=new d.Stream,n=this.timing.headersReceivedTimestamp;return this.readReadableStreamResponse(e,(function(e){var r=a.TimingUtil.getHiResTimestamp(),o=r-n;n=r,t.progress.chunks.push({downloadDuration:o,bytes:e.byteLength});var s=e instanceof Uint8Array?e.buffer:e;i.add(s)})).then((function(){i.end(),t.onSuccess()})).catch((function(e){t.logger.debug("Error reading of fetch response:",e),i.abort(t.getRequestError(e))})),Promise.resolve(i)}var r=e.headers.get("Content-Length"),o=r?parseInt(r):0;return this.progress.totalBytes=o,this.readReadableStreamResponse(e.clone()).catch((function(e){t.logger.debug("Error reading of fetch response:",e)})),this.readFullFetchResponse(e).then((function(e){return t.bytesReceivedCount=o,t.timing.doneTimestamp=a.TimingUtil.getHiResTimestamp(),t.onSuccess(),e}))},e.prototype.readReadableStreamResponse=function(e,t){var i=this;if(!e.body)return Promise.reject(new Error("response.body is null"));var n=e.body.getReader(),r=function(){return n.read().then((function(e){var n=e.done,o=e.value;return n?(i.timing.doneTimestamp=a.TimingUtil.getHiResTimestamp(),void i.onProgress(i.bytesReceivedCount)):o?(null==t||t(o),i.bytesReceivedCount+=o.byteLength,i.onProgress(i.bytesReceivedCount),r()):void 0}))};return r()},e.prototype.readFullFetchResponse=function(e){switch(this.request.responseType){case s.HttpResponseType.ARRAYBUFFER:return e.arrayBuffer();case s.HttpResponseType.TEXT:return e.text();case s.HttpResponseType.JSON:return e.json();default:return Promise.reject(new Error("Unsupported response type on fetch request"))}},e.prototype.onError=function(e){clearTimeout(this.cancelTimeout),this.logger.debug("Error performing fetch: ".concat(e.error),e.response),this.fetching.reject(e)},e}();function l(e){return![s.HttpRequestMethod.GET,s.HttpRequestMethod.HEAD].includes(e)}t.FetchController=c},27076:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.DeferredReject=void 0;var i=function(){function e(){this.rejected=!1}return e.prototype.reject=function(e){this.rejected=!0,this.rejectMessage=e},e.prototype.next=function(e){return this.rejected?Promise.reject(this.rejectMessage):e()},e}();t.DeferredReject=i},28171:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.MediaPlayerManifestApiFactory=void 0;var n=i(18665),r=i(62510),o=i(96953),a=function(){function e(){}return e.create=function(t,i){var r=i.serviceManager.get(n.ServiceName.ManifestService,i.sourceContext.sourceIdentifier);return new(function(){function i(){r.isHlsManifest()?this.hls=e.createHlsApi(r):r.isSmoothManifest()||(this.dash=e.createDashApi(t,r))}return i}())},e.createDashApi=function(t,i){return new(function(){function n(){}return n.prototype.getPeriod=function(){var n=i.getPeriodIdForTime(t.getCurrentTime()),r=i.getPeriod(new o.PeriodId(n));return r?e.createPeriodApi(i,r):null},n.prototype.listPeriods=function(){return i.getAllPeriods().map((function(t){return e.createPeriodApi(i,t)}))},n}())},e.createHlsApi=function(t){return new(function(){function i(){this.properties=t.getHlsTags()}return i.prototype.getVideoTracks=function(){return e.getVideoTracks(t,t.getFirstPeriod()._id)},i.prototype.getAudioTracks=function(){return e.getAudioTracks(t,t.getFirstPeriod()._id)},i.prototype.getTextTracks=function(){return e.getTextTracks(t,t.getFirstPeriod()._id)},i}())},e.createPeriodApi=function(t,i){return new(function(){function n(){this.id=i._id,this.properties={},i._id&&(this.properties.id=i._id),i._start&&(this.properties.start=i._start),i._duration&&(this.properties.duration=i._duration)}return n.prototype.getVideoTracks=function(){return e.getVideoTracks(t,this.id)},n.prototype.getAudioTracks=function(){return e.getAudioTracks(t,this.id)},n.prototype.getTextTracks=function(){return e.getTextTracks(t,this.id)},n.prototype.getMetadata=function(){return e.getMetadata(t,this.id)},n}())},e.getAudioTracks=function(t,i){return t.getAvailableAudio(i,!1).map((function(n){var r={id:n.id,label:n.label,lang:n.lang,getQualities:function(){return e.getAudioQualities(t,i,n.id)}};return n.role&&(r.role=n.role),r}))},e.getVideoTracks=function(t,i){return t.getAvailableVideo(i).map((function(n){var r={id:n.id,label:n.label,getQualities:function(){return e.getVideoQualities(t,i)}};return n.role&&(r.role=n.role),r}))},e.getTextTracks=function(e,t){return e.getAvailableSubtitles(t)},e.getMetadata=function(e,t){return e.getEventStreamEvents(e.findPeriod(t)).map((function(e){return{type:r.MetadataType.EVENT_STREAM,payload:e.data,start:e.startTime,end:e.endTime}}))},e.getAudioQualities=function(e,t,i){return e.getAudioRepresentations(t)[i]||[]},e.getVideoQualities=function(e,t){return e.getVideoRepresentations(t)},e}();t.MediaPlayerManifestApiFactory=a},28196:function(e,t,i){var n,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},o=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.ManifestReducer=void 0;var a=i(21829),s=i(65114),u=i(94938),d=i(77329);function c(e,t){return t.payload.manifest||e}function l(e,t){return r(r({},e),{isInitialized:t.payload.isInitialized})}function p(e,t){return 0===t.payload.representationIds.length?e:(0,u.removeRepresentations)(e,t.payload.representationIds)}function f(e,t){return t.payload.offset?r(r({},e),{Period:e.Period.map((function(e){return r(r({},e),{start:e.start+t.payload.offset})}))}):e}function h(e,t){if(!e.Period)return e;var i=t.periodId,n=t.timing,a=e.Period.findIndex((function(e){return e._id===i}));if(a<0)return e;var s=o([],e.Period,!0);return s[a]=r(r({},s[a]),n),r(r({},e),{Period:s})}function g(e,t){return r(r({},e),t)}function m(e,t){var i,n=t.adaptationSetId,a=t.updates,s=e.Period.findIndex((function(e){return e._id===n.periodId}));if(s<0)return e;var u=null===(i=e.Period[s])||void 0===i?void 0:i.AdaptationSet.findIndex((function(e){return e._internalId.equals(n)}));if(u<0)return e;var d=r({},e);d.Period=o([],d.Period,!0),d.Period[s]=r({},d.Period[s]);var c=d.Period[s];return c.AdaptationSet=o([],c.AdaptationSet,!0),c.AdaptationSet[u]=r(r({},c.AdaptationSet[u]),a),d}function v(e,t){var i,n,a,s=t,u=s._internalId;if(!e.Period)return e;var d=e.Period.findIndex((function(e){return e._id===u.periodId})),c=null===(i=e.Period[d])||void 0===i?void 0:i.AdaptationSet.findIndex((function(e){return e._internalId.equals(u)})),l=null===(a=null===(n=e.Period[d])||void 0===n?void 0:n.AdaptationSet[c])||void 0===a?void 0:a.Representation.findIndex((function(e){return e._internalId.equals(u)})),p=r({},e);p.Period=o([],p.Period,!0),p.Period[d]=r({},p.Period[d]);var f=p.Period[d];f.AdaptationSet=o([],f.AdaptationSet,!0),f.AdaptationSet[c]=r({},f.AdaptationSet[c]);var h=f.AdaptationSet[c];return h.Representation=o([],h.Representation,!0),h.Representation[l]=s,p}function S(e,t){var i=t.payload;return P(e,i.representationId,{associatedKid:i.associatedKid})}function y(e,t){var i=t.payload;return P(e,i.representationId,{isLoading:i.isLoading})}function T(e,t){var i,n=t.payload,o=n.representationId,a=n.loadFailureReason,s=function(e){var t;return e._internalId.equals(o)?a:null===(t=e._hls)||void 0===t?void 0:t.loadFailureReason};return r(r({},e),{Period:null===(i=e.Period)||void 0===i?void 0:i.map((function(e){var t;return r(r({},e),{AdaptationSet:null===(t=e.AdaptationSet)||void 0===t?void 0:t.map((function(e){var t;return r(r({},e),{Representation:null===(t=e.Representation)||void 0===t?void 0:t.map((function(e){return r(r({},e),{_hls:r(r({},e._hls),{loadFailureReason:s(e)})})}))})}))})}))})}function b(e,t){var i=t.payload;return P(e,i.representationId,{segmentIndex:i.segmentIndex})}function M(e,t){var i=t.payload;return P(e,i.representationId,{segmentIndexParsingError:i.segmentIndexParsingError})}function I(e,t){var i=t.payload;return P(e,i.representationId,{anchorPoint:i.anchorPoint})}function P(e,t,i){var n;return r(r({},e),{Period:null===(n=e.Period)||void 0===n?void 0:n.map((function(e){var n;return r(r({},e),{AdaptationSet:null===(n=e.AdaptationSet)||void 0===n?void 0:n.map((function(e){var n;return r(r({},e),{Representation:null===(n=e.Representation)||void 0===n?void 0:n.map((function(e){return e._internalId.equals(t)?r(r({},e),i):e}))})}))})}))})}function R(e,t){var i,n=t.payload,o=n.representationId,a=n.index,u=n.playbackTimeForIndex,d=(0,s.findRepresentation)(e,o);return d?P(e,o,r(r({},d),{SegmentList:null===(i=d.SegmentList)||void 0===i?void 0:i.map((function(e){return r(r({},e),{SegmentURL:E(e.SegmentURL,a,u)})}))})):e}function C(e,t){var i,n=t.payload,o=n.representationId,a=n.segmentUrl,u=n.segmentPlaybackTime,d=n.discontinuitySequenceNumber,c=(0,s.findRepresentation)(e,o);if(!(null==c?void 0:c.SegmentList)||0===c.SegmentList.length)return e;if(void 0!==(null===(i=c.SegmentList[0].SegmentURL[0])||void 0===i?void 0:i._playbackTime)||void 0===u)return e;var l=c.SegmentList[0].SegmentURL.findIndex((function(e){return e._media===a&&e._discontinuitySequenceNumber===d}));return-1===l?e:P(e,o,r(r({},c),{SegmentList:c.SegmentList.map((function(e){return r(r({},e),{SegmentURL:E(e.SegmentURL,l,u)})}))}))}function E(e,t,i){var n=o([],e,!0);return n[t]=r({},n[t]),n[t]._playbackTime=i,x(n,t),A(n,t),n}function A(e,t){for(var i=t+1;i<e.length;i++)e[i]=r({},e[i]),e[i]._playbackTime=e[i-1]._playbackTime+e[i-1]._duration}function x(e,t){for(var i=t-1;i>=0;i--)e[i]=r({},e[i]),e[i]._playbackTime=e[i+1]._playbackTime-e[i]._duration}function k(e){return r(r({},e),{SegmentURL:e.SegmentURL.map((function(e){var t=r({},e);return delete t._playbackTime,t}))})}function L(e){return r(r({},e),{Period:e.Period.map((function(e){return r(r({},e),{AdaptationSet:e.AdaptationSet.map((function(e){return r(r({},e),{Representation:e.Representation.map((function(e){var t;return r(r({},e),{SegmentList:null===(t=e.SegmentList)||void 0===t?void 0:t.map(k)})}))})}))})}))})}t.ManifestReducer=(0,a.default)({},((n={})[d.ManifestActionType.SetManifest]=c,n[d.ManifestActionType.SetManifestInitialized]=l,n[d.ManifestActionType.RemoveRepresentations]=p,n[d.ManifestActionType.UpdateManifest]=function(e,t){return g(e,t.payload)},n[d.ManifestActionType.UpdateAdaptationSet]=function(e,t){return m(e,t.payload)},n[d.ManifestActionType.UpdateRepresentation]=function(e,t){return v(e,t.payload)},n[d.ManifestActionType.UpdateRepresentationFailedDownload]=T,n[d.ManifestActionType.AdjustPeriodStartTimes]=f,n[d.ManifestActionType.UpdatePeriodTiming]=function(e,t){return h(e,t.payload)},n[d.ManifestActionType.SetRepresentationDrmKid]=S,n[d.ManifestActionType.SetRepresentationIsLoading]=y,n[d.ManifestActionType.SetRepresentationSegmentIndex]=b,n[d.ManifestActionType.SetRepresentationSegmentIndexParsingError]=M,n[d.ManifestActionType.SetRepresentationAnchorPoint]=I,n[d.ManifestActionType.InitPlaybackTimesFromIndex]=R,n[d.ManifestActionType.InitPlaybackTimesFromReferenceSegment]=C,n[d.ManifestActionType.ResetSegmentPlaybackTimes]=L,n))},28322:function(e,t,i){var n,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.RendererReducer=void 0;var o=i(21829),a=i(73448);function s(){return{mseObjectUrl:""}}function u(e,t){var i=t.payload;return r(r({},e),{mseObjectUrl:i.url})}t.RendererReducer=(0,o.default)(s(),((n={})[a.RendererActionType.SetMseObjectUrl]=u,n))},28337:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.sourceIdentifiersSelector=t.hasASourceStoreIdentifierChanged=void 0;var i=function(e,t){return e.length!==t.length||e.some((function(e){return!t.includes(e)}))};t.hasASourceStoreIdentifierChanged=i;var n=function(e){var t;return Object.keys(null!==(t=e.sources)&&void 0!==t?t:{})};t.sourceIdentifiersSelector=n},28499:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentPrefetcher=void 0;var n=i(27177),r=i(38255),o=i(66864),a=function(){function e(e){var t=this;this.processSegmentInfoQueue=function(e){var i=t.segmentInfoQueue.get(e);i&&0!==i.length&&t.chainSegmentPrefetch(i.shift()).then((function(){return t.processSegmentInfoQueue(e)}))},this.segmentInfoQueue=new r.ArrayMap,this.prefetchPromiseChainMap=new Map,this.decrypterMap=new Map,this.segmentLoaderMap=new Map,this.segmentPrefetchHandler=e,this.shouldDownloadBeCancelledCallbackMap=new Map}return e.prototype.updateContext=function(e){this.context=e},e.prototype.setShouldDownloadBeCancelledCallback=function(e,t){this.shouldDownloadBeCancelledCallbackMap.set(e,t)},e.prototype.removeShouldDownloadBeCancelledCallback=function(e){this.shouldDownloadBeCancelledCallbackMap.delete(e)},e.prototype.setDecrypter=function(e,t){this.decrypterMap.set(t,e),this.segmentLoaderMap.has(t)&&this.segmentLoaderMap.get(t).attachDecrypter(e)},e.prototype.removeDecrypter=function(e){this.decrypterMap.delete(e)},e.prototype.getDecrypter=function(e){return this.decrypterMap.get(e)},e.prototype.prefetchSegment=function(e){return this.chainSegmentPrefetch(e)},e.prototype.prefetchSegments=function(e){var t=this;e.forEach((function(e){return t.segmentInfoQueue.add(e.mimeType,e)})),this.segmentInfoQueue.keys.forEach((function(e){return t.processSegmentInfoQueue(e)}))},e.prototype.chainSegmentPrefetch=function(e){var t=this,i=this.getPrefetchPromiseChain(e.mimeType).then((function(){return t.fetchSegment(e)})).then((function(){})).catch((function(){}));return this.prefetchPromiseChainMap.set(e.mimeType,i),i},e.prototype.fetchSegment=function(e){var t=this;if(!(0,n.isContextAvailable)(this.context))return Promise.reject("PlayerContext not available");var i=this.getLoader(e).load(e).then((function(e){return e.read()})).then((function(e){return e.value}));return this.segmentPrefetchHandler.onPrefetch({segmentInfo:e,loadingPromise:i}),i.catch((function(i){(0,n.isContextAvailable)(t.context)&&t.context.logger.debug("segment prefetching failed for",e,"with",i)})),i},e.prototype.getPrefetchPromiseChain=function(e){return this.prefetchPromiseChainMap.has(e)||this.prefetchPromiseChainMap.set(e,Promise.resolve()),this.prefetchPromiseChainMap.get(e)},e.prototype.getLoader=function(e){return this.segmentLoaderMap.has(e.mimeType)||this.segmentLoaderMap.set(e.mimeType,this.createLoader(e)),this.segmentLoaderMap.get(e.mimeType)},e.prototype.createLoader=function(e){this.prefetchPromiseChainMap.set(e.mimeType,Promise.resolve());var t=new o.SegmentLoader(this.context,e.mimeType,this.createDownloadCancelCallbackFunction(e),!0);return t.attachDecrypter(this.decrypterMap.get(e.mimeType)),t},e.prototype.createDownloadCancelCallbackFunction=function(e){var t=this;return function(i){var n=e.mimeType,r=t.segmentLoaderMap.get(n),o=r?r.getCurrentLoadingSegmentInfo():null,a=void 0!==n?t.shouldDownloadBeCancelledCallbackMap.get(n):void 0,s=o?o.internalRepresentationId:e.internalRepresentationId;return void 0!==a&&a(i,s)}},e.prototype.clearPrefetchingQueue=function(e){this.segmentInfoQueue.delete(e)},e.prototype.reset=function(){this.segmentLoaderMap.forEach((function(e){return e.dispose()})),this.shouldDownloadBeCancelledCallbackMap.clear(),this.segmentLoaderMap.clear(),this.decrypterMap.clear()},e.prototype.dispose=function(){this.reset(),this.segmentLoaderMap=null,this.shouldDownloadBeCancelledCallbackMap=null},e}();t.SegmentPrefetcher=a},28915:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.isSegmentTiming=o,t.findSegmentIndexForTime=a,t.createInitSegmentInfoBase=d,t.getSegmentInfoTimeRange=c,t.generateSegmentId=l;var n=i(45711),r=i(70016);function o(e){return"startTime"in e&&(0,r.isNumber)(e.startTime)&&"duration"in e&&(0,r.isNumber)(e.duration)}function a(e,t,i){var n=!e||!e.length,r=t<e[0].startTime+e[0].duration;if(n||r)return 0;var o=e.length-1;if(t>=e[o].startTime)return o;var a=Math.max(t-e[0].startTime,0),d=Math.min(Math.floor(a/i),e.length-1);return s(e[0])&&s(e[d])?u(e,t,d):null}function s(e){return e&&(0,r.isNumber)(e.startTime)}function u(e,t,i){for(;e[i]&&e[i].startTime>t;)i--;for(;e[i]&&e[i].startTime+e[i].duration<=t;)i++;return i=Math.max(i,0),i=Math.min(i,e.length-1)}function d(e){var t=e._internalId;return{internalRepresentationId:t,representationId:t.representationId,periodId:t.periodId,mimeType:e._mimeType,isInitSegment:!0}}function c(e){if((null==e?void 0:e.duration)&&void 0!==(null==e?void 0:e.startTime))return{start:e.startTime,end:e.startTime+e.duration}}function l(e){var t,i,r=null!==(i=null===(t=e.discontinuitySequenceNumber)||void 0===t?void 0:t.toString())&&void 0!==i?i:e.periodId,o=e.byteRange?"-".concat(e.byteRange.start,":").concat(e.byteRange.end):"";return(0,n.calcHash)("".concat(r,":").concat(e.url).concat(o))}},29954:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.DrmKidErrorHandler=void 0;var n=i(18665),r=i(76650),o=i(28819),a=i(8272),s=i(3464),u=i(68294),d=function(){function e(e,t){var i=this;this.context=e,this.onDownloadedRepresentationExcludedCallback=t,this.onDrmKidsWithErrorsChanged=function(e,t){if(void 0!==e&&void 0!==t){var n=e.filter((function(e){return!t.includes(e)}));i.maybeExcludeRepresentations(n)}};var r=e.sourceContext.sourceIdentifier;this.store=e.store,this.logger=e.logger,this.sourceStore=e.serviceManager.get(n.ServiceName.SourceStoreService,r),this.manifestService=e.serviceManager.get(n.ServiceName.ManifestService,r),this.unsubscribeFromStore=(0,s.subscribe)(this.sourceStore)(u.getDrmKeyIdsWithErrors,this.onDrmKidsWithErrorsChanged)}return e.prototype.maybeExcludeRepresentations=function(e){var t=p(this.manifestService,e),i=this.store.getState();if(0!==t.length&&void 0!==i){var n=l(c(i),t);this.logger.debug("Excluding representations due to DRM licenses not being available",t),this.manifestService.excludeRepresentations(t)&&n&&(this.store.dispatch((0,r.clearMetricsHistory)("default",a.MetricType.RequestedRepresentations)),this.onDownloadedRepresentationExcludedCallback())}},e.prototype.trackSegment=function(e){var t=e.getDrmKid();if(void 0!==t){var i=this.sourceStore.getState(),n=e.getRepresentationId(),r=i?(0,u.getDrmKeyIdsWithErrors)(i):[];h(t,n,this.manifestService)&&(f(t,n,this.manifestService),this.maybeExcludeRepresentations(r))}},e.prototype.dispose=function(){this.unsubscribeFromStore()},e}();function c(e){return(0,o.getMetricsHistoryFromInstanceState)(e,"default",a.MetricType.RequestedRepresentations).map((function(e){return e.value}))}function l(e,t){return t.some((function(t){return e.some((function(e){return e.equals(t)}))}))}function p(e,t){return e.getAllRepresentations().filter((function(e){return e.associatedKid&&t.includes(e.associatedKid)})).map((function(e){return e._internalId}))}function f(e,t,i){i.setRepresentationDrmKid(t,e)}function h(e,t,i){return e!==i.getRepresentationDrmKid(t)}t.DrmKidErrorHandler=d},30855:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentUnavailabilityHandler=void 0;var n=i(62510),r=i(76885),o=function(){function e(e){this.triedRepsForCurrentSegment={},this.wasQualitySwitched=!1,this.context=e}return e.prototype.downloadSuccess=function(e){this.wasQualitySwitched&&e?this.wasQualitySwitched=!1:this.triedRepsForCurrentSegment={}},e.prototype.getNextLowerNotTriedQuality=function(e,t){for(var i=[],n=0;n<e.length&&!e[n]._internalId.equals(t);n++)this.triedRepsForCurrentSegment[e[n]._id]||i.push(e[n]);return i.length>0?i[i.length-1]._internalId:null},e.prototype.switchQuality=function(e,t){this.triedRepsForCurrentSegment[t.representationId]=!0;var i=this.getNextLowerNotTriedQuality(e,t);return i?(this.wasQualitySwitched=!0,i):(this.context.logger.log("no quality left, all tried"),null)},e.prototype.switchBaseURL=function(e){var t=e.availableBaseURLs,i=e.lastUsedBaseURLIndex+1;if((i%=t.length)===e.firstUsedBaseURLIndex)return this.context.logger.log("no base url left, all tried"),null;var o=e.baseURL;return e.lastUsedBaseURLIndex=i,e.baseURL=t[i],e.url=r.URLHelper.concatBaseUrlWithPartial(e.baseURL,e.mediaURL),this.context.eventHandler.dispatchEvent(n.PlayerEvent.ContentLocationChanged,{sourceLocationId:null!=o?o:"",targetLocationId:e.baseURL,reason:n.ContentLocationChangedReason.Failover}),e},e.prototype.shouldTryAlternatives=function(e){return e>=400&&e<=599||0===e},e}();t.SegmentUnavailabilityHandler=o},34268:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.PeriodSwitchTracker=void 0;var n=i(18665),r=i(62510),o=i(3464),a=i(331),s=i(72788),u=i(58211),d=function(){function e(e){var t=this;this.startPeriodSwitch=function(e,i){var n=t.getCurrentPlayingPeriodId(),o=e.switchingToPeriodId;n!==o&&(t.logger.debug("Starting switch from Period ".concat(n," to Period ").concat(o)),t.context.eventHandler.dispatchEvent(r.PlayerEvent.PeriodSwitch,{sourcePeriod:{periodId:n},targetPeriod:{periodId:o}}))},this.finishPeriodSwitch=function(e,i){var n=i.switchingToPeriodId,o=t.getCurrentPlayingPeriodId();if(n!==o){t.setCurrentPlayingPeriodId(n);var a=t.getCurrentPlayingPeriod();a&&a.AdaptationSet.forEach((function(e){return t.context.renderer.storeDrmInitDataFromManifest(e)})),t.logger.debug("Successfully switched from Period ".concat(o," to Period ").concat(n)),t.context.eventHandler.dispatchEvent(r.PlayerEvent.PeriodSwitched,{sourcePeriod:{periodId:o},targetPeriod:{periodId:n}})}},this.context=e,this.logger=e.logger,this.subscribeToPlayingTracksChanges()}return e.prototype.getManifestService=function(){return this.context.serviceManager.get(n.ServiceName.ManifestService,this.context.sourceContext.sourceIdentifier)},e.prototype.getCurrentPlayingPeriod=function(){return this.getManifestService().findPeriod(this.getCurrentPlayingPeriodId())},e.prototype.subscribeToPlayingTracksChanges=function(){var e=this.getSourceStore();!this.unsubscribeFromPeriodSwitchStarted&&e&&(this.unsubscribeFromPeriodSwitchStarted=(0,o.subscribe)(e)(u.getPlayingTracksState,this.startPeriodSwitch,u.isSwitchingPeriods)),!this.unsubscribeFromPlayingTracksStore&&e&&(this.unsubscribeFromPlayingTracksStore=(0,o.subscribe)(e)(u.getPlayingTracksState,this.finishPeriodSwitch,u.wasSwitchingToPeriodIdReset))},e.prototype.unsubscribeFromPlayingTracksChanges=function(){this.unsubscribeFromPlayingTracksStore&&(this.unsubscribeFromPlayingTracksStore(),this.unsubscribeFromPlayingTracksStore=void 0),this.unsubscribeFromPeriodSwitchStarted&&(this.unsubscribeFromPeriodSwitchStarted(),this.unsubscribeFromPeriodSwitchStarted=void 0)},e.prototype.getSourceStore=function(){return this.context.serviceManager.get(n.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier)},e.prototype.getCurrentLoadingPeriodId=function(){return this.currentLoadingPeriodId},e.prototype.setCurrentLoadingPeriodId=function(e){this.currentLoadingPeriodId=e},e.prototype.getCurrentPlayingPeriodId=function(){return(0,u.getPlayingPeriodId)(this.getSourceStore().getState())},e.prototype.setCurrentPlayingPeriodId=function(e){this.getSourceStore().dispatch((0,s.setPlayingPeriodId)(e))},e.prototype.resetPeriodIds=function(){this.setCurrentPlayingPeriodId(void 0),this.setCurrentLoadingPeriodId(void 0)},e.prototype.shouldHandleDroppedOutPeriod=function(){return!(null==this.getCurrentPlayingPeriodId()&&null==this.currentLoadingPeriodId)},e.prototype.isPeriodDroppedOut=function(e){return!this.getManifestService().getAllPeriods().map((function(e){return e._id})).includes(e)},e.prototype.isPlayingPeriodDroppedOut=function(){return this.isPeriodDroppedOut(this.getCurrentPlayingPeriodId())},e.prototype.isLoadingPeriodDroppedOut=function(){return this.isPeriodDroppedOut(this.currentLoadingPeriodId)},e.prototype.isLoadingLastPeriod=function(){return this.getManifestService().isLastPeriod(this.currentLoadingPeriodId)},e.prototype.setInitialPeriod=function(){var e=this.getManifestService().isLive()?this.getInitialPeriodForLive():this.getInitialPeriodForVod();this.currentLoadingPeriodId=e,this.setCurrentPlayingPeriodId(this.currentLoadingPeriodId),this.logger.debug("setting initial period to ".concat(this.currentLoadingPeriodId))},e.prototype.getInitialPeriodForLive=function(){var e=(0,a.toSeconds)(Date.now())-this.getManifestService().getDesiredDistanceToLiveEdge();return this.getManifestService().getPeriodIdForTime(e)},e.prototype.getInitialPeriodForVod=function(){var e=this.context.serviceManager.get(n.ServiceName.StartOffsetService).getStartOffset(this.context.sourceContext.source);return this.getManifestService().getPeriodIdForTime(e)},e.prototype.dispose=function(){this.unsubscribeFromPlayingTracksChanges()},e}();t.PeriodSwitchTracker=d},35655:function(e,t,i){var n=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.RepresentationUpdater=void 0;var r=i(91520),o=i(16368),a=i(93109),s=i(99162),u=function(e){function t(t,i,n,r){var o=e.call(this,t,r)||this;return o.context=t,o.representation=i,o.manifestLoader=n,o}return n(t,e),t.prototype.getRepresentation=function(){return this.representation},t.prototype.setRepresentation=function(e){this.representation._internalId.equals(e._internalId)||this.cancel(),this.representation=e,this.scheduleUpdate(!0)},t.prototype.updateRepresentation=function(){return this.getPayload()},t.prototype.stopRepresentationUpdate=function(t){return e.prototype.stop.call(this),this.manifestLoader.stopRepresentationUpdate(t)},t.prototype.update=function(){var e=this;return this.manifestLoader.updateRepresentation(this.representation).then((function(t){if(e.representation._id!==t._id)throw s.REPRESENTATION_UPDATE_CANCEL;return e.representation=t,t}))},t.prototype.getReloadIntervalInSeconds=function(){var e,t;return this.representation._requestTimestamp?(0,a.isPlayingLowLatencyHls)(this.context)&&(null===(e=r.ModuleManager.get(o.ModuleName.HLS))||void 0===e?void 0:e.canMakeBlockingPlaylistReload(this.representation,this.context.settings))?0:null!==(t=this.representation._updateInterval)&&void 0!==t?t:1/0:0},t.prototype.getLastReloadTimestamp=function(){return this.representation._requestTimestamp?this.representation._requestTimestamp:0},t}(s.AbstractUpdater);t.RepresentationUpdater=u},36225:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.addEncryptionKeyToStore=c,t.waitForKeyToBeLoaded=l;var n=i(3464),r=i(56093),o=i(55486),a=i(25040),s=i(56092),u=20;function d(e){var t=(0,a.getAllEncryptionKeys)(e.getState());t&&t.length>u&&e.dispatch((0,o.removeEncryptionKey)(t[0].uri))}function c(e,t,i){return i.dispatch((0,o.addEncryptionKey)({uri:e,key:void 0,loadState:s.KeyLoadState.Loading})),t.load(e).then((function(t){return null==i||i.dispatch((0,o.updateEncryptionKey)({uri:e,key:r.FormatHelper.bytesToHex(t),loadState:s.KeyLoadState.Loaded})),d(i),t})).catch((function(t){return null==i||i.dispatch((0,o.updateEncryptionKey)({uri:e,key:void 0,loadState:s.KeyLoadState.Cancel})),Promise.reject(t)}))}function l(e,t){return new Promise((function(i,o){var u=function(e){return e.loadState===s.KeyLoadState.Cancel?(d(),o(new Error("Encryption key load cancelled!"))):e.loadState===s.KeyLoadState.Loaded&&e.key?(d(),i(r.FormatHelper.hexToBytes(e.key).buffer)):void 0},d=(0,n.subscribe)(e)((function(e){return(0,a.getEncryptionKey)(e,t)}),u)}))}},36282:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.getMinSafeStartupBuffer=o;var n=i(79814),r=i(56435);function o(e,t){if((0,r.isTizen2016)()){var i=t.map((function(e){return n.MimeTypeHelper.getMediaType(e)})).map((function(t){return e.bufferSettings.getForwardTargetLevel(t)}));return Math.max.apply(Math,i)}return(0,r.isWebOs2020OrOlder)()||(0,r.isLegacyEdge)()?6:(0,r.isTizen2017)()||(0,r.isTizen2018)()||(0,r.isTizen2019)()?4:0}},36366:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.MediaPlayerController=void 0,t.isBufferChangeRequired=G,t.getDefaultMaxStartupBuffer=Y;var n=i(18665),r=i(60997),o=i(62510),a=i(15539),s=i(33696),u=i(76650),d=i(17990),c=i(58975),l=i(63668),p=i(27279),f=i(42055),h=i(3464),g=i(42283),m=i(3941),v=i(79705),S=i(53968),y=i(79814),T=i(56435),b=i(46462),M=i(91520),I=i(16368),P=i(80858),R=i(29954),C=i(13034),E=i(94938),A=i(96953),x=i(22916),k=i(58423),L=i(34268),_=i(25996),B=i(4053),D=i(77874),w=i(72788),O=i(58211),N=i(68294),F=i(59839),H=i(22645),U=i(21918),j=i(75498),q=i(93109),V=i(36282),K=function(){function e(e,t){var i=this;this.segmentControllerMap={},this.onSegmentAvailable=function(e){if(i.bufferController.isBufferClearingOngoing())W(e,i.getSourceStore());else{if(i.drmKidErrorHandler.trackSegment(e),i.shouldDropSegment(e))return i.context.logger.debug("Dropping segment ".concat(e.getUrl())),void W(e,i.getSourceStore());var t=e.getMimeType(),n=i.segmentControllerMap[t];n&&i.isAVMultiplexed(n)&&i.addRequiredMediaTypesForMuxedContent(e.getPeriodId()),i.bufferController.addSegment(e).then((function(){var e;null===(e=i.heartbeatService)||void 0===e||e.beat()})).catch((function(e){i.logger&&i.logger.debug(e)})),i.segmentAvailableCallback(e)}},this.endStallAtGap=function(){var e=i.bufferController.getBufferedRangesMap(),t=Math.max.apply(Math,Object.values(e).filter((function(e){return e.length>0})).map((function(e){return e[e.length-1].start})));i.seek(t,!1,!0)},this.mpdAvailableHandler=function(){var e;i.applyManifestTimings(),i.initialized&&i.updatePeriodInformation(),i.handleDroppedOutPeriods(),i.initializeThumbnails(),i.initialized&&(i.getAllSegmentControllers().forEach((function(e){return e.updateMpd()})),null===(e=i.timeshiftHandler)||void 0===e||e.onManifestUpdate()),i.context.serviceManager.get(n.ServiceName.TimedMetadataService).parseEventStream()},this.onPeriodSwitched=function(){(i.hasStreamEnded()||i.isInitialized()&&!i.playerStateService.isPlaying())&&i.start()},this.onSegmentRequestFinished=function(e){var t,n=408;if(e){if(e.success||e.httpStatus===n){var r=i.segmentControllerMap[e.mimeType];r&&i.updateAdaptationLogicData(e,r)}e.success||null===(t=i.heartbeatService)||void 0===t||t.beat()}},this.firePlaybackFinishedEvent=function(){i.getAllSegmentControllers().every((function(e){var t=i.streamTimeService.getTimeForNextSegment(e.getMimeType());return e.hasNext(t)}))?i.logger.debug("ignoring ended event in firePlaybackFinishedEvent as there are more segments available"):i.manifestService.isLastPeriod(i.periodSwitchTracker.getCurrentPlayingPeriodId())?i.bufferController.hasFutureBufferBlockData()||(i.stop(),i.logger.debug("Stopping buffer controller"),i.bufferController.stop(!0),i.playerStateService.transitionToStoppedState()):i.logger.debug("ignoring ended event in firePlaybackFinishedEvent as there is a future period")},this.context=e,this.logger=e.logger,this.settings=e.settings,this.bufferSettings=e.bufferSettings,this.eventHandler=e.eventHandler,this.renderer=e.renderer,this.segmentAvailableCallback=t,this.initialized=!1,this.initPromise=null,this.mimeTypes={},this.seekCounter=0,this.eosSignaled=!1,this.isSwitchingAudioTrack=!1,this.configEnhancer=new _.PlayerConfigEnhancer(e)}return e.prototype.addRequiredMediaTypesForMuxedContent=function(e){this.bufferController.addRequiredMediaType(e,"video/mp4"),this.bufferController.addRequiredMediaType(e,"audio/mp4")},e.prototype.restorePlaybackPositionAfterDownloadedRepresentationWasExcluded=function(){var e=this,t=this.manifestService.isLive(),i=t?this.getTimeShift():this.renderer.getCurrentTime();t?this.timeShift(i,(function(){}),!0).catch((function(t){return e.context.logger.debug("TimeShift failed",t)})):this.seek(i,!1,!1,!0)},e.prototype.onDownloadedRepresentationExcluded=function(){this.restorePlaybackPositionAfterDownloadedRepresentationWasExcluded(),this.context.logger.debug("manifest update interrupted by DRM key status change")},e.prototype.shouldDropSegment=function(e){var t=y.MimeTypeHelper.isSubtitle(e.getMimeType()),i=this.context.sourceContext.technology.streaming===s.StreamType.Hls,n=(0,f.getStartTimeOffset)(this.getSourceStore().getState())+this.manifestService.getDuration(),r=this.isVod()&&e.getPlaybackTime()>=n,o=this.eosSignaled||r,a=this.manifestService.isRepresentationExcluded(e.getRepresentationId());return!t&&!i&&o||a},e.prototype.getSourceStore=function(){return this.context.serviceManager.get(n.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier)},e.prototype.isAVMultiplexed=function(e){var t=g.CodecStringHelper.getExtractedCodecStrings(e.getCodecs());return Boolean(t.video&&t.audio)},e.prototype.getMaxTimeShift=function(){return this.getTimeShiftHandler().getMaxTimeShift()},e.prototype.removeEventHandlers=function(){this.eventHandler.off(o.PlayerEvent.SegmentRequestFinished,this.onSegmentRequestFinished)},e.prototype.subscribeToSeekProcess=function(){var e=this,t=this.context.store;!this.unsubscribeFromSeekProcess&&t&&(this.unsubscribeFromSeekProcess=(0,h.subscribe)(t)((function(e){if(e)return(0,c.getPlayerState)(e).seekingProcess}),(function(t,i){return t&&i&&e.onSeekProcessChange(t,i)})))},e.prototype.subscribeToManifestChanges=function(){var e=this.getSourceStore();!this.unsubscribeFromManifestStore&&e&&(this.unsubscribeFromManifestStore=(0,h.subscribe)(e)(D.getManifest,this.mpdAvailableHandler,(function(e,t){return e.isInitialized&&!t.isInitialized})))},e.prototype.unsubscribeFromManifestChanges=function(){this.unsubscribeFromManifestStore&&(this.unsubscribeFromManifestStore(),this.unsubscribeFromManifestStore=null)},e.prototype.subscribeToPlayingTracksChanges=function(){var e=this.getSourceStore();!this.unsubscribeFromPlayingTracksStore&&e&&(this.unsubscribeFromPlayingTracksStore=(0,h.subscribe)(e)(O.getPlayingTracksState,this.onPeriodSwitched,O.wasSwitchingToPeriodIdReset))},e.prototype.unsubscribeFromPlayingTracksChanges=function(){this.unsubscribeFromPlayingTracksStore&&(this.unsubscribeFromPlayingTracksStore(),this.unsubscribeFromPlayingTracksStore=void 0)},e.prototype.setupControllerForAdaptationSet=function(e){var t,i,n=(0,E.getMimeTypeForAdaptationSet)(e);if(y.MimeTypeHelper.isAV(n)){this.context.store.dispatch((0,u.initializeMetricsForMimeType)(n,this.context.settings)),this.getSourceStore().dispatch((0,B.setMediaTypeAction)(e._internalId,(0,x.resolveMediaTypes)(e))),this.mimeTypes[n]=(0,E.getCodecsFromAdaptationSet)(e);var r=this.periodSwitchTracker.getCurrentPlayingPeriodId();this.segmentControllerMap[n]=new k.SegmentController(this.context,this.onSegmentAvailable,n,this.mimeTypes[n],e.isTransmuxingRequired,this.manifestLoader,null!==(t=e._periodId)&&void 0!==t?t:r,this.getSourceStore()),null===(i=this.timeshiftHandler)||void 0===i||i.updateSegmentControllers(this.segmentControllerMap),this.transmuxer&&this.segmentControllerMap[n].setTransmuxer(this.transmuxer),r&&this.renderer.storeDrmInitDataFromManifest(e),this.context.segmentInfoService&&this.context.segmentInfoService.setSegmentControllers(Object.values(this.segmentControllerMap))}},e.prototype.updatePeriodForTimeshift=function(e,t,i){var n,r=this.periodSwitchTracker.getCurrentPlayingPeriodId();r||this.periodSwitchTracker.setCurrentPlayingPeriodId(e),this.periodSwitchTracker.getCurrentLoadingPeriodId()!==e&&i&&this.switchLoadingPeriodForAllMedia(e),t&&r!==e&&(this.context.logger.debug("Updating current playing period to ".concat(e)),this.periodSwitchTracker.setCurrentPlayingPeriodId(e)),r!==e&&(null===(n=(0,p.getSourceStore)(this.context))||void 0===n||n.dispatch((0,w.periodSwitchStarted)(e)))},e.prototype.updateVideoAndAudioMediaTypes=function(e){var t=e.AdaptationSet.find((function(e){return y.MimeTypeHelper.isVideo(e._mimeType)}));t&&this.bufferController.addRequiredMediaType(e._id,t._mimeType);var i=e.AdaptationSet.find((function(e){return y.MimeTypeHelper.isAudio(e._mimeType)}));i&&this.bufferController.addRequiredMediaType(e._id,i._mimeType)},e.prototype.updateSubtitleMediaTypes=function(e){var t=this;e.AdaptationSet.filter((function(e){return y.MimeTypeHelper.isSubtitle(e._mimeType)})).forEach((function(i){t.bufferController.addRequiredMediaType(e._id,i._mimeType)}))},e.prototype.updateMediaTypes=function(e){this.updateVideoAndAudioMediaTypes(e),this.updateSubtitleMediaTypes(e)},e.prototype.updatePeriodInformation=function(){var e=this;this.manifestService.getAllPeriods().forEach((function(t){e.updateMediaTypes(t),e.bufferController.checkIfEndOfBufferReached(e.settings.END_OF_BUFFER_TOLERANCE)}))},e.prototype.finishInit=function(){var e=this;return this.initPromise?this.initPromise:!0===this.initialized||null===this.manifestService.getManifest()?this.initPromise=Promise.resolve():(this.disposeSegmentControllers(),this.mimeTypes={},this.initPromise=this.renderer.ready().then((function(){return e.initializeDependents()})))},e.prototype.disposeSegmentControllers=function(){var e=this;this.segmentControllerMap&&(Object.keys(this.segmentControllerMap).forEach((function(t){return e.segmentControllerMap[t].dispose()})),this.segmentControllerMap={})},e.prototype.createTransmuxer=function(){var e=this;if(M.ModuleManager.has(I.ModuleName.ContainerTS)){var t=M.ModuleManager.get(I.ModuleName.ContainerTS);this.getAllSegmentControllers().filter((function(e){return e.isTransmuxerRequired()})).forEach((function(i){e.transmuxer=e.transmuxer||new t.WebWorkerTransmuxer(e.context,e.hasSeparateAvPlaylists()),i.setTransmuxer(e.transmuxer)})),this.subtitleService&&this.subtitleService.setTransmuxer(this.transmuxer)}},e.prototype.hasSeparateAvPlaylists=function(){return this.getAllSegmentControllers().filter((function(e){return y.MimeTypeHelper.isAV(e.getMimeType())})).length>1},e.prototype.initializeDependents=function(){this.periodSwitchTracker.setInitialPeriod(),this.initializeBufferController(),this.updatePeriodInformation();var e=this.periodSwitchTracker.getCurrentPlayingPeriod()||this.manifestService.getFirstPeriod();e&&this.initializeSegmentControllers(e),this.enableForcedSubtitles(),this.createTransmuxer(),this.initialized=!0},e.prototype.enableForcedSubtitles=function(){var e,t,i,n,r=this.getAudioSegmentController();if(this.subtitleService){var o=null!==(i=null===(t=null===(e=null==r?void 0:r.getCurrentLangObj)||void 0===e?void 0:e.call(r))||void 0===t?void 0:t.lang)&&void 0!==i?i:null===(n=(0,C.getPreferredLanguage)("audio/mp4"))||void 0===n?void 0:n.language;this.subtitleService.enableForcedSubtitle(o)}},e.prototype.initializeSegmentControllers=function(e){var t,i=this;this.maybeConsiderOnlyMuxedAVSet(null!==(t=null==e?void 0:e.AdaptationSet)&&void 0!==t?t:[]).forEach((function(e){return i.createSegmentControllerIfMediaTypeIsMissing(e)}))},e.prototype.maybeConsiderOnlyMuxedAVSet=function(e){var t,i,n,r=e.find((function(e){return y.MimeTypeHelper.isAudio(e._mimeType)})),o=e.find((function(e){return y.MimeTypeHelper.isVideo(e._mimeType)})),a=(null!==(i=null===(t=null==o?void 0:o.ContentComponent)||void 0===t?void 0:t.length)&&void 0!==i?i:0)>1;if(!r||!o||!a)return e;var s=(0,C.getHlsDefaultLanguage)(r._mimeType,this.getSourceStore().getState()),u=null===(n=o.ContentComponent)||void 0===n?void 0:n.find((function(e){return e._lang===(null==s?void 0:s.language)&&e._id===(null==s?void 0:s.name)}));return s&&u?[o]:e},e.prototype.createSegmentControllerIfMediaTypeIsMissing=function(e){var t=(0,E.getMimeTypeForAdaptationSet)(e);(0,y.isMediaTypeContained)(this.mimeTypes,t)||this.setupControllerForAdaptationSet(e)},e.prototype.initializeBufferController=function(){this.bufferController=new P.BufferController(this.context,this.endStallAtGap,this.manifestService.getTotalDuration(),this.periodSwitchTracker.getCurrentPlayingPeriodId())},e.prototype.getBufferLevel=function(e,t,i,n){return void 0===n&&(n=!0),this.bufferController?this.bufferController.getOverallBufferLevel(e,t,i,n):0},e.prototype.handleDroppedOutPeriods=function(){var e=this;this.periodSwitchTracker.shouldHandleDroppedOutPeriod()&&(this.periodSwitchTracker.isPlayingPeriodDroppedOut()?(this.logger.debug("Playing period dropped out of the manifest, timeshifting to current position"),this.timeShift(this.getTimeShift()).catch((function(t){return e.context.logger.debug("TimeShift failed",t)}))):this.periodSwitchTracker.isLoadingPeriodDroppedOut()&&(this.logger.debug("Current loading period (".concat(this.periodSwitchTracker.getCurrentLoadingPeriodId(),") ")+"dropped out of the manifest, switching to first available one"),this.getAllSegmentControllers().forEach((function(e){return e.invalidateOngoingRequests()})),this.switchLoadingPeriodForAllMedia(this.manifestService.getFirstPeriod()._id)))},e.prototype.initializeThumbnails=function(){var e=this;this.context.serviceManager.maybeCall(n.ServiceName.ThumbnailService,(function(t){e.context.sourceContext.source&&e.context.sourceContext.source.thumbnailTrack||t.addThumbnails(e.manifestService.getAllDashThumbnailSources())}))},e.prototype.applyManifestTimings=function(){var e=this.manifestService.isLive(),t=this.context.serviceManager.get(n.ServiceName.StartOffsetService),i=this.manifestService.hasSuggestedStartPosition();if(e){var r=this.manifestService.getTimeShiftBufferDepthSeconds();0!==r&&(this.metadataParsedService.expirationTimeInSeconds=r),i&&(t.manifestStartOffset=this.manifestService.getLiveStartOffset(),this.maybeAdjustDesiredLiveEdgeDistance())}else i&&(t.manifestStartOffset=this.manifestService.getVodStartOffset())},e.prototype.maybeAdjustDesiredLiveEdgeDistance=function(){if(!this.isInitialized()){var e=Math.abs(this.manifestService.getLiveStartOffset(!1));e=e>0?e:this.manifestService.getMaxSegmentDuration(),this.context.bufferSettings.setDistanceToLiveEdge(e)}},e.prototype.terminate=function(){var e,t,i=this;this.logger.debug("stopping playback"),this.stop(),this.getAllSegmentControllers().forEach((function(e){return e.stop()})),this.bufferController&&this.bufferController.stop(!0),this.transmuxer=(0,r.dispose)(this.transmuxer),this.timeshiftHandler=(0,r.dispose)(this.timeshiftHandler),this.drmKidErrorHandler=(0,r.dispose)(this.drmKidErrorHandler),(0,r.dispose)(this.periodSwitchTracker),this.cachedSeekableRange=null,this.renderer.off(b.MediaElementEvent.ended,this.firePlaybackFinishedEvent);var n=this.renderer.shutdown(!1).catch((function(e){return i.logger.debug("Got an error on shutdown",{message:e.message}),Promise.resolve()}));return this.removeEventHandlers(),this.unsubscribeFromPlayingTracksChanges(),this.initialized=!1,null===(e=this.periodSwitchTracker)||void 0===e||e.setCurrentPlayingPeriodId(void 0),this.mimeTypes=null,this.context.store.dispatch((0,l.resetDrmKidErrors)()),null===(t=this.manifestService)||void 0===t||t.resetExcludedRepresentations(),this.disposeSegmentControllers(),n},e.prototype.dispose=function(){this.timeshiftHandler=(0,r.dispose)(this.timeshiftHandler),this.bufferController=(0,r.dispose)(this.bufferController),(0,r.dispose)(this.periodSwitchTracker);try{this.removeEventHandlers()}catch(e){}this.unsubscribeFromManifestChanges(),this.unsubscribeFromPlayingTracksChanges(),this.disposeSegmentControllers(),this.unsubscribeFromSeekProcess&&this.unsubscribeFromSeekProcess(),this.subtitleService=null,this.heartbeatService=null,this.streamTimeService=null,this.segmentControllerMap=null,this.settings=null,this.bufferSettings=null,this.eventHandler=null,this.renderer=null,this.mimeTypes=null},e.prototype.acquireServices=function(){var e=this.context.sourceContext.sourceIdentifier;this.manifestService=this.context.serviceManager.get(n.ServiceName.ManifestService,e),this.playerStateService=this.context.serviceManager.get(n.ServiceName.PlayerStateService),this.subtitleService=this.context.serviceManager.get(n.ServiceName.SubtitleService,e),this.metadataParsedService=this.context.serviceManager.get(n.ServiceName.MetadataParsedService),this.manifestLoader=this.context.serviceManager.get(n.ServiceName.ManifestLoadingService,e),this.heartbeatService=this.context.serviceManager.get(n.ServiceName.HeartbeatService,e),this.streamTimeService=this.context.serviceManager.get(n.ServiceName.StreamTimeService),this.manifestUpdateScheduler=this.context.serviceManager.get(n.ServiceName.ManifestUpdateSchedulingService,e)},e.prototype.init=function(e){var t=this,i=this.getSourceStore();return this.periodSwitchTracker=new L.PeriodSwitchTracker(this.context),this.subscribeToPlayingTracksChanges(),this.subscribeToSeekProcess(),this.isAudioCatchingUp=!1,this.context.store.dispatch((0,l.resetDrmKidErrors)()),this.acquireServices(),i&&!this.drmKidErrorHandler&&(this.drmKidErrorHandler=new R.DrmKidErrorHandler(this.context,(function(){return t.onDownloadedRepresentationExcluded()}))),this.renderer.on(b.MediaElementEvent.ended,this.firePlaybackFinishedEvent),(this.manifestUpdateScheduler.isInitiated()?Promise.resolve():this.manifestUpdateScheduler.init(e.url.toString())).then((function(){return t.subscribeToManifestChanges(),t.mpdAvailableHandler(),t.configEnhancer.adjustToCurrentSource(t.context,t.manifestService),t.initialized?Promise.resolve():t.finishInit()}))},e.prototype.stop=function(){var e;this.logger.debug("Stopping main timer"),null===(e=this.heartbeatService)||void 0===e||e.stop(),this.initPromise=null,this.eventHandler.off(o.PlayerEvent.SegmentRequestFinished,this.onSegmentRequestFinished),this.unsubscribeFromManifestChanges()},e.prototype.isOneLoaderReady=function(){return this.getAllSegmentControllers().some((function(e){return e.canLoad()}))},e.prototype.areAllLoadersReady=function(){return this.getAllSegmentControllers().every((function(e){return e.canLoad()}))},e.prototype.isVod=function(){return!this.manifestService.isLive()},e.prototype.shouldStopDownloadWhenPaused=function(){return this.playerStateService.isPaused()&&this.settings.STOP_DOWNLOAD_ON_PAUSE&&this.manifestService.isLive()},e.prototype.shouldWaitForOtherMimeTypeDownload=function(e){var t=Object.keys(this.segmentControllerMap).find((function(t){return y.MimeTypeHelper.isAV(t)&&e!==t}));if(!t)return!1;if((0,q.isPlayingLowLatencyHls)(this.context))return!1;if(this.isAudioCatchingUp)return y.MimeTypeHelper.isAudio(t);var i=this.segmentControllerMap[t],n=this.streamTimeService.getTimeForNextSegment(t);if(i.hasNext(n)&&!i.hasDownloadError&&!i.hasDelayedSegmentsInTransmuxer()){var r=this.getBufferRelevantTime(),o=this.getCurrentForwardBuffer(e,r),a=this.getCurrentForwardBuffer(t,r);return this.settings.DISABLE_PARALLEL_SEGMENT_LOADING&&o===a?y.MimeTypeHelper.isVideo(t):!this.hasReachedTargetBufferLevel(t,a)&&n<this.streamTimeService.getTimeForNextSegment(e)}return i.getCurrentPeriodId()!==this.segmentControllerMap[e].getCurrentPeriodId()},e.prototype.hasReachedTargetBufferLevel=function(e,t){var i=y.MimeTypeHelper.getMediaType(e);return t>=this.bufferSettings.getForwardTargetLevel(i)},e.prototype.shouldEndOfStreamBeSignaled=function(){var e=this,t=this.getAllSegmentControllers(),i=this.bufferController.hasPendingSegments(),n=t.map((function(e){return e.getMimeType()})),r=t.some((function(e){return e.hasPendingSegments()})),o=n.some((function(t){return e.renderer.isDataBeingAppended(t)}));return!r&&!i&&!o},e.prototype.shouldSuspendHeartbeat=function(){return!this.isOneLoaderReady()||this.shouldStopDownloadWhenPaused()||this.bufferController.isBufferClearingOngoing()||this.isSwitchingAudioTrack},e.prototype.getActiveSegmentControllers=function(){var e=this;return this.getAllSegmentControllers().filter((function(t){return e.isSegmentControllerActive(t)}))},e.prototype.isSegmentControllerActive=function(e){var t=this.periodSwitchTracker.getCurrentLoadingPeriodId(),i=this.streamTimeService.getTimeForNextSegment(e.getMimeType());return e.canLoad()&&(e.hasNext(i)||e.hasPendingSegments(t))&&!this.shouldWaitForOtherMimeTypeDownload(e.getMimeType())},e.prototype.shouldClearSubtitleServiceBuffers=function(){var e;return!this.playerStateService.seekingOrTimeshifting&&Boolean(null===(e=this.subtitleService)||void 0===e?void 0:e.hasSubtitles())},e.prototype.clearSubtitleServiceBuffers=function(){var e,t=10;null===(e=this.subtitleService)||void 0===e||e.clearBuffersUntil(this.renderer.getCurrentTime()-t)},e.prototype.areMediaTypesFinalForPlayingPeriod=function(){var e=this.periodSwitchTracker.getCurrentPlayingPeriodId();return this.bufferController.areBufferBlockMediaTypesFinalForPeriod(e)},e.prototype.maybeStop=function(){var e=this.manifestService.isManifestFinalized(),t=this.manifestService.isLastPeriod(this.periodSwitchTracker.getCurrentLoadingPeriodId());e&&t&&(this.bufferController.stop(!1),this.shouldEndOfStreamBeSignaled()&&(this.signalEndOfStream(),this.stop()))},e.prototype.shouldWaitForStartup=function(){var e=this;if(!(this.playerStateService.isPlay()&&!this.playerStateService.hasBeenPlaying))return!1;var t=0!==this.context.settings.MAX_STARTUP_BUFFER?this.context.settings.MAX_STARTUP_BUFFER:Y();if(t<=0||t===1/0)return!1;if(this.bufferController.isSegmentStoreWaitingForSegments())return!1;var i=Object.keys(this.mimeTypes),n=this.getBufferRelevantTime(),r=i.map((function(t){return e.getCurrentForwardBuffer(t,n)})),o=Math.min.apply(Math,r),a=this.getSourceStore(),s=(0,N.getDrmState)(null==a?void 0:a.getState()),u=o>=(t=Math.max(t,this.context.settings.STARTUP_THRESHOLD,(0,v.getBufferEndSafetyMargin)(s),(0,V.getMinSafeStartupBuffer)(this.context,i)));return u&&this.logger.debug("Max startup buffer of ".concat(t," reached, delaying segment loading until playback has started"),{currentTime:n,maxStartupBuffer:t,bufferLevels:r}),u},e.prototype.getNextSegment=function(e,t){if(e.canLoad()&&this.renderer.shouldContinueBuffering()&&!this.shouldWaitForStartup()){var i=y.MimeTypeHelper.getMediaType(e.getMimeType()),r=this.bufferSettings.getForwardTargetLevel(i),o=this.getCurrentForwardBuffer(e.getMimeType(),this.getBufferRelevantTime());o>=r||(this.context.serviceManager.get(n.ServiceName.AdaptationService).addSample(e.getMimeType(),{bufferTargetLevel:r,bufferLevel:o}),e.hasNext(t)&&e.getNext(t))}},e.prototype.getBufferRelevantTime=function(){return this.playerStateService.seekingOrTimeshifting?this.playerStateService.targetPlaybackTime:this.renderer.getCurrentTime()},e.prototype.getCurrentForwardBuffer=function(e,t){return this.getBufferLevel(e,t,s.BufferType.ForwardDuration)},e.prototype.isLoadingLastPeriod=function(){return this.periodSwitchTracker.isLoadingLastPeriod()&&!this.isAudioCatchingUp},e.prototype.switchLoadingPeriod=function(){if(this.isAudioCatchingUp){var e=this.getAudioSegmentController(),t=e.getCurrentPeriodId(),i=this.manifestService.getNextPeriod(t)._id;this.switchLoadingPeriodForAudio(i),e.getCurrentPeriodId()===this.periodSwitchTracker.getCurrentLoadingPeriodId()&&(e.isTransmuxerRequired()&&e.getTransmuxer()&&(e.getTransmuxer().dispose(),e.setTransmuxer(this.transmuxer)),this.isAudioCatchingUp=!1,this.logger.debug("The audio SegmentController caught up to the overall loading period again."))}else{var n=this.manifestService.getNextPeriod(this.periodSwitchTracker.getCurrentLoadingPeriodId());n&&this.switchLoadingPeriodForAllMedia(n._id)}},e.prototype.switchLoadingPeriodForAllMedia=function(e){var t=this;e!==this.periodSwitchTracker.getCurrentLoadingPeriodId()&&(this.isAudioCatchingUp=!1,this.prepareLoadingPeriodSwitch(e),this.getAllSegmentControllers().filter((function(t){return t.getCurrentPeriodId()!==e})).forEach((function(i){i.switchPeriod(e),t.renderer.storeDrmInitDataFromManifest(i.getAdaptationSetForPeriodId(e))})),this.periodSwitchTracker.setCurrentLoadingPeriodId(e))},e.prototype.prepareLoadingPeriodSwitch=function(e){var t,i,n=this;(null!==(i=null===(t=this.manifestService.getPeriod(new A.PeriodId(e)))||void 0===t?void 0:t.AdaptationSet)&&void 0!==i?i:[]).forEach((function(e){return n.createSegmentControllerIfMediaTypeIsMissing(e)})),this.resetTransmuxer()},e.prototype.switchLoadingPeriodForAudio=function(e){var t=this.getAudioSegmentController(),i=t.getCurrentPeriodId();t.resetTransmuxer(),t.switchPeriod(e),this.logger.debug("Switched audio loading period from ".concat(i," to ").concat(e))},e.prototype.isAVCompletelyLoaded=function(){var e=this.getAllSegmentControllers().some((function(e){return e.hasPendingSegments()}))||this.bufferController.hasPendingSegments();return this.manifestService.isManifestFinalized()&&!e},e.prototype.getBufferedRanges=function(e){return void 0===e&&(e=!1),this.bufferController.getBufferedRangesMap(e)},e.prototype.resetTransmuxer=function(){this.transmuxer&&(this.transmuxer.dispose(),this.transmuxer=null),this.createTransmuxer()},e.prototype.restartBufferController=function(){this.bufferController.hasStopped()&&this.bufferController.restart()},e.prototype.shouldBuffersBeCleared=function(e,t,i,n){if(i&&0===e)return!1;var r=e<this.renderer.getCurrentTime()&&this.context.settings.CLEAR_BUFFERS_ON_SEEKING_BACKWARDS;return!t&&(!this.bufferController.isInBufferedRange(e)||r)||n},e.prototype.onSeekProcessChange=function(e,t){var i;if(-1===e.targetTime&&t.targetTime>=0)null===(i=this.playerStateService)||void 0===i||i.transitionToSeekedState(t.issuer!==a.STARTUP_ISSUER_NAME);else{var n=e.targetTime!==t.targetTime,r=e.targetTime>=0;n&&r&&this.seek(e.targetTime,e.issuer===a.STARTUP_ISSUER_NAME)}},e.prototype.seek=function(e,t,i,n){var r,o=this;if(void 0===t&&(t=!1),void 0===i&&(i=!1),void 0===n&&(n=!1),this.bufferController){this.restartBufferController(),this.seekCounter++;var a=this.seekCounter,s=null!==(r=this.manifestService.getPeriodIdForTime(e))&&void 0!==r?r:m.DEFAULT_PERIOD_ID;this.ensureInitialPeriod(s);var u,c,l=this.periodSwitchTracker.getCurrentPlayingPeriodId();this.logger.debug("seek from current period (".concat(l,") to target period ")+"(".concat(s,") - seek time in period is: ").concat(e)),this.bufferController.readyToSeek().then((function(){var r;if(c=o.shouldBuffersBeCleared(e,i,t,n),u=o.periodSwitchTracker.getCurrentLoadingPeriodId()!==s&&c,l!==s&&(null===(r=(0,p.getSourceStore)(o.context))||void 0===r||r.dispatch((0,w.periodSwitchStarted)(s))),c&&o.bufferController.isBufferAvailable())return o.clearBuffersOnSeek()})).then((function(){return o.initialized?(u&&o.switchLoadingPeriodForAllMedia(s),c&&o.getAllSegmentControllers().forEach((function(t){t.setAdaptionLogicStartupPhase(),t.seekTo(e)})),(t?Promise.resolve():o.start()).then((function(){o.hasStarted()&&o.heartbeatService.beat(),o.bufferController.setCurrentTime(e).then((function(t){var i;o.context.isDisposed||(o.seekCounter===a?(o.logger.debug("Successfully seeked to ".concat(t)),null===(i=(0,p.getSourceStore)(o.context))||void 0===i||i.dispatch((0,w.periodSwitchFinished)(s)),o.context.store.dispatch((0,d.updateSeekingProcess)({targetTime:-1,issuer:"",isInitial:!1,seekableRange:{start:-1,end:-1}}))):Q(o.context.store)&&o.context.logger.debug("Seek to ".concat(e," has finished but a newer operation has been started")))})).catch((function(e){o.logger.debug("Failed to set currentTime on seek",e)}))}))):(o.logger.debug("Aborting seek as MediaPlayerController has been terminated!"),Promise.resolve())}))}},e.prototype.ensureInitialPeriod=function(e){var t=this;void 0===this.periodSwitchTracker.getCurrentPlayingPeriodId()&&(this.logger.debug("current period is undefined, using seek target period "+e),this.periodSwitchTracker.setCurrentPlayingPeriodId(e),this.resetTransmuxer(),this.getAllSegmentControllers().forEach((function(e){e.switchPeriod(t.periodSwitchTracker.getCurrentPlayingPeriodId()),y.MimeTypeHelper.isAV(e.getMimeType())&&t.renderer.storeDrmInitDataFromManifest(e.getCurrentAdaptationSet())})),this.periodSwitchTracker.setCurrentLoadingPeriodId(this.periodSwitchTracker.getCurrentPlayingPeriodId()))},e.prototype.clearBuffersOnSeek=function(){var e=this;return this.context.logger.debug("Clearing buffer on seek"),this.eosSignaled=!1,this.getAllSegmentControllers().forEach((function(e){return e.cancelLoading()})),this.bufferController.clearBuffers().catch((function(){e.logger.debug("Failed to clear buffers on seek, carrying on regardless")}))},e.prototype.getAllSegmentControllers=function(){return Object.values(this.segmentControllerMap||{})},e.prototype.getAudioSegmentController=function(){return this.getAllSegmentControllers().find((function(e){return y.MimeTypeHelper.isAudio(e.getMimeType())}))},e.prototype.updateAdaptationLogicData=function(e,t){var i,r;if(!(e.isInit||!e.success&&this.playerStateService.seekingOrTimeshifting)){var o=y.MimeTypeHelper.getMediaType(t.getMimeType()),a=this.getBufferLevel(t.getMimeType(),this.renderer.getCurrentTime(),s.BufferType.ForwardDuration),u=this.bufferSettings.getForwardTargetLevel(o);null===(r=null===(i=this.context.serviceManager)||void 0===i?void 0:i.get(n.ServiceName.AdaptationService))||void 0===r||r.addSample(t.getMimeType(),{bufferTargetLevel:u,bufferLevel:a,segmentDuration:e.duration,segmentDownloadDuration:e.downloadTime,downloadCancelled:!e.success})}},e.prototype.signalEndOfStream=function(e){void 0===e&&(e=!0),this.isVod()&&(this.eosSignaled||(this.eosSignaled=!0,this.bufferController.setEndOfStream(!0),e&&this.stop()),this.bufferController.isStarted()&&this.bufferController.stop())},e.prototype.start=function(){var e=this;return this.hasStarted()?Promise.resolve():this.finishInit().then((function(){var t;e.bufferController.hasStreamEnded()&&(e.seek((0,f.getStartTimeOffset)(e.getSourceStore().getState())),e.getAllSegmentControllers().forEach((function(e){return e.resetTransmuxer()}))),e.bufferController.isStarted()||e.bufferController.restart(),e.getAllSegmentControllers().filter((function(e){return y.MimeTypeHelper.isAV(e.getMimeType())})).forEach((function(t){return e.renderer.storeDrmInitDataFromManifest(t.getCurrentAdaptationSet())})),e.logger.debug("Starting main timer"),e.eosSignaled=!1,null===(t=e.heartbeatService)||void 0===t||t.start(),e.subscribeToManifestChanges(),e.eventHandler.on(o.PlayerEvent.SegmentRequestFinished,e.onSegmentRequestFinished)}))},e.prototype.maybeTransitionToPausedState=function(e){(0,S.isSwitchingBufferBlocks)(this.getSourceStore())?this.context.logger.debug("Ignoring transition to Paused because buffer block switch is ongoing"):this.playerStateService.transitionToPausedState(!0,e,!0)},e.prototype.clearBuffers=function(e){var t=this;return Promise.all(e.map((function(e){return t.bufferController.clearCacheForMimeType(e),t.bufferController.clearBuffer(e)}))).then((function(){}))},e.prototype.restorePlaybackPositionAfterAudioTrackSwitch=function(e,t){var i=this;return this.bufferController.setEndOfStream(!1).then((function(){var n=i.clearBuffers(e.getSourceBufferTypes());return e.getCurrentPeriodId()!==i.periodSwitchTracker.getCurrentPlayingPeriodId()?i.prepareControllerForAudioTrackChange(e):e.getCurrentPeriodId()!==i.periodSwitchTracker.getCurrentLoadingPeriodId()&&i.periodSwitchTracker.setCurrentLoadingPeriodId(e.getCurrentPeriodId()),e.setAdaptionLogicStartupPhase(),e.seekTo(t),i.start().catch((function(){return i.logger.debug("Could not restart stopped MPC after audio track switch")})),n.then((function(){i.renderer.setCurrentTime(t).catch((function(e){return i.logger.debug("Failed to set currentTime on audio track switch",e)}))})).catch((function(e){return i.logger.debug("Failed to attempt to restore playback after audio track switch",e)}))}))},e.prototype.setAudio=function(e){var t,i=this,n=this.getAvailableAudio(this.periodSwitchTracker.getCurrentPlayingPeriodId()),r=n.find((function(t){return t.id===e}));if(!this.initialized||!r)return Promise.reject(null);var o=null===(t=this.getAudio())||void 0===t?void 0:t.id,a=n.find((function(e){return e.id===o}));if(!a||o===e)return Promise.reject(o);var s=Object.keys(this.segmentControllerMap),u=s.find(y.MimeTypeHelper.isAudio)||s.find(y.MimeTypeHelper.isVideo),d=this.manifestService.getAdaptationSet(a.adaptationSetId),c=this.manifestService.getAdaptationSet(r.adaptationSetId);return d&&c&&u?(this.isSwitchingAudioTrack=!0,this.maybeChangeAudioBufferType(d,c,u).then((function(e){return i.maybeHandleMuxedAudioTransition(d,c,r,e)})).then((function(e){return i.maybeUpdateHlsAudioPlaylist(e,r.id)})).then((function(e){return i.updateRelevantSegmentControllers(e,r)})).finally((function(){return i.isSwitchingAudioTrack=!1}))):Promise.reject("Did not find matching adaptation set")},e.prototype.maybeUpdateHlsAudioPlaylist=function(e,t){var i=this;return Object.keys(e).some(y.MimeTypeHelper.isAudio)&&z(this.manifestLoader)?this.manifestLoader.updateAdaptationSet(t).then((function(){return i.getAllSegmentControllers().forEach((function(e){return e.updateMpd()}))})).then((function(){return e})):Promise.resolve(e)},e.prototype.maybeChangeAudioBufferType=function(e,t,i){var n,r=this,o=G(t,e),a=y.MimeTypeHelper.isVideo(t._mimeType)||y.MimeTypeHelper.isVideo(e._mimeType),s=!(0,j.hasContentProtection)(e.ContentProtection,t.ContentProtection);if(a||!o&&!s)return Promise.resolve(((n={})[t._mimeType]="",n));var u=Object.keys(this.segmentControllerMap).find(y.MimeTypeHelper.isVideo);return this.recreateCurrentSegmentController(i,t).then((function(){return s&&u?r.clearBuffers([u]).then((function(){var e,t=r.segmentControllerMap[u],i=r.periodSwitchTracker.getCurrentPlayingPeriodId();t.getCurrentPeriodId()!==i&&r.segmentControllerMap[u].switchPeriod(i),r.segmentControllerMap[u].seekTo(r.renderer.getCurrentTime()),null===(e=r.timeshiftHandler)||void 0===e||e.updateSegmentControllers(r.segmentControllerMap)})):Promise.resolve()})).then((function(){return r.bufferController.changeBufferType(t._mimeType,r.mimeTypes[t._mimeType])}))},e.prototype.maybeHandleMuxedAudioTransition=function(e,t,i,n){var r=this;return y.MimeTypeHelper.isVideo(t._mimeType)||y.MimeTypeHelper.isVideo(e._mimeType)?this.clearBuffers([e._mimeType,t._mimeType]).then((function(){var n=y.MimeTypeHelper.isVideo(t._mimeType);return r.transmuxer.setShouldExpectSeparateAvSegments(!n),n?(r.disposeSegmentController(e),Promise.resolve()):(r.createSegmentControllerIfMediaTypeIsMissing(t),r.updateSegmentController(i,e._mimeType))})).then((function(){return n})):Promise.resolve(n)},e.prototype.disposeSegmentController=function(e){var t;if(e){var i=e._mimeType,n=this.segmentControllerMap[i];n.cancelLoading(),n.dispose(),delete this.segmentControllerMap[i],delete this.mimeTypes[i],null===(t=this.timeshiftHandler)||void 0===t||t.updateSegmentControllers(this.segmentControllerMap),this.getSourceStore().dispatch((0,B.removeActiveTrackAction)(e._internalId)),this.context.segmentInfoService&&this.context.segmentInfoService.setSegmentControllers(Object.values(this.segmentControllerMap))}},e.prototype.recreateCurrentSegmentController=function(e,t){var i=this,n=this.segmentControllerMap[e];return this.disposeSegmentController(n.getCurrentAdaptationSet()),this.clearBuffers([e]).then((function(){return i.setupControllerForAdaptationSet(t),t._mimeType}))},e.prototype.updateRelevantSegmentControllers=function(e,t){var i=this;return Promise.all(Object.keys(e).map((function(e){return i.updateSegmentController(t,e)}))).then((function(){}))},e.prototype.updateSegmentController=function(e,t){if(this.subtitleService&&this.subtitleService.enableForcedSubtitle(e.lang),this.segmentControllerMap[t].setCurrentLangObj(e),this.segmentControllerMap[t].cancelLoading(),this.hasStarted()||this.eosSignaled){var i=this.getBufferRelevantTime();return this.restorePlaybackPositionAfterAudioTrackSwitch(this.segmentControllerMap[t],i)}return Promise.resolve()},e.prototype.getAudio=function(){if(this.initialized){var e=void 0,t=Object.keys(this.segmentControllerMap).find((function(e){return y.MimeTypeHelper.isAudio(e)}));if(t&&(e=this.segmentControllerMap[t].getCurrentLangObj()),!e){var i=Object.keys(this.segmentControllerMap).find((function(e){return y.MimeTypeHelper.isVideo(e)}));if(i&&this.segmentControllerMap[i].getSourceBufferTypes()){var n=this.segmentControllerMap[i];if(n.getSourceBufferTypes().some((function(e){return y.MimeTypeHelper.isAudio(e)}))){var r=n.getCurrentAdaptationSet()._internalId;e=this.getAvailableAudio(this.periodSwitchTracker.getCurrentPlayingPeriodId()).find((function(e){return e.adaptationSetId===r}))||n.getCurrentLangObj()}}}return e||null}return null},e.prototype.prepareControllerForAudioTrackChange=function(e){if(this.isAVMultiplexed(e))this.switchLoadingPeriodForAllMedia(this.periodSwitchTracker.getCurrentPlayingPeriodId());else{if(this.logger.debug("audio needs to load previous period(s) and catch up to the overall loading period"),e.isTransmuxerRequired()){var t=M.ModuleManager.get(I.ModuleName.ContainerTS);e.setTransmuxer(new t.WebWorkerTransmuxer(this.context,this.hasSeparateAvPlaylists()))}this.isAudioCatchingUp=!0,this.switchLoadingPeriodForAudio(this.periodSwitchTracker.getCurrentPlayingPeriodId())}},e.prototype.getPlaybackRepresentation=function(e){for(var t in this.segmentControllerMap)if(this.segmentControllerMap[t]&&t.includes(e))return this.context.serviceManager.get(n.ServiceName.TimedMetadataService).getPlaybackRepresentation(t)},e.prototype.getTimeShift=function(){return this.getTimeShiftHandler().getTimeShift()},e.prototype.getTimeShiftLiveEdge=function(){return this.manifestService.isLive()?this.getTimeShiftHandler().getTimeShiftLiveEdge():0},e.prototype.getPlaybackTimeForTimeShiftOffset=function(e){return this.getTimeShiftHandler().calculatePlaybackTimeForTimeShiftOffset(e)},e.prototype.getTimeShiftHandler=function(){return this.timeshiftHandler||(this.timeshiftHandler=new U.TimeShiftHandler(this.context,this,this.bufferController,this.segmentControllerMap)),this.timeshiftHandler},e.prototype.timeShift=function(e,t,i){return void 0===i&&(i=!1),this.getTimeShiftHandler().timeShift(e,t,i)},e.prototype.getAvailableAudio=function(e){return this.manifestService.getAvailableAudio(e)},e.prototype.hasStreamEnded=function(){return!!this.bufferController&&this.bufferController.hasStreamEnded()},e.prototype.getCurrentTime=function(){return this.renderer.getCurrentTime()},e.prototype.getSeekableRange=function(){if(this.manifestService.isLive()){if(this.playerStateService.seekingOrTimeshifting&&this.cachedSeekableRange)return this.cachedSeekableRange;var e=this.getTimeShiftLiveEdge(),t=e+this.getMaxTimeShift();return this.cachedSeekableRange={start:t,end:e}}var i=this.getAllSegmentControllers().find((function(e){return y.MimeTypeHelper.isAV(e.getMimeType())}));return i?i.getMPDHandler().getSeekableRange():{start:-1,end:-1}},e.prototype.getAvailableSegments=function(){var e=this,t={};return Object.keys(this.segmentControllerMap).forEach((function(i){t[i]=e.segmentControllerMap[i].getSegmentInfos()})),t},e.prototype.isInitialized=function(){return this.initialized},e.prototype.hasStarted=function(){var e,t;return null!==(t=null===(e=this.heartbeatService)||void 0===e?void 0:e.started)&&void 0!==t&&t},e}();function z(e){var t=M.ModuleManager.get(I.ModuleName.HLS,!1),i=null==t?void 0:t.M3u8Loader;return void 0!==i&&e instanceof i}function G(e,t){return!(0,j.areAudioMimeCodecsCompatible)({mimeType:e._mimeType,codec:(0,E.getCodecsFromAdaptationSet)(e)},{mimeType:t._mimeType,codec:(0,E.getCodecsFromAdaptationSet)(t)})}function W(e,t){var i=e.getPlaybackTimeRange(),n=(0,H.getTrackIdentifier)(e.getSegmentInfo());i&&(null==t||t.dispatch((0,F.removeStreamTimeRange)(n,i,H.StreamTimeRangeType.Loading)))}function Q(e){var t=e.getState();if(!t)return!1;var i=(0,c.getPlayerState)(t).seekingProcess.issuer;return i!==a.STARTUP_ISSUER_NAME&&""!==i}function Y(){return(0,T.isWebOS)()||(0,T.isTizen)()?2.5:1/0}t.MediaPlayerController=K},37702:function(e,t){function i(e){var t=[];return e.forEach((function(e){t.push(e)})),t}function n(e){var t=[];return e.forEach((function(e,i){t.push(i)})),t}Object.defineProperty(t,"__esModule",{value:!0}),t.getValues=i,t.getKeys=n},38353:function(e,t,i){var n=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},o=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.ManifestService=void 0,t.initializeInternalIds=H,t.addDrmKidsToRepresentations=U,t.iterateAdaptationSets=q,t.getNearestPeriodForTime=V,t.findLowestPossibleBandwidthFromRepresentations=z,t.getMatchingRepresentationsByBandwidth=G,t.getMatchingRepresentationByBandwidth=Q,t.getCodecPriorities=X,t.getCodecOfAdaptationSetMimeType=Z;var a=i(25550),s=i(28764),u=i(18665),d=i(60997),c=i(62510),l=i(57620),p=i(68039),f=i(63668),h=i(95328),g=i(91397),m=i(34731),v=i(27177),S=i(81361),y=i(42283),T=i(44388),b=i(79814),M=i(65114),I=i(331),P=i(70016),R=i(91520),C=i(16368),E=i(41735),A=i(77874),x=i(9827),k=i(13034),L=i(67883),_=i(94938),B=i(43752),D=i(96953),w=i(38925),O="Outdated",N=function(e){function t(t,i){var n=e.call(this,{onRestore:function(){return n.onRestore()}})||this;n.context=t,n.lastManifestUpdateId=0,n.dispatchQualityRemovedEvent=function(e){var t=n.getAdaptationSet(e._internalId);if(t){var i=function(t){n.context.eventHandler.dispatchEvent(t,{quality:{id:e._id,bitrate:e._bandwidth}})};b.MimeTypeHelper.isAudio(t._mimeType)?i(c.PlayerEvent.AudioQualityRemoved):b.MimeTypeHelper.isVideo(t._mimeType)&&i(c.PlayerEvent.VideoQualityRemoved)}},n.copyRequiredPropertiesToRepresentations=function(e){(e.Representation||[]).forEach((function(t){e._codecs&&!t._codecs&&(t._codecs=e._codecs),e._width&&!t._width&&(t._width=e._width),e._height&&!t._height&&(t._height=e._height)})),delete e.SegmentTemplate},n.sourceContext=i,n.settings=t.settings,n.excludedRepresentations=[];var r=n.sourceContext.sourceIdentifier;return n.synchronizedTimeService=t.serviceManager.get(u.ServiceName.SynchronizedTimeService,r),n.sourceStore=t.serviceManager.get(u.ServiceName.SourceStoreService,r),n}return n(t,e),Object.defineProperty(t.prototype,"mpdExtractor",{get:function(){return this._mpdExtractor||(this._mpdExtractor=new B.MPDExtractor(this.context,this.sourceContext)),this._mpdExtractor},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"manifest",{get:function(){var e;return(0,A.getManifest)(null===(e=this.sourceStore)||void 0===e?void 0:e.getState())},enumerable:!1,configurable:!0}),t.prototype.setManifest=function(e){this.sourceStore.dispatch((0,E.setManifestAction)(this.preProcessManifest(e)))},t.prototype.updateManifest=function(e){var t=this;this.lastManifestUpdateId++;var i=this.lastManifestUpdateId;return this.synchronizeWithTimeserver(e).then((function(){return t.ensureStillSameOperation(i),t.adjustManifestToKeySystemSupport(e)})).then((function(){t.ensureStillSameOperation(i),t.reinitializeAndUpdateManifest(e)})).catch((function(e){if(e!==O)throw e;t.context.logger.debug("Skipping manifest update, since a newer manifest is available")}))},t.prototype.ensureStillSameOperation=function(e){if(e!==this.lastManifestUpdateId)throw O},t.prototype.adjustManifestToKeySystemSupport=function(e){var t,i,n,r,o=this,a=null!==(n=null===(i=null===(t=this.context.serviceManager)||void 0===t?void 0:t.get(u.ServiceName.DrmService))||void 0===i?void 0:i.getUsedOrEarlyResolvedKeySystemUid())&&void 0!==n?n:null;if(!a||!e.Period||!(null===(r=this.context.serviceManager)||void 0===r?void 0:r.has(u.ServiceName.DrmDetectorService)))return Promise.resolve();var s=this.context.serviceManager.get(u.ServiceName.DrmDetectorService),d=$(e,this.mpdExtractor),c=d.audioCapabilities,l=d.videoCapabilities;return s.getSupportedCapabilities(a,c,l).then((function(t){return ee(e,t,o.context.logger)}))},t.prototype.reinitializeAndUpdateManifest=function(e){if((0,v.isContextAvailable)(this.context)&&this.context.serviceManager){if(this.disposeMpdExtractor(),e.isInitialized=!1,this.setManifest(e),this.isLive()){var t=this.getFirstPeriod(),i=this.isHlsManifest()&&t?t.start:(0,I.toSeconds)(this.getAvailabilityStartTime());this.sourceStore.dispatch((0,h.setStartTimeOffset)(i))}this.mpdExtractor.adjustPeriodStartTimes();var n=this.parseStreamDuration();(0,l.maybeFireDurationChangedEvent)(this.parsedDuration,n,this.context.eventHandler),this.parsedDuration=n,this.setManifestInitialized(!0),this.context.serviceManager.maybeCall(u.ServiceName.HeartbeatService,(function(e){e.started&&e.beat()}),void 0,this.context.sourceContext.sourceIdentifier)}},t.prototype.excludeRepresentations=function(e){var t=this,i=e.map((function(e){return t.getRepresentationById(e)})).filter((function(e){return Boolean(e)}));this.setManifestInitialized(!1);var n=ne(this.manifest.Period);this.excludedRepresentations=S.ArrayHelper.union(this.excludedRepresentations,e),this.sourceStore.dispatch((0,E.removeRepresentationsAction)(this.excludedRepresentations));var r=ne(this.manifest.Period);return Object.keys(n).every((function(e){return null!=r[e]&&n[e].length===r[e].length}))?(this.disposeMpdExtractor(),i.forEach(this.dispatchQualityRemovedEvent),this.setManifestInitialized(!0),!0):(this.context.eventHandler.fireError(new s.PlayerError(a.ErrorCode.SOURCE_ERROR,void 0,"No playable track left")),!1)},t.prototype.isRepresentationExcluded=function(e){return this.excludedRepresentations.some((function(t){return t.equals(e)}))},t.prototype.resetExcludedRepresentations=function(){this.excludedRepresentations=[]},t.prototype.onRestore=function(){this.setPlayerDRM(this.getManifest()),this.sourceStore.dispatch((0,E.setManifestAction)(H(this.getManifest())))},t.getSupplementalProperties=function(e,t){return!e.SupplementalProperty||e.SupplementalProperty.length<=0?null:e.SupplementalProperty.filter((function(e){return e._schemeIdUri===t}))},t.prototype.findMatchingSwitchableAdaptationSets=function(e,t,i){var n=i&&i.length>0;if(!n&&!this.settings.ADAPTATION_SET_SWITCHING_WITHOUT_SUPPLEMENTAL_PROPERTY)return null;var r=[],o=e._group,a=Z(e);return n&&i.forEach((function(e){e._value&&r.push.apply(r,e._value.split(","))})),this.settings.ADAPTATION_SET_SWITCHING_WITHOUT_SUPPLEMENTAL_PROPERTY&&t.forEach((function(e){r.includes(e._id)||r.push(e._id)})),t.filter((function(t){if(!r.includes(t._id)||t._id===e._id)return!1;var i=t._group===o,n=t._mimeType===e._mimeType,s=Z(t)===a,u=t._lang===e._lang;return n&&s&&i&&u}))},t.prototype.getSwitchableAdaptationSets=function(e){var i=this;return e.AdaptationSet?e.AdaptationSet.reduce((function(n,r){var a=t.getSupplementalProperties(r,"urn:mpeg:dash:adaptation-set-switching:2016"),s=i.findMatchingSwitchableAdaptationSets(r,e.AdaptationSet,a);return s&&0!==s.length?S.ArrayHelper.addAndMergeIntersects(n,o([r],s,!0)):n}),[]):[]},t.mergeAdaptationSetProperties=function(e,t,i){var n=e.hasOwnProperty(i),r=t.hasOwnProperty(i);(n||r)&&(n?r||(t[i]=[]):e[i]=[],e[i]=e[i].concat(t[i]))},t.prototype.getAdaptationSetPropertyValues=function(e,t){return e.filter((function(e){return e.hasOwnProperty(t)})).map((function(e){return e[t]}))},t.prototype.updateAdaptationSetMinMaxValues=function(e,t){var i=[e].concat(t),n=this.getAdaptationSetPropertyValues(i,"_minWidth"),r=this.getAdaptationSetPropertyValues(i,"_maxWidth"),o=this.getAdaptationSetPropertyValues(i,"_minHeight"),a=this.getAdaptationSetPropertyValues(i,"_maxHeight"),s=this.getAdaptationSetPropertyValues(i,"_minBandwidth"),u=this.getAdaptationSetPropertyValues(i,"_maxBandwidth");n.length>0&&(e._minWidth=Math.min.apply(null,n)),r.length>0&&(e._maxWidth=Math.max.apply(null,r)),o.length>0&&(e._minHeight=Math.min.apply(null,o)),a.length>0&&(e._maxHeight=Math.max.apply(null,a)),s.length>0&&(e._minBandwidth=Math.min.apply(null,s)),u.length>0&&(e._maxBandwidth=Math.max.apply(null,u))},t.prototype.mergeAdaptationSets=function(e,i){i.forEach(this.copyRequiredPropertiesToRepresentations);var n=i.shift();i.forEach((function(e){["Representation","ContentComponent","ContentProtection","SupplementalProperty"].forEach((function(i){return t.mergeAdaptationSetProperties(n,e,i)}))})),this.updateAdaptationSetMinMaxValues(n,i),e.AdaptationSet=e.AdaptationSet.filter((function(e){return!i.includes(e)}))},t.prototype.sortAdaptationSetRepresentations=function(e){var t;null===(t=null==e?void 0:e.AdaptationSet)||void 0===t||t.forEach((function(e){var t;null===(t=null==e?void 0:e.Representation)||void 0===t||t.sort((function(e,t){return e._bandwidth-t._bandwidth}))}))},t.prototype.mergeSwitchableAdaptationSets=function(e){var t=this;return e.Period.forEach((function(e){t.getSwitchableAdaptationSets(e).forEach((function(i){t.mergeAdaptationSets(e,i)})),t.sortAdaptationSetRepresentations(e)})),e},t.prototype.setRepresentationDrmKid=function(e,t){this.sourceStore.dispatch((0,E.setRepresentationDrmKidAction)(e,t))},t.prototype.getRepresentationDrmKid=function(e){var t;return null===(t=this.getRepresentationById(e))||void 0===t?void 0:t.associatedKid},t.prototype.setManifestInitialized=function(e){this.sourceStore.dispatch((0,E.setManifestInitializedAction)(e))},t.prototype.setPlayerDRM=function(e){var t=this.sourceContext.source.drm,i=F(e),n={};i.length>0&&(n=r({},i[0])),null!=t&&Object.keys(t).length>0&&Object.keys(t).forEach((function(e){n[e]=null!=n[e]?r(r({},n[e]),t[e]):t[e]})),this.addDrmServiceAndSetDrmConfig(n);var o=Object.keys(n).length>0?(0,f.setManifestDrm)(n):(0,f.resetManifestDrm)();this.sourceStore.dispatch(o)},t.prototype.addDrmServiceAndSetDrmConfig=function(e){if(R.ModuleManager.has(C.ModuleName.DRM)&&Object.keys(e).length>0&&!this.context.serviceManager.has(u.ServiceName.DrmService)){var t=R.ModuleManager.get(C.ModuleName.DRM).DrmService;this.context.serviceManager.set(u.ServiceName.DrmService,new t(this.context))}this.context.serviceManager.maybeCall(u.ServiceName.DrmService,(function(t){t.isDrmConfigSet()||t.setDrmConfig(e)}))},t.prototype.getPreviousRepDrmKidAssociation=function(){var e=new Map;return this.getAllRepresentations().filter((function(e){return e._internalId&&e.associatedKid})).forEach((function(t){return e.set(t._internalId.key(),t.associatedKid)})),e},t.prototype.preProcessManifest=function(e){var t=this.getPreviousRepDrmKidAssociation();return e.Period&&e.Period.length>0?(U(e=(0,_.orderAdaptationSetsByCodec)(e,X(this.context.config,this.sourceContext.source))),e=this.mergeSwitchableAdaptationSets(e),R.ModuleManager.has(C.ModuleName.Envivio)&&(e=R.ModuleManager.get(C.ModuleName.Envivio).fixSegmentTimelineLiveStream(e)),this.isSuspended()||this.setPlayerDRM(e),e=H(e),j(e=(0,_.removeRepresentations)(e,this.excludedRepresentations),t),e):(this.context.eventHandler.fireError(new s.PlayerError(a.ErrorCode.SOURCE_ERROR,void 0,"No playable track left")),null)},t.prototype.parseStreamDuration=function(){if(this.mpdExtractor){var e=NaN,t=this.mpdExtractor.getDuration();return t&&(this.cachedDurationString&&this.cachedDurationString===t?e=this.cachedDuration:(e=T.DurationConverter.getDurationInSec(t),this.cachedDurationString=t,this.cachedDuration=e)),isNaN(e)&&this.isLive()?1/0:e}return 0},t.prototype.getDuration=function(){return this.parsedDuration},t.prototype.isLive=function(){return!!this.manifest&&this.mpdExtractor.isLive()},t.prototype.getAvailabilityStartTime=function(){return this.mpdExtractor.getAvailabilityStartTime()},t.prototype.getAvailabilityEndTime=function(){return this.mpdExtractor.getAvailabilityEndTime()},t.prototype.isAvailabilityEndTimeExceeded=function(){var e=this.getAvailabilityEndTime();return e&&Date.now()>e.getTime()},t.prototype.findPeriod=function(e){return(0,M.findPeriod)(this.manifest,e)},t.prototype.getAllPeriods=function(){return this.manifest&&this.manifest.Period?this.manifest.Period:[]},t.prototype.getPeriod=function(e){return e?this.findPeriod(e.periodId):null},t.prototype.isLanguageAvailable=function(e,t,i){var n=(0,M.findPeriod)(this.manifest,e)||J(this.manifest);return(0,k.findAdaptationSetsForMimeType)(n,t).some((function(e){var t=e._lang&&i.lang&&e._lang===i.lang,n=i.adaptationSetId&&e._internalId.equals(i.adaptationSetId);return t||n}))},t.prototype.findAdaptationSet=function(e,t,i,n){void 0===n&&(n=!1);var r=(0,M.findPeriod)(this.manifest,e)||J(this.manifest);if(void 0!==r){var o=this.sourceStore.getState(),a=(0,k.findAdaptationSetOfMimeType)(t,r,{sourceState:o,langObj:i,periodSwitched:n,playbackConfig:this.context.config.playback});return a||this.context.logger.debug("No AdaptationSet found to match mime type "+t),a}},t.prototype.getManifest=function(){return this.manifest},t.prototype.getAvailableAudio=function(e,t){return this.mpdExtractor.getAvailableAudio(e,t)},t.prototype.getAvailableVideo=function(e){return this.mpdExtractor.getAvailableVideo(e)},t.prototype.getAvailableSubtitles=function(e){return this.mpdExtractor.getAvailableSubtitles(e)},t.prototype.getClosedCaptionLabels=function(e){return this.mpdExtractor.getClosedCaptionLabels(e)},t.prototype.getVideoRepresentations=function(e){return this.mpdExtractor.getVideoQualities(e)},t.prototype.getAudioRepresentations=function(e){return this.mpdExtractor.getAudioQualities(e)},t.prototype.getPeriodDuration=function(e,t){var i;return this.mpdExtractor.getPeriodDuration(e,null!=t?t:null===(i=this.getManifest())||void 0===i?void 0:i.Period)},t.prototype.estimatePeriodDuration=function(e){var t,i;return this.isLastPeriod(e._id)?null!==(i=re(e.AdaptationSet[0]))&&void 0!==i?i:1/0:null!==(t=e.duration)&&void 0!==t?t:this.getPeriodDuration(e._id)},t.prototype.hasMultiplePeriods=function(){return this.manifest&&this.manifest.Period&&this.manifest.Period.length>1},t.prototype.hasSinglePeriod=function(){return this.manifest&&this.manifest.Period&&1===this.manifest.Period.length},t.prototype.getEstimatedDvrWindowLength=function(){var e=this;return this.getAllPeriods().reduce((function(t,i){return t+e.estimatePeriodDuration(i)}),0)},t.prototype.getTimeShiftBufferDepthSeconds=function(){var e=this.mpdExtractor.getTimeShiftBufferDepthSeconds(),t=-this.getEstimatedDvrWindowLength();return Math.max(e,t)},t.prototype.getRequestTimestamp=function(){return this.mpdExtractor.getRequestTimestamp()},t.prototype.getMediaPresentationDuration=function(){var e,t=T.DurationConverter.getDurationInSec(null===(e=this.manifest)||void 0===e?void 0:e._mediaPresentationDuration);return isNaN(t)?0:t},t.prototype.calculateCumulativePeriodDuration=function(e){for(var t,i,n=0,r=0,o=null!==(i=null===(t=this.manifest)||void 0===t?void 0:t.Period)&&void 0!==i?i:[];r<o.length;r++){var a=o[r];if(a._id===e)break;n+=this.getPeriodDuration(a._id)}return this.isLive()?(0,I.toSeconds)(Date.now()-this.getAvailabilityStartTime())-n:n},t.prototype.getMinimumUpdatePeriod=function(){return(0,B.getMinimumUpdatePeriodInSeconds)(this.manifest)},t.prototype.getContentProtectionForAdaptationSet=function(e){return B.MPDExtractor.getContentProtectionForAdaptationSet(e)},t.prototype.getLangObjectFromAdaptationSet=function(e,t){return this.mpdExtractor.getLangObjectFromAdaptationSet(e,t)},t.prototype.toSubtitleTrack=function(e,t){return this.mpdExtractor.toSubtitleTrack(e,t)},t.prototype.getDRMCapabilitiesForPeriod=function(e){return this.mpdExtractor.getDRMCapabilitiesForPeriod(e)},t.prototype.hasManifestTypeChanged=function(e){return!(!this.manifest||this.manifest._type===e)&&(this.context.logger.log("Manifest has just changed from "+this.manifest._type+" to "+e),!0)},t.prototype.getPeriodIdForTime=function(e){var t,i=this.getAllPeriods();return 1===i.length?i[0]._id:null===(t=V(i,e))||void 0===t?void 0:t._id},t.prototype.dispose=function(){this.disposeMpdExtractor(),e.prototype.dispose.call(this)},t.prototype.disposeMpdExtractor=function(){this._mpdExtractor=(0,d.dispose)(this._mpdExtractor)},t.prototype.getTotalDurationFromManifest=function(){var e=this.getFirstPeriod();if(this.cachedDuration&&e&&e.start)return e.start+this.cachedDuration},t.prototype.getTotalDuration=function(){if(this.isLive())return this.getTotalDurationFromManifest()||1/0;var e=this.getLastPeriod();return e?(e.start||e._start&&T.DurationConverter.getDurationInSec(e._start)||0)+this.mpdExtractor.getPeriodDuration(e._id):0},t.prototype.getFirstPeriod=function(){return J(this.manifest)},t.prototype.getLastPeriod=function(){var e=this.getAllPeriods();if(e.length>0)return e[e.length-1]},t.prototype.isLastPeriod=function(e){var t=this.getLastPeriod();return!!t&&e===t._id},t.prototype.isManifestFinalized=function(){return!this.isLive()},t.prototype.isFirstPeriod=function(e){var t=this.getFirstPeriod();return!!t&&t._id===e},t.prototype.isHlsManifest=function(){var e,t;return null!==(t=null===(e=this.getManifest())||void 0===e?void 0:e._isHls)&&void 0!==t&&t},t.prototype.isDashManifest=function(){return!this.isHlsManifest()&&!this.isSmoothManifest()},t.prototype.isSmoothManifest=function(){var e,t;return"smooth"===(null===(t=null===(e=this.manifest)||void 0===e?void 0:e.originalFormat)||void 0===t?void 0:t.format)},t.prototype.getHlsTags=function(){if(!this.isHlsManifest())return null;var e=R.ModuleManager.get(C.ModuleName.HLS).selectors,t=e.getHlsState;return(0,e.getCustomTags)(t(this.sourceStore.getState()))},t.prototype.isMimeTypePartOfPeriod=function(e,t){var i=this.getPeriod(new D.PeriodId(t));return(i?i.AdaptationSet:[]).some((function(t){return t._mimeType===e}))},t.prototype.getNextPeriod=function(e){if(this.manifest)for(var t=this.findPeriod(e),i=this.manifest.Period.indexOf(t)+1;i<this.manifest.Period.length;i++){var n=this.manifest.Period[i];if(n)return n}return null},t.prototype.getEventStreamEvents=function(e){return this.mpdExtractor.getEventStreamEvents(e)},t.prototype.hasAdaptationSets=function(e){var t=this.manifest;if(t&&t.Period){var i=t.Period.find((function(t){return t._id===e}));return Boolean(i&&i.AdaptationSet)}return!1},t.prototype.synchronizeWithTimeserver=function(e){if(this.synchronizedTimeService){var t=this.synchronizedTimeService.getSynchronizationPromise();if(t)return t;if(e.UTCTiming&&e.UTCTiming.length>0)return this.synchronizedTimeService.synchronizeWithServer(e.UTCTiming,e._downloadTime)}return Promise.resolve()},t.prototype.getAdaptationSet=function(e){if(e&&this.manifest)return(0,M.findAdaptationSet)(this.manifest,e)},t.prototype.getRepresentationById=function(e){if(e&&this.manifest)return(0,M.findRepresentation)(this.manifest,e)},t.prototype.representationExists=function(e){return Boolean(this.getRepresentationById(e))},t.prototype.getLowestBandwidthOfAdaptationSet=function(e){return this.findLowestPossibleBandwidth(e,-1/0)},t.prototype.findLowestPossibleBandwidth=function(e,t){void 0===t&&(t=-1/0);var i=this.getAdaptationSet(e);return i?z(i.Representation,t):1/0},t.prototype.getMatchingRepresentationsByBandwidth=function(e,t,i){void 0===i&&(i=function(){return!0});var n=this.getAdaptationSet(e);return n?G(n.Representation,t,i):[]},t.prototype.getMatchingRepresentationByBandwidth=function(e,t,i){void 0===i&&(i=function(){return!0});var n=this.getAdaptationSet(e);return n?Q(n.Representation,t,i):null},t.prototype.getAllRepresentations=function(){return this.getAllPeriods().flatMap((function(e){var t;return(null===(t=e.AdaptationSet)||void 0===t?void 0:t.flatMap((function(e){return e.Representation})))||[]}))},t.prototype.getRepresentation=function(e,t){return this.getRepresentationById(new w.RepresentationId(e,t))},t.prototype.getAllAdaptationSets=function(){return this.getAllPeriods().flatMap((function(e){return e.AdaptationSet}))},t.prototype.getAllImageAdaptationSets=function(){return this.getAllAdaptationSets().filter((function(e){return e._mimeType.includes("image")&&"image"===e._contentType}))},t.prototype.getAllDashThumbnailSources=function(){var e=this;return this.getAllImageAdaptationSets().map((function(t){var i=e.findPeriod(t._internalId.periodId);return{adaptationSet:t,timing:{startTime:i.start,duration:i.duration}}}))},t.prototype.getAdaptationSetIndex=function(e){var t=this.findPeriod(e.periodId);return t&&t.AdaptationSet&&0!==t.AdaptationSet.length?t.AdaptationSet.findIndex((function(t){return t._internalId.equals(e)})):-1},t.prototype.getAvailableBaseURLsForRepresentation=function(e){var t=this.getAdaptationSet(e);if(void 0===e||!t)return[];for(var i=[],n=t.Representation,r=0;r<n.length;r++)if(n[r]._internalId.equals(e)&&n[r].BaseURL&&n[r].BaseURL.length>0){i=n[r].BaseURL;break}var o=[];for(r=0;r<i.length;r++)o.push((0,g.forceReallocation)(i[r].url));return o},t.prototype.findDownloadedHlsRepresentation=function(e){var t=this.getAdaptationSet(e);return t?t.Representation.find((function(e){return e._hls&&Boolean(e._hls.requestTimestamp)})):null},t.prototype.isManifestFetchRequired=function(e){if(!this.isHlsManifest())return!1;var t=Date.now(),i=this.getRepresentationById(e),n=Boolean(i.SegmentList)&&i.SegmentList.length>0&&Boolean(i.SegmentList[0].SegmentURL),r=n?i.SegmentList[0].SegmentURL.length:0,o=!n||r>0,a=t-(i._requestTimestamp||t),s=this.isLive()&&a>=this.getMinimumUpdatePeriod();return!o||s},t.prototype.hasSelfInitialisingSegments=function(){return this.manifest.Period.map((function(e){return e.AdaptationSet})).reduce((function(e,t){return e.concat(t)}),[]).map((function(e){return e.Representation})).reduce((function(e,t){return e.concat(t)}),[]).some((function(e){var t=Boolean(e.SegmentList)&&e.SegmentList.length>0?e.SegmentList:[],i=Boolean(e.SegmentBase)&&e.SegmentBase.length>0,n=Boolean(e.SegmentTemplate)&&e.SegmentTemplate.length>0?e.SegmentTemplate:[];return!(e.init||i||t.some((function(e){return Boolean(e.init)}))||n.some((function(e){return Boolean(e._initialization)})))}))},t.prototype.getAvailabilityTimeComplete=function(e){var t=this.getRepresentationById(e),i=t.SegmentList||t.SegmentBase||t.SegmentTemplate;return!(i&&i[0]&&i[0]._availabilityTimeComplete)||JSON.parse(i[0]._availabilityTimeComplete)},t.prototype.isSegmentInfoLoaded=function(e){var t=this.getRepresentationById(e);return!!t&&!(!t.segmentIndex&&!t.segmentIndexParsingError)},t.prototype.isPrecedingPeriod=function(e,t){if(!(e&&t&&this.manifest&&this.manifest.Period))return!1;var i=this.manifest.Period.findIndex((function(t){return t._id===e})),n=this.manifest.Period.findIndex((function(e){return e._id===t}));return i>=0&&n>=0&&n<i},t.prototype.getMaxSegmentDuration=function(){var e=T.DurationConverter.getDurationInSec(this.manifest._maxSegmentDuration);return isNaN(e)?0:e},t.prototype.getDesiredDistanceToLiveEdge=function(){var e,t=null===(e=this.context.serviceManager.get(u.ServiceName.LiveLatencyService))||void 0===e?void 0:e.getTargetLatency();return(0,P.isDefined)(t)?t:this.context.bufferSettings.getForwardTargetLevel()+this.settings.LIVE_EDGE_DISTANCE},t.prototype.hasSuggestedStartPosition=function(){return this.manifest.hasOwnProperty("_startOffset")},t.prototype.getVodStartOffset=function(){return Math.max(0,Math.min(this.manifest._startOffset,this.getDuration()-this.settings.SEEK_TO_END_OFFSET))},t.prototype.getLiveStartOffset=function(e){void 0===e&&(e=!0);var t=this.manifest._startOffset;return t-=Math.abs(this.getTimeShiftBufferDepthSeconds()),e&&(t+=this.getDesiredDistanceToLiveEdge(),t=Math.min(0,t)),t},t.prototype.initSegmentStartTimesFromReferenceSegment=function(e){this.sourceStore.dispatch((0,E.initPlaybackTimesFromReferenceSegment)(e))},t.prototype.initializeSegmentStartTimesFromStart=function(e){var t,i,n=this.getRepresentationById(e),r=null===(i=null===(t=null==n?void 0:n.SegmentList)||void 0===t?void 0:t[0])||void 0===i?void 0:i.SegmentURL;if(n&&(null==r?void 0:r.length)){var o=r.length-1,a=(0,I.toSeconds)(n._requestTimestamp)-r[o]._duration;this.context.logger.debug("Initializing segment start times with start time of last segment ".concat(a),e),this.sourceStore.dispatch((0,E.initPlaybackTimesFromIndex)(e,o,a))}else this.context.logger.debug("Failed to initialize segment start times, no segments found",e)},t.prototype.hasSegmentStartTimeForHlsRepresentation=function(e){var t,i,n,r=this.getRepresentationById(e);return void 0!==(null===(n=null===(i=null===(t=null==r?void 0:r.SegmentList)||void 0===t?void 0:t[0])||void 0===i?void 0:i.SegmentURL[0])||void 0===n?void 0:n._playbackTime)},t.prototype.getStartTimeForHlsSegment=function(e,t){var i,n,r,o,a,s=this.getRepresentationById(e),u=null!==(r=null===(n=null===(i=null==s?void 0:s.SegmentList)||void 0===i?void 0:i[0])||void 0===n?void 0:n.SegmentURL)&&void 0!==r?r:[],d=(0,x.findSegmentUrlIndexWithinSegmentList)(t,u);return-1!==d&&void 0!==u[d]._playbackTime?u[d]._playbackTime:null!==(a=null===(o=J(this.manifest))||void 0===o?void 0:o.start)&&void 0!==a?a:void 0},t.prototype.isTimeNearPeriodEnd=function(e,t,i){var n=this.findPeriod(t),r=null==n?void 0:n.start;if(void 0===r)return!1;var o=r+this.getPeriodDuration(n._id,this.getAllPeriods());return e>=r&&e<=o&&o-e<=i},t}(m.Suspendable);function F(e){return null==e.Period?[]:e.Period.filter((function(e){return e.AdaptationSet&&e.AdaptationSet.length>0})).reduce((function(e,t){return e.concat(t.AdaptationSet)}),[]).filter((function(e){return e.ContentProtection&&e.ContentProtection.length>0})).flatMap((function(e){return e.ContentProtection})).filter((function(e){return null!=e.laurl&&e.laurl.length>0})).flatMap((function(e){return e.laurl.map((function(t){return r(r({},t),{drmType:p.DRMSchemeIDURIs[e._schemeIdUri]})}))})).filter((function(e){return null!=e.drmType})).map((function(e){var t;return(t={})[e.drmType]={LA_URL:e._licenseUrl},t}))}function H(e){var t;return(e=r({},e)).Period=null===(t=e.Period)||void 0===t?void 0:t.map((function(e){var t;return r(r({},e),{AdaptationSet:null===(t=e.AdaptationSet)||void 0===t?void 0:t.map((function(t,i){var n,o=(0,L.createAdaptationSetIdFromMimeTypeAndIndex)(e._id,t._mimeType,i);return r(r({},t),{_internalId:o,_periodId:e._id,Representation:null===(n=t.Representation)||void 0===n?void 0:n.map((function(e){return r(r({},e),{_internalId:new w.RepresentationId(o,e._id)})}))})}))})})),e}function U(e){if(R.ModuleManager.has(C.ModuleName.DRM)){var t=R.ModuleManager.get(C.ModuleName.DRM).ContentProtectionHelper.getFirstKeyId;q(e,(function(e){for(var i=t(e.ContentProtection),n=0;e.Representation&&n<e.Representation.length;n++){var r=e.Representation[n];(i||r.ContentProtection)&&(r.associatedKid=r.ContentProtection&&t(r.ContentProtection)||i)}}))}}function j(e,t){e.Period.flatMap((function(e){var t;return null!==(t=null==e?void 0:e.AdaptationSet)&&void 0!==t?t:[]})).flatMap((function(e){var t;return null!==(t=null==e?void 0:e.Representation)&&void 0!==t?t:[]})).forEach((function(e){t.has(e._internalId.key())&&(e.associatedKid=t.get(e._internalId.key()))}))}function q(e,t){for(var i=0;e.Period&&i<e.Period.length;i++)for(var n=e.Period[i],r=0;n.AdaptationSet&&r<n.AdaptationSet.length;r++)t(n.AdaptationSet[r])}function V(e,t){if(e&&e.length){if(t<e[0].start)return e[0];var i=e[e.length-1];return t>=i.start?i:K(e,t)||i}}function K(e,t){for(var i=e.length-1;i>=0;i--){var n=e[i].start+e[i].duration;if(t>=e[i].start&&t<n)return e[i];if(t>=n&&t<e[i+1].start)return e[i+1]}}function z(e,t){return void 0===t&&(t=-1/0),e.reduce((function(e,i){return i._bandwidth>=t?Math.min(e,i._bandwidth):e}),1/0)}function G(e,t,i){void 0===i&&(i=function(){return!0});var n=e.filter(i),r=n.filter((function(e){return e._bandwidth===t}));return r.length>0?r:W(n,t)}function W(e,t){var i=e.map((function(e){return e._bandwidth})).reduce((function(e,i){return Math.abs(i-t)<Math.abs(e-t)?i:e}));return e.filter((function(e){return e._bandwidth===i}))}function Q(e,t,i){void 0===e&&(e=[]),void 0===i&&(i=function(){return!0});var n=e.filter(i),r=null,o=-1/0;return n.forEach((function(e){e._bandwidth>o&&e._bandwidth<=t&&(o=e._bandwidth,r=e)})),r||n[0]}function Y(e,t,i){var n=i?"videoCodecPriority":"audioCodecPriority";return(null==t?void 0:t.options)&&t.options[n]?t.options[n]:e.playback&&e.playback[n]?e.playback&&e.playback[n]:[]}function X(e,t){return{video:Y(e,t,!0),audio:Y(e,t,!1)}}function J(e){return null==e?void 0:e.Period[0]}function Z(e){var t=(0,_.getCodecsFromAdaptationSet)(e);if(!t||0===t.length)return null;var i=y.CodecStringHelper.getExtractedCodecStrings(t),n=e._mimeType.split("/")[0];return i[n]?y.CodecStringHelper.extractCodec(i[n]):null}function $(e,t){var i=[],n=[];return e.Period.forEach((function(e){var r=t.getDRMCapabilitiesFromPeriod(e);i=i.concat(r[0]),n=n.concat(r[1])})),{audioCapabilities:i,videoCapabilities:n}}function ee(e,t,i){e.Period.forEach((function(e){e.AdaptationSet=e.AdaptationSet.filter((function(n){return n.Representation=n.Representation.filter((function(r){var o=e.ContentProtection||n.ContentProtection,a=(null==o?void 0:o.length)>0,s=(0,B.getContentTypeFromMimeTypeAndCodecs)(r._mimeType,r._codecs),u=te(s,t.audioCapabilities,t.videoCapabilities);return a&&!u&&i.debug("Representation not supported by MediaKeySystem (".concat(s)),!a||u})),!b.MimeTypeHelper.isAV(n._mimeType)||n.Representation.length>0}))}))}function te(e,t,i){return b.MimeTypeHelper.isAudio(e)?Boolean(t.find((function(t){return t.contentType===e}))):!b.MimeTypeHelper.isVideo(e)||Boolean(i.find((function(t){return t.contentType===e})))}function ie(e){return e.AdaptationSet.map((function(e){return e._mimeType})).filter((function(e,t,i){return b.MimeTypeHelper.isAV(e)&&i.indexOf(e)===t}))}function ne(e){return e.reduce((function(e,t){return e[t._id]=ie(t),e}),{})}function re(e){var t,i,n,r,o,a,s,u;return(null==e?void 0:e.SegmentTemplate)?null===(n=null===(i=null===(t=e.SegmentTemplate[0])||void 0===t?void 0:t.SegmentTimeline)||void 0===i?void 0:i[0])||void 0===n?void 0:n.totalDuration:(null==e?void 0:e.Representation)?null===(u=null===(s=null===(a=null===(o=null===(r=e.Representation[0])||void 0===r?void 0:r.SegmentTemplate)||void 0===o?void 0:o[0])||void 0===a?void 0:a.SegmentTimeline)||void 0===s?void 0:s[0])||void 0===u?void 0:u.totalDuration:void 0}t.ManifestService=N},38765:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.TimedMetadataService=void 0;var r=i(18665),o=i(60997),a=i(62510),s=i(54861),u=i(489),d=i(76650),c=i(28819),l=i(8272),p=i(79814),f=i(38925),h=i(19300),g=i(51399),m=function(){function e(e){var t,i=this;this.context=e,this.onGenericMetadataAvailable=function(e){if(e.hasOwnProperty("metadata")){var t={metadataType:e.metadataType.toUpperCase(),metadata:e.metadata};(e.start||0===e.start)&&(t.start=e.start),e.end&&(t.end=e.end),i.context.eventHandler.dispatchEvent(a.PlayerEvent.Metadata,t)}},this.inbandMetadataCallback=function(e){e&&e.hasOwnProperty("metadataType")&&i.context.eventHandler.dispatchEvent(a.PlayerEvent.Metadata,{metadataType:e.metadataType,metadata:e.metadata})},this.onSegmentPlayback=function(e){var t=e.metadata.event;if(void 0!==t.periodId&&!i.periodSwitchMonitor.isPeriodExpected(t.periodId))return!1;i.currentlyPlayedSegment[t.mimeType]=t,i.playbackRepresentationId[t.mimeType]=f.RepresentationId.from(e.metadata.representationId),i.discontinuityMonitor.maybeFirePeriodSwitchEvents(t),i.periodSwitchMonitor.maybeHandlePeriodSwitch(t),i.previousRepresentationIds[t.mimeType]&&i.previousRepresentationIds[t.mimeType].id===t.representationId||i.handlePlaybackInfo(t),i.context.eventHandler.dispatchEvent(a.PlayerEvent.SegmentPlayback,t)},this.previousRepresentationIds={},this.playbackRepresentationId={},this.currentlyPlayedSegment={},this.manifestService=this.context.serviceManager.get(r.ServiceName.ManifestService,e.sourceContext.sourceIdentifier);var n=((t={})[u.TimedMetadataType.InBand]=this.inbandMetadataCallback,t[u.TimedMetadataType.SegmentPlayback]=this.onSegmentPlayback,t[u.TimedMetadataType.Manifest]=this.onGenericMetadataAvailable,t[u.TimedMetadataType.EventStream]=this.onGenericMetadataAvailable,t[u.TimedMetadataType.DateRange]=this.onGenericMetadataAvailable,t[u.TimedMetadataType.CustomTag]=this.onGenericMetadataAvailable,t);this.metadataService=new s.MetadataService(e,n),this.discontinuityMonitor=new h.DiscontinuityMonitor(e),this.periodSwitchMonitor=new g.PeriodSwitchMonitor(e)}return e.prototype.clearSegmentBoundMetadata=function(e){this.metadataService.removeMatchingCues(u.TimedMetadataType.SegmentPlayback,(function(t){return t.content.metadata.mimeType===e}))},e.prototype.onSegmentAvailable=function(e){var t,i=this,n=e.segment,r=e.extractedMetadata,o=e.isSegmentOfMainStream,a=e.presentationTimeOffset;if(o){t=T(n);var s=n.getSegmentInfo();s.metadata&&s.metadata.forEach((function(e){i.processSegmentInfoMetadata(e,t,n.getPeriodId())})),S(s)&&this.handleCustomMetadata(s),this.handleId3Metadata(r,a,n.getPeriodId())}else t=y(n);this.registerSegmentPlaybackEvent(n,t),n.getInbandEvents().forEach((function(e){i.processInbandEvent(e,n.getPeriodId())})),this.discontinuityMonitor.onSegmentAvailable(n)},e.prototype.registerSegmentPlaybackEvent=function(e,t){if(!e.isInit()){var i=e.getSegmentInfo(),n=e.getMimeType(),r={metadata:{event:t,representationId:{_periodId:i.internalRepresentationId.periodId,_adaptationSetId:i.internalRepresentationId.adaptationSetId,_representationId:i.internalRepresentationId.representationId},mimeType:n}};this.metadataService.addToTimeline(u.TimedMetadataType.SegmentPlayback,t.playbackTime,r),(0,c.getMetricsState)(this.context.store.getState())[n]||this.context.store.dispatch((0,d.initializeMetricsForMimeType)(n,this.context.settings)),this.context.store.dispatch((0,d.addMetricsValue)(n,l.MetricType.SegmentInformation,{bitrate:8*e.getNetworkRequestSize()/e.getDuration(),duration:e.getDuration(),playbackTime:i.startTime||e.getPlaybackTime(),representationId:e.getRepresentationId()}))}},e.prototype.parseEventStream=function(){var e=this;this.manifestService.getAllPeriods().forEach((function(t){e.manifestService.getEventStreamEvents(t).forEach((function(i){e.processEventStreamEvent(i,t)}))}))},e.prototype.processEventStreamEvent=function(e,t){var i={metadataType:a.MetadataType.EVENT_STREAM,metadata:e.data,start:e.startTime,end:e.endTime};this.metadataService.addToMetadataParsedService(e.startTime,n(n({},i),{data:e.data}),t._id),this.metadataService.addToTimeline(u.TimedMetadataType.EventStream,e.startTime,i)},e.prototype.getPlaybackRepresentation=function(e){var t=this.context.serviceManager.get(r.ServiceName.ManifestService,this.context.sourceContext.sourceIdentifier),i=null==t?void 0:t.getRepresentationById(this.playbackRepresentationId[e]);if(i)return i=n({},i),this.currentlyPlayedSegment[e]&&(i.uid=this.currentlyPlayedSegment[e].uid),i},e.prototype.trackContentBoundary=function(e,t){this.periodSwitchMonitor.trackIncomingPeriod(e)},e.prototype.resetContentBoundaryTracking=function(){this.periodSwitchMonitor.reset()},e.prototype.handlePlaybackInfo=function(e){var t,i;p.MimeTypeHelper.isAudio(e.mimeType)?(t=a.PlayerEvent.AudioPlaybackQualityChanged,i={id:e.representationId,bitrate:parseFloat(e.mediaInfo.bitrate)}):p.MimeTypeHelper.isVideo(e.mimeType)&&(t=a.PlayerEvent.VideoPlaybackQualityChanged,i={id:e.representationId,bitrate:parseFloat(e.mediaInfo.bitrate),width:parseFloat(e.mediaInfo.width),height:parseFloat(e.mediaInfo.height)}),t&&this.context.eventHandler.dispatchEvent(t,{targetQuality:i,sourceQuality:this.previousRepresentationIds[e.mimeType]||null}),this.previousRepresentationIds[e.mimeType]=i},e.prototype.processDateRangeEvent=function(e,t){var i={metadataType:a.MetadataType.DATERANGE,metadata:e.data,start:e.startTime,end:e.endTime};this.metadataService.addToMetadataParsedService(e.startTime,n(n({},i),{data:e.data}),t),this.metadataService.addToTimeline(u.TimedMetadataType.DateRange,e.startTime,i)},e.prototype.addSegmentToMetadataParsedService=function(e,t){var i=this;e.forEach((function(e){if(e._metadata&&e._metadata.length>0&&e._playbackTime){var n={metadataType:a.MetadataType.CUSTOM,metadata:e._metadata,data:e._metadata,start:e._playbackTime,end:e._playbackTime+e._duration};i.metadataService.addToMetadataParsedService(e._playbackTime,n,t)}}))},e.prototype.handleId3Metadata=function(e,t,i){var n=this;e&&Array.isArray(e.id3)&&e.id3.length>0&&e.id3.forEach((function(e){n.publishId3Metadata(e,t,i)}))},e.prototype.publishId3Metadata=function(e,t,i){var r=e.presentationTime-t,o={metadataType:a.MetadataType.ID3,metadata:e.data,start:r};this.metadataService.addToMetadataParsedService(r,n(n({},o),{data:e.data}),i),this.metadataService.addToTimeline(u.TimedMetadataType.InBand,r,o)},e.prototype.handleCustomMetadata=function(e){var t=this,i=this.manifestService.getRepresentationById(e.internalRepresentationId);i.SegmentList[0].SegmentURL.forEach((function(n,r){n._media===e.url&&(n._playbackTime||v(i.SegmentList[0].SegmentURL,r,e.startTime),t.metadataService.addToTimeline(u.TimedMetadataType.CustomTag,e.startTime,{metadataType:a.MetadataType.CUSTOM,metadata:n._metadata,start:e.startTime,end:e.startTime+e.duration}))})),this.addSegmentToMetadataParsedService(i.SegmentList[0].SegmentURL,e.periodId)},e.prototype.processInbandEvent=function(e,t){var i=e.presentationTime,r={metadataType:a.MetadataType.EMSG,metadata:e,start:i};this.metadataService.addToMetadataParsedService(i,n(n({},r),{data:e}),t),this.metadataService.addToTimeline(u.TimedMetadataType.InBand,i,r)},e.prototype.processSegmentInfoMetadata=function(e,t,i){var r=e.type;if(r===a.MetadataType.CUSTOM)t.EXPERIMENTAL=t.EXPERIMENTAL||{},t.EXPERIMENTAL.hlsAttributes=t.EXPERIMENTAL.hlsAttributes||[],t.EXPERIMENTAL.hlsAttributes=t.EXPERIMENTAL.hlsAttributes.concat(e.attributes);else{var o={metadataType:/^CUE-(IN|OUT(-CONT)?)$/.test(r)?a.MetadataType.CUETAG:a.MetadataType[e.type],metadata:e,start:t.playbackTime};this.metadataService.addToMetadataParsedService(o.start,n(n({},o),{data:e}),i),this.metadataService.addToTimeline(u.TimedMetadataType.Manifest,o.start,o)}},e.prototype.dispose=function(){this.metadataService=(0,o.dispose)(this.metadataService),this.manifestService=null,this.discontinuityMonitor=(0,o.dispose)(this.discontinuityMonitor),this.periodSwitchMonitor.dispose()},e}();function v(e,t,i){for(var n=t,r=i;n>=0;)e[n]._playbackTime=r,r-=e[n]._duration,n--;for(n=t+1,r=i+e[t]._duration;n<e.length;)e[n]._playbackTime=r,r+=e[n]._duration,n++}function S(e){return e.metadata&&e.metadata.length>0&&e.metadata[0].type===a.MetadataType.CUSTOM}function y(e){var t=e.getSegmentInfo();return{uid:btoa(e.getUrl()),url:e.getUrl(),mimeType:e.getMimeType(),playbackTime:e.getPlaybackTime(),presentationTimestamp:e.getBaseMediaDecodeTime()/e.getTimescale(),duration:e.getDuration(),mediaInfo:e.getMediaInfo(),wallClockTime:t.wallClockTime,discontinuitySequenceNumber:t.discontinuitySequenceNumber,periodId:void 0===t.discontinuitySequenceNumber?t.periodId:void 0}}function T(e){var t=y(e),i=e.getSegmentInfo();return i.dateTime&&(t.dateTime=i.dateTime),i.representationId&&(t.representationId=i.representationId),t}t.TimedMetadataService=m},38925:function(e,t,i){var n=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.RepresentationId=void 0;var r=i(67883),o=function(e){function t(t,i){var n=e.call(this,t.periodId,t.adaptationSetId)||this;return n._representationId=i,n}return n(t,e),Object.defineProperty(t.prototype,"representationId",{get:function(){return this._representationId},enumerable:!1,configurable:!0}),t.prototype.equals=function(t){return e.prototype.equals.call(this,t)&&this.representationId===(null==t?void 0:t.representationId)},t.prototype.key=function(){return e.prototype.key.call(this)+"-"+this.representationId},t.from=function(e){return e._representationId&&e._adaptationSetId&&e._periodId?new t(new r.AdaptationSetId(e._periodId,e._adaptationSetId),e._representationId):null},t}(r.AdaptationSetId);t.RepresentationId=o},40392:function(e,t){function i(e){return 8*e}Object.defineProperty(t,"__esModule",{value:!0}),t.bytesToBits=i},41661:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentCache=void 0,t.areSegmentsIdentical=a;var n=i(81361),r=i(86865),o=function(){function e(e){this.maxSize=Math.max(0,e),this.cache=[]}return e.prototype.contains=function(e){return this.cache.some((function(t){return a(e.getSegmentInfo(),t.getSegmentInfo())}))},e.prototype.maintainCacheSize=function(e){this.cache=0!==e?this.cache.slice(-e):[]},e.prototype.add=function(e){this.contains(e)||(this.cache.push(e),this.maintainCacheSize(this.maxSize))},e.prototype.get=function(e){return this.cache.find((function(t){return a(e,t.getSegmentInfo())}))},e.prototype.clear=function(){this.maintainCacheSize(0)},e.prototype.remove=function(e){this.cache=this.cache.filter((0,n.invert)(e))},e.prototype.getCapacity=function(){return this.maxSize},e.prototype.getCachedCount=function(){return this.cache.length},e.prototype.getCachedSegments=function(){return this.cache.slice(0)},e}();function a(e,t){return(e.isInitSegment?["url","periodId","byteRange","representationId"]:["url","periodId","byteRange","discontinuitySequenceNumber"]).every((function(i){return"object"==typeof e[i]?(0,r.compareValues)(e[i],t[i]):e[i]===t[i]}))}t.SegmentCache=o},41735:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.setManifestAction=o,t.setManifestInitializedAction=a,t.removeRepresentationsAction=s,t.updateManifestAction=u,t.updateAdaptationSetAction=d,t.updateRepresentationAction=c,t.updatePeriodTimingAction=l,t.adjustPeriodStartTimes=p,t.setRepresentationDrmKidAction=f,t.updateRepresentationFailedDownloadAction=h,t.setRepresentationSegmentIndexAction=g,t.setRepresentationIsLoadingAction=m,t.setRepresentationSegmentIndexParsingErrorAction=v,t.setRepresentationAnchorPointAction=S,t.initPlaybackTimesFromIndex=y,t.initPlaybackTimesFromReferenceSegment=T,t.resetSegmentPlaybackTimes=b;var n=i(15231),r=i(77329);function o(e){return(0,n.createAction)(r.ManifestActionType.SetManifest,{manifest:e})}function a(e){return(0,n.createAction)(r.ManifestActionType.SetManifestInitialized,{isInitialized:e})}function s(e){return(0,n.createAction)(r.ManifestActionType.RemoveRepresentations,{representationIds:e})}function u(e){return(0,n.createAction)(r.ManifestActionType.UpdateManifest,e)}function d(e,t){return(0,n.createAction)(r.ManifestActionType.UpdateAdaptationSet,{adaptationSetId:e,updates:t})}function c(e){return(0,n.createAction)(r.ManifestActionType.UpdateRepresentation,e)}function l(e,t){return(0,n.createAction)(r.ManifestActionType.UpdatePeriodTiming,{periodId:e,timing:t})}function p(e){return(0,n.createAction)(r.ManifestActionType.AdjustPeriodStartTimes,{offset:e})}function f(e,t){return(0,n.createAction)(r.ManifestActionType.SetRepresentationDrmKid,{representationId:e,associatedKid:t})}function h(e,t){return(0,n.createAction)(r.ManifestActionType.UpdateRepresentationFailedDownload,{representationId:e,loadFailureReason:t})}function g(e,t){return(0,n.createAction)(r.ManifestActionType.SetRepresentationSegmentIndex,{representationId:e,segmentIndex:t})}function m(e,t){return(0,n.createAction)(r.ManifestActionType.SetRepresentationIsLoading,{representationId:e,isLoading:t})}function v(e,t){return(0,n.createAction)(r.ManifestActionType.SetRepresentationSegmentIndexParsingError,{representationId:e,segmentIndexParsingError:t})}function S(e,t){return(0,n.createAction)(r.ManifestActionType.SetRepresentationAnchorPoint,{representationId:e,anchorPoint:t})}function y(e,t,i){return(0,n.createAction)(r.ManifestActionType.InitPlaybackTimesFromIndex,{representationId:e,index:t,playbackTimeForIndex:i})}function T(e){return(0,n.createAction)(r.ManifestActionType.InitPlaybackTimesFromReferenceSegment,{representationId:e.getRepresentationId(),segmentUrl:e.getUrl(),segmentPlaybackTime:e.getPlaybackTime(),discontinuitySequenceNumber:e.getSegmentInfo().discontinuitySequenceNumber})}function b(){return(0,n.createAction)(r.ManifestActionType.ResetSegmentPlaybackTimes)}},42346:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentStore=t.INITIAL_BUFFER_BLOCK_ID=void 0;var r=i(96873),o=i(33669),a=i(27279),s=i(42283),u=i(3941),d=i(80043),c=i(79814),l=i(91520),p=i(16368),f=i(16280),h=i(22645),g=i(55937),m=i(73022),v=i(19724);t.INITIAL_BUFFER_BLOCK_ID=0;var S=.01,y=function(){function e(e,i){this.context=e,this.delayedSegmentQueue=[],this.requiredMediaTypes={};var n=new g.BufferBlock(t.INITIAL_BUFFER_BLOCK_ID);this.requiredMediaTypes={},this.segregation=new v.Segregation(this.requiredMediaTypes,i),this.bufferBlocks=[n]}return e.prototype.getBufferBlockByPeriodId=function(e){return this.bufferBlocks.find((function(t){return t.hasTrackedPeriodId(e)}))},e.prototype.hasSegmentsForBufferBlock=function(e){return this.getAllSegmentsFromBufferBlock(e).hasNext()},e.prototype.isWaitingForSegments=function(){return 0!==this.delayedSegmentQueue.length},e.prototype.hasDataSegmentsForBufferBlock=function(e){return Object.keys(e.mediaTypes).filter((function(e){return c.MimeTypeHelper.isAV(e)})).every((function(t){return e.hasDataSegments(t)}))},e.prototype.getTargetBufferBlock=function(e,t){return this.getBufferBlockForPlaybackTime(e)||this.getClosestFutureBufferBlock(e)||this.getBufferBlockByPeriodId(t)},e.prototype.canSwitchToBufferBlock=function(e){if(!e)return!1;var t=this.areBufferBlockMediaTypesFinal(e);return this.hasDataSegmentsForBufferBlock(e)&&t},e.prototype.canSwitchToBufferBlockForTime=function(e){var t=this.getBufferBlockForPlaybackTime(e);return this.canSwitchToBufferBlock(t)},e.prototype.areBufferBlockMediaTypesFinalForPeriod=function(e){var t,i=this;if(void 0===this.requiredMediaTypes[e])return!1;var n=this.getBufferBlockByPeriodId(e);if(!n||0===Object.keys(null!==(t=null==n?void 0:n.mediaTypes)&&void 0!==t?t:{}).length)return!1;var r=Object.keys(n.mediaTypes);return Object.keys(this.requiredMediaTypes[e]).filter((function(e){return c.MimeTypeHelper.isAV(e)})).every((function(t){var n;return r.includes(t)&&(null===(n=i.requiredMediaTypes[e][t])||void 0===n?void 0:n.codec)}))},e.prototype.areBufferBlockMediaTypesFinal=function(e){var t,i=this;return!(!e||0===Object.keys(null!==(t=null==e?void 0:e.mediaTypes)&&void 0!==t?t:{}).length)&&e.getAllKnownPeriodIds().every((function(e){return i.areBufferBlockMediaTypesFinalForPeriod(e)}))},e.prototype.addRequiredMediaType=function(e,t){this.requiredMediaTypes[e]||(this.requiredMediaTypes[e]={}),this.requiredMediaTypes[e][t]||(this.requiredMediaTypes[e][t]={mimeType:t});var i=this.getBufferBlockByPeriodId(e);i&&(i.mediaTypes[t]||i.addMediaType({mimeType:t}))},e.prototype.changeMediaType=function(e,t){var i=this,n=Object.keys(this.requiredMediaTypes),r=t;n.forEach((function(n){t||(r=Object.keys(i.requiredMediaTypes[n]).find((function(t){return c.MimeTypeHelper.getMediaType(t)===c.MimeTypeHelper.getMediaType(e)}))),i.removeRequiredMediaType(n,r),i.addRequiredMediaType(n,e)}))},e.prototype.removeRequiredMediaType=function(e,t){if(this.requiredMediaTypes[e]){delete this.requiredMediaTypes[e][t];var i=this.getBufferBlockByPeriodId(e);i&&i.removeMediaType(t)}this.clearSegments(t)},e.prototype.updateRequiredMediaTypes=function(e,t){var i;if(this.requiredMediaTypes[e]){var r=this.requiredMediaTypes[e][t.mimeType];this.requiredMediaTypes[e][t.mimeType]=n(n({},r),t)}else this.requiredMediaTypes[e]=((i={})[t.mimeType]=t,i)},e.prototype.findBestMatchingBufferBlockForSegment=function(e,t){var i=this;if(1===this.bufferBlocks.length&&this.segregation.canAddSegmentToBufferBlock(e,this.bufferBlocks[0],t))return this.bufferBlocks[0];var n=this.bufferBlocks.reduce((function(n,r){return i.segregation.canAddSegmentToBufferBlock(e,r,t)&&(!n||r.getMinDistanceFromBufferBlock(e)<n.getMinDistanceFromBufferBlock(e))?r:n}),void 0);if(n){var r=n.getCommonPlaybackTimeRanges(),o=e.getPlaybackTime(),a=T(this.bufferBlocks.filter((function(e){return e!==n}))),s=M(r.start,a),u=b(r.end,a);if(s<=o&&o<=u)if(!this.isTimeInOrBeyondFutureIncompatibleBufferBlock(o,n,a))return n}},e.prototype.isTimeInOrBeyondFutureIncompatibleBufferBlock=function(e,t,i){return i.filter((function(e){return isFinite(e.range.start)&&e.range.start>t.getCommonPlaybackTimeRanges().start&&isFinite(e.range.end)&&e.range.end>t.getCommonPlaybackTimeRanges().end})).some((function(t){var i=t.range.start<=e&&e<=t.range.end,n=e>t.range.end;return i||n}))},e.prototype.areAllMimeTypesAvailableInQueue=function(e,t){var i=this,n=null!=e?e:u.DEFAULT_PERIOD_ID;return Object.keys(this.requiredMediaTypes[n]).filter((function(e){return c.MimeTypeHelper.isAV(e)})).every((function(e){return i.delayedSegmentQueue.some((function(i){return i.getMimeType()===e&&i.getSegmentInfo().discontinuitySequenceNumber===t&&i.getPeriodId()===n}))}))},e.prototype.areDiscontinuitiesMisaligned=function(e){var t,i=null===(t=(0,a.getSourceState)(this.context))||void 0===t?void 0:t.hls;return!!i&&(0,l.ModuleManager.get(p.ModuleName.HLS).selectors.areDiscontinuitiesMisaligned)(i,Object.keys(this.requiredMediaTypes[u.DEFAULT_PERIOD_ID]).filter((function(e){return c.MimeTypeHelper.isAV(e)})),e.getDuration())},e.prototype.addSegment=function(e){var t=this;if(I(e,this.bufferBlocks)){var i=e.getSegmentInfo(),n=i.discontinuitySequenceNumber,r=i.periodId;if(this.delayedSegmentQueue.push(e),this.context.logger.debug("[SegmentStore] Reached new content boundary ".concat(null!=n?n:r," with ").concat(e.getMimeType()," segment"),{url:e.getUrl()}),this.areAllMimeTypesAvailableInQueue(r,n)){var o=this.delayedSegmentQueue.filter((function(e){return e.getSegmentInfo().discontinuitySequenceNumber===n&&e.getSegmentInfo().periodId===r}));this.context.logger.debug("[SegmentStore] ".concat(n?"Discontinuity":"Period"," ").concat(null!=n?n:r," is ready: adding segments to store")),this.pushToBufferBlocks(o),this.delayedSegmentQueue=this.delayedSegmentQueue.filter((function(e){return e.getSegmentInfo().discontinuitySequenceNumber!==n||e.getSegmentInfo().periodId!==r}))}else this.areDiscontinuitiesMisaligned(e)&&(this.context.logger.debug("[SegmentStore] Detected misaligned discontinuities: flushing all delayed segments"),this.delayedSegmentQueue.forEach((function(e){return t.pushToBufferBlocks([e])})),this.delayedSegmentQueue=[])}else this.pushToBufferBlocks([e])},e.prototype.pushToBufferBlocks=function(e){var t=this,i=e.reduce((function(e,i){var n=t.segregation.getSegregationCriteria(i),r=t.findBestMatchingBufferBlockForSegment(i,n);return null===e?r:r!==e?void 0:r}),null);i||(i=this.createNewBufferBlock(),this.bufferBlocks.push(i)),e.forEach((function(e){var n=t.segregation.getSegregationCriteria(e);t.context.logger.debug("[SegmentStore] Added ".concat(e.getMimeType()," segment to BB with ID ").concat(i.getId()),{segment:e.getUrl(),segmentCriteria:n});var o=(0,a.getSourceStore)(t.context),s=(0,h.getTrackIdentifier)(e.getSegmentInfo());null==o||o.dispatch((0,r.removeLoadedRange)(s,(0,f.segmentToBufferBlockTimeRange)(e))),i.addSegment(e),null==o||o.dispatch((0,r.addLoadedRange)(s,(0,f.segmentToBufferBlockTimeRange)(e))),i.getSegregationCriteria(e.getMimeType())||i.setSegregationCriteria(n,e.getMimeType());var u=i.getMediaType(e.getMimeType());u&&t.updateRequiredMediaTypes(e.getPeriodId(),u)}))},e.prototype.getNewBufferBlockId=function(){var e=this.bufferBlocks[this.bufferBlocks.length-1];return e?e.getId()+1:t.INITIAL_BUFFER_BLOCK_ID},e.prototype.createNewBufferBlock=function(){var e=this.getNewBufferBlockId();return this.context.logger.debug("[SegmentStore] creating new BB with ID ".concat(e)),new g.BufferBlock(e)},e.prototype.getMediaTypes=function(e){var t=this;return e&&this.requiredMediaTypes[e]?Object.keys(this.requiredMediaTypes[e]).map((function(i){return t.requiredMediaTypes[e][i]})):[]},e.prototype.getAllBufferBlocks=function(){return this.bufferBlocks},e.prototype.getCurrentBufferBlockId=function(){var e,i=this.bufferBlocks[0];return null!==(e=null==i?void 0:i.getId())&&void 0!==e?e:t.INITIAL_BUFFER_BLOCK_ID},e.prototype.deleteOldBufferBlocks=function(e){this.bufferBlocks=this.bufferBlocks.filter((function(t){return t.getId()>=e&&t.getAllSegments().length>0}))},e.prototype.getActiveBufferBlock=function(){var e;return null!==(e=this.getBufferBlock(this.getCurrentBufferBlockId()))&&void 0!==e?e:null},e.prototype.getBufferBlock=function(e){return this.bufferBlocks.find((function(t){return t.getId()===e}))},e.prototype.getBufferBlockForSegment=function(e){return this.getBufferBlock(e.getBufferBlockId())},e.prototype.getBufferBlockForPlaybackTime=function(e){var t=this,i=this.bufferBlocks.filter((function(i){var n=0===e?t.getSmallestMaxSegmentDuration(i)-S:S;return Object.keys(i.mediaTypes).filter((function(e){return c.MimeTypeHelper.isAV(e)})).every((function(t){var r,o;return(null===(r=i.getPlaybackTimeRange(t))||void 0===r?void 0:r.start)<=e+n&&(null===(o=i.getPlaybackTimeRange(t))||void 0===o?void 0:o.end)>=e}))}));return i[i.length-1]},e.prototype.getClosestFutureBufferBlock=function(e){return this.bufferBlocks.filter((function(t){return t.getMinCommonStartTime()>=e})).sort((function(e,t){return e.getMinCommonStartTime()-t.getMinCommonStartTime()}))[0]},e.prototype.getNextSegment=function(){var e,t;return null!==(t=null===(e=this.getActiveBufferBlock())||void 0===e?void 0:e.getNextSegment())&&void 0!==t?t:null},e.prototype.removeSegment=function(e){var t=this.getBufferBlockForSegment(e);t&&t.removeSegment(e),this.delayedSegmentQueue=this.delayedSegmentQueue.filter((function(t){return t!==e}))},e.prototype.getAllSegmentsFromBufferBlock=function(e,t){if(!e)return d.EmptyIterator.getInstance();var i=e.getAllSegments();return t?new d.FilterIterator(new d.ArrayIterator(i),(function(e){return e.getMimeType()===t})):new d.ArrayIterator(i)},e.prototype.getNextBufferBlock=function(e){return this.bufferBlocks.find((function(t){return t.getId()>e}))},e.prototype.getFutureBufferBlocks=function(e){return this.bufferBlocks.filter((function(t){return t.getId()>=e}))},e.prototype.getPrecedingSegments=function(e,t){var i=this.delayedSegmentQueue.filter((function(i){return(0,g.isPrecedingSegment)(i,e,t)}));return this.bufferBlocks.flatMap((function(i){return i.getPrecedingSegments(e,t)})).concat(i)},e.prototype.clearSegments=function(e){this.context.logger.debug("[SegmentStore] clearing all segments for ".concat(e)),this.bufferBlocks.forEach((function(t){return t.clearSegments(e)})),this.delayedSegmentQueue=this.delayedSegmentQueue.filter((function(t){return t.getMimeType()!==e}))},e.prototype.getSmallestMaxSegmentDuration=function(e){return Boolean(e)&&e.hasMaxSegmentDurations()?e.getMinMaxSegmentDuration():-1},e.prototype.getSmallestSafeBufferSize=function(e,t){var i=this.getBufferBlockForPlaybackTime(e);return(null==i?void 0:i.hasMaxSegmentDuration(t))?i.getMaxSegmentDuration(t)+1:1},e.prototype.getAvailablePositionForTime=function(e,t){var i=e;if(t&&e>t){var n=this.getBufferBlockForPlaybackTime(e);n||(n=this.getClosestFutureBufferBlock(e))&&(i=n.getMinCommonStartTime())}return i},e.prototype.getPlaybackTimesForBufferBlock=function(e,t){var i=this,n=this.getBufferBlock(e);return t.map((function(e){var t=new d.FilterIterator(i.getAllSegmentsFromBufferBlock(n,e),(function(e){return!e.isInit()}));return t.hasNext()?t.next().getPlaybackTime():-1/0}))},e.prototype.hasStartedLoadingNextBufferBlock=function(e){var t=this.getNextBufferBlock(e);return!!t&&this.hasDataSegmentsForBufferBlock(t)},e.prototype.shouldSwitchBufferBlock=function(e,t){return!(0,m.areBufferBlocksEqual)(e,this.getBufferBlock(t))},e.prototype.getSegmentStoreRangesMap=function(e,t){var i={};return t.forEach((function(e){return i[e]=[]})),this.getFutureBufferBlocks(this.getCurrentBufferBlockId()).map((function(e){return e.getId()})).concat(1/0).map((function(t){return(0,o.getBufferBlockLoadedRanges)(e,t)})).forEach((function(e){return Object.keys(e).forEach((function(t){return i[t].push(e[t])}))})),i},e}();function T(e){return e.map((function(e){return{range:e.getCommonPlaybackTimeRanges(),block:e}})).sort((function(e,t){return e.range.start-t.range.start}))}function b(e,t){return t.reduce((function(t,i){return i.range.start>=e?Math.min(i.range.start,t):t}),1/0)}function M(e,t){return t.reduce((function(t,i){return i.range.end<=e?Math.max(i.range.start,t):t}),-1/0)}function I(e,t){var i=e.getSegmentInfo(),n=i.codecs,r=i.discontinuitySequenceNumber,o=i.periodId;return null!=r?!t.some((function(e){return e.hasTrackedDiscontinuity(r)}))&&!(0,s.isAVMuxedTogether)(n):void 0!==o&&!t.some((function(e){return e.hasTrackedPeriodId(o)}))}t.SegmentStore=y},43412:function(e,t,i){var n=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),r=this&&this.__decorate||function(e,t,i,n){var r,o=arguments.length,a=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,n);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(o<3?r(a):o>3?r(t,i,a):r(t,i))||a);return o>3&&a&&Object.defineProperty(t,i,a),a};Object.defineProperty(t,"__esModule",{value:!0}),t.ManifestUpdateScheduler=void 0,t.isRepresentationUpToDate=S;var o=i(28764),a=i(18665),s=i(90637),u=i(16937),d=i(76420),c=i(3464),l=i(34731),p=i(79814),f=i(331),h=i(41735),g=i(61743),m=i(35655),v=function(e){function t(t,i){var n=e.call(this,{onSuspend:function(){return n.onSuspend()},onRestore:function(){return n.onRestore()}})||this;n.initiated=!1,n.trackUpdaters={},n.selectActiveTracks=function(e){return null==e?void 0:e.activeTracks},n.handleActiveTracksChange=function(e){e&&n.manifestService.isHlsManifest()&&n.updateTracks(e)},n.selectPlaybackState=function(e){return e.player&&e.player.playbackState},n.handlePlaybackStateChange=function(e){e&&(e===d.PlaybackState.Paused?n.stop():e===d.PlaybackState.Playing&&n.start())},n.updatedRepresentation=function(e){n.updateAdaptationSetInManifest(e),n.manifestService.updateManifest(n.manifestService.getManifest()).catch((function(){n.logger.debug("Updating the manifest failed")}))},n.representationUpdateError=function(e,t){if(n.logger.debug("Rejection while updating the representation",e),e===s.RequestError.Failed||e===s.RequestError.TimedOut||e instanceof o.PlayerError){var i=e instanceof o.PlayerError?e.name:e;n.sourceStore.dispatch((0,h.updateRepresentationFailedDownloadAction)(t._internalId,i)),n.manifestService.updateManifest(n.manifestService.getManifest()).catch((function(e){n.logger.debug("Error while updating the manifest after the representation failed to load",e)}))}},n.manifestDownloadError=function(){n.logger.debug("Error while loading manifest")},n.context=t,n.sourceContext=i;var r=n.sourceContext.sourceIdentifier;return n.manifestService=t.serviceManager.get(a.ServiceName.ManifestService,r),n.manifestLoader=t.serviceManager.get(a.ServiceName.ManifestLoadingService,r),n.store=t.store,n.sourceStore=t.serviceManager.get(a.ServiceName.SourceStoreService,r),n.eventHandler=t.eventHandler,n.logger=t.logger,n.unsubscribeFromActiveTrackChanges=n.subscribeToActiveTrackChanges(),n.unsubscribeFromPausePlayingChanges=n.subscribeToPausePlayingChanges(t),n}return n(t,e),t.prototype.subscribeToActiveTrackChanges=function(){var e=this;return(0,c.subscribe)(this.sourceStore)((function(t){return e.selectActiveTracks(t)}),this.handleActiveTracksChange)},t.prototype.updateTracks=function(e){this.processNewActiveTrack(e),this.removeDeactivatedTrackUpdaters(e)},t.prototype.processNewActiveTrack=function(e){var t=this;Object.keys(e).filter((function(t){return e[t].selectedRepresentationId})).forEach((function(i){t.updateTrackUpdater(e[i].selectedRepresentationId)}))},t.prototype.removeDeactivatedTrackUpdaters=function(e){var t=this;Object.keys(this.trackUpdaters).filter((function(t){return!Object.keys(e).includes(t)})).forEach((function(e){t.trackUpdaters[e].stop(),delete t.trackUpdaters[e]}))},t.prototype.subscribeToPausePlayingChanges=function(e){return(0,c.subscribe)(this.store)(this.selectPlaybackState,this.handlePlaybackStateChange,(function(){return e.settings.STOP_DOWNLOAD_ON_PAUSE}))},t.prototype.updateTrackUpdater=function(e){var t=this.manifestService.getRepresentationById(e),i=e.adaptationSetId;if(this.trackUpdaters[i]){if(!this.trackUpdaters[i].getRepresentation()._internalId.equals(e)){this.trackUpdaters[i].setRepresentation(t)}}else this.trackUpdaters[t._internalId.adaptationSetId]=this.initializeTrackUpdater(t)},t.prototype.stopRepresentationUpdates=function(e){var t,i=this.trackUpdaters[e._internalId.adaptationSetId];return null!==(t=null==i?void 0:i.stopRepresentationUpdate(e))&&void 0!==t?t:Promise.resolve()},t.prototype.scheduleRepresentationUpdate=function(e){var t=this.trackUpdaters[e._internalId.adaptationSetId];null==t||t.setRepresentation(e),null==t||t.start()},t.prototype.initializeTrackUpdater=function(e){var t=this,i=new m.RepresentationUpdater(this.context,e,this.manifestLoader,{success:this.updatedRepresentation,error:function(e){return t.representationUpdateError(e,i.getRepresentation())}});return p.MimeTypeHelper.isAV(e._mimeType)||i.setRepresentation(e),i.start(),i},t.prototype.updateAdaptationSetInManifest=function(e){this.sourceStore.dispatch((0,h.updateRepresentationAction)(e))},t.prototype.init=function(e){var t=this;return this.manifestLoader.load(e).then((function(e){return t.manifestService.updateManifest(e).then((function(){t.initiated=!0,t.manifestService.isLive()&&!t.manifestService.isHlsManifest()&&t.scheduleManifestReloading(e)}))})).catch((function(e){return e instanceof o.PlayerError||e instanceof Error?Promise.reject(e):Promise.reject(new Error("The loading of the manifest has failed or was cancelled"))}))},t.prototype.scheduleManifestReloading=function(e){var t=this;this.manifestUpdater=new g.ManifestUpdater(this.context,this.manifestLoader,e,{minAllowedUpdatePeriod:this.context.settings.MINIMUM_ALLOWED_UPDATE_PERIOD,maxAllowedUpdatePeriod:this.context.settings.MAXIMUM_ALLOWED_UPDATE_PERIOD},{success:function(e){return t.manifestService.updateManifest(e)},error:this.manifestDownloadError}),this.manifestUpdater.start()},t.prototype.waitForRepUpdate=function(e){return 0===Object.keys(this.trackUpdaters).length||S(this.manifestService.getRepresentationById(e))?Promise.resolve():this.trackUpdaters[e.adaptationSetId].updateRepresentation().then((function(){}))},t.prototype.stop=function(){Object.values(this.trackUpdaters).forEach((function(e){return e.stop()})),this.manifestUpdater&&this.manifestUpdater.stop()},t.prototype.start=function(){Object.values(this.trackUpdaters).filter((function(e){return e.isStopped})).forEach((function(e){e.start()})),this.manifestUpdater&&this.manifestUpdater.isStopped&&this.manifestUpdater.start()},t.prototype.onSuspend=function(){this.unsubscribeFromPausePlayingChanges()},t.prototype.onRestore=function(){this.unsubscribeFromPausePlayingChanges=this.subscribeToPausePlayingChanges(this.context)},t.prototype.isInitiated=function(){return this.initiated},t.prototype.dispose=function(){var t;this.unsubscribeFromActiveTrackChanges(),this.unsubscribeFromPausePlayingChanges(),this.stop(),null===(t=this.manifestUpdater)||void 0===t||t.dispose(),Object.values(this.trackUpdaters).forEach((function(e){e.dispose()})),this.manifestUpdater=null,this.trackUpdaters=null,e.prototype.dispose.call(this)},r([(0,u.trackPerformance)("ManifestUpdateScheduler.init",!0)],t.prototype,"init",null),t}(l.Suspendable);function S(e){var t;return!!e._requestTimestamp&&e._requestTimestamp+(0,f.toMilliSeconds)(null!==(t=e._updateInterval)&&void 0!==t?t:1/0)>Date.now()}t.ManifestUpdateScheduler=v},43752:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.MPDExtractor=void 0,t.stripUnderscorePrefix=v,t.applyProperties=S,t.representationToQuality=y,t.getMinimumUpdatePeriodInSeconds=C,t.getContentTypeFromMimeTypeAndCodecs=E;var n=i(18665),r=i(33696),o=i(42055),a=i(42283),s=i(44388),u=i(79814),d=i(331),c=i(54838),l=i(91520),p=i(16368),f=i(41735),h=i(77874),g=i(77870),m=function(){function e(e,t){var i=this;this.isLive=function(){var e;return"dynamic"===(null===(e=i.getMpd())||void 0===e?void 0:e._type)},this.context=e,this.sourceContext=t,this.settings=e.settings,this.adjustedPeriodStartTimes=!1,this.synchronizedTimeService=e.serviceManager.get(n.ServiceName.SynchronizedTimeService,this.sourceContext.sourceIdentifier),this.sourceStore=e.serviceManager.get(n.ServiceName.SourceStoreService,this.sourceContext.sourceIdentifier)}return e.prototype.getMpd=function(){var e;return(0,h.getManifest)(null===(e=this.sourceStore)||void 0===e?void 0:e.getState())},e.prototype.getPeriods=function(){var e,t;return null!==(t=null===(e=this.getMpd())||void 0===e?void 0:e.Period)&&void 0!==t?t:[]},e.prototype.getAdaptationSets=function(e){var t=this.getPeriods().find((function(t){return t._id===e}));return t?t.AdaptationSet:[]},e.prototype.adjustPeriodStartTimes=function(){var e,t,i,n=null===(e=this.sourceStore)||void 0===e?void 0:e.getState();if(n&&!this.adjustedPeriodStartTimes&&!(null===(t=this.getMpd())||void 0===t?void 0:t._isHls)){var r=this.isLive()?(0,d.toSeconds)(this.getAvailabilityStartTime()):(0,o.getStartTimeOffset)(n);null===(i=this.sourceStore)||void 0===i||i.dispatch((0,f.adjustPeriodStartTimes)(r)),this.adjustedPeriodStartTimes=!0}},e.prototype.getAvailabilityStartTime=function(){var e;return this.isLive()&&(null===(e=this.getMpd())||void 0===e?void 0:e._availabilityStartTime)?c.Util.getUtcDate(this.getMpd()._availabilityStartTime).getTime()-this.getTimeDifference():0},e.prototype.getAvailabilityEndTime=function(){var e;if(null===(e=this.getMpd())||void 0===e?void 0:e.hasOwnProperty("_availabilityEndTime")){var t=c.Util.getUtcDate(this.getMpd()._availabilityEndTime).getTime();return new Date(t-this.getTimeDifference())}return null},e.prototype.getTimeDifference=function(){return this.synchronizedTimeService?this.synchronizedTimeService.getTimeDifference():0},e.prototype.getType=function(){var e;return null===(e=this.getMpd())||void 0===e?void 0:e._type},e.prototype.getDuration=function(){var e;return null===(e=this.getMpd())||void 0===e?void 0:e._mediaPresentationDuration},e.prototype.getRequestTimestamp=function(){var e;return null===(e=this.getMpd())||void 0===e?void 0:e._requestTimestamp},e.isValueWithinBounds=function(e,t,i){return!e||e>=t&&e<=i},e.prototype.isAllowedVideoQuality=function(t){var i=e.isValueWithinBounds(t.width,this.context.config.adaptation.resolution.minSelectableVideoWidth,this.context.config.adaptation.resolution.maxSelectableVideoWidth),n=e.isValueWithinBounds(t.height,this.context.config.adaptation.resolution.minSelectableVideoHeight,this.context.config.adaptation.resolution.maxSelectableVideoHeight),r=e.isValueWithinBounds(t.bitrate,this.settings.MIN_SELECTABLE_VIDEO_BITRATE,this.settings.MAX_SELECTABLE_VIDEO_BITRATE);return i&&n&&r},e.prototype.isAllowedAudioQuality=function(t){return e.isValueWithinBounds(t.bitrate,this.settings.MIN_SELECTABLE_AUDIO_BITRATE,this.settings.MAX_SELECTABLE_AUDIO_BITRATE)},e.prototype.getAllowedQualities=function(e,t){var i=this;if(!e)return[];var n=t.filter((function(t){return u.MimeTypeHelper.isVideo(e)&&i.isAllowedVideoQuality(t)||u.MimeTypeHelper.isAudio(e)&&i.isAllowedAudioQuality(t)}));return n.length<1&&t.length>0?t:n},e.prototype.getQualityForAdaptationSet=function(e){if(!e)return[];var t=e.Representation.map((function(t){return y(t,e._mimeType,e._codecs)}));return this.settings.EXCLUDE_DISALLOWED_REPRESENTATIONS&&(t=this.getAllowedQualities(e._mimeType,t)),t},e.prototype.getAudioQualities=function(e){var t=this,i=this.getValidPeriodId(e);return this.getAdaptationSets(i).reduce((function(e,i){return i._mimeType&&u.MimeTypeHelper.isAudio(i._mimeType)&&(e[i._internalId.adaptationSetId]=t.getQualityForAdaptationSet(i)),e}),{})},e.prototype.getVideoQualities=function(e){var t=this.getValidPeriodId(e),i=this.getAdaptationSets(t).find((function(e){return e._mimeType&&u.MimeTypeHelper.isVideo(e._mimeType)}));return i?this.getQualityForAdaptationSet(i):[]},e.prototype.getLangObjectFromAdaptationSet=function(e,t,i){var n={id:null,lang:null,adaptationSetId:null,kind:null,label:"off",url:null},r=e._mimeType&&e._mimeType.indexOf(t)>-1,o=i&&e._mimeType.indexOf(i)>-1;return u.MimeTypeHelper.isSubtitle(t)&&(n.kind="subtitle"),e.Role&&e.Role.length>0&&(n.role=e.Role.map((function(e){return S(e,{})}))),(r||o)&&(S(e,n,["_lang","_isFragmented"]),n.lang=n.lang||"und",n.label=e._label||n.lang,n.adaptationSetId=e._internalId,n.id=e._internalId.adaptationSetId),n},e.prototype.toSubtitleTrack=function(e,t,i){var n,r=this.getLangObjectFromAdaptationSet(e,t,i),o={id:r.id,lang:r.lang,kind:r.kind,isFragmented:null!==(n=r.isFragmented)&&void 0!==n&&n,isSideloaded:!1,enabled:!1,label:r.label};return null!=r.role&&(o.role=r.role),o.forced=R(o,e.Representation[0]),o},e.prototype.getAvailableSubtitles=function(e){var t=this,i=0;return e=this.getValidPeriodId(e),this.getAdaptationSets(e).filter((function(e){return u.MimeTypeHelper.isSubtitle(e._mimeType)})).map((function(e){var n=t.toSubtitleTrack(e,"application","text");return n.id||(i++,n.id="sub_".concat(i)),n}))},e.prototype.getClosedCaptionLabels=function(e){return e=this.getValidPeriodId(e),this.getAdaptationSets(e).filter((function(e){return e.ClosedCaptionLabels})).flatMap((function(e){return Object.keys(e.ClosedCaptionLabels).map((function(t){var i=e.ClosedCaptionLabels[t];return{id:t,kind:"captions",lang:i.lang||"unknown",label:i.label||"Captions (".concat(t,")")}}))}))},e.prototype.getAvailableAudio=function(e,t){var i=this;void 0===t&&(t=!0);var n=this.getValidPeriodId(e),o=this.getAdaptationSets(n),a=0,s=o.filter((function(e){return u.MimeTypeHelper.isAudio(e._mimeType)})).map((function(e){var t=i.getLangObjectFromAdaptationSet(e,r.MediaType.Audio);return t&&"off"!==t.label?(t.id||(t.id="audio_"+a,a++),t):null}));if(t){var d=o.find((function(e){return u.MimeTypeHelper.isVideo(e._mimeType)}));if(d&&d.ContentComponent){var c=d.ContentComponent.filter((function(e){return e._contentType===r.MediaType.Audio})).map((function(e){return{id:e._id,lang:e._lang,label:e._id,adaptationSetId:d._internalId}}));s=s.concat(c)}}return s.filter((function(e){return e}))},e.prototype.getAvailableVideo=function(e){var t=this.getValidPeriodId(e),i=this.getAdaptationSets(t).find((function(e){return u.MimeTypeHelper.isVideo(e._mimeType)}));return i?[this.getLangObjectFromAdaptationSet(i,r.MediaType.Video)]:[]},e.getContentProtectionForManifestElement=function(e){var t;return(null===(t=null==e?void 0:e.ContentProtection)||void 0===t?void 0:t.length)&&l.ModuleManager.has(p.ModuleName.DRM)?l.ModuleManager.get(p.ModuleName.DRM).ContentProtectionHelper.parseContentProtectionDescriptors(e.ContentProtection):[]},e.getContentProtectionForAdaptationSet=function(t){for(var i=[],n=0;n<t.Representation.length;n++)i=i.concat(e.getContentProtectionForManifestElement(t.Representation[n]));return i=i.concat(e.getContentProtectionForManifestElement(t))},e.prototype.getTimeShiftBufferDepthSeconds=function(){if(this.isLive()){var e=this.getMpd()._timeShiftBufferDepth,t=(0,d.toSeconds)(Date.now()-this.getAvailabilityStartTime());return e===Number.NEGATIVE_INFINITY?-t:Math.max(e,-t)}return 0},e.getCurrentAndSuccessorPeriod=function(e,t){for(var i=[null,0,null],n=i[0],r=i[1],o=i[2];r<e.length;r++)if(e[r]._id===t){n=e[r],o=e[r+1]||null;break}return[n,o]},e.prototype.getPeriodDuration=function(t,i){var n;i=null!=i?i:this.getPeriods();var r=e.getCurrentAndSuccessorPeriod(i,t),o=r[0],a=r[1];if(o&&o._duration)return s.DurationConverter.getDurationInSec(o._duration);if(o&&o.start&&a&&a.start)return a.start-o.start;if(null===(n=this.getMpd())||void 0===n?void 0:n._mediaPresentationDuration){var u=s.DurationConverter.getDurationInSec(this.getMpd()._mediaPresentationDuration);if(!isNaN(u))return u}return Number.POSITIVE_INFINITY},e.prototype.getDRMCapabilitiesForPeriod=function(e){e=this.getValidPeriodId(e);var t=this.getPeriods().find((function(t){return t._id===e}));return this.getDRMCapabilitiesFromPeriod(t)},e.prototype.getDRMCapabilitiesFromPeriod=function(t){for(var i=[],n=[],r=0,o=t.AdaptationSet;r<o.length;r++){var a=o[r],s=a._mimeType,d=e.getMimeTypeCodecStringForAdaptationSet(a);u.MimeTypeHelper.isVideo(s)?n=n.concat(d):u.MimeTypeHelper.isAudio(s)&&(i=i.concat(d))}return[i,n]},e.prototype.dispose=function(){this.settings=null},e.getMimeTypeCodecStringForAdaptationSet=function(t){for(var i=[],n=0,r=t.Representation;n<r.length;n++){var o=r[n],a=e.getAttributeFromRepresentationOrAdaptationSet("_codecs",o,t),s=t._mimeType;if(t._mimeType&&a){var u=E(s,a);i.push({contentType:u})}}return i},e.getAttributeFromRepresentationOrAdaptationSet=function(e,t,i){return t.hasOwnProperty(e)&&t[e]?t[e]:i.hasOwnProperty(e)&&i[e]?i[e]:null},e.prototype.getValidPeriodId=function(e){var t=this.getPeriods();if(t.find((function(t){return t._id===e})))return e;var i=t[t.length-1]._id;return this.context.logger.debug("Period "+e+" not available - return last period ("+i+")"),i},e.prototype.getEventStreamEvents=function(e){var t=this;return e&&e.EventStream?e.EventStream.filter((function(e){return e.Event})).flatMap((function(i){var n,r=Number(null!==(n=i._timescale)&&void 0!==n?n:1);return t.mapToEventStreamEvent(e.start,i,r)})):[]},e.prototype.mapToEventStreamEvent=function(e,t,i){var n=this;return t.Event.map((function(r){var o={},a=r._presentationTime||0;if(o.startTime=a/i+e,r._duration){var s=r._duration/i;o.endTime=o.startTime+s}else o.endTime=o.startTime;return o.data=n.getEventData(r,t._schemeIdUri),o}))},e.prototype.getEventData=function(e,t){var i=/<event[^>]*>(.*)(<\/event>|\/>)/g.exec(e.rawXmlRepresentation),n=void 0;i&&i.length>1&&(n=i[1]);var r={};return Object.getOwnPropertyNames(e).filter((function(e){return!["_presentationTime","_duration","__text"].some((function(t){return t===e}))&&0===e.lastIndexOf("_",0)})).forEach((function(t){r[t.substring(1,t.length)]=e[t]})),{properties:r,content:n,schemeIdUri:t}},e}();function v(e){return 0===e.indexOf("_")?e.substring(1):e}function S(e,t,i){return void 0===i&&(i=[]),Object.keys(e).filter((function(e){return 0===i.length||i.indexOf(e)>-1})).forEach((function(i){t[v(i)]=e[i]})),t}function y(e,t,i){if(!e)return null;var n={id:e._id?String(e._id):null,bitrate:e._bandwidth||0},r=a.CodecStringHelper.getExtractedCodecStrings(e._codecs||i);return M(e,t)?T(n,e,r):b(e,t)?I(n,e,r):(n.label=e._label||P(n.bitrate),n)}function T(e,t,i){var n,r;i.audio&&(e.codec=i.audio);var o=null===(r=null===(n=t.AudioChannelConfiguration)||void 0===n?void 0:n.find((function(e){var t=e._schemeIdUri,i=e._value;return t===g.DashSchemeUri.AudioChannelConfig&&void 0!==i})))||void 0===r?void 0:r._value;return o&&(e.channels=parseInt(o)),e.label=t._label||P(e.bitrate),e}function b(e,t){return u.MimeTypeHelper.isVideo(t)}function M(e,t){return u.MimeTypeHelper.isAudio(t)}function I(e,t,i){e.width=t._width||0,e.height=t._height||0,i.video&&(e.codec=i.video),t.hasOwnProperty("_frameRate")&&(e.frameRate=t._frameRate);var n=P(e.bitrate);return e.width&&e.height&&(n="".concat(e.width,"x").concat(e.height,", ").concat(n)),e.label=t._label||n,e}function P(e){return"".concat(Math.round(e/1e3),"kbps")}function R(e,t){var i,n;return Boolean(null===(i=e.role)||void 0===i?void 0:i.some((function(e){return e.value&&["forced_subtitle","forced-subtitle"].includes(e.value)})))||Boolean(null===(n=null==t?void 0:t._hls)||void 0===n?void 0:n.isForced)}function C(e){return null==(null==e?void 0:e._minimumUpdatePeriod)?1/0:s.DurationConverter.getDurationInSec(e._minimumUpdatePeriod)}function E(e,t){return"".concat(e,'; codecs="').concat(t,'"')}t.MPDExtractor=m},44910:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.MseModuleDefinition=void 0;var n=i(96873),r=i(33669),o=i(16368),a=i(73543),s=i(67883),u=i(48928),d=i(94938),c=i(38925),l=i(85586),p=i(10997),f=i(26382),h=i(58423),g=i(13788),m=i(67550),v=i(92395),S=i(72207),y=i(4053),T=i(82311),b=i(58211),M=i(93326),I=i(59839),P=i(22645),R=i(23414),C={removeStreamTimeRange:I.removeStreamTimeRange,removeLoadedRange:n.removeLoadedRange,addLoadedRange:n.addLoadedRange,resetLoadedRanges:n.resetLoadedRanges,resetStreamTimeline:I.resetStreamTimeline,removeActiveTrackAction:y.removeActiveTrackAction,setRepresentationIdAction:y.setRepresentationIdAction,setMediaTypeAction:y.setMediaTypeAction},E={getPlayingPeriodId:b.getPlayingPeriodId,getContainerFormat:T.getContainerFormat,getLoadedRangesForMimeType:r.getLoadedRangesForMimeType,getSegmentInfos:M.getSegmentInfos},A={containerFormat:a.ContainerFormat,streamTimeRangeType:P.StreamTimeRangeType};t.MseModuleDefinition={name:o.ModuleName.EngineBitmovin,module:{MediaPlayer:l.MediaPlayer,SegmentController:h.SegmentController,technologyChecker:new R.TechnologyChecker,Stream:S.Stream,FetchController:f.FetchController,SegmentInfoService:g.SegmentInfoService,SegmentPrefetchingService:v.SegmentPrefetchingService,AdRestorationOptimizationService:p.AdRestorationOptimizationService,ManifestCachingService:u.ManifestCachingService,SegmentListMPDHandler:m.SegmentListMPDHandler,actions:C,selectors:E,mseModuleTypes:A,AdaptationSetId:s.AdaptationSetId,RepresentationId:c.RepresentationId,getTrackIdentifier:P.getTrackIdentifier,getCodecsFromAdaptationSet:d.getCodecsFromAdaptationSet,getMimeTypeForAdaptationSet:d.getMimeTypeForAdaptationSet}},t.default=t.MseModuleDefinition},46678:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentLoaderPool=void 0,t.getLoaderPoolSize=S;var n=i(76650),r=i(28819),o=i(8272),a=i(27279),s=i(27177),u=i(79814),d=i(70016),c=i(91520),l=i(16368),p=i(59839),f=i(22645),h=i(28915),g=i(66864),m=1,v=function(){function e(e,t){this.context=e,this.mimeType=t.mimeType,this.loaders=[],this.shouldDownloadBeCancelledCallback=t.shouldDownloadBeCancelledCallback,this.addLoaders(S(this.mimeType,e.settings.SEGMENT_LOADER_POOL_SIZE)),this.attachDecrypter()}return e.prototype.addLoaders=function(e){for(var t=0;t<e;t++)this.loaders.push(new g.SegmentLoader(this.context,this.mimeType,this.shouldDownloadBeCancelledCallback))},e.prototype.getDecrypter=function(e){if(this.context.segmentPrefetchingService&&this.context.segmentPrefetchingService.hasMp4Decrypter(this.mimeType))return this.context.segmentPrefetchingService.getDecrypter(this.mimeType);var t=c.ModuleManager.get(l.ModuleName.Crypto).createDecrypter,i=b(e.drm.clearkey)?e.drm.clearkey:[];return t(this.context,this.mimeType,i)},e.prototype.getClearKeyLoader=function(e){return new(0,c.ModuleManager.get(l.ModuleName.Crypto).ClearKeyLoader)(this.context,e)},e.prototype.attachDecrypter=function(){var e=this.context.sourceContext.source,t=y(e)?this.getDecrypter(e):null;if(t&&(this.loaders.forEach((function(e){return e.attachDecrypter(t)})),this.context.segmentPrefetchingService&&this.context.segmentPrefetchingService.setDecrypter(t,this.mimeType),T(e.drm.clearkey))){var i=this.getClearKeyLoader(e.drm.clearkey);this.loaders.forEach((function(e){return e.attachClearKeyLoader(i)}))}},e.prototype.isLoading=function(){return this.loaders.some((function(e){return e.isLoading()}))},e.prototype.isFreeLoaderAvailable=function(){return this.loaders.some((function(e){return!e.isLoading()}))},e.prototype.updateRequestedRepresentationMetric=function(e){var t=(0,r.getMetricsHistoryFromInstanceState)(this.context.store.getState(),"default",o.MetricType.RequestedRepresentations);(t?t.map((function(e){return e.value})):[]).includes(e)||this.context.store.dispatch((0,n.addMetricsValue)("default",o.MetricType.RequestedRepresentations,e))},e.prototype.load=function(e){var t=this.loaders.find((function(e){return!e.isLoading()}));if(!t)return this.context.logger.debug("Failed to load segment. No free loader!",e.url),Promise.reject(null);this.updateRequestedRepresentationMetric(e.internalRepresentationId),this.context.logger.debug("[SegmentLoader ".concat(this.loaders.indexOf(t),"][").concat(e.mimeType,"][").concat(e.representationId,"] Loading ").concat(e.startTime," ").concat(e.url));var i=(0,h.getSegmentInfoTimeRange)(e),n=(0,f.getTrackIdentifier)(e),r=(0,a.getSourceStore)(this.context);return i&&(null==r||r.dispatch((0,p.addStreamTimeRange)(n,i,f.StreamTimeRangeType.Loading))),t.load(e).catch((function(e){throw i&&(null==r||r.dispatch((0,p.removeStreamTimeRange)(n,i,f.StreamTimeRangeType.Loading))),e}))},e.prototype.cancelLoading=function(){var e=this;return Promise.all(this.loaders.map((function(e){return e.isLoading()&&e.cancelLoading(),Promise.resolve(e.getCurrentLoadPromise())}))).then((function(){})).catch((function(t){(null==t?void 0:t.reason)!==g.SegmentLoadingErrorReason.CANCEL&&(0,s.isContextAvailable)(e.context)&&e.context.logger.debug("Error while loading a segment ",t)}))},e.prototype.getLoadingPeriodIds=function(){return this.loaders.flatMap((function(e){return e.getAllLoadingPeriodIds()}))},e.prototype.dispose=function(){this.cancelLoading(),this.loaders.forEach((function(e){return e.dispose()})),this.loaders=null},e}();function S(e,t){var i=u.MimeTypeHelper.extractContentType(e);return t&&(0,d.isNumber)(t[i])?Math.max(m,t[i]):m}function y(e){var t,i;return b(null===(t=null==e?void 0:e.drm)||void 0===t?void 0:t.clearkey)||T(null===(i=null==e?void 0:e.drm)||void 0===i?void 0:i.clearkey)}function T(e){return Boolean(null==e?void 0:e.LA_URL)}function b(e){return(null==e?void 0:e.length)>0}t.SegmentLoaderPool=v},48727:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentInitType=t.Segment=void 0;var r,o=i(81361),a=i(79814),s=i(28915),u=function(){function e(e,t,i,n,a,s,u,d,c,l){void 0===s&&(s=r.NONE),this.inbandEvents=[],this.data=e,this.mimeType=t,this.codec=i,this.periodId=n,this.duration=a,this.initType=s,this.isSegmentIndependentlyDecodable=u,this.encrypted=!1,this.metadata={subtitles:[]},this.networkRequestSize=d,this.representationId=c,this.playbackTime=l,this._isDirty=!1,this.drmInitData=null,e instanceof ArrayBuffer||"string"==typeof e&&(this.data=o.ArrayHelper.stringToArrayWithoutEncoding(e).buffer)}return e.prototype.setDrmKid=function(e){this.drmKid=e},e.prototype.hasDrmKid=function(){return Boolean(this.getDrmKid())},e.prototype.getDrmKid=function(){var e,t,i;return null!==(i=null!==(e=this.drmKid)&&void 0!==e?e:null===(t=this.initSegment)||void 0===t?void 0:t.getDrmKid())&&void 0!==i?i:void 0},e.prototype.setDrmInitData=function(e){this.drmInitData=e},e.prototype.hasDrmInitData=function(){var e;return(null===(e=this.drmInitData)||void 0===e?void 0:e.length)>0},e.prototype.getDrmInitData=function(){return this.hasDrmInitData()?this.drmInitData:[]},e.prototype.getMimeType=function(){return this.mimeType},e.prototype.isInit=function(){return this.initType===r.INIT},e.prototype.isSelfInit=function(){return this.initType===r.SELF_INIT},e.prototype.isEncrypted=function(){return this.encrypted},e.prototype.setEncrypted=function(e){this.encrypted=e},e.prototype.setUrl=function(e){this.loadedUrl=e},e.prototype.getUrl=function(){return this.loadedUrl},e.prototype.getData=function(){return this.data},e.prototype.setData=function(e){this.data=e},e.prototype.getMetadata=function(){return this.metadata},e.prototype.setMetadata=function(e){this.metadata=e},e.prototype.getPlaybackTime=function(){return this.playbackTime},e.prototype.setPlaybackTime=function(e){this.playbackTime=e},e.prototype.getPlaybackEndTime=function(){return this.playbackTime+this.duration},e.prototype.getPlaybackTimeRange=function(){return this.duration?{start:this.getPlaybackTime(),end:this.getPlaybackEndTime()}:(0,s.getSegmentInfoTimeRange)(this.getSegmentInfo())},e.prototype.getBaseMediaDecodeTime=function(){return this.baseMediaDecodeTime},e.prototype.setBaseMediaDecodeTime=function(e){this.baseMediaDecodeTime=e},e.prototype.getPresentationTimeOffset=function(){return this.presentationTimeOffset},e.prototype.setPresentationTimeOffset=function(e){this.presentationTimeOffset=e},e.prototype.getTFDTBoxOffsets=function(){return this.tfdtBoxOffsets},e.prototype.setTFDTBoxOffsets=function(e){this.tfdtBoxOffsets=e},e.prototype.getParserMetadataValue=function(e){var t;return this.getParserMetadata()&&this.getParserMetadata()[e]?this.getParserMetadata()[e]:(null===(t=this.getInitSegment())||void 0===t?void 0:t.getParserMetadata())?this.getInitSegment().getParserMetadata()[e]:void 0},e.prototype.getTimescale=function(){return this.getParserMetadataValue("timescale")},e.prototype.getIvSize=function(){return this.getParserMetadataValue("ivSize")},e.prototype.getDefaultSampleDuration=function(){return this.getParserMetadataValue("defaultSampleDuration")},e.prototype.getDefaultSampleSize=function(){return this.getParserMetadataValue("defaultSampleSize")},e.prototype.setTimescale=function(e){this.updateParserMetadata({timescale:e})},e.prototype.getPeriodId=function(){return this.periodId},e.prototype.getCodec=function(){return this.codec},e.prototype.setCodec=function(e){this.codec=e},e.prototype.getDuration=function(){return this.duration},e.prototype.setDuration=function(e){this.duration=e},e.prototype.getNetworkRequestSize=function(){return this.networkRequestSize},e.prototype.getRepresentationId=function(){return this.representationId},e.prototype.getParsedData=function(){return this.parsedData},e.prototype.setParsedData=function(e){this.parsedData=e},e.prototype.getSegmentInfo=function(){return this.segmentInfo},e.prototype.isIndependentlyDecodable=function(){return this.isSegmentIndependentlyDecodable},e.prototype.setSegmentInfo=function(e){this.segmentInfo=e},e.prototype.getMediaInfo=function(){var e={};return this.segmentInfo&&this.segmentInfo.bitrate&&(e.bitrate=this.segmentInfo.bitrate),a.MimeTypeHelper.isAudio(this.mimeType)?this.segmentInfo&&this.segmentInfo.sampleRate&&(e.sampleRate=this.segmentInfo.sampleRate):a.MimeTypeHelper.isVideo(this.mimeType)&&(this.segmentInfo&&this.segmentInfo.width&&(e.width=this.segmentInfo.width),this.segmentInfo&&this.segmentInfo.height&&(e.height=this.segmentInfo.height),this.segmentInfo&&this.segmentInfo.frameRate&&(e.frameRate=this.segmentInfo.frameRate)),e},e.prototype.isDirty=function(){return this._isDirty},e.prototype.setDirty=function(e){this._isDirty=e},e.prototype.setInbandEvents=function(e){this.inbandEvents=e},e.prototype.getInbandEvents=function(){return this.inbandEvents},e.prototype.setLastSegment=function(e){this.lastSegment=e},e.prototype.isLastSegment=function(){return this.lastSegment},e.prototype.wasLoadedFrom=function(e){return this.getUrl().includes(e)},e.prototype.getParserMetadata=function(){return this.parserMetadata},e.prototype.setParserMetadata=function(e){this.parserMetadata=e},e.prototype.updateParserMetadata=function(e){this.parserMetadata=n(n({},this.parserMetadata),e)},e.prototype.setBufferBlockId=function(e){this.bufferBlockId=e},e.prototype.getBufferBlockId=function(){return this.bufferBlockId},e.prototype.setInitSegment=function(e){this.initSegment=e},e.prototype.getInitSegment=function(){return this.initSegment},e}();t.Segment=u,function(e){e[e.NONE=0]="NONE",e[e.INIT=1]="INIT",e[e.SELF_INIT=2]="SELF_INIT"}(r||(t.SegmentInitType=r={}))},48928:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.ManifestCachingService=void 0;var n=i(76885),r=function(){function e(){this.cache={}}return e.prototype.set=function(e,t,i){var r=n.URLHelper.toFullUrl(e);o(r,this.cache),this.cache[r][t]=i},e.prototype.setExpire=function(e,t){var i=n.URLHelper.toFullUrl(e);o(i,this.cache),this.cache[i].expire=t},e.prototype.get=function(e,t){var i=n.URLHelper.toFullUrl(t),r=this.cache[i];if(r&&a(r.expire))return r[e];delete this.cache[i]},e.prototype.cacheHttpResponse=function(e,t,i){void 0===i&&(i=1/0),this.set(t,"httpResponse",e),this.setExpire(t,i)},e.prototype.cacheParsedManifest=function(e,t,i){void 0===i&&(i=1/0),this.set(t,"parsedManifest",e),this.setExpire(t,i)},e.prototype.cacheSegmentList=function(e,t,i){void 0===i&&(i=1/0),this.set(t,"segmentList",e),this.setExpire(t,i)},e.prototype.getHttpResponse=function(e){return this.get("httpResponse",e)},e.prototype.getParsedManifest=function(e){return this.get("parsedManifest",e)},e.prototype.getSegmentList=function(e){return this.get("segmentList",e)},e.prototype.clear=function(){this.cache={}},e.prototype.dispose=function(){this.cache={}},e}();function o(e,t){t[e]||(t[e]={})}function a(e){return e>=Date.now()}t.ManifestCachingService=r},49118:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.throwPlaylistDownloadError=a;var n=i(25550),r=i(28764),o=i(90637);function a(e,t,i,a){if(e.logger.debug(t),a instanceof r.PlayerError)e.eventHandler.fireError(a);else if(u(a)){var d=n.ErrorCode[a];e.eventHandler.fireError(new r.PlayerError(d,s(i),t))}else a!==o.RequestError.TimedOut?e.eventHandler.fireError(new r.PlayerError(n.ErrorCode.SOURCE_COULD_NOT_LOAD_MANIFEST,s(i),t)):e.eventHandler.fireError(new r.PlayerError(n.ErrorCode.NETWORK_MANIFEST_DOWNLOAD_TIMEOUT,s(i),"Failed to load the segment: the request timed out."))}function s(e){return{url:null==e?void 0:e.Uri,mimeType:null==e?void 0:e._mimeType,codecs:null==e?void 0:e._codecs,bitrate:null==e?void 0:e._bandwidth}}function u(e){return!!e&&Object.keys(n.ErrorCode).includes(e)}},49233:function(e,t,i){var n,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},o=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.BufferReducer=void 0;var a=i(92712),s=i(21829),u=i(18434),d=i(16280);function c(){return{isLoadingRangeFinished:!1,isBufferBlockSwitchOngoing:!1,isTimestampRollingOver:!1,bufferStateMap:{}}}function l(e,t){var i,n=t.payload;return r(r({},e),{bufferStateMap:r(r({},e.bufferStateMap),(i={},i[n.mimeType]=r(r({},e.bufferStateMap[n.mimeType]),{rendererRanges:n.ranges}),i))})}function p(e,t){var i,n=t.payload;return r(r({},e),{bufferStateMap:r(r({},e.bufferStateMap),(i={},i[n.mimeType]=r(r({},e.bufferStateMap[n.mimeType]),{maxBufferSize:n.maxBufferSize}),i))})}function f(e,t){var i=t.payload;return r(r({},e),{isBufferBlockSwitchOngoing:i.isOngoing})}function h(e,t){var i=t.payload;return r(r({},e),{isTimestampRollingOver:i.isRollingOver})}function g(e,t){var i,n,s,u=t.payload,c=u.mimeType,l=u.range;if(!c)return e;var p=null!==(s=null===(n=e.bufferStateMap[c])||void 0===n?void 0:n.loadedRanges)&&void 0!==s?s:[];return p.some((function(e){return(0,a.isFullyIncluded)(l,e)}))?e:r(r({},e),{bufferStateMap:r(r({},e.bufferStateMap),(i={},i[c]=r(r({},e.bufferStateMap[c]),{loadedRanges:d.mergeBufferBlockTimeRanges.apply(void 0,o(o([],p,!1),[l],!1))}),i))})}function m(e,t){var i,n,o,s=t.payload,u=s.mimeType,c=s.range;if(!u)return e;var l=null!==(o=null===(n=e.bufferStateMap[u])||void 0===n?void 0:n.loadedRanges)&&void 0!==o?o:[];return l.every((function(e){return!(0,a.isOverlapping)(c,e)}))?e:r(r({},e),{bufferStateMap:r(r({},e.bufferStateMap),(i={},i[u]=r(r({},e.bufferStateMap[u]),{loadedRanges:(0,d.excludeBufferBlockTimeRange)(l,c)}),i))})}function v(e,t){var i,n,o,a=t.payload.mimeType;if(void 0===a){if(void 0===e)return c();var s=r(r({},e),{bufferStateMap:r({},e.bufferStateMap)});return Object.keys(s.bufferStateMap).forEach((function(e){s.bufferStateMap[e]=r(r({},s.bufferStateMap[e]),{loadedRanges:[]})})),s}return(null!==(o=null===(n=e.bufferStateMap[a])||void 0===n?void 0:n.loadedRanges)&&void 0!==o?o:[]).length>0?r(r({},e),{bufferStateMap:r(r({},e.bufferStateMap),(i={},i[a]=r(r({},e.bufferStateMap[a]),{loadedRanges:[]}),i))}):e}function S(e,t){var i=t.payload.isFinished;return i!==e.isLoadingRangeFinished?r(r({},e),{isLoadingRangeFinished:i}):e}t.BufferReducer=(0,s.default)(c(),((n={})[u.BufferActionType.SetBufferMaxSize]=p,n[u.BufferActionType.SetRendererRanges]=l,n[u.BufferActionType.SetBufferBlockSwitchOngoing]=f,n[u.BufferActionType.SetTimestampRollingOver]=h,n[u.BufferActionType.AddLoadedRange]=g,n[u.BufferActionType.RemoveLoadedRange]=m,n[u.BufferActionType.ResetLoadedRanges]=v,n[u.BufferActionType.SetLoadingRangeFinished]=S,n))},51399:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.PeriodSwitchMonitor=void 0;var n=i(27279),r=i(72788),o=function(){function e(e){this.context=e,this.expectedPeriodIds=[]}return e.prototype.isPeriodExpected=function(e){return this.expectedPeriodIds.includes(e)},e.prototype.trackIncomingPeriod=function(e){this.expectedPeriodIds.includes(e)||this.expectedPeriodIds.push(e)},e.prototype.maybeHandlePeriodSwitch=function(e){var t,i,o=e.periodId,a=o!==this.currentPeriodId;o&&a&&this.expectedPeriodIds.includes(o)&&(this.currentPeriodId=o,null===(t=(0,n.getSourceStore)(this.context))||void 0===t||t.dispatch((0,r.periodSwitchStarted)(o)),null===(i=(0,n.getSourceStore)(this.context))||void 0===i||i.dispatch((0,r.periodSwitchFinished)(o)))},e.prototype.reset=function(){this.expectedPeriodIds=[]},e.prototype.dispose=function(){this.currentPeriodId=void 0,this.expectedPeriodIds=[]},e}();t.PeriodSwitchMonitor=o},55486:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.removeEncryptionKey=t.updateEncryptionKey=t.addEncryptionKey=void 0;var n=i(15231),r=i(56092),o=function(e){return(0,n.createAction)(r.EncryptionKeyActionKey.AddEncryptionKey,e)};t.addEncryptionKey=o;var a=function(e){return(0,n.createAction)(r.EncryptionKeyActionKey.UpdateEncryptionKey,e)};t.updateEncryptionKey=a;var s=function(e){return(0,n.createAction)(r.EncryptionKeyActionKey.RemoveEncryptionKey,{keyUri:e})};t.removeEncryptionKey=s},55937:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.BufferBlock=t.EncryptionState=void 0,t.isPrecedingSegment=u;var n,r=i(79814),o=i(37702);!function(e){e.Encrypted="encrypted",e.Clear="clear"}(n||(t.EncryptionState=n={}));var a=function(){function e(e){this.trackedDiscoNumbersMap={},this.trackedPeriodIdsMap={},this.id=e,this.cachedSegments=[],this.blockStateMap=new Map}return e.prototype.addSegment=function(e){var t=e.getMimeType();e.isInit()&&this.removeLastInitSegment(t),this.trackDiscontinuityNumber(e),this.trackPeriodId(e);var i=e.isInit()?0:e.getDuration();this.cachedSegments.push({segment:e}),e.isInit()||(this.setMaxSegmentDuration(t,Math.max(i,this.getMaxSegmentDuration(t))),this.updatePlaybackTimeRange(e,t)),e.setBufferBlockId(this.id),this.updateMediaTypes(e)},e.prototype.trackDiscontinuityNumber=function(e){var t=e.getSegmentInfo(),i=t.discontinuitySequenceNumber,n=t.mimeType;n&&(this.trackedDiscoNumbersMap[n]||(this.trackedDiscoNumbersMap[n]=[]),null==i||this.trackedDiscoNumbersMap[n].includes(i)||this.trackedDiscoNumbersMap[n].push(i))},e.prototype.trackPeriodId=function(e){var t=e.getSegmentInfo(),i=t.periodId,n=t.mimeType;n&&(this.trackedPeriodIdsMap[n]||(this.trackedPeriodIdsMap[n]=[]),null==i||this.trackedPeriodIdsMap[n].includes(i)||this.trackedPeriodIdsMap[n].push(i))},e.prototype.setSegregationCriteria=function(e,t){this.blockStateMap.has(t)||this.initializeBlockStateMapForMimeType(t),this.getBlockState(t).segregationCriteria=e},e.prototype.initializeBlockStateMapForMimeType=function(e){var t={};return this.blockStateMap.set(e,t),t},e.prototype.updateMediaTypeForMimeType=function(e){var t=this.getBlockState(e.mimeType)||this.initializeBlockStateMapForMimeType(e.mimeType);t.mediaType?(s(e,"codec",(function(e){t.mediaType.codec=e})),s(e,"timescale",(function(e){t.mediaType.timescale=e}))):t.mediaType=e},e.prototype.getSegregationCriteria=function(e){var t;return null===(t=this.getBlockState(e))||void 0===t?void 0:t.segregationCriteria},e.prototype.updateMediaTypes=function(e){var t,i,n=e.getMimeType(),r=this.getMediaType(n);r?(null!==(t=r.codec)&&void 0!==t||(r.codec=e.getCodec()),null!==(i=r.timescale)&&void 0!==i||(r.timescale=e.getTimescale())):(this.blockStateMap.has(n)||this.blockStateMap.set(n,{}),this.getBlockState(n).mediaType={mimeType:n,codec:e.getCodec(),timescale:e.getTimescale()})},e.prototype.getMediaType=function(e){var t,i;return null!==(i=null===(t=this.getBlockState(e))||void 0===t?void 0:t.mediaType)&&void 0!==i?i:null},e.prototype.addMediaType=function(e){r.MimeTypeHelper.isAV(e.mimeType)&&!this.getMediaType(e.mimeType)&&(this.blockStateMap.has(e.mimeType)||this.initializeBlockStateMapForMimeType(e.mimeType),this.getBlockState(e.mimeType).mediaType=e)},e.prototype.removeMediaType=function(e){this.clearSegments(e),this.blockStateMap.delete(e)},e.prototype.hasMediaType=function(e){var t,i;return null!==(i=null===(t=this.blockStateMap)||void 0===t?void 0:t.has(e))&&void 0!==i&&i},e.prototype.getMediaTypes=function(){return(0,o.getValues)(this.blockStateMap).map((function(e){return e.mediaType}))},e.prototype.getMediaTypesWithoutSubs=function(){return this.getMediaTypes().filter((function(e){return r.MimeTypeHelper.isAV(e.mimeType)}))},e.prototype.removeSegment=function(e){var t=this.cachedSegments.findIndex((function(t){return t.segment===e}));t>-1&&this.cachedSegments.splice(t,1)},e.prototype.clearSegments=function(e){this.cachedSegments=this.cachedSegments.filter((function(t){return t.segment.getMimeType()!==e}));var t=this.getBlockState(e);t&&(t.playbackTimeRange=void 0),this.trackedDiscoNumbersMap[e]=[]},e.prototype.getPrecedingSegments=function(e,t){var i=[];return this.cachedSegments.forEach((function(n){u(n.segment,e,t)&&i.push(n.segment)})),i},e.prototype.updatePlaybackTimeRange=function(e,t){var i=this.getPlaybackTimeRange(t),n=e.getPlaybackTime(),r=n+e.getDuration();i?(n<i.start&&(i.start=n),r>i.end&&(i.end=r)):this.getBlockState(t).playbackTimeRange={start:n,end:r}},e.prototype.getPlaybackTimeRange=function(e){var t,i;return null!==(i=null===(t=this.getBlockState(e))||void 0===t?void 0:t.playbackTimeRange)&&void 0!==i?i:null},e.prototype.getId=function(){return this.id},e.prototype.getAllKnownPeriodIds=function(){var e;return(e=[]).concat.apply(e,Object.values(this.trackedPeriodIdsMap))},e.prototype.getMinCommonStartTime=function(){return(0,o.getValues)(this.blockStateMap).filter((function(e){return e.playbackTimeRange})).map((function(e){return e.playbackTimeRange})).reduce((function(e,t){return Math.max(t.start,e)}),-1/0)},e.prototype.getCommonPlaybackTimeRanges=function(){return(0,o.getValues)(this.blockStateMap).filter((function(e){return e.playbackTimeRange})).map((function(e){return e.playbackTimeRange})).reduce((function(e,t){return{start:Math.max(t.start,e.start),end:Math.min(t.end,e.end)}}),{start:-1/0,end:1/0})},e.prototype.setMaxSegmentDuration=function(e,t){this.blockStateMap.has(e)||this.initializeBlockStateMapForMimeType(e),this.getBlockState(e).maxSegmentDuration=t},e.prototype.getMaxSegmentDuration=function(e){var t;return(null===(t=this.getBlockState(e))||void 0===t?void 0:t.maxSegmentDuration)||0},e.prototype.hasMaxSegmentDuration=function(e){var t;return null!=(null===(t=this.getBlockState(e))||void 0===t?void 0:t.maxSegmentDuration)},e.prototype.hasMaxSegmentDurations=function(){return Boolean((0,o.getValues)(this.blockStateMap).map((function(e){return e.maxSegmentDuration})).find((function(e){return e>=0})))},e.prototype.getMinMaxSegmentDuration=function(){var e=Number.MAX_VALUE;return(0,o.getValues)(this.blockStateMap).map((function(e){return e.maxSegmentDuration})).forEach((function(t){return e=t<e?t:e})),e},e.prototype.getAllSegmentsForMimeType=function(e){return this.getAllSegments().filter((function(t){return t.getMimeType()===e}))},e.prototype.getAllSegments=function(){return this.cachedSegments.map((function(e){return e.segment}))},e.prototype.hasDataSegments=function(e){return this.cachedSegments.some((function(t){return!t.segment.isInit()&&t.segment.getMimeType()===e}))},e.prototype.hasSegments=function(){return this.getAllSegments().length>0},e.prototype.hasTrackedDiscontinuity=function(e){var t=this;return Object.keys(this.trackedDiscoNumbersMap).some((function(i){return t.trackedDiscoNumbersMap[i].includes(e)}))},e.prototype.hasTrackedPeriodId=function(e){var t=this;return Object.keys(this.trackedPeriodIdsMap).some((function(i){return t.trackedPeriodIdsMap[i].includes(e)}))},e.prototype.getNextSegment=function(){var e=this.cachedSegments.filter((function(e){return!e.pendingRemoval}));if(0===e.length)return null;var t=e.find((function(t){return e.filter((function(e){return t.segment.getMimeType()===e.segment.getMimeType()})).some((function(e){return!e.segment.isInit()}))}));return t?(t.pendingRemoval=!0,t.segment):null},e.prototype.removeLastInitSegment=function(e){var t=this.getAllSegmentsForMimeType(e);if(t.length>0){var i=t[t.length-1];i.isInit()&&this.removeSegment(i)}},e.prototype.getBlockState=function(e){return this.blockStateMap.get(e)},Object.defineProperty(e.prototype,"mediaTypes",{get:function(){var e={};return this.blockStateMap.forEach((function(t,i){e[i]=t.mediaType})),e},enumerable:!1,configurable:!0}),e.prototype.getMinDistanceFromBufferBlock=function(e){var t=this.getCommonPlaybackTimeRanges();return Math.min(Math.min(Math.abs(e.getPlaybackTime()-t.start),Math.abs(e.getPlaybackTime()-t.end)),Math.min(Math.abs(e.getPlaybackEndTime()-t.start),Math.abs(e.getPlaybackEndTime()-t.end)))},e}();function s(e,t,i){e.hasOwnProperty(t)&&i(e[t])}function u(e,t,i){var n=e.isInit(),r=e.getMimeType()!==t;return!n&&!r&&e.getPlaybackTime()+e.getDuration()<i}t.BufferBlock=a},56092:function(e,t){var i,n;Object.defineProperty(t,"__esModule",{value:!0}),t.EncryptionKeyActionKey=t.KeyLoadState=void 0,function(e){e.Loading="loading",e.Loaded="loaded",e.Cancel="cancel"}(i||(t.KeyLoadState=i={})),function(e){e.AddEncryptionKey="@instance/sources/@source/encryptionkey/addEncryptionKey",e.UpdateEncryptionKey="@instance/sources/@source/encryptionkey/updateEncryptionKey",e.RemoveEncryptionKey="@instance/sources/@source/encryptionkey/removeEncryptionKey"}(n||(t.EncryptionKeyActionKey=n={}))},58211:function(e,t,i){var n,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.PlayingTracksReducer=t.isSwitchingPeriods=t.wasSwitchingToPeriodIdReset=t.getPlayingTracksState=t.getPlayingPeriodId=void 0;var o=i(21829),a=i(98086),s={},u=function(e){var t;return null===(t=e.playingTracks)||void 0===t?void 0:t.playingPeriodId};t.getPlayingPeriodId=u;var d=function(e){return e.playingTracks};t.getPlayingTracksState=d;var c=function(e,t){return void 0!==(null==t?void 0:t.switchingToPeriodId)&&void 0===(null==e?void 0:e.switchingToPeriodId)};t.wasSwitchingToPeriodIdReset=c;var l=function(e,t){return void 0===(null==t?void 0:t.switchingToPeriodId)&&void 0!==(null==e?void 0:e.switchingToPeriodId)};t.isSwitchingPeriods=l,t.PlayingTracksReducer=(0,o.default)(s,((n={})[a.PlayingTracksActionKey.SetPlayingPeriodId]=function(e,t){var i=t.payload;return r(r({},e),{playingPeriodId:i})},n[a.PlayingTracksActionKey.FinishedPeriodSwitch]=function(e,t){var i=t.payload;return r(r({},e),{switchingToPeriodId:e.switchingToPeriodId===i?void 0:e.switchingToPeriodId})},n[a.PlayingTracksActionKey.StartedPeriodSwitch]=function(e,t){var i=t.payload;return r(r({},e),{switchingToPeriodId:i})},n))},58423:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)},r=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentController=void 0,t.getSegmentManager=re,t.extractContainerFormat=oe;var o=i(52442),a=i(25550),s=i(28764),u=i(63546),d=i(35148),c=i(18665),l=i(60997),p=i(62510),f=i(58935),h=i(90637),g=i(57782),m=i(28463),v=i(76650),S=i(28819),y=i(8272),T=i(27279),b=i(3464),M=i(27177),I=i(42283),P=i(79814),R=i(65114),C=i(331),E=i(70016),A=i(76885),x=i(91520),k=i(16368),L=i(41108),_=i(73543),B=i(94938),D=i(43752),w=i(22916),O=i(96707),N=i(30855),F=i(4053),H=i(82311),U=i(41735),j=i(58211),q=i(59839),V=i(22645),K=i(93109),z=i(9140),G=i(74830),W=i(84519),Q=i(49118),Y=i(41661),X=i(20417),J=i(28915),Z=i(66864),$=i(60704),ee=i(12482),te=function(){function e(e,t,i,n,r,a,s,u){var d=this;this.context=e,this.onDataAvailableCallback=t,this.mimeType=i,this.manifestLoader=a,this.HLSModule=x.ModuleManager.get(k.ModuleName.HLS,!1),this.loadedAdaptationSetIds={},this.loadingDataSegmentInfos=[],this.isBackupStreamSwitchOngoing=!1,this.hasDownloadError=!1,this.invalidateOngoingSegmentInfoRequest=!1,this.disableDownloadCancelingForNextSegment=!1,this.isProcessingSegment=!1,this.parkedSegmentSubscriptions=[],this.onSegmentInfoError=function(e){if((0,M.isContextAvailable)(d.context)){var t="Error obtaining ".concat(d.mimeType," segment info");d.lastSegmentInfoError&&d.lastSegmentInfoError===e?d.logger.insane(t,e):d.debug(t,e),d.lastSegmentInfoError=e}},this.handleCancelLoadingError=function(e){d.debug("Error in cancelLoading of segmentLoaderPool ",e)},this.getNext=function(e,t){var i,n=!(null===(i=d.segmentManager)||void 0===i?void 0:i.canLoad())||d.pendingSegmentInfoRequest||d.hasActiveTransmuxingJobs()||d.isBackupStreamSwitchOngoing||d.isProcessingSegment;if(d.currentAdaptationSetId&&!n){d.debug("Getting next segment for time ".concat(e));var r={periodId:d.currentPeriodId,recommendedRepresentationId:t,time:e};d.getSegmentInfo(r).then((function(e){return d.loadFromSegmentInfo(e)})).catch(d.onSegmentInfoError)}},this.onSegmentLoaded=function(e){d.hasDownloadError=!1,d.maybeUpdateContainerFormat(e);var t=d.verifySegment(e);if(t!==o.SegmentVerificationResult.Drop){if(t!==o.SegmentVerificationResult.Park)if(d.isTransmuxerRequired()){var i=e.getSegmentInfo();i.presentationTimeOffset&&isNaN(i.presentationTimeOffset)&&(d.mpdHandler.setTimestampOffset(0),i.presentationTimeOffset=0),d.transmux(e)}else{var n=!1;d.currentAdaptationSetId&&d.segmentManager&&(n=d.segmentPreProcessor.onSegmentAvailable(e,{id3:[],closedCaptions:[]},d.currentAdaptationSetId,d.segmentManager)),n||ne(e.getSegmentInfo(),d.sourceStore),e.getMimeType().indexOf("mp4")>-1&&!d.maybeIsHlsManifest()&&(d.isLastSegment=e.isLastSegment())}}else ne(e.getSegmentInfo(),d.sourceStore)},this.onShouldDownloadBeCancelled=function(e,t){var i,n=Boolean(null===(i=d.adaptationService)||void 0===i?void 0:i.shouldDownloadBeCancelled(d.mimeType,e,t));return d.maybeIsHlsManifest()&&d.hls.possiblyDependentSegments&&n?(d.debug("Download should be cancelled, but this is disabled for segments not starting with key frames"),!1):n};var l=e.sourceContext.sourceIdentifier;if(this.currentPeriodId=s,this.sourceStore=u,this.manifestUpdateScheduler=e.serviceManager.get(c.ServiceName.ManifestUpdateSchedulingService,l),this.segmentUnavailabilityHandler=new N.SegmentUnavailabilityHandler(this.context),this.adaptationService=this.context.serviceManager.get(c.ServiceName.AdaptationService),this.streamTimeService=this.context.serviceManager.get(c.ServiceName.StreamTimeService),this.hls={isTransmuxingRequired:r&&!this.isSubtitleSegmentController,transmuxer:void 0,possiblyDependentSegments:!1,isAvMuxedTogether:(0,I.isAVMuxedTogether)(n),discardNextSegment:!1,muxedAudioTrackIndex:0},this.isContainerFormatKnown()){var p=this.getContainerFormat();this.hls.isTransmuxingRequired=p.source!==p.target}this.init()}return Object.defineProperty(e.prototype,"logger",{get:function(){return this.context.logger},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"settings",{get:function(){return this.context.settings},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventHandler",{get:function(){return this.context.eventHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"segmentManager",{get:function(){return re(this.context,this.mimeType,this.segmentParser,this.hls,this.currentLangObj)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isSubtitleSegmentController",{get:function(){return P.MimeTypeHelper.isSubtitle(this.mimeType)},enumerable:!1,configurable:!0}),e.prototype.getUserFeedbackForAdaptation=function(e){var t,i=this.getAdaptationEventType();if(i){var n=null===(t=this.adaptationService)||void 0===t?void 0:t.getUserSelectedRepresentation(this.mimeType,this.currentAdaptationSetId,e);return n?(this.eventHandler.dispatchEvent(i,{representationID:n._internalId.representationId}),n._internalId):void this.eventHandler.dispatchEvent(i,{representationID:e.representationId})}},e.prototype.getAdaptationEventType=function(){return P.MimeTypeHelper.isAudio(this.mimeType)?p.PlayerEvent.AudioAdaptation:P.MimeTypeHelper.isVideo(this.mimeType)?p.PlayerEvent.VideoAdaptation:null},e.prototype.getPrefetchedRepresentationId=function(){var e;if(null===(e=this.context.adRestorationOptimizationService)||void 0===e?void 0:e.hasPrefetchedSegment(this.mimeType,this.lastSegmentInfo))return this.context.adRestorationOptimizationService.getRepresentationForPrefetchedSegment(this.mimeType)},e.prototype.selectRepresentation=function(e){if(!this.mpdHandler.canSwitchRepresentation(e.time)&&this.currentRepresentationId)return this.currentRepresentationId;var t=this.getPrefetchedRepresentationId();if(t)return t;var i=this.sourceStore.getState(),n=!!i&&Boolean((0,m.getPreferredBitrate)((0,m.getAdaptationState)(i),this.mimeType)),r=this.getSuggestedRepresentationFromAbr(),o=e.recommendedRepresentationId,a=(null==o?void 0:o.periodId)===this.getCurrentPeriodId();o&&a&&!n&&(r=o);var s=r?this.getUserFeedbackForAdaptation(r):void 0;return s?(this.disableDownloadCancelingForNextSegment=!0,s):r},e.prototype.getSuggestedRepresentationFromAbr=function(){var e,t,i={hlsDependentSegments:this.hls.possiblyDependentSegments,segmentDuration:this.mpdHandler.getSegmentDuration()};return null===(e=this.adaptationService)||void 0===e?void 0:e.selectRepresentation(this.mimeType,i,null===(t=this.getCurrentAdaptationSet())||void 0===t?void 0:t.Representation)},e.prototype.switchRepresentation=function(e){var t=this.currentRepresentationId?this.currentRepresentationId.representationId:null;this.debug("switching representation from ".concat(t," to ").concat(e.representationId)),this.mpdHandler.setRepresentationId(e),this.currentRepresentationId=e},e.prototype.invalidateOngoingRequests=function(){this.invalidateOngoingSegmentInfoRequest=!0},e.prototype.fireQualityChangeEvent=function(e,t){var i,n=se(e,this.context),r=se(t,this.context),o=n?(0,D.representationToQuality)(n,this.mimeType):void 0,a=r?(0,D.representationToQuality)(r,this.mimeType):void 0;if(a){var s={targetQuality:a,targetQualityId:a.id,sourceQuality:o,sourceQualityId:null!==(i=null==o?void 0:o.id)&&void 0!==i?i:null};P.MimeTypeHelper.isVideo(this.mimeType)?this.context.eventHandler.dispatchEvent(p.PlayerEvent.VideoDownloadQualityChange,s):P.MimeTypeHelper.isAudio(this.mimeType)&&this.context.eventHandler.dispatchEvent(p.PlayerEvent.AudioDownloadQualityChange,s)}},e.prototype.getSegmentInfo=function(e){var t,i=this,r=void 0!==(null===(t=this.lastSegmentInfo)||void 0===t?void 0:t.segmentNumber)?this.lastSegmentInfo.segmentNumber+1:0,o=this.selectRepresentation(e),a=!o.equals(this.currentRepresentationId);return a&&P.MimeTypeHelper.isAV(this.mimeType)&&this.fireQualityChangeEvent(this.currentRepresentationId,o),this.invalidateOngoingSegmentInfoRequest=!1,this.pendingSegmentInfoRequest=this.maybeWaitForRepresentationUpdate(o).then((function(){return i.currentAdaptationSetId&&i.mpdHandler.setAdaptationSetId(i.currentAdaptationSetId,o),a&&i.switchRepresentation(o),i.mpdHandler.getNextSegmentInfo(e.time)})).then((function(e){var t,a=null!==(t=i.currentRepresentationId)&&void 0!==t?t:o;if(i.addSegmentInfos(e,r,a),i.shouldInvalidateSegmentInfo(e))throw(0,M.isContextAvailable)(i.context)&&i.debug("invalidating ongoing SegmentInfo request for",n({},e)),"ongoing segment info request was invalidated";return i.lastSegmentInfo=e,e})).catch((function(t){var r,a;if(i.pendingSegmentInfoRequest=void 0,ue(t)){var s=null!==(a=null!==(r=null==t?void 0:t.name)&&void 0!==r?r:null==t?void 0:t.message)&&void 0!==a?a:t;i.debug("HLS playlist loading failed for representation ".concat(o.representationId," (").concat(s,")"));var u=i.maybeTryLowerQuality(o);if(u)return i.debug("Trying with lower quality representation ".concat(u.representationId)),i.getSegmentInfo(n(n({},e),{recommendedRepresentationId:u}));i.handleFailedRepresentationDownload(o,t)}throw t})).finally((function(){i.expectedTargetTimeAfterSeek=void 0})),this.pendingSegmentInfoRequest},e.prototype.shouldInvalidateSegmentInfo=function(e){return this.invalidateOngoingSegmentInfoRequest&&e&&!e.isInitSegment&&!this.isSegmentInfoInExpectedTimeRange(e)},e.prototype.isSegmentInfoInExpectedTimeRange=function(e){return void 0!==this.expectedTargetTimeAfterSeek&&(0,E.isNumber)(e.startTime)&&(0,E.isNumber)(e.duration)&&this.expectedTargetTimeAfterSeek>=e.startTime&&this.expectedTargetTimeAfterSeek<=e.startTime+e.duration},e.prototype.getDataSegmentInfo=function(e){var t={periodId:this.currentPeriodId,time:e};return this.currentRepresentationId&&this.currentRepresentationId.periodId===this.getCurrentPeriodId()?this.getSegmentInfo(n(n({},t),{recommendedRepresentationId:this.currentRepresentationId})):this.getSegmentInfo(t)},e.prototype.addSegmentInfos=function(e,t,i){e&&(this.applyRepresentationInformationToSegmentInfo(e,i),this.parseDateTimeFormSegmentUrl(e),this.applyBaseUrlsToSegmentInfo(i,e),e.duration||e.isInitSegment||(e.duration=this.mpdHandler.getSegmentDuration()),(void 0===e.segmentNumber||isNaN(e.segmentNumber))&&(e.segmentNumber=t),e.discardData=this.hls.discardNextSegment,this.hls.discardNextSegment=!1,this.disableDownloadCancelingForNextSegment&&(e.preventDownloadCanceling=!0,this.disableDownloadCancelingForNextSegment=!1))},e.prototype.applyRepresentationInformationToSegmentInfo=function(e,t){var i=se(t,this.context);i&&(e.representationId=t.representationId,e.internalRepresentationId=t,e.mimeType=this.mimeType,e.bitrate=i._bandwidth,e.periodId=t.periodId,e.codecs=this.getCodecs(),P.MimeTypeHelper.isAudio(this.mimeType)?(e.sampleRate=i._audioSamplingRate,e.width=void 0,e.height=void 0):P.MimeTypeHelper.isVideo(this.mimeType)&&(e.width=i._width,e.height=i._height,e.frameRate=i._frameRate))},e.prototype.parseDateTimeFormSegmentUrl=function(e){if(P.MimeTypeHelper.isVideo(this.mimeType)&&!e.dateTime&&this.settings.AKAMAI_DATETIME_PARSING&&this.HLSModule){var t=null!=e.duration?this.HLSModule.PlaylistUtils.getProgramDateTimeFromSegmentUrl(e.url,e.duration):void 0;t&&(e.dateTime=t)}},e.prototype.applyBaseUrlsToSegmentInfo=function(e,t){var i=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(t){return t.getAvailableBaseURLsForRepresentation(e)}),void 0,this.context.sourceContext.sourceIdentifier);t.availableBaseURLs=i,t.firstUsedBaseURLIndex=0,this.lastSegmentInfo&&this.lastSegmentInfo.lastUsedBaseURLIndex&&(t.firstUsedBaseURLIndex=this.lastSegmentInfo.lastUsedBaseURLIndex),t.lastUsedBaseURLIndex=t.firstUsedBaseURLIndex,this.maybeIsHlsManifest()?(t.baseURL="",t.mediaURL||(t.mediaURL=t.url)):(t.baseURL=i[t.firstUsedBaseURLIndex],t.mediaURL=t.mediaURL||t.url,delete t.url),t.url=A.URLHelper.concatBaseUrlWithPartial(t.baseURL,t.mediaURL)},e.prototype.maybeWaitForRepresentationUpdate=function(e){var t,i,n,r,o=this,a=null!==(i=null===(t=this.currentAdaptationSetId)||void 0===t?void 0:t.adaptationSetId)&&void 0!==i?i:"",s=null===(n=this.sourceStore.getState().activeTracks)||void 0===n?void 0:n[a],u=!(null===(r=null==s?void 0:s.selectedRepresentationId)||void 0===r?void 0:r.equals(e));return this.sourceStore.dispatch((0,F.setRepresentationIdAction)(e)),u?this.manifestUpdateScheduler?this.manifestUpdateScheduler.waitForRepUpdate(e).then((function(){return o.mpdHandler.updateRepresentation(e)})):Promise.reject("ManifestUpdateScheduler does not exist."):Promise.resolve()},e.prototype.isTransmuxing=function(){var e;return this.isTransmuxerRequired()&&Boolean(null===(e=this.hls.transmuxer)||void 0===e?void 0:e.isTransmuxing())},e.prototype.hasActiveTransmuxingJobs=function(){var e;return!!this.isTransmuxerRequired()&&(this.hasDelayedSegmentsInTransmuxer()||Boolean(null===(e=this.hls.transmuxer)||void 0===e?void 0:e.hasActiveJobs()))},e.prototype.hasDelayedSegmentsInTransmuxer=function(){var e;return P.MimeTypeHelper.isAudio(this.mimeType)&&this.isTransmuxerRequired()&&Boolean(null===(e=this.hls.transmuxer)||void 0===e?void 0:e.hasDelayedAudioSegments())},e.prototype.cancelTransmuxing=function(){var e,t,i=this;(null!==(t=null===(e=this.hls.transmuxer)||void 0===e?void 0:e.discardCurrentJobs(this.mimeType))&&void 0!==t?t:[]).forEach((function(e){return ne(e.mp2tsSegment.getSegmentInfo(),i.sourceStore)}))},e.prototype.cancelLoading=function(){var e;this.invalidateOngoingSegmentInfoRequest=!0,null===(e=this.segmentManager)||void 0===e||e.cancel().catch(this.handleCancelLoadingError)},e.prototype.seekTo=function(e){var t=this;null!==e&&this.currentAdaptationSetId&&(this.cancelLoading(),this.cancelTransmuxing(),this.loadingDataSegmentInfos=[],this.hasDownloadError=!1,this.isLastSegment=!1,this.hlsTimelineTracker&&this.hlsTimelineTracker.reset(),this.isTransmuxerRequired()&&this.hls.transmuxer&&this.hls.transmuxer.resetCaptionParser(),e>=0&&(this.expectedTargetTimeAfterSeek=e,this.mpdHandler.seekTo(e),this.context.serviceManager.maybeCall(c.ServiceName.TimedMetadataService,(function(e){return e.clearSegmentBoundMetadata(t.mimeType)}))))},e.prototype.hasNextVod=function(e){var t,i=this,n=this.maybeFindPeriod(this.currentPeriodId),r=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.getMediaPresentationDuration()}),0,this.context.sourceContext.sourceIdentifier);if(n&&n.hasOwnProperty("_duration"))t=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.getPeriodDuration(i.currentPeriodId)}),void 0,this.context.sourceContext.sourceIdentifier);else{if(0===r)return!1;t=r}return this.mpdHandler.hasNext(t)},e.prototype.hasNextLive=function(e){var t=this;if(this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.isAvailabilityEndTimeExceeded()}),!1,this.context.sourceContext.sourceIdentifier))return this.logger.log("Stream (".concat(this.mimeType,") availability end time reached, stopping download of further segments.")),this.isLastSegment=!0,!1;this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.isLastPeriod(t.currentPeriodId)}),!1,this.context.sourceContext.sourceIdentifier)&&this.mpdHandler.resolvePendingSegmentInfoRequests();var i=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.getPeriodDuration(t.currentPeriodId)}),void 0,this.context.sourceContext.sourceIdentifier);return this.mpdHandler.hasNext(i)},e.prototype.hasNext=function(e){var t=this,i=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.isLastPeriod(t.currentPeriodId)}),!1,this.context.sourceContext.sourceIdentifier);return!(this.isLastSegment&&i||!this.currentAdaptationSetId||!this.mpdHandler)&&(this.maybeIsLive()?this.hasNextLive(e):this.hasNextVod(e))},e.prototype.onTransmuxed=function(e){var t=this;if(e.transmuxedSegments.every((function(e){return null!==e.getCodec()}))){!this.hls.possiblyDependentSegments&&ae(e)&&(this.debug("Encountered HLS segment not starting with a key frame"),this.hls.possiblyDependentSegments=!0);var i=e.transmuxedSegments.find((function(e){return"video/mp4"===e.getMimeType()})),n=e.transmuxedSegments.filter((function(e){return"audio/mp4"===e.getMimeType()})),r=[i,n[this.hls.muxedAudioTrackIndex]||n[0]].filter((function(e){return Boolean(e)}));r.forEach((function(e){return t.timestampOffsetService.maybeAdjustTimestampOffsetAtDiscontinuityChange(e,t.mpdHandler)})),this.segmentManager&&this.currentAdaptationSetId&&this.segmentPreProcessor.onMuxedSegmentAvailable(e,r,this.currentAdaptationSetId,this.segmentManager);var o=r[r.length-1];o&&o.getMimeType().indexOf("mp4")>-1&&(this.isLastSegment=o.isLastSegment()),this.debug("Transmuxing done",e.originalSegment.getUrl())}else{var a=e.originalSegment.getUrl();this.debug("Discarding muxing because codecs were not available in the segment: ".concat(a))}},e.prototype.hasPendingSegments=function(e){var t,i,n,r,o,a;return e?(o=this.isTransmuxerRequired()&&Boolean(this.hls.transmuxer)&&this.hls.transmuxer.isTransmuxingSegmentsForPeriod(e),a=null!==(i=null===(t=this.segmentManager)||void 0===t?void 0:t.isLoading(e))&&void 0!==i&&i):(o=this.isTransmuxing(),a=null!==(r=null===(n=this.segmentManager)||void 0===n?void 0:n.isLoading())&&void 0!==r&&r),a||o||this.isProcessingSegment||Boolean(this.pendingSegmentInfoRequest)},e.prototype.transmux=function(e){var t=this;if(this.hls.transmuxer){var i=e.getSegmentInfo();this.hls.transmuxer.isSetupForDiscontinuity(i.discontinuitySequenceNumber)||(void 0===i.startTime&&(this.debug("resetting segment playback times for new initialization"),this.sourceStore.dispatch((0,U.resetSegmentPlaybackTimes)())),(0,G.initializeHlsSegmentStartTimes)(e,this.context,this.mpdHandler));var n=Date.now();this.hls.transmuxer.transmuxSegment(e).then((function(e){var r,o=(0,C.toSeconds)(Date.now()-n),a={time:o,representationId:i.internalRepresentationId};return t.context.store.dispatch((0,v.addMetricsValue)(t.mimeType,y.MetricType.TransmuxingTime,a)),t.context.logger.debug("Transmuxed ".concat(t.mimeType," segment in: ").concat(null==o?void 0:o.toFixed(2),", quality: ").concat(null!==(r=i.representationId)&&void 0!==r?r:"")),t.onTransmuxed(e)})).catch((function(i){ne(e.getSegmentInfo(),t.sourceStore),i!==o.TransmuxingRejectionReason.TRANSMUXING_ABORTED?t.dispatchInvalidSegmentWarning(e,"Transmuxing failed: ".concat(i)):t.debug(i,e.getUrl())}))}},e.prototype.maybeUpdateContainerFormat=function(e){var t,i;if(!this.isContainerFormatKnown()||this.isSegmentEmpty(e)){var n=(0,z.resolveContainerFormat)(this.mimeType,e,this.maybeIsHlsManifest());this.debug("Container format is ".concat(null==n?void 0:n.source)),n&&this.sourceStore.dispatch((0,F.setContainerFormatAction)(e.getRepresentationId(),n)),this.hls.isTransmuxingRequired=Boolean(n&&n.source!==n.target),!this.hls.isTransmuxingRequired&&P.MimeTypeHelper.isAV(this.mimeType)&&(null===(t=this.hls.transmuxer)||void 0===t||t.dispose(),this.context.serviceManager.maybeCall(c.ServiceName.SubtitleService,(function(e){e.setTransmuxer(void 0)}),void 0,null===(i=this.context.sourceContext)||void 0===i?void 0:i.sourceIdentifier))}},e.prototype.verifySegment=function(e){var t;if(this.isSegmentEmpty(e))return this.debug("Warning: Loaded segment has size 0 bytes, skipping further processing! May cause gap!",e.getUrl()),null===(t=this.hlsTimelineTracker)||void 0===t||t.reset(),o.SegmentVerificationResult.Drop;if(this.isTransmuxerRequired()){if(!x.ModuleManager.has(k.ModuleName.ContainerTS))return this.context.eventHandler.fireError(new L.PlayerModuleMissingError(k.ModuleName.ContainerTS)),o.SegmentVerificationResult.Drop}else if(!this.maybeIsSmoothManifest()&&this.isInvalidMp4Segment(e)){if(this.context.settings.DROP_INVALID_SEGMENTS)return this.dispatchInvalidSegmentWarning(e,"The loaded MP4 segment is invalid, skipping further processing! May cause gap!"),o.SegmentVerificationResult.Drop;this.dispatchInvalidSegmentWarning(e,"The loaded MP4 segment is invalid. This may cause playback issues!")}var i=x.ModuleManager.get(k.ModuleName.LowLatency,!1),n=this.context.serviceManager.get(c.ServiceName.ManifestService,this.context.sourceContext.sourceIdentifier);if((0,K.isPlayingLowLatencyHls)(this.context)&&i&&n){var r=i.verifyLlHlsSegment(e,n);if(r===o.SegmentVerificationResult.Drop)return this.debug("LL-HLS Segment Verification: Dropping (partial) segment as it disappeared from playlist",e.getUrl()),o.SegmentVerificationResult.Drop;if(r===o.SegmentVerificationResult.Park)return this.parkSegment(e),o.SegmentVerificationResult.Park}return o.SegmentVerificationResult.Publish},e.prototype.parkSegment=function(e){var t=this,i=function(t){var i;if(t.manifest)return null===(i=(0,R.findRepresentation)(t.manifest,e.getRepresentationId()))||void 0===i?void 0:i._requestTimestamp},n=function(e,t){return e!==t},r=function(){o(),t.removeParkedSegmentSubscriber(o),t.onSegmentLoaded(e)},o=(0,b.subscribe)(this.sourceStore)(i,r,n);this.parkedSegmentSubscriptions.push(o)},e.prototype.removeParkedSegmentSubscriber=function(e){var t=this.parkedSegmentSubscriptions.findIndex((function(t){return t===e}));t>-1&&this.parkedSegmentSubscriptions.splice(t,1)},e.prototype.isSegmentEmpty=function(e){var t=e.getData();return!!(t&&t.byteLength<1)},e.prototype.isInvalidMp4Segment=function(e){if(!x.ModuleManager.has(k.ModuleName.ContainerMP4)||!P.MimeTypeHelper.isAV(this.mimeType))return!1;var t=x.ModuleManager.get(k.ModuleName.ContainerMP4,!1),i=t.isValidMp4,n=t.MP4Parser;return this.segmentParser instanceof n&&!i(e)},e.prototype.dispatchInvalidSegmentWarning=function(e,t){this.eventHandler.dispatchEvent(p.PlayerEvent.Warning,new u.PlayerWarning(d.WarningCode.PLAYBACK_INVALID_DATA_SEGMENT_ENCOUNTERED,t,{mimeType:e.getMimeType(),segmentUrl:e.getUrl()}))},e.prototype.getContainerFormat=function(){var e=this.sourceStore.getState();return e&&(0,H.getContainerFormat)(e,P.MimeTypeHelper.getMediaType(this.mimeType))},e.prototype.isContainerFormatKnown=function(){return Boolean(this.getContainerFormat())},e.prototype.loadFromSegmentInfo=function(e){var t=this;return this.pendingSegmentInfoRequest=void 0,this.isProcessingSegment=!0,e?(this.loadingDataSegmentInfos.push(e),this.lastSegmentInfoError=void 0,this.ensureInitSegmentInfo(e),this.loadDataSegment(e).catch((function(i){return t.handleSegmentLoadingError(i,e)})).finally((function(){return t.isProcessingSegment=!1}))):Promise.resolve()},e.prototype.ensureInitSegmentInfo=function(e){var t=this.getInitSegmentInfoForDataSegmentInfo(e);t&&(e.init=t)},e.prototype.debug=function(e){for(var t,i,n,o=[],a=1;a<arguments.length;a++)o[a-1]=arguments[a];(null===(i=this.logger)||void 0===i?void 0:i.isLevelEnabled(f.LogLevel.DEBUG))&&(t=this.logger).debug.apply(t,r(["[".concat(this.mimeType,"][").concat(null===(n=this.currentRepresentationId)||void 0===n?void 0:n.representationId,"] SegmentController: ").concat(e)],o,!1))},e.prototype.loadDataSegment=function(e){var t;return(null===(t=this.logger)||void 0===t?void 0:t.isLevelEnabled(f.LogLevel.DEBUG))&&this.debug("Loading ".concat(JSON.stringify(e))),this.loadSegment(e)},e.prototype.getInitSegmentInfoForDataSegmentInfo=function(e){var t=null;return(t=e.init?n(n({},e.init),{isInitSegment:!0,internalRepresentationId:e.internalRepresentationId}):this.mpdHandler.getInitSegmentInfo(e.internalRepresentationId))&&this.addSegmentInfos(t,void 0,e.internalRepresentationId),t},e.prototype.loadSegment=function(e){var t=this;return this.segmentManager?this.segmentManager.getSegment(e).then((function(e){return t.extractSegmentFromStream(e)})):Promise.reject()},e.prototype.extractSegmentFromStream=function(e){var t=this;return new Promise((function(i,n){e.transform((function(e){ie(e.getSegmentInfo(),t.loadingDataSegmentInfos),t.onSegmentLoaded(e)}),(function(){return i()}),(function(e){var i=e.message,r={reason:i};return n(r),r.reason!==Z.SegmentLoadingErrorReason.CANCEL&&t.debug("Error loading segment",i),i}))}))},e.prototype.handleSegmentLoadingError=function(e,t){var i;null===(i=(0,T.getSourceStore)(this.context))||void 0===i||i.dispatch((0,g.clearPreferredBitrateForMimeTypeAction)(this.mimeType)),ne(t,this.sourceStore);var n=(null==e?void 0:e.info)?e.info.response.status:0;switch(null==e?void 0:e.reason){case Z.SegmentLoadingErrorReason.CANCEL:this.debug("Download was cancelled as the segment is probably outdated");break;case Z.SegmentLoadingErrorReason.ERROR_DECRYPTING:this.debug("Could not decrypt segment",t.url),this.eventHandler.dispatchEvent(p.PlayerEvent.Warning,new u.PlayerWarning(d.WarningCode.DRM_COULD_NOT_DECRYPT_SEGMENT));break;case Z.SegmentLoadingErrorReason.ERROR_LOADING:this.handleDownloadError(n,t,Z.SegmentLoadingErrorReason.ERROR_LOADING);break;case Z.SegmentLoadingErrorReason.TIMEOUT:this.handleDownloadError(n,t,Z.SegmentLoadingErrorReason.TIMEOUT);break;case Z.SegmentLoadingErrorReason.ABR_TIMEOUT:this.onABRCancellation(t);break;default:this.debug("Unexpected error while loading the segment",{url:t.url,error:e})}},e.prototype.switchToHlsBackupStream=function(e,t){var i=this;return this.HLSModule&&this.manifestLoader instanceof this.HLSModule.M3u8Loader?this.manifestLoader.switchToBackupStream(e,t).then((function(e){return e.length>0})).catch((function(e){return i.debug("Could not switch to HLS backup stream: ".concat(e)),Promise.resolve(!1)})):Promise.resolve(!1)},e.prototype.getFailedSegmentIndex=function(){var e=this.settings.CHUNKED_CMAF_STREAMING?1:this.loadingDataSegmentInfos.length;return this.mpdHandler.getIndex()-e},e.prototype.handleDownloadError=function(e,t,i){(null==t?void 0:t.isPreloadHint)?this.debug("Ignoring download error of preload hint segment.",t.url):(this.debug("Failed to load segment (code: ".concat(e,") ").concat(t.url)),this.segmentUnavailabilityHandler.shouldTryAlternatives(e)?this.tryToFindAValidSegment(t,e,i):(this.storeFailedDownloadRange(t),this.hasDownloadError=!0))},e.prototype.tryToFindAValidSegment=function(e,t,i){var n=this;if(this.currentRepresentationId&&this.currentRepresentationId.equals(e.internalRepresentationId)){var r=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return n.currentAdaptationSetId&&e.getRepresentation(n.currentAdaptationSetId,n.currentRepresentationId.representationId)}),null,this.context.sourceContext.sourceIdentifier);if(r){var o=!1;this.isBackupStreamSwitchOngoing=!0,this.switchToHlsBackupStream(r,e).then((function(e){return o=e})).finally((function(){return n.isBackupStreamSwitchOngoing=!1})).then((function(){var e;return null===(e=n.segmentManager)||void 0===e?void 0:e.cancel()})).then((function(){return n.tryToRecoverWithValidSegment(o,e,t,i)}))}}else this.debug("Ignoring download error of previous representation.")},e.prototype.tryToRecoverWithValidSegment=function(e,t,i,n){var r,o=this.getFailedSegmentIndex();if(this.loadingDataSegmentInfos=[],e)this.mpdHandler.setIndex(Math.max(0,o));else if(r=this.segmentUnavailabilityHandler.switchBaseURL(t))this.context.logger.debug("Switching ".concat(this.mimeType," segment location to ").concat(r.baseURL)),this.loadFromSegmentInfo(r);else{var a=this.maybeTryLowerQuality(this.currentRepresentationId);if(a){if(this.streamTimeService){this.mpdHandler.setIndex(Math.max(0,o)),this.debug("Trying with lower quality segment ".concat(a.representationId));var s=this.streamTimeService.getTimeForNextSegment((0,V.getTrackIdentifier)(t)),u=this.mpdHandler.getLatestTimeForPossibleSwitch(s);this.getNext(u,a)}}else this.hasDownloadError=!0,this.handleFailedSegmentDownload(t,i,n)}},e.prototype.maybeTryLowerQuality=function(e){var t,i,n;if(e&&(null===(t=this.adaptationService)||void 0===t?void 0:t.isAuto(this.mimeType))){var r=null!==(n=null===(i=this.maybeGetAdaptationSet(this.currentAdaptationSetId))||void 0===i?void 0:i.Representation)&&void 0!==n?n:[];return this.segmentUnavailabilityHandler.switchQuality(r,e)}},e.prototype.handleFailedRepresentationDownload=function(e,t){var i;if(this.maybeIsLive()&&this.settings.INFINITE_RETRIES_FOR_LIVE){this.debug("Could not load any segment of any quality from any CDN: restart retries."),this.segmentUnavailabilityHandler.downloadSuccess(!1);var n=se(this.currentRepresentationId,this.context);n&&(this.sourceStore.dispatch((0,U.updateRepresentationFailedDownloadAction)(n._internalId,null)),null===(i=this.manifestUpdateScheduler)||void 0===i||i.scheduleRepresentationUpdate(n))}else{var r=se(e,this.context);(0,Q.throwPlaylistDownloadError)(this.context,"Could not load segments of any quality from any CDN",r,t)}},e.prototype.handleFailedSegmentDownload=function(e,t,i){this.storeFailedDownloadRange(e),this.maybeIsLive()&&this.settings.INFINITE_RETRIES_FOR_LIVE?(this.debug("Could not load current segment of any quality from any CDN (all retries failed), skipping it..."),this.continueWithNextSegment(e)):(0,X.throwDownloadError)(this.context,"Segment not available at any quality or location",t,e,i)},e.prototype.storeFailedDownloadRange=function(e){var t,i=(0,J.getSegmentInfoTimeRange)(e);if(i&&(0,M.isContextAvailable)(this.context)&&e.mimeType){var n=(0,V.getTrackIdentifier)(e);null===(t=(0,T.getSourceStore)(this.context))||void 0===t||t.dispatch((0,q.addStreamTimeRange)(n,i,V.StreamTimeRangeType.Failed))}},e.prototype.continueWithNextSegment=function(e){if(this.segmentUnavailabilityHandler.downloadSuccess(!1),e.hasDownloadFailed=!0,null!=e.startTime&&null!=e.duration){var t=e.startTime+e.duration,i=this.mpdHandler.adjustTimeToNextSegmentStart(t);this.getNext(i)}},e.prototype.onABRCancellation=function(e){var t,i=this;(0,M.isContextAvailable)(this.context)&&(this.debug("Download took too long, cancelled it",e.url),!e.isInitSegment&&this.mpdHandler&&(null===(t=this.segmentManager)||void 0===t||t.cancel().then((function(){if(i.mpdHandler){var e=i.getFailedSegmentIndex();i.loadingDataSegmentInfos=[],i.debug("Trying to get the same segment with a lower bitrate."),i.mpdHandler.setIndex(Math.max(0,e))}}))))},e.prototype.findAdaptationSetId=function(e,t,i){var n=this;if(void 0===i&&(i=!1),this.isSubtitleSegmentController&&!(null==t?void 0:t.id))return this.currentLangObj=t,void(this.segmentManager&&this.cancelLoading());var r=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(r){return r.findAdaptationSet(e,n.mimeType,t,i)}),null,this.context.sourceContext.sourceIdentifier);return r?(this.currentLangObj=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.getLangObjectFromAdaptationSet(r,n.mimeType)}),void 0,this.context.sourceContext.sourceIdentifier),r._internalId):void 0},e.prototype.getCurrentAdaptationSet=function(){return this.maybeGetAdaptationSet(this.currentAdaptationSetId)},e.prototype.getAdaptationSetForPeriodId=function(e){return this.maybeGetAdaptationSet(this.loadedAdaptationSetIds[e])||this.getCurrentAdaptationSet()},e.prototype.updateMpd=function(){var e;this.debug("Caught a manifest update event");var t=!this.maybeFindPeriod(this.currentPeriodId);for(var i in this.currentAdaptationSetId=this.findAdaptationSetId(this.currentPeriodId,this.currentLangObj,!1),!t&&this.currentAdaptationSetId&&(this.loadedAdaptationSetIds[this.currentPeriodId]=this.currentAdaptationSetId,null===(e=this.adaptationService)||void 0===e||e.setAdaptationSetId(this.mimeType,this.currentAdaptationSetId),this.mpdHandler.setAdaptationSetId(this.currentAdaptationSetId,this.currentRepresentationId,{isPeriodSwitch:!1,isManifestUpdate:!0})),this.loadedAdaptationSetIds){this.maybeFindPeriod(i)||delete this.loadedAdaptationSetIds[i]}},e.prototype.activateSubtitleSegmentController=function(e){var t;this.isSubtitleSegmentController&&(this.currentAdaptationSetId&&(this.mpdHandler.setAdaptationSetId(this.currentAdaptationSetId),null===(t=this.adaptationService)||void 0===t||t.setAdaptationSetId(this.mimeType,this.currentAdaptationSetId)),isNaN(this.mpdHandler.getTimestampOffset())&&this.mpdHandler.setTimestampOffset(0),this.seekTo(e))},e.prototype.setCurrentLangObj=function(e){var t;if(this.hls.isAvMuxedTogether&&P.MimeTypeHelper.isVideo(this.mimeType)){var i=this.maybeGetAdaptationSet(this.currentAdaptationSetId);if(null==i?void 0:i.ContentComponent){var n=i.ContentComponent.find((function(t){return"audio"===t._contentType&&t._id===e.id}));if(n)return this.hls.muxedAudioTrackIndex=i.ContentComponent.filter((function(e){return"audio"===e._contentType})).indexOf(n),void(this.currentLangObj=e)}}e.id!==(null===(t=this.currentLangObj)||void 0===t?void 0:t.id)&&this.switchAdaptationSetId(e)},e.prototype.switchAdaptationSetId=function(e){var t=this.currentAdaptationSetId;if(this.currentAdaptationSetId&&this.sourceStore.dispatch((0,F.removeActiveTrackAction)(this.currentAdaptationSetId)),this.currentAdaptationSetId=this.findAdaptationSetId(this.currentPeriodId,e),this.currentAdaptationSetId){var i=this.maybeGetAdaptationSet(this.currentAdaptationSetId);i&&this.sourceStore.dispatch((0,F.setMediaTypeAction)(i._internalId,(0,w.resolveMediaTypes)(i))),this.loadedAdaptationSetIds[this.currentPeriodId]=this.currentAdaptationSetId,t&&!this.currentAdaptationSetId.equals(t)&&this.switchDependentToNewLanguageObject(e)}},e.prototype.switchDependentToNewLanguageObject=function(e){var t,i;this.currentLangObj=e,this.previousExplicitlySetLangObj=e,this.debug("Trying to set language to ID ".concat(this.currentLangObj.id)+" (lang: ".concat(this.currentLangObj.lang,") for mpd handler and adaption handler")),this.currentAdaptationSetId&&(this.mpdHandler.setAdaptationSetId(this.currentAdaptationSetId),null===(t=this.adaptationService)||void 0===t||t.setAdaptationSetId(this.mimeType,this.currentAdaptationSetId),null===(i=this.adaptationService)||void 0===i||i.selectRepresentation(this.mimeType))},e.prototype.getCurrentLangObj=function(){return this.currentLangObj},e.prototype.stop=function(){var e,t;this.cancelLoading(),null===(e=this.segmentManager)||void 0===e||e.clearCache(),null===(t=this.adaptationService)||void 0===t||t.shutdown(this.mimeType)},e.prototype.init=function(){var e,t,i=this,n=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.getFirstPeriod()}),void 0,this.context.sourceContext.sourceIdentifier);!this.currentPeriodId&&n&&(this.currentPeriodId=n._id);var r=W.MPDHandlerFactory.createInstance(this.context,this.mimeType,this.manifestLoader,this.currentPeriodId);if(r){this.mpdHandler=r;var o,u=oe(this.mimeType);u&&(o=(0,O.createSegmentParser)(this.context,u)),o?this.segmentParser=o:this.debug("Could not create segment parser for mime type"),this.timestampOffsetService=new ee.TimestampOffsetService(this.context,this.mimeType),this.segmentPreProcessor=new $.SegmentPreProcessor(this.context,this.mimeType,this.onDataAvailableCallback,this.hls,this.segmentUnavailabilityHandler,this.timestampOffsetService,this.segmentParser),this.segmentPreProcessor.setMpdHandler(this.mpdHandler),this.hasDownloadError=!1,this.currentAdaptationSetId=this.findAdaptationSetId(this.currentPeriodId,this.currentLangObj),this.currentAdaptationSetId&&(this.loadedAdaptationSetIds[this.currentPeriodId]=this.currentAdaptationSetId),this.context.serviceManager.maybeCall(c.ServiceName.SegmentService,(function(e){return e.add({mimeType:i.mimeType,shouldDownloadBeCancelledCallback:i.onShouldDownloadBeCancelled},i.segmentParser,i.currentLangObj)})),this.maybeIsHlsManifest()&&(this.hlsTimelineTracker=new this.HLSModule.HlsTimelineTracker(this.context,this.settings.GAP_TOLERANCE),this.segmentPreProcessor.setHlsTimelineTracker(this.hlsTimelineTracker)),P.MimeTypeHelper.isMP4(this.mimeType)&&this.context.serviceManager.maybeCall(c.ServiceName.SubtitleService,(function(e){e.setupCea608CaptionExtractor(i.context.logger)}),void 0,null===(e=this.context.sourceContext)||void 0===e?void 0:e.sourceIdentifier),this.currentAdaptationSetId&&(null===(t=this.adaptationService)||void 0===t||t.setAdaptationSetId(this.mimeType,this.currentAdaptationSetId),this.mpdHandler.setAdaptationSetId(this.currentAdaptationSetId,void 0,{isPeriodSwitch:!0}),this.maybeIsLive()&&!this.settings.ENABLE_SEEK_FOR_LIVE&&this.mpdHandler.timeShift(0,this.currentPeriodId)),this.isLastSegment=!1}else this.eventHandler.fireError(new s.PlayerError(a.ErrorCode.SOURCE_MANIFEST_INVALID))},e.prototype.getSegmentInfos=function(){var e,t=this,i=null===(e=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.getAvailableBaseURLsForRepresentation(t.currentRepresentationId)}),null,this.context.sourceContext.sourceIdentifier))||void 0===e?void 0:e[0];return this.mpdHandler.getSegmentInfos(i)},e.prototype.getCurrentPeriodId=function(){return this.currentPeriodId},e.prototype.getTargetLanguageObject=function(e){var t=this;return this.previousExplicitlySetLangObj&&this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(i){return i.isLanguageAvailable(e,t.mimeType,t.previousExplicitlySetLangObj)}),!1,this.context.sourceContext.sourceIdentifier)?this.previousExplicitlySetLangObj:this.currentLangObj},e.prototype.switchPeriod=function(e){var t,i,n,r=this,o=this.getTargetLanguageObject(e);if(this.debug("switching period from ".concat(this.currentPeriodId," to ").concat(e)),this.cancelLoading(),null===(t=this.segmentManager)||void 0===t||t.clearCache(),this.removeCachedSegmentsOfPrecedingPeriods(),this.currentPeriodId=e,this.currentRepresentationId=void 0,this.currentAdaptationSetId=this.findAdaptationSetId(e,o,!0),this.currentAdaptationSetId&&(this.loadedAdaptationSetIds[e]=this.currentAdaptationSetId),this.currentAdaptationSetId){null===(i=this.adaptationService)||void 0===i||i.setAdaptationSetId(this.mimeType,this.currentAdaptationSetId);var u=this.mpdHandler.getPendingSegmentInfoRequest(),d=W.MPDHandlerFactory.createInstance(this.context,this.mimeType,this.manifestLoader,e,o);d?(this.mpdHandler.dispose(),this.mpdHandler=d):this.eventHandler.fireError(new s.PlayerError(a.ErrorCode.SOURCE_MANIFEST_INVALID)),this.segmentPreProcessor.setMpdHandler(this.mpdHandler),this.mpdHandler.setPendingSegmentInfoRequest(u),this.mpdHandler.setAdaptationSetId(this.currentAdaptationSetId,void 0,{isPeriodSwitch:!0})}if(this.isTransmuxerRequired()){var l=this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return r.currentAdaptationSetId&&e.findDownloadedHlsRepresentation(r.currentAdaptationSetId)}),null,this.context.sourceContext.sourceIdentifier);l&&this.mpdHandler.setRepresentationId(l._internalId),this.hlsTimelineTracker&&this.hlsTimelineTracker.reset(),null===(n=this.segmentManager)||void 0===n||n.clearCache(!1)}this.hasDownloadError=!1,this.isLastSegment=!1},e.prototype.getMPDHandler=function(){return this.mpdHandler},e.prototype.getIndex=function(){return this.mpdHandler&&this.mpdHandler.getIndex()||0},e.prototype.getSourceBufferTypes=function(){var e=[];return this.isTransmuxerRequired()&&this.hls.isAvMuxedTogether?(e.push("video/mp4"),e.push("audio/mp4")):e.push(this.mimeType),e},e.prototype.getCodecs=function(){var e,t=this.getCurrentAdaptationSet();return null!==(e=t&&(0,B.getCodecsFromAdaptationSet)(t))&&void 0!==e?e:void 0},e.prototype.getLiveEdgeTime=function(){return this.maybeIsLive()?this.mpdHandler.getLiveEdgeTime():-1},e.prototype.setAdaptionLogicStartupPhase=function(){var e;null===(e=this.adaptationService)||void 0===e||e.setStartupPhase(this.mimeType)},e.prototype.getMimeType=function(){return this.mimeType},e.prototype.removeCachedSegmentsOfPrecedingPeriods=function(){var e=this,t=this.context.store.getState(),i=t?(0,S.getMetricsState)(t):void 0;if(this.segmentManager&&i){var n=this.sourceStore.getState(),r=n?(0,j.getPlayingPeriodId)(n):void 0;this.segmentManager.removeCachedInitSegments((function(t){return e.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.isPrecedingPeriod(r,t.getPeriodId())}),!1,e.context.sourceContext.sourceIdentifier)})),((0,S.getMetricsHistory)(i,this.mimeType,y.MetricType.CachedInitSegments)||[]).filter((function(t){var i=t.value.internalRepId.periodId;return e.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.isPrecedingPeriod(r,i)}),!1,e.context.sourceContext.sourceIdentifier)})).forEach((function(t){return e.context.store.dispatch((0,v.removeMetricsValue)(e.mimeType,y.MetricType.CachedInitSegments,t))}))}},e.prototype.canLoad=function(){var e;return Boolean(null===(e=this.segmentManager)||void 0===e?void 0:e.canLoad())},e.prototype.isTransmuxerRequired=function(){return this.hls.isTransmuxingRequired},e.prototype.setTransmuxer=function(e){this.hls.transmuxer=e},e.prototype.getTransmuxer=function(){return this.hls.transmuxer},e.prototype.resetTransmuxer=function(){this.hls.transmuxer&&this.hls.transmuxer.reset()},e.prototype.maybeIsHlsManifest=function(){return this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.isHlsManifest()}),!1,this.context.sourceContext.sourceIdentifier)},e.prototype.maybeIsSmoothManifest=function(){return this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.isSmoothManifest()}),!1,this.context.sourceContext.sourceIdentifier)},e.prototype.maybeIsLive=function(){return this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(e){return e.isLive()}),!1,this.context.sourceContext.sourceIdentifier)},e.prototype.maybeFindPeriod=function(e){return this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(t){return t.findPeriod(e)}),null,this.context.sourceContext.sourceIdentifier)},e.prototype.maybeGetAdaptationSet=function(e){return this.context.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(t){return t.getAdaptationSet(e)}),null,this.context.sourceContext.sourceIdentifier)},e.prototype.dispose=function(){var e,t;(0,l.dispose)(this.mpdHandler),(0,l.dispose)(this.timestampOffsetService),(0,l.dispose)(this.segmentPreProcessor),null===(e=this.adaptationService)||void 0===e||e.release(this.mimeType),this.hlsTimelineTracker&&this.hlsTimelineTracker.dispose(),(0,M.isContextAvailable)(this.context)&&(null===(t=this.context.serviceManager.get(c.ServiceName.SegmentService))||void 0===t||t.disposeOfType(this.mimeType,this.currentLangObj)),this.parkedSegmentSubscriptions.forEach((function(e){return e()}))},e}();function ie(e,t){var i=t.findIndex((function(t){return(0,Y.areSegmentsIdentical)(t,e)}));i>-1&&t.splice(i,1)}function ne(e,t){var i=(0,J.getSegmentInfoTimeRange)(e),n=(0,V.getTrackIdentifier)(e);i&&(null==t||t.dispatch((0,q.removeStreamTimeRange)(n,i,V.StreamTimeRangeType.Loading)))}function re(e,t,i,n,r){var o=e.serviceManager.get(c.ServiceName.AdaptationService),a=e.serviceManager.get(c.ServiceName.ManifestService,e.sourceContext.sourceIdentifier),s=function(i,r){var s=Boolean(null==o?void 0:o.shouldDownloadBeCancelled(t,i,r));return(null==a?void 0:a.isHlsManifest())&&n.possiblyDependentSegments&&s?(e.logger.debug("Download should be cancelled, but this is disabled for segments not starting with key frames"),!1):s};return e.serviceManager.maybeCall(c.ServiceName.SegmentService,(function(e){return e.add({mimeType:t,shouldDownloadBeCancelledCallback:s},i,r)}))}function oe(e){var t=e.split("/")[1].toLowerCase();return Object.values(_.ContainerFormat).find((function(e){return e===t}))}function ae(e){return e.transmuxedSegments.some((function(e){return P.MimeTypeHelper.isAV(e.getMimeType())&&!e.isIndependentlyDecodable()}))}function se(e,t){if(e&&(0,M.isContextAvailable)(t))return t.serviceManager.maybeCall(c.ServiceName.ManifestService,(function(t){return t.getRepresentationById(e)}),void 0,t.sourceContext.sourceIdentifier)}function ue(e){return e instanceof s.PlayerError?[a.ErrorCode.SOURCE_MANIFEST_INVALID,a.ErrorCode.SOURCE_COULD_NOT_LOAD_MANIFEST].includes(e.code):e===h.RequestError.Failed||e===h.RequestError.TimedOut}t.SegmentController=te},59305:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.StreamReadError=void 0,function(e){e.Aborted="aborted",e.ParallelRead="parallelread"}(i||(t.StreamReadError=i={}))},59839:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.addStreamTimeRange=o,t.removeStreamTimeRange=a,t.resetStreamTimeline=s;var n=i(15231),r=i(1590);function o(e,t,i){var o={start:t.start,end:t.end,type:i};return(0,n.createAction)(r.StreamTimelineActionType.StreamTimeRangeAdd,{mimeType:e,streamTimeRange:o})}function a(e,t,i){return(0,n.createAction)(r.StreamTimelineActionType.StreamTimeRangeRemove,{mimeType:e,range:t,type:i})}function s(e){return(0,n.createAction)(r.StreamTimelineActionType.StreamTimelineReset,e)}},59879:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.StreamTimeService=void 0;var n=i(18665),r=i(81361),o=i(22645),a=function(){function e(e,t){this.serviceManager=e,this.sourceIdentifier=t}return Object.defineProperty(e.prototype,"sourceState",{get:function(){return this.serviceManager.get(n.ServiceName.SourceStoreService,this.sourceIdentifier).getState()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"streamTimelineState",{get:function(){var e;return null===(e=this.sourceState)||void 0===e?void 0:e.streamTimeline},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bufferStateMap",{get:function(){var e;return null===(e=this.sourceState)||void 0===e?void 0:e.buffer.bufferStateMap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"playerStateService",{get:function(){return this.serviceManager.get(n.ServiceName.PlayerStateService)},enumerable:!1,configurable:!0}),e.prototype.getTimeForNextSegment=function(e){var t,i,n,o,a=(0,r.findFromEnd)(this.streamTimelineState[e]||[],s),u=null===(t=this.bufferStateMap)||void 0===t?void 0:t[e],d=[a,(null===(i=null==u?void 0:u.loadedRanges)||void 0===i?void 0:i[u.loadedRanges.length-1])||(null===(n=null==u?void 0:u.rendererRanges)||void 0===n?void 0:n[u.rendererRanges.length-1])].filter((function(e){return e})).map((function(e){return e.end})),c=Math.max.apply(Math,d);return isFinite(c)?c:(null===(o=this.playerStateService)||void 0===o?void 0:o.seekingOrTimeshifting)?this.playerStateService.targetPlaybackTime:0},e.prototype.dispose=function(){},e}();function s(e){return[o.StreamTimeRangeType.Loading,o.StreamTimeRangeType.Failed].includes(e.type)}t.StreamTimeService=a},60704:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentPreProcessor=void 0;var n=i(18665),r=i(62510),o=i(76650),a=i(8272),s=i(27279),u=i(79814),d=i(34586),c=i(91520),l=i(16368),p=i(43752),f=i(59839),h=i(22645),g=i(93109),m=i(74830),v=i(41661),S=i(9378),y=i(28915),T=i(67550),b="SegmentPreProcessor",M=function(){function e(e,t,i,r,o,a,s){this.context=e,this.mimeType=t,this.onDataAvailableCallback=i,this.hls=r,this.segmentUnavailabilityHandler=o,this.timestampOffsetService=a,this.segmentParser=s,this.isKeyframeRecoveryOngoing=!1,this.downloadRepresentationId=null;var u=e.sourceContext.sourceIdentifier;this.manifestService=this.context.serviceManager.get(n.ServiceName.ManifestService,u)}return Object.defineProperty(e.prototype,"logger",{get:function(){return this.context.logger},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventHandler",{get:function(){return this.context.eventHandler},enumerable:!1,configurable:!0}),e.prototype.setHlsTimelineTracker=function(e){this.hlsTimelineTracker=e},e.prototype.setMpdHandler=function(e){this.mpdHandler=e},e.prototype.onMuxedSegmentAvailable=function(e,t,i,n){var r=this,o=t.find((function(e){return I(e,r.hls)}));if(o){var a=!o.getRepresentationId().equals(this.downloadRepresentationId),s=this.maybeHandleTimelineGap(o,i,a),u=s.shouldMakeSegmentAvailable,d=s.shouldCacheSegment;u?t.forEach((function(t){var o=e.metadata[e.transmuxedSegments.indexOf(t)];r.onSegmentAvailable(t,o,i,n)||n.cacheSegment(e.originalSegment)})):(d&&n.cacheSegment(e.originalSegment),E(e.originalSegment,this.context))}},e.prototype.onSegmentAvailable=function(e,t,i,n){var r,o,a;return this.logger.debug("onSegmentAvailable ".concat(e.getMimeType()).concat(e.isInit()?" init":""," segment [").concat(e.getUrl(),"]")),this.segmentUnavailabilityHandler.downloadSuccess(e.isInit()),null===(o=(r=this.mpdHandler).generateSmoothInitSegment)||void 0===o||o.call(r,e,this.segmentParser),this.hls.isTransmuxingRequired||d.TextSegmentAnalyzer.isPlainTextPayload(e.getData())||null===(a=this.segmentParser)||void 0===a||a.parseSegment(e),!!e.isInit()||this.onDataSegmentAvailable(e,t,i,n)},e.prototype.onDataSegmentAvailable=function(e,t,i,r){var o,a,s,u,c=this,l=!e.getRepresentationId().equals(this.downloadRepresentationId);I(e,this.hls)&&l&&this.updateDownloadedRepresentation(e,this.downloadRepresentationId);var p=this.hls.isTransmuxingRequired,f=!0;if(this.manifestService.isHlsManifest()){if(this.maybeUpdateLlHlsSegmentInfo(e),!p&&!d.TextSegmentAnalyzer.isPlainTextPayload(e.getData())){(isNaN(this.mpdHandler.getTimestampOffset())||0===this.mpdHandler.getTimestampOffset())&&(0,m.initializeHlsSegmentStartTimes)(e,this.context,this.mpdHandler),this.timestampOffsetService.maybeAdjustTimestampOffsetAtDiscontinuityChange(e,this.mpdHandler)&&(null===(o=this.segmentParser)||void 0===o||o.parseSegment(e));var h=this.maybeHandleTimelineGap(e,i,l),g=h.shouldMakeSegmentAvailable,v=h.shouldCacheSegment;if(!g)return v&&r.cacheSegment(e),!1}R(e)&&this.timestampOffsetService.maybeAdjustTimestampOffsetAtDiscontinuityChange(e,this.mpdHandler),this.manifestService.initSegmentStartTimesFromReferenceSegment(e)}if(p&&this.hls.possiblyDependentSegments&&l&&I(e,this.hls)&&!this.isKeyframeRecoveryOngoing){this.logger.debug("Just switched playlists, isIndependentlyDecodable=".concat(e.isIndependentlyDecodable(),", (").concat(e.getMimeType(),", ").concat(e.getUrl(),")"));var S=this.mpdHandler.getIndex()>1;!e.isIndependentlyDecodable()&&S&&(this.logger.debug("Started keyframe recovery"),this.mpdHandler.setIndex(this.mpdHandler.getIndex()-2),f=!1,this.hls.discardNextSegment=!0,this.isKeyframeRecoveryOngoing=!0)}if(!f)return!1;C(e,this.context),this.context.serviceManager.get(n.ServiceName.TimedMetadataService).onSegmentAvailable({segment:e,extractedMetadata:t,isSegmentOfMainStream:I(e,this.hls),presentationTimeOffset:this.mpdHandler.getTimestampOffset()}),this.context.serviceManager.maybeCall(n.ServiceName.SubtitleService,(function(i){i.handleClosedCaptions(c.mpdHandler,e,null==t?void 0:t.closedCaptions,p,e.getCodec())}),void 0,null===(a=this.context.sourceContext)||void 0===a?void 0:a.sourceIdentifier);var y=e.getPlaybackTime(),T=null===(s=e.getSegmentInfo())||void 0===s?void 0:s.startTime,M=T?Math.abs(y-T):void 0;return this.logger.debug("[".concat(b,"][").concat(e.getMimeType(),"]: playbackTimeForSegment=").concat(y," expected=").concat(T," (diff=").concat(null==M?void 0:M.toFixed(3),") ").concat(e.getUrl())),I(e,this.hls)&&(null===(u=this.hlsTimelineTracker)||void 0===u||u.trackPlaybackTime(e),this.isKeyframeRecoveryOngoing=!1),this.onDataAvailableCallback(e),f},e.prototype.maybeUpdateLlHlsSegmentInfo=function(e){var t=c.ModuleManager.get(l.ModuleName.LowLatency,!1);(0,g.isPlayingLowLatencyHls)(this.context)&&t&&t.applyUpdatedSegmentInfoProps(e,this.manifestService)},e.prototype.updateDownloadedRepresentation=function(e,t){this.triggerOnDownloadQualityChanged(e.getMimeType(),t,e.getRepresentationId());var i=e.getSegmentInfo(),n={id:i.representationId,bitrate:i.bitrate,width:i.width,height:i.height};this.context.store.dispatch((0,o.addMetricsValue)(this.mimeType,a.MetricType.DownloadedRepresentation,n)),this.downloadRepresentationId=e.getRepresentationId()},e.prototype.triggerOnDownloadQualityChanged=function(e,t,i){var n;if(u.MimeTypeHelper.isVideo(e))n=r.PlayerEvent.VideoDownloadQualityChanged;else{if(!u.MimeTypeHelper.isAudio(e))return;n=r.PlayerEvent.AudioDownloadQualityChanged}var o=(0,p.representationToQuality)(this.manifestService.getRepresentationById(t),e),a=(0,p.representationToQuality)(this.manifestService.getRepresentationById(i),e);this.eventHandler.dispatchEvent(n,{targetQuality:a,targetQualityId:a.id,sourceQuality:o,sourceQualityId:o?o.id:null})},e.prototype.hasHlsTimelineGap=function(e,t){var i={hasGap:!1};if(e&&this.manifestService.isHlsManifest()&&!R(e)&&I(e,this.hls)&&this.hlsTimelineTracker){var n=S.SegmentInfoProvider.findHlsRepresentationForSegment(e,this.manifestService.getAdaptationSet(t));if(n&&(i=this.hlsTimelineTracker.checkForTimelineGap(e,n.representation,n.index)).hasGap&&void 0!==i.correctedIndex){this.logger.debug("Encountered timeline gap (".concat(i.gapSizeSec,"), just loaded ").concat(e.getUrl()));var r=n.representation._internalId,o=this.mpdHandler instanceof T.SegmentListMPDHandler&&this.mpdHandler.getSegmentListEntries(r)&&this.mpdHandler.getSegmentListEntries(r)[i.correctedIndex];if(o&&(0,v.areSegmentsIdentical)(e.getSegmentInfo(),o))return this.logger.debug("Gap handling: Segment is identical to the one we just loaded, not rewinding"),i.hasGap=!1,i;o&&o.hasDownloadFailed?(this.logger.debug("Gap handling: Could not handle gap, segment has failed to download"),i.couldHandle=!1):(this.logger.debug("Gap handling: Correcting index from ".concat(this.mpdHandler.getIndex()," to ").concat(i.correctedIndex)),this.mpdHandler.setIndex(i.correctedIndex))}}return i},e.prototype.maybeHandleTimelineGap=function(e,t,i){if(!(this.hlsTimelineTracker.isGapVerificationNeeded()||this.hlsTimelineTracker.isSeekTargetVerificationNeeded()||i))return{shouldMakeSegmentAvailable:!0,shouldCacheSegment:!1};var n=this.hasHlsTimelineGap(e,t);return n.hasGap?n.couldHandle?P(n)?{shouldMakeSegmentAvailable:!1,shouldCacheSegment:!0}:{shouldMakeSegmentAvailable:!1,shouldCacheSegment:!1}:(this.hlsTimelineTracker.reset(),{shouldMakeSegmentAvailable:!0,shouldCacheSegment:!1}):{shouldMakeSegmentAvailable:!0,shouldCacheSegment:!1}},e.prototype.dispose=function(){this.downloadRepresentationId=null,this.hlsTimelineTracker=null,this.manifestService=null,this.onDataAvailableCallback=null,this.hls=null,this.segmentUnavailabilityHandler=null,this.timestampOffsetService=null,this.segmentParser=null,this.mpdHandler=null},e}();function I(e,t){return!t.isAvMuxedTogether||u.MimeTypeHelper.isVideo(e.getMimeType())}function P(e){var t;return(null!==(t=e.gapSizeSec)&&void 0!==t?t:0)>0}function R(e){return u.MimeTypeHelper.isSubtitle(e.getMimeType())}function C(e,t){var i=(0,s.getSourceStore)(t),n=e.getSegmentInfo(),r=(0,y.getSegmentInfoTimeRange)(n),o=e.getPlaybackTimeRange(),a=(0,h.getTrackIdentifier)(n);i&&r&&o&&(i.dispatch((0,f.removeStreamTimeRange)(a,r,h.StreamTimeRangeType.Loading)),i.dispatch((0,f.addStreamTimeRange)(a,o,h.StreamTimeRangeType.Loading)))}function E(e,t){var i=(0,s.getSourceStore)(t),n=e.getSegmentInfo(),r=(0,y.getSegmentInfoTimeRange)(n),o=(0,h.getTrackIdentifier)(n);i&&r&&i.dispatch((0,f.removeStreamTimeRange)(o,r,h.StreamTimeRangeType.Loading))}t.SegmentPreProcessor=M},61743:function(e,t,i){var n=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.ManifestUpdater=void 0;var r=i(99162),o=i(43752),a=function(e){function t(t,i,n,r,o){var a=e.call(this,t,o)||this;return a.context=t,a.manifestLoader=i,a.manifest=n,a.settings=r,a}return n(t,e),t.prototype.getUpdatedManifest=function(){return this.getPayload()},t.prototype.update=function(){var e=this;return this.manifestLoader.load(this.manifest._currentLoadingUrl).then((function(t){return e.manifest=t,t}))},t.prototype.getReloadIntervalInSeconds=function(){var e=(0,o.getMinimumUpdatePeriodInSeconds)(this.manifest);return isNaN(e)?this.settings.minAllowedUpdatePeriod:Math.min(this.settings.maxAllowedUpdatePeriod,Math.max(this.settings.minAllowedUpdatePeriod,e))},t.prototype.getLastReloadTimestamp=function(){return this.manifest._requestTimestamp},t}(r.AbstractUpdater);t.ManifestUpdater=a},64330:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.SynchronizedTimeService=void 0;var n=i(331),r=i(77118),o=i(13127),a=i(89010),s=function(){function e(e,t){this.context=e,this.sourceContext=t,this.timeDifferenceMs=0;var i=new a.HttpHeadTimeProvider(this.context,new o.ISO8601TimeDecoder),n=new a.HttpGetTimeProvider(this.context,new o.XSDateTimeDecoder),r=new a.HttpGetTimeProvider(this.context,new o.ISO8601TimeDecoder),s=new a.StringTimeProvider(new o.XSDateTimeDecoder);this.serverTimeProviders={"urn:mpeg:dash:utc:http-head:2012":i,"urn:mpeg:dash:utc:http-head:2014":i,"urn:mpeg:dash:utc:http-xsdate:2012":n,"urn:mpeg:dash:utc:http-xsdate:2014":n,"urn:mpeg:dash:utc:http-iso:2012":r,"urn:mpeg:dash:utc:http-iso:2014":r,"urn:mpeg:dash:utc:direct:2012":s,"urn:mpeg:dash:utc:direct:2014":s},this.synchronizeWithConfiguredServer()}return e.prototype.synchronizeWithConfiguredServer=function(){var e=(0,r.getUtcTimingSources)(this.context.config,this.sourceContext.source);e.length>0&&this.synchronizeWithServer(e)},e.prototype.getTimeDifference=function(){return this.timeDifferenceMs},e.prototype.requestServerTime=function(e){var t=this,i=e.shift();if(!i)return Promise.reject(new Error("No time source left to try"));if(!this.serverTimeProviders[i.schemeIdUri])return this.context.logger.debug("UTCTiming scheme ".concat(i.schemeIdUri," is not supported.")),this.requestServerTime(e);this.currentServerTimeProvider=this.serverTimeProviders[i.schemeIdUri];var n=Date.now();return this.currentServerTimeProvider.requestTime(i.value).then((function(e){return e+(Date.now()-n)/2})).catch((function(){return t.context.logger.debug("UTCTiming scheme ".concat(i.schemeIdUri," resulted in an error")),t.requestServerTime(e)}))},e.prototype.synchronizeWithServer=function(e,t){var i=this;return void 0===t&&(t=0),this.driftPromise||(this.driftPromise=this.requestServerTime(e).then((function(e){i.currentServerTimeProvider=void 0,i.timeDifferenceMs=e+(0,n.toMilliSeconds)(t)-Date.now(),i.context.logger.debug("Synchronized client time with time server - drift: ".concat((0,n.toSeconds)(i.timeDifferenceMs),"s"))})).catch((function(){i.context.logger.debug("Synchronizing client time with time server unsuccessful")}))),this.driftPromise},e.prototype.getSynchronizationPromise=function(){return this.driftPromise},e.prototype.reset=function(){this.timeDifferenceMs=0,this.driftPromise=void 0,this.currentServerTimeProvider&&(this.currentServerTimeProvider.cancelRequest(),this.currentServerTimeProvider=void 0)},e.prototype.dispose=function(){this.reset()},e}();t.SynchronizedTimeService=s},64729:function(e,t){function i(e,t,i,n){var r,o=i.getPeriodIdForTime(e);if(!o)return e;var a=i.isTimeNearPeriodEnd(e,o,t),s=i.getNextPeriod(o);return a&&s?(n.debug("Target time ".concat(e," too close to end of period ").concat(o,", adjusting to next period start ").concat(s.start)),null!==(r=s.start)&&void 0!==r?r:e):e}Object.defineProperty(t,"__esModule",{value:!0}),t.adjustTargetTimeToPeriodStart=i},66137:function(e,t,i){var n,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},o=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.EncryptionKeyReducer=void 0;var a=i(21829),s=i(56092),u=[];t.EncryptionKeyReducer=(0,a.default)(u,((n={})[s.EncryptionKeyActionKey.AddEncryptionKey]=function(e,t){var i=t.payload;return o(o([],e,!0),[r({},i)],!1)},n[s.EncryptionKeyActionKey.UpdateEncryptionKey]=function(e,t){var i=t.payload,n=e.findIndex((function(e){return e.uri===i.uri}));if(-1===n)return e;var a=e[n],s=o([],e,!0);return s.splice(n,1,r(r({},a),i)),s},n[s.EncryptionKeyActionKey.RemoveEncryptionKey]=function(e,t){var i=t.payload;return o([],e,!0).filter((function(e){return e.uri!==i.keyUri}))},n))},66864:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentLoader=t.State=t.SegmentLoadingErrorReason=void 0;var r,o,a=i(52442),s=i(25550),u=i(28764),d=i(18665),c=i(62510),l=i(90637),p=i(6311),f=i(23234),h=i(36564),g=i(67345),m=i(76650),v=i(28819),S=i(8272),y=i(56093),T=i(79814),b=i(331),M=i(70016),I=i(91520),P=i(16368),R=i(41108),C=i(73543),E=i(48727),A=i(82311),x=i(25040),k=i(40392),L=i(86674),_=i(36225),B=i(5206),D=i(4141),w=i(72207);!function(e){e.CANCEL="CANCEL",e.ERROR_LOADING="ERROR_LOADING",e.ERROR_DECRYPTING="ERROR_DECRYPTING",e.BUSY="BUSY",e.UNKNOWN="UNKNOWN",e.TIMEOUT="TIMEOUT",e.ABR_TIMEOUT="ABR_TIMEOUT"}(r||(t.SegmentLoadingErrorReason=r={})),function(e){e[e.IDLE=1]="IDLE",e[e.LOADING=2]="LOADING",e[e.CANCELLING=3]="CANCELLING",e[e.TERMINATED=4]="TERMINATED",e[e.TIMEOUT=5]="TIMEOUT",e[e.ABR_TIMEOUT=6]="ABR_TIMEOUT"}(o||(t.State=o={}));var O=408,N=.1,F=function(){function e(e,t,i,r){void 0===r&&(r=!1);var a=this;if(this.context=e,this.cancelLoading=function(e){var t,i,n,r,s,u,d;if(void 0===e&&(e=o.CANCELLING),clearTimeout(a.loadTimeoutID),clearInterval(a.progressCheckIntervalId),a.loadTimeoutID=null,a.isLoading()){var c=a.currentSegmentInfoMap[a.loadingUrl];if(delete a.currentSegmentInfoMap[a.loadingUrl],a.state=e,a.loader.cancel(),void 0!==a.currentRequestProgress){var l=null!==(i=null===(t=a.currentRequestProgress.responseTiming)||void 0===t?void 0:t.headersReceivedTimestamp)&&void 0!==i?i:-1,f=null!==(r=null===(n=a.currentRequestProgress.responseTiming)||void 0===n?void 0:n.openedTimestamp)&&void 0!==r?r:-1,h=null!==(s=a.currentRequestProgress.elapsedTime)&&void 0!==s?s:-1,g=l-f;h>=0&&g>=0&&h>g&&a.context.store.dispatch((0,m.addMetricsValue)(a.mimeType,S.MetricType.DownloadInformation,{bytes:a.currentRequestProgress.loadedBytes,time:h,timeToFirstByte:g})),(null===(u=a.currentRequestProgress)||void 0===u?void 0:u.chunks)&&c&&a.dispatchCancelledChunkedDownloadMetrics(c),a.canceledSegmentRequestFinishedEvent={success:!1,httpStatus:O,url:a.loadingUrl,downloadTime:h,size:a.currentRequestProgress.loadedBytes,duration:null!==(d=a.segmentDuration)&&void 0!==d?d:0,isInit:a.isInit,mimeType:a.mimeType,uid:void 0,timeToFirstByte:(0,p.hasLoadedData)(a.currentRequestProgress)?g:-1}}a.currentRequestProgress=void 0}},this.checkLoadingProgress=function(){var e,t,i,r=null!==(i=null===(t=null===(e=a.currentRequestProgress)||void 0===e?void 0:e.responseTiming)||void 0===t?void 0:t.sendTimestamp)&&void 0!==i?i:-1;void 0===a.currentRequestProgress||r<0||a.onProgress(n(n({},a.currentRequestProgress),{elapsedTime:f.TimingUtil.getHiResTimestamp()-r}))},this.onProgress=function(e){var t,i=.5,r=null!==(t=e.elapsedTime)&&void 0!==t?t:-1,s=a.currentSegmentInfoMap[a.loadingUrl];a.currentRequestProgress=n(n({},e),{segmentDuration:a.segmentDuration,url:a.loadingUrl,representationBitrate:s.bitrate}),a.isInit||r<=i||a.state!==o.LOADING||a.onDownloadShouldBeCancelledCallback(a.currentRequestProgress,s.internalRepresentationId)&&a.cancelLoading(o.ABR_TIMEOUT)},this.onSuccess=function(e,t,i){var n,r,o,s,u,d,l=a.context.store.getState(),p=l?(0,v.getMetricsState)(l):void 0;if(a.context.logger.debug("Downloaded ".concat(a.mimeType," ").concat(a.isInit?"init":"data"," segment in: ").concat(null===(n=e.elapsedTime)||void 0===n?void 0:n.toFixed(2),", TTFB: ").concat(null===(r=e.timeToFirstByte)||void 0===r?void 0:r.toFixed(2),", quality: ").concat(null!==(s=null===(o=a.currentSegmentInfoMap[a.loadingUrl])||void 0===o?void 0:o.representationId)&&void 0!==s?s:"")),!a.isInit&&p&&a.mimeType in p&&(a.context.store.dispatch((0,m.addMetricsValue)(a.mimeType,S.MetricType.DownloadTime,t)),a.context.store.dispatch((0,m.addMetricsValue)(a.mimeType,S.MetricType.DownloadSuccess,!0)),(0,h.isDownloadTimeInformationValid)(e)&&a.context.store.dispatch((0,m.addMetricsValue)(a.mimeType,S.MetricType.DownloadInformation,{bytes:e.length,time:e.elapsedTime,timeToFirstByte:e.timeToFirstByte})),null===(u=a.currentRequestProgress)||void 0===u?void 0:u.chunks)){a.logger.debug("[SegmentLoader] ",a.currentRequestProgress.chunks);var f=(0,L.calculateDownloadRates)(a.currentRequestProgress.chunks),g=(0,L.calcAverage)((0,L.filterValuesExceedingAverageByMargin)(f)),y=a.currentRequestProgress.loadedBytes,T=a.currentSegmentInfoMap[a.loadingUrl],b=T.duration,M=void 0!==b?(0,k.bytesToBits)(y)/b:void 0,I=T.bitrate;a.logger.debug("[SegmentLoader] DL rate (AVG-based filter): ".concat((0,L.bpsToMbps)(g),", segmentBitrate: ").concat(M)),a.context.store.dispatch((0,m.addMetricsValue)(a.mimeType,S.MetricType.ChunkedDownloadInformation,{downloadRate:g,segmentBitrate:M,representationBitrate:I}))}clearTimeout(a.loadTimeoutID),clearInterval(a.progressCheckIntervalId),a.loadTimeoutID=void 0;var P={success:!0,httpStatus:e.status,url:e.url,downloadTime:e.elapsedTime,size:e.length,duration:a.segmentDuration,isInit:a.isInit,mimeType:a.mimeType,uid:a.loadingUid,timeToFirstByte:null!==(d=e.timeToFirstByte)&&void 0!==d?d:-1};a.eventHandler.dispatchEvent(c.PlayerEvent.SegmentRequestFinished,P)},this.logger=e.logger,this.eventHandler=e.eventHandler,this.customLoaderArgs=e.config.tweaks.segmentLoaderArgs||null,this.customLoader=e.config.tweaks.segmentLoader||null,this.segmentDuration=1,this.state=o.IDLE,this.mimeType=t,this.keyLoader=new B.KeyLoader(this.context),this.onDownloadShouldBeCancelledCallback=i,this.currentLoadPromise=null,this.currentSegmentInfoMap={},this.canceledSegmentRequestFinishedEvent=null,this.isPrefetchingLoader=r,I.ModuleManager.has(P.ModuleName.ContainerMP4)){var s=I.ModuleManager.get(P.ModuleName.ContainerMP4);this.cmafChunkParser=new s.CmafChunkParser(this.context)}this.setupContentLoader()}return e.prototype.dispatchCancelledChunkedDownloadMetrics=function(e){var t,i,n,r,o,a,s,u,d=(null!==(n=null===(i=null===(t=this.currentRequestProgress)||void 0===t?void 0:t.responseTiming)||void 0===i?void 0:i.progressTimestamp)&&void 0!==n?n:f.TimingUtil.getHiResTimestamp())-(null!==(a=null===(o=null===(r=this.currentRequestProgress)||void 0===r?void 0:r.responseTiming)||void 0===o?void 0:o.headersReceivedTimestamp)&&void 0!==a?a:0),c=(0,k.bytesToBits)(null!==(u=null===(s=this.currentRequestProgress)||void 0===s?void 0:s.loadedBytes)&&void 0!==u?u:0)/d,l=void 0===e.duration?1:Math.max(1-d/e.duration,N);this.context.store.dispatch((0,m.addMetricsValue)(this.mimeType,S.MetricType.ChunkedDownloadInformation,{downloadRate:c*l,segmentBitrate:void 0,representationBitrate:e.bitrate}))},e.prototype.isLoading=function(){var e=Boolean(this.keyLoader&&this.keyLoader.isLoading());return this.state===o.LOADING||this.state===o.CANCELLING||this.loader&&this.loader.isLoading()||e},e.prototype.isRetrying=function(){return this.loader&&this.loader.isRetrying()},e.prototype.load=function(e){var t=this;if(this.isLoading())return this.logger.debug("Error loading segment, loader is busy!"+this.loadingUrl),Promise.reject({reason:r.BUSY});this.loader&&this.loader.dispose();var i=this.defaultLoaderArgs;return e.isPreloadHint&&(i=n(n({},i),{maxRetries:0})),this.canceledSegmentRequestFinishedEvent=null,this.currentRequestProgress=void 0,this.loader=(0,D.getLoader)(this.context,i),this.state=o.LOADING,this.loadingUrl=e.url,this.loadingUid=btoa(this.loadingUrl),this.currentSegmentInfoMap[this.loadingUrl]=e,this.isInit=e.isInitSegment,this.segmentDuration=e.duration,this.currentLoadPromise=this.maybeLoadEncryptionKey(e).then((function(){return t.loadSegment(e)})).catch((function(e){throw t.state=o.IDLE,t.currentLoadPromise=null,e})),this.currentLoadPromise},e.prototype.maybeLoadEncryptionKey=function(e){var t,i=this;this.encryptionInfo=e.key;var n=Boolean(null===(t=this.encryptionInfo)||void 0===t?void 0:t.buffer);if(!this.encryptionInfo||n)return Promise.resolve();switch(this.encryptionInfo.method){case a.HlsEncryptionMethod.AES_128:return this.getKey(e).then((function(e){e&&(i.encryptionInfo.buffer=e)}));case a.HlsEncryptionMethod.SAMPLE_AES:return this.eventHandler.fireError(new u.PlayerError(s.ErrorCode.SOURCE_ENCRYPTION_METHOD_NOT_SUPPORTED,{"encryption-method":this.encryptionInfo.method,supported:a.HlsEncryptionMethod.AES_128},"The ".concat(a.HlsEncryptionMethod.SAMPLE_AES," encryption method is not supported."))),Promise.reject({reason:r.ERROR_DECRYPTING});case a.HlsEncryptionMethod.SAMPLE_AES_CTR:return Promise.resolve();default:return Promise.reject({reason:r.ERROR_DECRYPTING})}},e.prototype.getCurrentLoadPromise=function(){return this.currentLoadPromise},e.prototype.getSourceStore=function(){var e;return null===(e=this.context.serviceManager)||void 0===e?void 0:e.get(d.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier)},e.prototype.getKey=function(e){var t,i=null===(t=e.key)||void 0===t?void 0:t.uri,n=this.getSourceStore();if(!n||!i)return Promise.reject(new Error("Source store not available."));var r=n.getState(),o=(0,x.getEncryptionKey)(r,i);return(null==o?void 0:o.uri)?o.key?Promise.resolve(y.FormatHelper.hexToBytes(o.key).buffer):(0,_.waitForKeyToBeLoaded)(n,i):(0,_.addEncryptionKeyToStore)(i,this.keyLoader,n)},e.prototype.getPreFetchedSegment=function(e,t){var i;return this.isPrefetchingLoader||!(null===(i=this.context.adRestorationOptimizationService)||void 0===i?void 0:i.hasPrefetchedSegment(t))?null:(this.currentSegmentInfoMap[e]&&(this.currentSegmentInfoMap[e].wasPrefetched=!0),this.context.adRestorationOptimizationService.getPrefetchedSegment(t,this.onDownloadShouldBeCancelledCallback))},e.prototype.setupLoadingProgressCheckInterval=function(){clearInterval(this.progressCheckIntervalId);var e=(0,b.toMilliSeconds)(this.context.settings.SEGMENT_LOADING_PROGRESS_CHECK_INTERVAL);e<=0||(this.progressCheckIntervalId=window.setInterval(this.checkLoadingProgress,e))},e.prototype.loadSegment=function(e){var t,i=this,n={};e.byteRange&&(n.Range=["bytes=",e.byteRange.start,"-",e.byteRange.end].join(""));var r=this.context.sourceContext.source&&this.context.sourceContext.source.hasOwnProperty("options")&&Boolean(this.context.sourceContext.source.options.withCredentials);clearTimeout(this.loadTimeoutID),clearInterval(this.progressCheckIntervalId),this.loadTimeoutID=void 0,this.isInit||e.preventDownloadCanceling||(this.loadTimeoutID=setTimeout((function(){return i.cancelLoading(o.TIMEOUT)}),(0,b.toMilliSeconds)(this.context.settings.XHR_TIMEOUT)));var a=this.getPreFetchedSegment(this.loadingUrl,e);if(a)return this.context.logger.debug("return prefetched segment for",this.loadingUrl),a.then((function(e){var t=new w.Stream;return i.loadingFinished(),t.add(e),e.getSegmentInfo().wasPrefetched=!0,t.end(),t})).catch((function(e){throw e}));var s=null===(t=this.getSourceStore())||void 0===t?void 0:t.getState(),u=s&&(0,A.getContainerFormat)(s,T.MimeTypeHelper.getMediaType(this.mimeType)),d=(null==u?void 0:u.source)===C.ContainerFormat.MP4,c=Boolean(this.encryptionInfo)||Boolean(this.decrypter),l=this.context.settings.CHUNKED_CMAF_STREAMING&&this.cmafChunkParser&&d&&!e.isInitSegment&&!c;return this.setupLoadingProgressCheckInterval(),this.loader.load(this.loadingUrl,g.HttpRequestMethod.GET,g.HttpResponseType.ARRAYBUFFER,null,n,r,this.mimeType,l).then((function(e){return i.handleResponse(e.body)})).catch((function(e){throw i.createLoadingError(e)}))},e.prototype.createLoadingError=function(e){var t,i;[o.CANCELLING,o.TIMEOUT,o.ABR_TIMEOUT].includes(this.state)?(this.logger.debug("Cancelled ".concat(this.mimeType," download [").concat(this.loadingUrl,"]")),this.canceledSegmentRequestFinishedEvent&&(null===(t=this.eventHandler)||void 0===t||t.dispatchEvent(c.PlayerEvent.SegmentRequestFinished,this.canceledSegmentRequestFinishedEvent)),i={reason:H(this.state)}):this.state===o.LOADING&&(i={reason:r.ERROR_LOADING,info:{response:e,isInit:this.isInit}});return i},e.prototype.getAllLoadingPeriodIds=function(){var e=this;return Object.keys(this.currentSegmentInfoMap).map((function(t){var i;return null===(i=e.currentSegmentInfoMap[t])||void 0===i?void 0:i.periodId})).filter(M.isDefined)},e.prototype.getCurrentLoadingSegmentInfo=function(){return this.isLoading()&&this.loadingUrl&&this.currentSegmentInfoMap[this.loadingUrl]?this.currentSegmentInfoMap[this.loadingUrl]:null},e.prototype.attachDecrypter=function(e){this.decrypter=this.decrypter||e},e.prototype.attachClearKeyLoader=function(e){this.clearKeyLoader=this.clearKeyLoader||e},e.prototype.terminate=function(){var e,t=this;this.isLoading()?(null===(e=this.currentLoadPromise)||void 0===e||e.catch((function(){t.state=o.TERMINATED})),this.cancelLoading()):this.state=o.TERMINATED},e.prototype.createSegment=function(e){var t,i;i=this.isInit?E.SegmentInitType.INIT:E.SegmentInitType.NONE;var n=this.currentSegmentInfoMap[this.loadingUrl],r=new E.Segment(e,this.mimeType,null!==(t=n.codecs)&&void 0!==t?t:null,n.periodId,this.segmentDuration,i,!0,e.byteLength,n.internalRepresentationId,n.startTime);return r.setUrl(this.loadingUrl),r.setSegmentInfo(n),r},e.prototype.maybeDecryptResponseData=function(e,t){return this.encryptionInfo&&this.encryptionInfo.method===a.HlsEncryptionMethod.AES_128&&!t?this.decryptAes128(e):this.decrypter?this.decryptClearKey(e):Promise.resolve(e)},e.prototype.decryptAes128=function(e){return I.ModuleManager.has(P.ModuleName.Crypto)?(this.logger.debug("Decrypting AES-128-encryped segment "+this.loadingUrl+", KEY: 0x"+y.FormatHelper.bytesToHex(this.encryptionInfo.buffer)+", IV: 0x"+y.FormatHelper.bytesToHex(this.encryptionInfo.iv)),new(0,I.ModuleManager.get(P.ModuleName.Crypto).CryptoApi)(this.context,"AES-CBC",this.encryptionInfo.buffer,this.encryptionInfo.iv,this.mimeType).decrypt(e).then((function(e){if(!e||0===e.byteLength)throw{reason:r.ERROR_DECRYPTING};return e})).catch((function(){throw{reason:r.ERROR_DECRYPTING}}))):(this.context.eventHandler.fireError(new R.PlayerModuleMissingError(P.ModuleName.Crypto)),Promise.reject({reason:r.ERROR_DECRYPTING}))},e.prototype.decryptClearKey=function(e){var t=this,i=Promise.resolve(e);if(this.isInit)i=this.decrypter.parseInitSegment(e),this.clearKeyLoader&&(i=i.then((function(e){return t.clearKeyLoader.load(t.decrypter.getKeyIds()).then((function(i){return t.decrypter.addKeys(i),Promise.resolve(e)}))})));else if(this.decrypter.isEncrypted()){var n=y.FormatHelper.bytesToHex(this.decrypter.getKey());this.logger.debug("Decrypting SAMPLE-AES-encrypted segment ".concat(this.loadingUrl,", KEY: 0x").concat(n)),i=this.decrypter.decryptDataSegment(e)}return i.catch((function(){throw{reason:r.ERROR_DECRYPTING}}))},e.prototype.handleResponse=function(e,t){var i=this;if(void 0===t&&(t=!1),e instanceof ArrayBuffer){this.segmentStream=new w.Stream;var n=Date.now();this.maybeDecryptResponseData(e,t).then((function(t){var a;i.state===o.TERMINATED||i.state===o.CANCELLING?i.segmentStream.abort(r.CANCEL):(i.segmentStream.add(i.createSegment(t)),i.segmentStream.end());var s=i.currentSegmentInfoMap[i.loadingUrl];if(e!==t&&s){var u=(0,b.toSeconds)(Date.now()-n),d=s.internalRepresentationId,c={time:u,representationId:d};i.context.store.dispatch((0,m.addMetricsValue)(i.mimeType,S.MetricType.DecryptionTime,c)),i.context.logger.debug("Decrypted ".concat(i.mimeType," segment in: ").concat(null==u?void 0:u.toFixed(2),", quality: ").concat(null!==(a=null==d?void 0:d.representationId)&&void 0!==a?a:""))}})).catch((function(){return i.segmentStream.abort(r.ERROR_DECRYPTING)})).finally((function(){return i.loadingFinished()}))}else this.segmentStream=e.transform((function(e){return i.cmafChunkParser.appendData(e),i.cmafChunkParser.getCompleteChunks()}),(function(){}),(function(e){return e.message})).transform((function(e){return i.createSegment(e)}),(function(){return i.loadingFinished()}),(function(e){i.logger.debug("Error handling fetch segment request",e);var t=r.ERROR_LOADING;return e.message===l.RequestError.Canceled&&(t=H(i.state)),i.loadingFinished(),t}));return Promise.resolve(this.segmentStream)},e.prototype.loadingFinished=function(){this.state=o.IDLE,this.currentLoadPromise=null,this.cmafChunkParser&&this.cmafChunkParser.reset(),this.currentSegmentInfoMap&&delete this.currentSegmentInfoMap[this.loadingUrl]},e.prototype.dispose=function(){this.terminate(),clearTimeout(this.loadTimeoutID),clearInterval(this.progressCheckIntervalId),this.loader&&this.loader.dispose&&this.loader.dispose(),this.keyLoader&&this.keyLoader.dispose(),I.ModuleManager.has(P.ModuleName.Crypto)&&I.ModuleManager.get(P.ModuleName.Crypto).WebWorkerCrypto.dispose(),this.loader=null,this.customLoader=null,this.customLoaderArgs=null,this.keyLoader=null,this.loadingUrl=null,this.loadingUid=null,this.encryptionInfo=null,this.currentSegmentInfoMap={},this.currentRequestProgress=void 0,this.decrypter=null,this.eventHandler=null},e.prototype.setupContentLoader=function(){var e=this;this.defaultLoaderArgs=n(n({},(0,D.getLoaderConfig)(this.context,this.mimeType)),{onSuccess:this.onSuccess,onProgress:this.onProgress,onFailure:function(t,i){var n,r;e.context.store.dispatch((0,m.addMetricsValue)(e.mimeType,S.MetricType.DownloadSuccess,!1)),(null==i?void 0:i.name)===l.RequestError.TimedOut&&(e.state=o.TIMEOUT),clearTimeout(e.loadTimeoutID),clearInterval(e.progressCheckIntervalId),e.loadTimeoutID=void 0,delete e.currentSegmentInfoMap[e.loadingUrl],e.eventHandler.dispatchEvent(c.PlayerEvent.SegmentRequestFinished,{success:!1,httpStatus:t&&t.status||0,url:null!==(n=null==t?void 0:t.url)&&void 0!==n?n:e.loadingUrl,downloadTime:-1,size:-1,duration:e.segmentDuration,isInit:e.isInit,mimeType:e.mimeType,uid:void 0,timeToFirstByte:null!==(r=null==t?void 0:t.timeToFirstByte)&&void 0!==r?r:-1})}})},e}();function H(e){switch(e){case o.ABR_TIMEOUT:return r.ABR_TIMEOUT;case o.TIMEOUT:return r.TIMEOUT;default:return r.CANCEL}}t.SegmentLoader=F},67550:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentListMPDHandler=t.PHANTOM_SEGMENT_URL=void 0,t.assignSegmentStartTimesFromHlsRepresentation=I,t.copyPropValues=E,t.getMatchingPart=A;var r=i(52442),o=i(18665),a=i(42055),s=i(91397),u=i(81361),d=i(44388),c=i(331),l=i(70016),p=i(91520),f=i(16368),h=i(92103),g=i(93326),m=i(93109),v=i(73553),S=i(28915);t.PHANTOM_SEGMENT_URL="PHANTOM-SEGMENT";var y=function(){function e(e){this.context=e,this.sourceContext=e.sourceContext,this.settings=e.settings,this.logger=e.logger,this.manifestService=e.serviceManager.get(o.ServiceName.ManifestService,this.sourceContext.sourceIdentifier),this.distanceToListStartSeconds=0,this.segmentListMap={},this.timestampOffset=NaN}return Object.defineProperty(e.prototype,"playerStateService",{get:function(){return this.context.serviceManager.get(o.ServiceName.PlayerStateService)},enumerable:!1,configurable:!0}),e.prototype.createSegmentListEntry=function(e,t){var i;if(!e)return null;var o={isInitSegment:!1,url:e._media,duration:e._duration,internalRepresentationId:t,periodId:t.periodId};if(e._key&&e._key.method!==r.HlsEncryptionMethod.NONE&&(o.key=e._key),void 0!==e._playbackTime&&(o.startTime=e._playbackTime),e._dateTime&&(o.dateTime=e._dateTime),e._metadata&&(o.metadata=e._metadata),e._byteRange&&(o.byteRange=e._byteRange),e._init&&(o.init=e._init,(null===(i=e._init.key)||void 0===i?void 0:i.method)===r.HlsEncryptionMethod.NONE&&(o.init=n({},o.init),delete o.init.key)),void 0!==e._mediaSequence&&(o.mediaSequence=e._mediaSequence),void 0!==e._discontinuitySequenceNumber&&(o.discontinuitySequenceNumber=e._discontinuitySequenceNumber),e._parts){var a=o.dateTime;o.parts=e._parts.map((function(e){var t=P(e,o);return a&&(t.dateTime=a,t.startTime=(0,c.toSeconds)(a.getTime()),a=new Date(a.getTime()+(0,c.toMilliSeconds)(e.duration))),t}))}return o.segmentId=(0,S.generateSegmentId)(o),o},e.prototype.removeDroppedOutSegmentsFromList=function(e,t,i){if(t.SegmentURL.length<1)return e.entries=[],e.totalDuration=0,e.entries.length;for(var n=this.createSegmentListEntry(t.SegmentURL[0],i),r=0,o=0,a=0;a<e.entries.length;a++){var s=e.entries[a];if((0,h.isIdenticalSegmentInfo)(s,n))break;r+=s.duration,e.entries.splice(a,1),a--,o++,this.currentRepresentationId.equals(i)&&this.rewindOneSegment()}return e.totalDuration-=r,o},e.prototype.addNewSegmentsToList=function(e,t,i){for(var n=[],r=e.entries[e.entries.length-1],o=0,a=t.SegmentURL.length-1;a>=0;a--){var s=this.createSegmentListEntry(t.SegmentURL[a],i);if(r&&(0,h.isIdenticalSegmentInfo)(r,s)){if(R(r)){var u=C(r,s);e.totalDuration+=u}break}r&&(0,h.isIdenticalInitSegmentInfo)(r.init,s.init)&&(s.init=r.init),isNaN(s.duration)&&(s.duration=e.maximumSegmentDuration),s.duration/=e.timescale,o+=s.duration,n.unshift(s)}return e.totalDuration+=o,e.entries=e.entries.concat(n),n.length},e.prototype.updateSegmentList=function(e,t){var i,n,r=t.SegmentList,o=t._internalId.representationId,a=null===(i=this.currentRepresentationId)||void 0===i?void 0:i.equals(t._internalId),s=null===(n=null==r?void 0:r[0])||void 0===n?void 0:n.SegmentURL.length;if(!a||s){e.Uri&&e.Uri!==t.Uri&&this.resetSegmentListUponFailover(e,null==r?void 0:r[0],a),e.Uri=t.Uri,e.timescale=1,e.maximumSegmentDuration=1,e.startNumber=1;for(var u=function(i){var n=r[i];if(n.hasOwnProperty("_duration")&&(e.maximumSegmentDuration=Number(n._duration)),["_timescale","_startNumber","_presentationTimeOffset"].filter((function(e){return n.hasOwnProperty(e)})).forEach((function(t){return e[t.substr(1)]=n[t]})),d.updateInitInfo(e,n,t),n.hasOwnProperty("SegmentURL")){var a=d.removeDroppedOutSegmentsFromList(e,n,t._internalId),s=d.addNewSegmentsToList(e,n,t._internalId);d.logManifestUpdateStats(t._mimeType,o,a,s),d.manifestService.isHlsManifest()?I(e.entries,t._internalId,d.manifestService):d.assignSegmentStartTimesForDash(e,t._internalId)}},d=this,c=0;c<r.length;c++)u(c)}},e.prototype.resetSegmentListUponFailover=function(e,t,i){var n,r,o,a,s,d;if(this.manifestService.isLive()&&i){var c=null!==(o=null===(r=null===(n=e.entries)||void 0===n?void 0:n[0])||void 0===r?void 0:r.mediaSequence)&&void 0!==o?o:1/0,l=(null!==(s=null===(a=null==t?void 0:t.SegmentURL[0])||void 0===a?void 0:a._mediaSequence)&&void 0!==s?s:0)-c,p=L(e);!isNaN(l)&&l>0&&l<p&&this.rewindSegmentListIndexBy(l)}(0,u.clearArray)(null!==(d=e.entries)&&void 0!==d?d:[])},e.prototype.updateInitInfo=function(e,t,i){var n,r=null===(n=p.ModuleManager.get(f.ModuleName.DASH,!1))||void 0===n?void 0:n.initSegmentInfoSourceDetectors.isInitializationNode;(null==r?void 0:r(t))?e.init=p.ModuleManager.get(f.ModuleName.DASH,!1).initSegmentInfoProviders.provideSegmentInfoFromInitializationNodeProperty(t,i):delete e.init},e.prototype.logManifestUpdateStats=function(e,t,i,n){(i>0||n>0)&&this.logger.debug("[".concat(e,"][").concat(t,"] Updated segment list, removed ").concat(i," segments and added ").concat(n," segments"))},e.prototype.assignSegmentStartTimesForDash=function(e,t){var i=this.getMinStartTime(t),n=0;e.entries.forEach((function(e){var t;e.startTime=i+n,n+=null!==(t=e.duration)&&void 0!==t?t:0}))},e.prototype.extrapolateStartTimesFromDiscontinuityStarts=function(e){var t,i,n,r,o=null===(i=null===(t=this.getSourceStateService())||void 0===t?void 0:t.getState())||void 0===i?void 0:i.hls;if(o){var a=e.getRepresentationId().representationId,s=this.getSegmentListEntries(this.currentRepresentationId),u=p.ModuleManager.get(f.ModuleName.HLS).selectors,d=u.getDiscoSequenceTimings,c=u.getMergedDiscontinuityTimings,l=b(s,null!==(n=d(o,e.getMimeType()))&&void 0!==n?n:c(o));if(!l){var h=null===(r=this.getCurrentPeriod())||void 0===r?void 0:r.start;if(void 0===h)return;l={index:0,discontinuityStartTime:h},this.logger.debug("Could not find discontinuity boundary with start time, using period start time ".concat(h))}this.logger.debug("Assigning segment start times for rep ".concat(a," from discontinuity at index ").concat(l.index," with time ").concat(l.discontinuityStartTime)),s[l.index].startTime=l.discontinuityStartTime;for(var g=l.index-1;g>=0;g--)s[g].startTime=s[g+1].startTime-s[g+1].duration;for(g=l.index+1;g<s.length;g++)s[g].startTime=s[g-1].startTime+s[g-1].duration}},e.prototype.getSourceStateService=function(){return this.context.serviceManager.get(o.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier)},e.prototype.getListIndexForSegment=function(e,t){for(var i=-1,n=this.getSegmentListEntries(t),r=n.length-1;r>=0;r--)if((0,h.isIdenticalSegmentInfo)(n[r],e)){i=r;break}return i},e.prototype.populateSegmentListMap=function(e){var t,i=this,n=null===(t=this.manifestService)||void 0===t?void 0:t.getAdaptationSet(e);n&&n.Representation.forEach((function(e){var t=i.segmentListMap[e._internalId.key()];if(t||(t=i.initializeSegmentList(e._internalId.key())),e.hasOwnProperty("SegmentList")&&(i.updateSegmentList(t,e),p.ModuleManager.has(f.ModuleName.DASH))){var n=p.ModuleManager.get(f.ModuleName.DASH).initSegmentInfoSourceDetectors.isInitializationNode;if(!t.hasOwnProperty("init")&&e.hasOwnProperty("SegmentBase")&&e.SegmentBase&&n(e.SegmentBase[0])){var r=p.ModuleManager.get(f.ModuleName.DASH).initSegmentInfoProviders.provideSegmentInfoFromInitializationNodeProperty;t.init=r(e.SegmentBase[0],e)}}}))},e.prototype.selectNewRepresentationId=function(){var e,t,i=this,n=null===(e=this.context.serviceManager.get(o.ServiceName.SourceStoreService,this.sourceContext.sourceIdentifier).getState().activeTracks)||void 0===e?void 0:e[this.adaptationSetId.adaptationSetId];if((null===(t=null==n?void 0:n.selectedRepresentationId)||void 0===t?void 0:t.periodId)===this.adaptationSetId.periodId)return n.selectedRepresentationId;if(this.currentRepresentationId){var r=this.manifestService.getRepresentation(this.adaptationSetId,this.currentRepresentationId.representationId);if(r)return r._internalId}var a=function(e){return i.isSegmentInfoLoaded(e._internalId)};return(this.getMatchingRepresentationByBandwidth(a)||this.getMatchingRepresentationByBandwidth())._internalId},e.prototype.getMatchingRepresentationByBandwidth=function(e){return this.manifestService.getMatchingRepresentationByBandwidth(this.adaptationSetId,this.getCurrentBandwidth(),e)},e.prototype.shouldUpdateRepresentation=function(){var e=this.adaptationSetId.equals(this.currentRepresentationId),t=!this.currentRepresentationId||Boolean(this.manifestService.getRepresentationById(this.currentRepresentationId));return!this.currentRepresentationId||!e||!t},e.prototype.setAdaptationSetId=function(e){this.adaptationSetId=e,this.populateSegmentListMap(this.adaptationSetId);var t,i=this.manifestService.isLive(),n=0;this.shouldUpdateRepresentation()&&(this.currentRepresentationId=this.selectNewRepresentationId());var r=this.segmentListMap[this.currentRepresentationId.key()];if(r||(r=this.initializeSegmentList(this.currentRepresentationId.key())),r&&i&&r.entries&&r.entries.length>0){var o=r.entries.length-1;this.listIndex>o&&(n=this.listIndex-o),this.listIndex=Math.min(o,this.listIndex||0),this.listIndex=Math.max(0,this.listIndex),t=r.entries[this.listIndex]}if(this.listIndex=this.listIndex||0,i&&(this.distanceToListStartSeconds=this.getSegmentDuration()*this.settings.LIVE_SEGMENT_LIST_START_INDEX_OFFSET),i){if(t&&r){var a=this.getListIndexForSegment(t,this.currentRepresentationId);a>=0?this.listIndex=a+n:this.reset()}else this.reset();var s=this.checkForSegmentInfoError();s?this.rejectPendingSegmentInfoRequest(s):this.resolvePendingSegmentInfoRequests()}else this.resolvePendingSegmentInfoRequests()},e.prototype.initializeSegmentList=function(e){var t={totalDuration:0,entries:[],SegmentURL:[],startNumber:0,init:null,maximumSegmentDuration:1,timescale:1};return this.segmentListMap[e]=t,t},e.prototype.getCurrentBandwidth=function(){var e=0,t=this.getCurrentRepresentation();return t&&(e=t._bandwidth),e},e.prototype.checkForSegmentInfoError=function(){var e;if(this.manifestService.isHlsManifest()){var t=this.manifestService.getRepresentationById(this.currentRepresentationId);if(null===(e=null==t?void 0:t._hls)||void 0===e?void 0:e.loadFailureReason)return t._hls.loadFailureReason}return this.hasNext()||this.manifestService.isLastPeriod(this.currentRepresentationId.periodId)?null:v.SegmentInfoError.PERIOD_COMPLETE},e.prototype.reset=function(){this.settings.ENABLE_SEEK_FOR_LIVE?this.listIndex=0:this.timeShift(0)},e.prototype.syncSegmentStartTimeUsingMediaSequenceNumber=function(e,t){var i,n,r=this.manifestService.getRepresentationById(e),o=this.manifestService.getRepresentationById(t),a=(0,l.isNumber)(null===(i=null==o?void 0:o._hls)||void 0===i?void 0:i.mediaSequence)&&(0,l.isNumber)(null===(n=null==r?void 0:r._hls)||void 0===n?void 0:n.mediaSequence);if(r&&o&&a){var s=o._hls.mediaSequence-r._hls.mediaSequence,d=this.getSegmentListEntries(e),c=this.getSegmentListEntries(t);if(!(s>=d.length)){c[0].startTime=d[s].startTime;var p=function(e,t){(0,l.isNumber)(e.startTime)&&(0,l.isNumber)(e.duration)&&(t.startTime=e.startTime+e.duration)};(0,u.forEachFromIndex)(c,1,p)}}},e.prototype.setRepresentationId=function(e){var t=this.manifestService.getRepresentationById(e),i=this.getSegmentListEntries(this.currentRepresentationId)[this.listIndex];if(t){var n=void 0===this.currentRepresentationId,r=this.currentRepresentationId;if(this.currentRepresentationId=e,this.manifestService.isLive()&&t._hls)if(this.settings.HLS_SYNC_SEGMENT_PLAYBACK_TIME_VIA_MEDIA_SEQUENCE&&this.syncSegmentStartTimeUsingMediaSequenceNumber(r,this.currentRepresentationId),n)this.reset();else if(this.settings.HLS_SYNC_VIA_MEDIA_SEQUENCE){var o=this.manifestService.getRepresentationById(r)._hls.mediaSequence+this.listIndex;this.listIndex=o-t._hls.mediaSequence,this.listIndex=Math.max(this.listIndex,0)}else if(null!=(null==i?void 0:i.startTime)&&!isNaN(i.startTime)){var a=this.getIndexForTime(i.startTime);null===a||isNaN(a)||(this.listIndex=this.adaptListIndexToCorrectIndex(a,i.startTime))}return!0}return!1},e.prototype.adaptListIndexToCorrectIndex=function(e,t){var i=this.getSegmentListEntries(this.currentRepresentationId),n=[i[e],i[e+1]];if(e===i.length-1||n.some((function(e){return!e.startTime})))return e;var r=n.map((function(e){return Math.abs(e.startTime-t)})),o=Math.min.apply(this,r);return e+r.indexOf(o)},e.prototype.getInitSegmentInfo=function(){var e=this.getSegmentList(this.currentRepresentationId);if(null==e?void 0:e.init)return e.init.presentationTimeOffset=this.getTimestampOffset(),e.init;var t=this.getSegmentListEntries(this.currentRepresentationId);if(this.isIndexInBounds(t)&&this.listIndex>-1){var i=n({},t[this.listIndex]);if(i.init){var r=i.init;return r.isInitSegment=!0,r}}return null},e.prototype.getNextSegmentInfo_=function(e){var t;if(!(this.getSegmentListEntries(this.currentRepresentationId).length<1||this.listIndex<0)){var i=this.isTargetTimeOfOngoingSeek(e);return i&&this.logger.debug("".concat(null===(t=this.adaptationSetId)||void 0===t?void 0:t.adaptationSetId," given time ").concat(e," matches seek time, performing precise segment/part search"),e),(0,m.isPlayingLowLatencyHls)(this.context)&&this.setListIndexForTime(e,i),this.getNextSegmentOrPart(e,i)}},e.prototype.isTargetTimeOfOngoingSeek=function(e){var t;return e===(null===(t=this.playerStateService)||void 0===t?void 0:t.targetPlaybackTime)||!1},e.prototype.setListIndexForTime=function(e,t){var i;if(0!==e){if(this.listIndex=this.getIndexForTime(e),!t){var n=this.getSegmentListEntries(this.currentRepresentationId),r=n[this.listIndex];if(!(r.parts&&r.parts.length>0)){var o=this.listIndex;this.listIndex=x(e,this.listIndex,n),this.listIndex!==o&&this.logger.debug("Snapped segment index for time ".concat(e," to next segment boundary at ").concat(null===(i=n[this.listIndex])||void 0===i?void 0:i.startTime))}}}else this.context.logger.debug("Ignoring target time of 0 for LL-HLS")},e.prototype.getNextSegmentOrPart=function(e,t){var i=this.getSegmentListEntries(this.currentRepresentationId);if(this.isIndexInBounds(i)){var n=i[this.listIndex];if(!(n.parts&&n.parts.length>0))return this.getNextFullSegmentInfo();if(R(n))return A(n,e,t);var r=A(n,e,t);return r?n.parts.indexOf(r)>0?r:this.getNextFullSegmentInfo():(this.listIndex++,this.getNextSegmentOrPart(e,t))}},e.prototype.getNextSegmentInfo=function(e){var t=this.getNextSegmentInfo_(e);return t?(this.populateSegmentInfoProps(t),Promise.resolve(t)):this.queueSegmentInfoRequest({time:e})},e.prototype.populateSegmentInfoProps=function(e){return e.presentationTimeOffset=this.getTimestampOffset(),e.startTime&&(e.wallClockTime=(0,c.toMilliSeconds)(e.startTime)),e},e.prototype.getNextFullSegmentInfo=function(){return this.getSegmentListEntries(this.currentRepresentationId)[this.listIndex++]},e.prototype.getSegmentInfos=function(){var e=this,t={},i=this.manifestService.getAdaptationSet(this.adaptationSetId);return i&&i.Representation.forEach((function(i){var n=e.getSegmentListEntries(i._internalId);t[i._id]=n.map((function(e){return{url:e.url,duration:e.duration,startTime:e.startTime}}))})),t},e.prototype.getSubtitleUrl=function(){return this.manifestService.getAvailableSubtitles(this.adaptationSetId.periodId)[0].url},e.prototype.getStartNumber=function(){var e=this.getSegmentList(this.currentRepresentationId);return e?e.startNumber:1},e.prototype.getSegmentDuration=function(){var e=this,t=this.getSegmentList(this.currentRepresentationId),i=this.getSegmentListEntries(this.currentRepresentationId);if(t)return i.length>0&&(0,l.isNumber)(t.totalDuration)?t.totalDuration/i.length:t.maximumSegmentDuration;if(this.manifestService.getAdaptationSet(this.adaptationSetId)){var n=Object.keys(this.segmentListMap).map((function(t){return e.segmentListMap[t]})).find((function(e){return null!==e}));if(n)return n.maximumSegmentDuration}return 1},e.prototype.seekTo=function(e){e>-1&&(this.listIndex=this.getIndexForTime(e))},e.prototype.isIndexInBounds=function(e){return this.listIndex<e.length},e.prototype.hasNext=function(){var e=this.getSegmentListEntries(this.currentRepresentationId);return!this.currentRepresentationId||!e.length||(!this.manifestService.isLive()?this.isIndexInBounds(e):this.hasNextLive(e))},e.prototype.hasNextLive=function(e){return!!this.manifestService.isLastPeriod(this.currentRepresentationId.periodId)||this.isIndexInBounds(e)},e.prototype.setTimestampOffset=function(e,t){void 0===t&&(t=1),this.timestampOffset=e/t},e.prototype.getTimestampOffset=function(){if(!isNaN(this.timestampOffset))return this.timestampOffset;var e=this.getCurrentPeriod(),t=0;if(this.currentRepresentationId){var i=this.getSegmentList(this.currentRepresentationId);i&&!isNaN(i.presentationTimeOffset)&&(t=i.presentationTimeOffset/i.timescale)}return e&&!this.manifestService.isHlsManifest()&&(t-=e.start),t},e.prototype.getIndex=function(){return this.listIndex},e.prototype.setIndex=function(e){if("number"==typeof e)return e<0?(this.logger.debug("Tried to set index at MPDHandler to value smaller 0. Setting to 0."),void(this.listIndex=0)):void(this.listIndex=e);this.logger.debug("Tried to set index at MPDHandler with parameter not being a number!")},e.prototype.queueSegmentInfoRequest=function(e){var t,i=this;if(this.pendingSegmentInfoRequest)return Promise.reject("fail");var r=this.manifestService.getRepresentationById(this.currentRepresentationId);return(null===(t=null==r?void 0:r._hls)||void 0===t?void 0:t.loadFailureReason)?Promise.reject(r._hls.loadFailureReason):(this.logger.debug("No segment info was found for the target time. Waiting for representation update."),new Promise((function(t,r){i.pendingSegmentInfoRequest=n(n({},e),{resolve:t,reject:r})})))},e.prototype.resolvePendingSegmentInfoRequests=function(){if(this.pendingSegmentInfoRequest){var e=this.getNextSegmentInfo_(this.pendingSegmentInfoRequest.time);e?(this.populateSegmentInfoProps(e),this.pendingSegmentInfoRequest.resolve(e),this.pendingSegmentInfoRequest=null):this.manifestService.isLive()||this.hasNext()||this.pendingSegmentInfoRequest.reject(v.SegmentInfoError.END_OF_STREAM_REACHED)}},e.prototype.rejectPendingSegmentInfoRequest=function(e){this.pendingSegmentInfoRequest&&(this.pendingSegmentInfoRequest.reject(e),this.pendingSegmentInfoRequest=null)},e.prototype.getLiveEdgeIndex=function(){var e=this.getSegmentListEntries(this.currentRepresentationId);return e.length>0?e.length-1:0},e.prototype.getIndexForOffset=function(e){for(var t,i=Math.abs(e),n=this.getSegmentDuration(),r=this.getSegmentListEntries(this.currentRepresentationId),o=this.getLiveEdgeIndex(),a=0,s=r.length-1;s>=0&&!((a+=null!==(t=r[s].duration)&&void 0!==t?t:n)>=i);s--)o--;return Math.max(0,o)},e.prototype.timeShift=function(e,t,i){if(this.manifestService.isLive()){var n=this.listIndex;this.listIndex=i&&this.hasStartTimes()?this.getIndexForTime(i):this.getIndexForOffset(e),this.logger.debug("timeShift changes index from ".concat(n," to ").concat(this.listIndex,", based on")+" offset ".concat(e," and target time ").concat(i))}},e.prototype.hasStartTimes=function(){var e,t=null===(e=this.getSegmentListEntries(this.currentRepresentationId)[0])||void 0===e?void 0:e.startTime;return(0,l.isNumber)(t)&&t>=0},e.prototype.updateRepresentation=function(e){return Promise.resolve(e)},e.prototype.isSegmentInfoLoaded=function(e){if(void 0===e&&(e=this.currentRepresentationId),!e)return!1;var t=this.getSegmentListEntries(e),i=this.manifestService.getRepresentationById(e);return!(!t.length||!i)},e.prototype.rewindSegmentListIndexBy=function(e){this.logger.debug("Rewinding segment list index by ".concat(e," segments")),this.listIndex-=e,this.listIndex<0&&(this.logger.debug("List array index is smaller than 0, probably a discontinuity. Using 0."),this.listIndex=0)},e.prototype.rewindOneSegment=function(){return this.listIndex--,!(this.listIndex<0)||(this.logger.debug("List array index is smaller than 0, probably a discontinuity. Using 0."),this.listIndex=0,!1)},e.prototype.getCurrentPeriod=function(){return this.manifestService.getPeriod(this.currentRepresentationId)},e.prototype.getExpectedPresentationTime=function(e){var t=this.getCurrentPeriod().start;return(this.manifestService.hasSinglePeriod()?this.getSeekableRange().start:t)+this.getSegmentListEntries(this.currentRepresentationId).slice(0,e).reduce((function(e,t){return e+t.duration}),0)},e.prototype.getCurrentRepresentation=function(){return this.manifestService.getRepresentationById(this.currentRepresentationId)},e.prototype.getSegmentList=function(e){if(e)return this.segmentListMap[e.key()]},e.prototype.getSegmentListEntries=function(e){var t,i;return e?this.settings.ENABLE_SEGMENT_INFO_PROVIDER_FROM_STORE&&this.manifestService.isHlsManifest()?this.getStoreSegmentInfos(e):null!==(i=null===(t=this.getSegmentList(e))||void 0===t?void 0:t.entries)&&void 0!==i?i:[]:[]},e.prototype.getStoreSegmentInfos=function(e){var t,i=null===(t=this.getSourceStateService())||void 0===t?void 0:t.getState();return(0,g.getSegmentInfos)(i,e.key())},e.prototype.findListIndexWithoutSegmentStartTimes=function(e,t){var i,n;if(!this.adaptationSetId)return 0;for(var r=this.manifestService.getPeriod(this.adaptationSetId),o=null!==(i=null==r?void 0:r.start)&&void 0!==i?i:0,a=this.getSegmentListEntries(this.currentRepresentationId),s=0,u=o;u<=t&&s<a.length;s++)u+=null!==(n=a[s].duration)&&void 0!==n?n:0;return Math.max(s-1,0)},e.prototype.getIndexForTime=function(e){var t=this.getSegmentList(this.currentRepresentationId),i=this.getSegmentListEntries(this.currentRepresentationId);if(!t||0===i.length||!(0,l.isNumber)(t.totalDuration))return 0;var n=this.getMinStartTime(),r=Math.max(n,e),o=t.totalDuration/i.length,a=(0,S.findSegmentIndexForTime)(i,r,o);return null!==a?a:this.findListIndexWithoutSegmentStartTimes(t,r)},e.prototype.getMinStartTime=function(e){void 0===e&&(e=this.currentRepresentationId);var t=this.manifestService.hasSinglePeriod()?this.getSeekableRange().start:this.manifestService.getPeriod(e).start;return this.manifestService.isFirstPeriod(e.periodId)&&(t+=this.distanceToListStartSeconds),t},e.prototype.getLiveEdgeTime=function(){var e,t=null===(e=this.manifestService)||void 0===e?void 0:e.getLastPeriod();return t?t.start+d.DurationConverter.getDurationInSec(t._duration):1/0},e.prototype.getActualTimeShiftBufferDepth=function(){return this.manifestService.getTimeShiftBufferDepthSeconds()},e.prototype.getSeekableRange=function(){var e={start:0,end:0},t=this.manifestService.getTotalDuration();if(!this.manifestService.isLive())return e.start=(0,a.getStartTimeOffset)(this.getSourceStateService().getState()),e.end=t,e;var i=this.manifestService.getTimeShiftBufferDepthSeconds(),n=(0,c.toSeconds)(this.manifestService.getRequestTimestamp()-this.manifestService.getAvailabilityStartTime());if(e.start=Math.max(n+i,0),e.end=Math.max(n,0),p.ModuleManager.has(f.ModuleName.HLS)&&this.manifestService.isHlsManifest()){var r=p.ModuleManager.get(f.ModuleName.HLS).selectors,o=r.getHlsState;"EVENT"===(0,r.getPlaylistType)(o(this.getSourceStateService().getState()))?(e.start=0,e.end=t):e.start+=this.distanceToListStartSeconds}return e.end-=this.manifestService.getDesiredDistanceToLiveEdge(),e},e.prototype.getPendingSegmentInfoRequest=function(){return this.pendingSegmentInfoRequest},e.prototype.setPendingSegmentInfoRequest=function(e){this.pendingSegmentInfoRequest=e},e.prototype.canSwitchRepresentation=function(e){var t,i=this.getSegmentListEntries(this.currentRepresentationId)[this.listIndex];if(!i)return!0;var n=k(e,i,this.settings.LL_HLS_SEGMENT_END_TIME_MATCHING_TOLERANCE);if(!R(i)&&n)return!0;var r=this.isTargetTimeOfOngoingSeek(e);return!((null===(t=null==i?void 0:i.parts)||void 0===t?void 0:t.length)&&A(i,e,r)!==i.parts[0])},e.prototype.getLatestTimeForPossibleSwitch=function(e){var t,i=this.getLLHlsInfoForTime(e),n=i.isLoadingCurrentSegmentsParts,r=i.currentSegment;return r&&n&&null!==(t=r.startTime)&&void 0!==t?t:e},e.prototype.adjustTimeToNextSegmentStart=function(e){var t=this.getLLHlsInfoForTime(e),i=t.isLoadingCurrentSegmentsParts,n=t.currentSegment;return i&&(null==n?void 0:n.startTime)&&(null==n?void 0:n.duration)?n.startTime+n.duration:e},e.prototype.getLLHlsInfoForTime=function(e){var t,i=T(e,this.getSegmentListEntries(this.currentRepresentationId)),n=null===(t=null==i?void 0:i.parts)||void 0===t?void 0:t[0],r=this.isTargetTimeOfOngoingSeek(e),o=!1;if(n&&i){var a=A(i,e,r);o=void 0!==a&&a!==n}return{currentSegment:i,isLoadingCurrentSegmentsParts:o}},e.prototype.dispose=function(){this.settings=null,this.manifestService=null,this.adaptationSetId=null,this.currentRepresentationId=null,this.manifestService=null,this.pendingSegmentInfoRequest=null,this.segmentListMap=null},e}();function T(e,t){var i,n,r;if(t&&!(t.length<1)&&(null===(i=t[0])||void 0===i?void 0:i.duration)){var o=(0,S.findSegmentIndexForTime)(t,e,t[0].duration),a=null===o,s=e<(null!==(r=null===(n=t[0])||void 0===n?void 0:n.startTime)&&void 0!==r?r:1/0),u=t[t.length-1];if(void 0!==u.startTime&&void 0!==u.duration){var d=e>u.startTime+u.duration;if(!(a||s||d))return t[o]}}}function b(e,t){for(var i=e[0].discontinuitySequenceNumber,n=1;n<e.length;n++){var r=e[n].discontinuitySequenceNumber;if(r!==i){var o=M(t,r);if(null!=o)return{index:n,discontinuityStartTime:o}}}return null}function M(e,t){var i=e[String(t)];return i&&i.startTime>0?i.startTime:(i=e[String(t-1)])&&i.endTime>0?i.endTime:null}function I(e,t,i){if(e.length&&i.hasSegmentStartTimeForHlsRepresentation(t)){var n;null!=e[0].startTime?n=(0,u.findIndexFromEnd)(e,(function(e){return null!=e.startTime}))+1:(e[0].startTime=i.getStartTimeForHlsSegment(t,e[0]),n=1);var r=function(e,t){return t.startTime=e.startTime+e.duration};(0,u.forEachFromIndex)(e,n,r)}}function P(e,t){var i={url:e.uri,duration:e.duration,isInitSegment:!1,internalRepresentationId:t.internalRepresentationId,isPreloadHint:Boolean(e.isPreloadHint),isIndependent:Boolean(e.independent)};return e.byteRange&&(i.byteRange=e.byteRange),t.key&&(i.key=t.key),t.init&&(i.init=t.init),(0,l.isDefined)(t.mediaSequence)&&(i.mediaSequence=t.mediaSequence),(0,l.isDefined)(t.discontinuitySequenceNumber)&&(i.discontinuitySequenceNumber=t.discontinuitySequenceNumber),i}function R(e){return e.url.endsWith(t.PHANTOM_SEGMENT_URL)}function C(e,i){var n=i.duration-e.duration;return E(i,e,["url","mediaURL","duration","parts","init","key","discontinuitySequenceNumber","isDiscontinuityStart"]),!e.url.endsWith(t.PHANTOM_SEGMENT_URL)&&(e.url=(0,s.forceReallocation)(e.url)),n}function E(e,t,i){i.forEach((function(i){return t[i]=e[i]}))}function A(e,t,i){var n,r=null===(n=e.parts)||void 0===n?void 0:n.every(S.isSegmentTiming);if(e.parts&&r){var o=e.parts,a=o.reduce((function(e,t){return e+t.duration}),0)/o.length,s=(0,S.findSegmentIndexForTime)(o,t,a);if(null!=s)return i?e.parts[0]:(s=x(t,s,o),e.parts[s])}}function x(e,t,i){var n=i[t],r=n.startTime+n.duration;return Math.abs(e-n.startTime)<=Math.abs(e-r)?t:t+1}function k(e,t,i){return void 0!==t.startTime&&void 0!==t.duration&&Math.abs(e-(t.startTime+t.duration))<i}function L(e){var t,i,n=24,r=e.totalDuration/(null!==(i=null===(t=e.entries)||void 0===t?void 0:t.length)&&void 0!==i?i:1);return Math.ceil(n/r)}t.SegmentListMPDHandler=y},67883:function(e,t,i){var n=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.AdaptationSetId=void 0,t.createAdaptationSetIdFromMimeTypeAndIndex=o;var r=function(e){function t(t,i){var n=e.call(this,t)||this;return n._adaptationSetId=i,n}return n(t,e),Object.defineProperty(t.prototype,"adaptationSetId",{get:function(){return this._adaptationSetId},enumerable:!1,configurable:!0}),t.prototype.equals=function(t){return e.prototype.equals.call(this,t)&&this.adaptationSetId===(null==t?void 0:t.adaptationSetId)},t.prototype.key=function(){return e.prototype.key.call(this)+"-"+this.adaptationSetId},t}(i(96953).PeriodId);function o(e,t,i){return new r(e,"".concat(t,"-").concat(i))}t.AdaptationSetId=r},68294:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getDrmUsedKeySystem=t.getDrmKeyIdsWithErrors=t.getDrmState=void 0;var i=function(e){return null==e?void 0:e.drm};t.getDrmState=i;var n=function(e){var t,i;return null!==(i=null===(t=e.drm)||void 0===t?void 0:t.keyIdsWithErrors)&&void 0!==i?i:[]};t.getDrmKeyIdsWithErrors=n;var r=function(e){var i;return(null===(i=(0,t.getDrmState)(e))||void 0===i?void 0:i.usedKeySystem)||{kind:null,uid:null}};t.getDrmUsedKeySystem=r},71827:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.engineBitmovinSourceReducers=void 0;var n=i(82311),r=i(66137),o=i(28196),a=i(58211),s=i(96280),u=i(49233),d=i(28322),c=i(14764);t.engineBitmovinSourceReducers={manifest:o.ManifestReducer,activeTracks:n.ActiveTracksReducer,playingTracks:a.PlayingTracksReducer,encryptionKeys:r.EncryptionKeyReducer,buffer:u.BufferReducer,renderer:d.RendererReducer,streamTimeline:c.StreamTimelineReducer,segmentInfoMap:s.SegmentInfoMapReducer}},72207:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.Stream=void 0;var n=i(10981),r=i(59305),o=i(6082),a=function(){function e(){var e=this;this.resolveDeferredRead=function(){e.deferredRead&&!e.isEmpty()&&(e.deferredRead.resolve({done:!1,value:e.chunks.shift()}),e.deferredRead=null)},this.state=o.StreamState.Open,this.chunks=[],this.deferredEnded=new n.Deferred,this.deferredEnded.promise.catch((function(){}))}return e.prototype.add=function(e){this.state===o.StreamState.Open&&(this.chunks.push(e),this.resolveDeferredRead())},e.prototype.read=function(){if(this.deferredRead)return Promise.reject({name:r.StreamReadError.ParallelRead});if(this.state===o.StreamState.Ended&&this.isEmpty())return Promise.resolve({done:!0});if(this.state===o.StreamState.Aborted)return Promise.reject({name:r.StreamReadError.Aborted,message:this.getError()});this.deferredRead=new n.Deferred;var e=this.deferredRead.promise;return this.resolveDeferredRead(),e},e.prototype.end=function(){this.state===o.StreamState.Open&&(this.state=o.StreamState.Ended,this.deferredRead&&this.isEmpty()&&(this.deferredRead.resolve({done:!0}),this.deferredRead=null),this.deferredEnded.resolve())},e.prototype.abort=function(e){var t;if(this.state===o.StreamState.Open){this.state=o.StreamState.Aborted,this.error=e;var i={name:o.StreamState.Aborted,message:null!==(t=this.getError())&&void 0!==t?t:""};this.deferredRead&&(this.deferredRead.reject(i),this.deferredRead=null),this.deferredEnded.reject(i)}},e.prototype.transform=function(t,i,n){var r=this,o=new e,a=function(){r.read().then((function(e){var n=e.done,r=e.value;if(n)i&&i(),o.end();else{var s=null!=r&&t(r);s&&o.add(s),a()}})).catch((function(e){n?o.abort(n(e)):o.abort(e)}))};return a(),o},e.prototype.isEmpty=function(){return 0===this.chunks.length},e.prototype.getError=function(){return this.error||null},e}();t.Stream=a},72477:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.UrlInitSegmentProvider=void 0;var n=i(76650),r=i(8272),o=i(41661),a=i(46678),s=function(){function e(e,t){this.currentlyLoading={},this.context=e,this.loaderPool=new a.SegmentLoaderPool(e,{mimeType:t,shouldDownloadBeCancelledCallback:function(){return!1}}),this.cache=new o.SegmentCache(1/0)}return e.prototype.getSegment=function(e){var t=this,i=this.cache.get(e);return(i?Promise.resolve(i):this.loadSegment(e)).then((function(e){return i||t.cacheSegment(e),e}))},e.prototype.isLoading=function(){return this.loaderPool.isLoading()},e.prototype.loadSegment=function(e){var t=this;if(this.loaderPool.isFreeLoaderAvailable()||this.loaderPool.addLoaders(1),null!=this.currentlyLoading[e.url])return this.currentlyLoading[e.url];this.context.logger.debug("Loading init segment",e.url);var i=this.loaderPool.load(e).then((function(e){return e.read()})).then((function(t){if(!t.value)throw new Error("SegmentStream error ".concat(e.url));return t.value})).finally((function(){delete t.currentlyLoading[e.url]}));return this.currentlyLoading[e.url]=i,i},e.prototype.cacheSegment=function(e){this.cache.add(e),this.context.store.dispatch((0,n.addMetricsValue)(e.getMimeType(),r.MetricType.CachedInitSegments,{internalRepId:e.getSegmentInfo().internalRepresentationId}))},e.prototype.getCachedSegment=function(e){return this.cache.get(e)},e.prototype.removeCachedSegments=function(e){this.cache.remove(e)},e.prototype.dispose=function(){this.cache.clear(),this.loaderPool.dispose()},e}();t.UrlInitSegmentProvider=s},72788:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.periodSwitchStarted=t.periodSwitchFinished=t.setPlayingPeriodId=void 0;var n=i(15231),r=i(98086),o=function(e){return(0,n.createAction)(r.PlayingTracksActionKey.SetPlayingPeriodId,e)};t.setPlayingPeriodId=o;var a=function(e){return(0,n.createAction)(r.PlayingTracksActionKey.FinishedPeriodSwitch,e)};t.periodSwitchFinished=a;var s=function(e){return(0,n.createAction)(r.PlayingTracksActionKey.StartedPeriodSwitch,e)};t.periodSwitchStarted=s},73022:function(e,t){function i(e,t){return!(!e||!t)&&e===t}Object.defineProperty(t,"__esModule",{value:!0}),t.areBufferBlocksEqual=i},73543:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.ContainerFormat=void 0,function(e){e.MP4="mp4",e.TS="ts",e.WEBM="webm"}(i||(t.ContainerFormat=i={}))},73553:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentInfoError=void 0,function(e){e.END_OF_STREAM_REACHED="END_OF_STREAM_REACHED",e.PERIOD_COMPLETE="PERIOD_COMPLETE",e.SEGMENT_EXCEEDING_PERIOD_DURATION="SEGMENT_EXCEEDING_PERIOD_DURATION"}(i||(t.SegmentInfoError=i={}))},73731:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.ActiveTracksActionType=void 0,function(e){e.SetSelectedRepresentationId="@instance/sources/@source/activetracks/setselectedrepresentationid",e.SetMediaType="@instance/sources/@source/activetracks/setmediatype",e.SetContainerFormat="@instance/sources/@source/activetracks/setcontainerformat",e.RemoveActiveTrack="@instance/sources/@source/activetracks/remove",e.ClearActiveTracks="@instance/sources/@source/activetracks/clear"}(i||(t.ActiveTracksActionType=i={}))},74830:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.initializeHlsSegmentStartTimes=o;var n=i(18665),r=i(67550);function o(e,t,i){var o=t.sourceContext.sourceIdentifier,a=t.serviceManager.get(n.ServiceName.ManifestService,o);if(a&&!a.getAllAdaptationSets().some((function(e){return e.Representation.some((function(e){var t,i;return void 0!==(null===(i=null===(t=e.SegmentList)||void 0===t?void 0:t[0].SegmentURL[0])||void 0===i?void 0:i._playbackTime)}))}))){var s=e.getRepresentationId();a.initializeSegmentStartTimesFromStart(s),(0,r.assignSegmentStartTimesFromHlsRepresentation)(i.getSegmentListEntries(e.getRepresentationId()),s,a)}}},75498:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.areAudioMimeCodecsCompatible=r,t.hasContentProtection=o;var n=i(42283);function r(e,t){return e.mimeType===t.mimeType&&n.CodecStringHelper.canSwitchBetweenAudioCodecs(e.codec,t.codec)}function o(e,t){return void 0===e&&(e=[]),void 0===t&&(t=[]),0===e.length&&0===t.length||e.length>0&&t.length>0}},77118:function(e,t,i){var n=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.getUtcTimingSources=o;var r=i(25226);function o(e,t){var i=[];return s(e,t).forEach((function(e){var t=a(e.method);t&&i.push({schemeIdUri:t,value:e.serverUrl})})),i}function a(e){switch(e){case r.LiveSynchronizationMethod.HttpHead:return"urn:mpeg:dash:utc:http-head:2014";case r.LiveSynchronizationMethod.HttpXsDate:return"urn:mpeg:dash:utc:http-xsdate:2014";case r.LiveSynchronizationMethod.HttpIso:return"urn:mpeg:dash:utc:http-iso:2014";default:return}}function s(e,t){var i,r,o=(null===(i=e.live)||void 0===i?void 0:i.synchronization)||[],a=(null===(r=t.live)||void 0===r?void 0:r.synchronization)||[];return n(n([],o,!0),a.filter((function(e){return!o.some((function(t){return t.method===e.method}))})),!0)}},77329:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.ManifestActionType=void 0,function(e){e.SetManifest="@instance/manifest/setmanifest",e.SetManifestInitialized="@instance/manifest/setmanifestinitialized",e.RemoveRepresentations="@instance/manifest/removerepresentations",e.UpdateManifest="@instance/manifest/updatemanifest",e.UpdateAdaptationSet="@instance/manifest/updateadaptationset",e.UpdateRepresentation="@instance/manifest/updaterepresentation",e.UpdatePeriodTiming="@instance/manifest/updateperiodtiming",e.AdjustPeriodStartTimes="@instance/manifest/adjustperiodstarttimes",e.SetRepresentationDrmKid="@instance/manifest/setrepresentationdrmkid",e.UpdateRepresentationFailedDownload="@instance/manifest/setrepresentationfaileddownload",e.SetRepresentationSegmentIndex="@instance/manifest/setrepresentationsegmentindex",e.SetRepresentationIsLoading="@instance/manifest/setrepresentationisloading",e.SetRepresentationSegmentIndexParsingError="@instance/manifest/setrepresentationsegmentindexparsingerror",e.SetRepresentationAnchorPoint="@instance/manifest/setrepresentationacnhorpoint",e.InitPlaybackTimesFromIndex="@instance/manifest/initplaybacktimesfromindex",e.InitPlaybackTimesFromReferenceSegment="@instance/manifest/initplaybacktimesfromreferencesegment",e.ResetSegmentPlaybackTimes="@instance/manifest/resetsegmentplaybacktimes"}(i||(t.ManifestActionType=i={}))},77870:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.DashSchemeUri=void 0,function(e){e.AudioChannelConfig="urn:mpeg:dash:23003:3:audio_channel_configuration:2011"}(i||(t.DashSchemeUri=i={}))},77874:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getManifest=void 0;var i=function(e){return null==e?void 0:e.manifest};t.getManifest=i},80858:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.BufferController=void 0;var r=i(92712),o=i(52442),a=i(25550),s=i(28764),u=i(63546),d=i(35148),c=i(18665),l=i(60997),p=i(62510),f=i(33696),h=i(94304),g=i(76650),m=i(28819),v=i(8272),S=i(58975),y=i(76420),T=i(96873),b=i(33669),M=i(3464),I=i(3941),P=i(10981),R=i(53968),C=i(79814),E=i(56435),A=i(331),x=i(70016),k=i(54838),L=i(46462),_=i(91520),B=i(16368),D=i(41108),w=i(77874),O=i(72788),N=i(16280),F=i(68294),H=i(59839),U=i(14764),j=i(22645),q=i(55937),V=i(16492),K=i(15109),z=i(27076),G=i(42346),W=i(19724),Q="The targeted buffer BufferBlock is no longer available",Y=function(){function e(e,t,i,n){var r=this;this.currentRendererMediaTypes=[],this.isAlreadyStarted=!1,this.isStartup=!0,this.finishPlayback=!1,this.hasStreamEndedInternal=!1,this.activeInitSegments={},this.bufferMaxSizeChangedSubscriptionMap={},this.isTimeshiftCancelled=!1,this.negativeDtsCompensationOffset=0,this.updateTotalDuration=function(){r.totalDuration=r.manifestService.getTotalDuration(),r.totalDuration>(r.renderer.getDuration()||0)&&r.setDurationOnRenderer()},this.setDurationOnRenderer=function(){r.finishPlayback||r.renderer.setDuration(r.totalDuration)},this.onRendererSetupFailed=function(e){r.context.logger.debug("Renderer setup failed with",e)},this.onVideoElementTimeUpdate=function(){Date.now()-r.lastBackwardBufferClearingTimestamp>r.minimalBackwardBufferClearingInterval&&r.canClearBackwardBuffer()&&r.maintainBackwardBufferLevel(),r.checkIfEndOfBufferReached(r.settings.END_OF_BUFFER_TOLERANCE)},this.onTimeNotAdvancing=function(){var e,t=r.settings.END_OF_BUFFER_RECOVERY_TOLERANCE,i=null===(e=r.segmentStore)||void 0===e?void 0:e.getBufferBlock(r.currentPlayingBufferBlockId);i&&r.isAtEndOfBufferBlock()&&(t=r.segmentStore.getSmallestMaxSegmentDuration(i)),r.checkIfEndOfBufferReached(t)},this.onVideoElementEnded=function(){var e;if(!(0,R.isSwitchingBufferBlocks)(r.getSourceStore())){var t=null===(e=r.segmentStore)||void 0===e?void 0:e.getNextBufferBlock(r.currentPlayingBufferBlockId);if(t){var i=r.renderer.getCurrentTime(!0);r.tryToSwitchBufferBlock(i,t)}}},this.onVideoElementStalled=function(){r.checkIfEndOfBufferReached(r.settings.END_OF_BUFFER_RECOVERY_TOLERANCE)},this.context=e,this.logger=e.logger,this.settings=e.settings,this.bufferSettings=e.bufferSettings,this.eventHandler=e.eventHandler,this.renderer=e.renderer,this.manifestService=e.serviceManager.get(c.ServiceName.ManifestService,e.sourceContext.sourceIdentifier),this.segmentStore=new G.SegmentStore(this.context,this.settings),this.endStallAtGapCallback=t,this.bufferStallingService=new K.BufferStallingService(this.getPlayerStateService(),this.logger,this.context.renderer),this.totalDuration=i,this.currentPlayingBufferBlockId=G.INITIAL_BUFFER_BLOCK_ID,this.dataSegmentsPushedMap=new Map,this.isEndOfStreamReached=!1,this.lastBackwardBufferClearingTimestamp=0,this.minimalBackwardBufferClearingInterval=(0,A.toMilliSeconds)(this.context.settings.MINIMAL_BACKWARD_BUFFER_CLEARING_INTERVAL),this.currentDiscoOrPeriodForMediaTypes=new Map,this.currentBufferBlockDeferredReject=new z.DeferredReject,this.drmConfigValidPromise=Promise.resolve(null),this.bufferRangesCache=new V.BufferRangesCache(this.context,this.segmentStore),this.subscribeToPlaybackStoppedStateChange(),this.renderer.ready().then((function(){r.setDurationOnRenderer(),r.shouldSendLicenseRequestsImmediately()&&r.setupDRM(n)})).catch(this.onRendererSetupFailed),this.unsubscribeFromStoreSeekedListener=(0,M.subscribe)(this.context.store)((function(e){return e&&(0,S.getPlayerState)(e).seekState}),(function(){r.canClearBackwardBuffer()&&r.maintainBackwardBufferLevel()}),(function(e,t){return e===y.SeekState.Seeked&&t!==y.SeekState.Seeked}))}return e.prototype.subscribeToPlaybackStoppedStateChange=function(){var e=this,t=this.context.store;void 0!==t&&(this.unsubscribeFromStoreStoppedListener=(0,M.subscribe)(t)((function(e){return e&&(0,S.getIsStopped)((0,S.getPlayerState)(e))}),(function(){return e.hasStreamEndedInternal=!0}),(function(e){return!0===e})))},Object.defineProperty(e.prototype,"isCurrentlyStalled",{get:function(){return this.bufferStallingService.isRendererStalling()},enumerable:!1,configurable:!0}),e.prototype.shouldSendLicenseRequestsImmediately=function(){var e,t;return Boolean(null===(t=(0,F.getDrmState)(null===(e=this.getSourceStore())||void 0===e?void 0:e.getState()))||void 0===t?void 0:t.immediateLicenseRequest)},e.prototype.createSourceBuffers=function(e,t){var i=this,n=Promise.resolve();return this.bufferCreationDeferred=new P.Deferred,this.isStartup||(n=this.renderer.shutdown(!0)),this.activeInitSegments={},this.bufferCreationDeferred.resolve(n.then((function(){return i.renderer.ready()})).then((function(){var n;i.setDurationOnRenderer(),i.clearBufferMaxSizeChangedSubscription(),e.forEach((function(e){var t=e.mimeType;i.addMimeTypeAndCodec(t,e.codec)||(i.logger.debug("Could not create source buffer for ".concat(t)),i.context.store.dispatch((0,g.removeMetricsForMimeType)(t))),i.subscribeToBufferMaxSizeChanged(t)})),i.currentRendererMediaTypes=e.filter((function(e){return C.MimeTypeHelper.isAV(e.mimeType)}));var r=null!==(n=t.getAllKnownPeriodIds()[0])&&void 0!==n?n:I.DEFAULT_PERIOD_ID;return i.setupDRM(r),Promise.resolve(i.currentRendererMediaTypes.length)}))),this.bufferCreationDeferred.promise.catch((function(e){return i.logger.warn(e)})).then((function(){i.bufferCreationDeferred=null})),this.bufferCreationDeferred.promise},e.prototype.subscribeToBufferMaxSizeChanged=function(e){var t=this,i=this.getSourceStore();i&&(this.bufferMaxSizeChangedSubscriptionMap[e]=i.subscribe((function(){return t.adjustMaxBufferLevels(e)}),(0,b.hasBufferMaxSizeChanged)(e)))},e.prototype.unsubscribeFromBufferMaxSizeChanged=function(e){var t=this.bufferMaxSizeChangedSubscriptionMap[e];t&&(t(),delete this.bufferMaxSizeChangedSubscriptionMap[e])},e.prototype.setupDRM=function(e){var t,i=this,r=(0,F.getDrmState)(null===(t=this.getSourceStore())||void 0===t?void 0:t.getState());if(void 0!==r&&Object.keys(r).length>0){var a=n(n({},this.context.sourceContext.source),{drm:r}),s=this.manifestService.getDRMCapabilitiesForPeriod(e);Object.keys(h.KeySystemMap).some((function(e){return a.drm.hasOwnProperty(e)}))&&(this.drmConfigValidPromise=this.renderer.setDrmConfig(r,s),this.drmConfigValidPromise.catch((function(e){(null==e?void 0:e.message)!==o.DrmSetupError.CANCELLED&&i.logger.debug("Could not initialize renderer with given DRM config: ".concat(null==e?void 0:e.message),e)})))}},e.prototype.getSourceStore=function(){var e;return null===(e=this.context.serviceManager)||void 0===e?void 0:e.get(c.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier)},e.prototype.maybeWaitForBufferClearingPromise=function(){var e=this;return this.bufferClearingPromise?this.bufferClearingPromise.catch((function(){return e.isTimeshiftCancelled=!0,Promise.reject("Failed to clear buffer on timeshift, stopping segment cache processing")})).then((function(){return e.isTimeshiftCancelled?(e.isTimeshiftCancelled=!1,Promise.reject("Timeshift cancelled, stopping segment cache processing")):Promise.resolve()})):Promise.resolve()},e.prototype.addSegment=function(e){var t,i=this;if(!this.segmentStore)return Promise.reject("Skipping segment processing, segment store is already disposed");this.logger.insane("adding segment to cache for period ".concat(e.getPeriodId()," with playback time ").concat(e.getPlaybackTime()," and mime type ").concat(e.getMimeType())),this.segmentStore.addSegment(e);var n=this.getCurrentPlayingBufferBlockId(),r=this.segmentStore.getBufferBlock(n);if(this.segmentStore.getNextBufferBlock(n)&&this.hasFinishedLoadingBufferBlock(r)&&(null===(t=this.getSourceStore())||void 0===t||t.dispatch((0,T.setLoadingRangeFinished)(!0))),this.subscribeToManifestChange(),Z(e,this.getSourceStore()),!this.areBufferBlockMediaTypesFinalForPeriod(e.getPeriodId()))return Promise.resolve();var o=Promise.resolve();if(this.delayedBufferBlockSwitchContext){var a=this.delayedBufferBlockSwitchContext.time,s=this.findBufferBlockForTime(a);if(!this.segmentStore.canSwitchToBufferBlock(s))return Promise.resolve();o=this.maybeWaitForBufferClearingPromise().then((function(){return i.tryToSwitchBufferBlock(a,s)}))}else if((0,R.isSwitchingBufferBlocks)(this.getSourceStore())||this.isBufferAvailable()||this.bufferCreationDeferred)this.bufferCreationDeferred&&(o=this.bufferCreationDeferred.promise);else{var u=this.segmentStore.getBufferBlockForSegment(e),d=null==u?void 0:u.getMediaTypesWithoutSubs();if(!d||!u)return this.logger.debug("Chosen buffer block for segment unexpectedly had no mediaTypes"),Promise.resolve();o=this.createSourceBuffers(d,u)}return o.then((function(){return i.renderer?i.renderer.ready():Promise.reject("Stopping queue processing, renderer is not available")})).then((function(){return i.processSegmentCache()}))},e.prototype.removePrecedingSegmentsFromStore=function(e,t){var i=this;this.segmentStore&&this.segmentStore.getPrecedingSegments(e,t).forEach((function(e){var t;i.segmentStore.removeSegment(e),null===(t=i.getSourceStore())||void 0===t||t.dispatch((0,T.removeLoadedRange)(e.getMimeType(),(0,N.segmentToBufferBlockTimeRange)(e)))}))},e.prototype.clearBackwardBuffer=function(e,t){var i=this;return this.removePrecedingSegmentsFromStore(e,t),this.lastBackwardBufferClearingTimestamp=Date.now(),this.renderer.removeData(e,0,t).catch((function(t){return i.logger.debug("Error clearing ".concat(e," backward buffer:"),t)}))},e.prototype.updateBackwardBufferClearingInterval=function(){if(this.segmentStore){var e=this.segmentStore.getBufferBlock(this.currentPlayingBufferBlockId),t=e?this.segmentStore.getSmallestMaxSegmentDuration(e):-1;this.minimalBackwardBufferClearingInterval=(0,A.toMilliSeconds)(Math.max(t/2,this.context.settings.MINIMAL_BACKWARD_BUFFER_CLEARING_INTERVAL))}},e.prototype.maintainBackwardBufferLevel=function(){var e,t,i=this;this.updateBackwardBufferClearingInterval();var n=null===(e=this.segmentStore)||void 0===e?void 0:e.getBufferBlock(this.currentPlayingBufferBlockId),r=this.renderer.getCurrentTime(),o=(0,b.getBufferStateMap)(null===(t=this.getSourceStore())||void 0===t?void 0:t.getState());o&&Object.keys(o).forEach((function(e){var t,a=null===(t=(0,b.getRendererBufferedRanges)(o,e)[0])||void 0===t?void 0:t.start,s=C.MimeTypeHelper.getMediaType(e);if(void 0!==a&&(null==n?void 0:n.hasMaxSegmentDuration(e))){var u=n.getMaxSegmentDuration(e)+1,d=i.bufferSettings.getBackwardTargetLevel(s),c=Math.max(u,d),l=r-c;a<l&&i.clearBackwardBuffer(e,l).catch((function(e){i.context.logger.debug("Could not clear backward buffer",e)}))}}))},e.prototype.setEndOfStream=function(e){var t;return e&&this.suspendEosSignalling||!this.renderer?(this.logger.debug("Skipping setting EOS"),Promise.resolve()):(e&&(null===(t=this.getSourceStore())||void 0===t||t.dispatch((0,T.setLoadingRangeFinished)(!0))),this.isEndOfStreamReached=e,this.renderer.setEndOfStream(e))},e.prototype.adjustMaxBufferLevels=function(e){var t,i=(0,b.getBufferState)(null===(t=this.getSourceStore())||void 0===t?void 0:t.getState()),n=void 0!==i?(0,b.getBufferMaxSize)(i,e):b.DefaultBufferMaxSize;if(this.logger.debug("Exceeded quota for ".concat(e," with ").concat(n," seconds of data in the buffers")),!this.context.settings.NO_QUOTA_EXCEEDED_ADJUSTMENT){var r=this.renderer.getCurrentTime(),o=this.segmentStore.getSmallestSafeBufferSize(r,e);this.bufferSettings.adjustMaxBufferLevels(o,e,n)}},e.prototype.updateTimestampOffsetForNegativePlaybackTime=function(){var e,t=this.segmentStore.getBufferBlock(this.currentPlayingBufferBlockId),i=$(this.segmentStore.getAllSegmentsFromBufferBlock(t).toArray()),n=null!==(e=null==i?void 0:i.getPlaybackTime())&&void 0!==e?e:0;n<0&&(this.eventHandler.dispatchEvent(p.PlayerEvent.Warning,new u.PlayerWarning(d.WarningCode.PLAYBACK_NEGATIVE_DECODING_TIMESTAMP_ENCOUNTERED)),this.logger.debug("Encountered negative DTS ".concat(n,", attempting to correct PTO...")),this.negativeDtsCompensationOffset=n)},e.prototype.processSegmentCache=function(){var e=this;return this.isStartup?this.isStartupThresholdReached()?(this.updateTimestampOffsetForNegativePlaybackTime(),this.pushSegmentsToRenderer(1/0).then((function(){return e.endStartupPhase()}))):Promise.resolve():this.pushSegmentsToRenderer(1/0)},e.prototype.getBufferedRangesMap=function(e){var t;void 0===e&&(e=!0);var i=(0,b.getBufferState)(null===(t=this.getSourceStore())||void 0===t?void 0:t.getState());return void 0!==i&&this.segmentStore?e?this.bufferRangesCache.getRanges(i):(0,b.getRendererBufferRangesMap)(this.segmentStore,i)(this.currentPlayingBufferBlockId):{}},e.prototype.addSegmentsFromCacheToRenderer=function(e,t){var i=this;if(!this.segmentStore)return Promise.resolve();var n=this.segmentStore.getAllSegmentsFromBufferBlock(e),o=n.hasNext()&&n.next();return o?(this.segmentStore.removeSegment(o),this.addDataSegmentToRenderer(o).catch((function(e){i.logger.warn("Could not add segment to renderer (mimeType: ".concat(o.getMimeType(),", playbackTime: ").concat(o.getPlaybackTime(),")"),e)})).then((function(){var n;o.isInit()||null===(n=i.getSourceStore())||void 0===n||n.dispatch((0,T.removeLoadedRange)(o.getMimeType(),(0,N.segmentToBufferBlockTimeRange)(o))),i.dataSegmentsPushedMap.set(o.getMimeType(),!o.isInit());var a=r.BufferRangeHelper.getTotalCommonBufferLength(i.getBufferedRangesMap());return!(0,R.isSwitchingBufferBlocks)(i.getSourceStore())||a<t?i.addSegmentsFromCacheToRenderer(e,t):Promise.resolve()}))):Promise.resolve()},e.prototype.hasFinishedLoadingBufferBlock=function(e){var t,i,n=this;if(!e)return!1;var r=this.hasStopped(),o=null===(t=this.segmentStore)||void 0===t?void 0:t.hasStartedLoadingNextBufferBlock(this.currentPlayingBufferBlockId),a=null===(i=this.segmentStore)||void 0===i?void 0:i.hasSegmentsForBufferBlock(e);return!this.currentRendererMediaTypes.some((function(e){return n.renderer.isDataBeingAppended(e.mimeType)}))&&(o||r)&&!a},e.prototype.isSegmentStoreWaitingForSegments=function(){var e,t;return null!==(t=null===(e=this.segmentStore)||void 0===e?void 0:e.isWaitingForSegments())&&void 0!==t&&t},e.prototype.pushSegmentsToRenderer=function(e){var t=this,i=this.segmentStore.getBufferBlock(this.currentPlayingBufferBlockId);return this.suspendEosSignalling=!0,this.addSegmentsFromCacheToRenderer(i,e).then((function(){var e,n;t.suspendEosSignalling=!1,t.isCurrentlyStalled&&!t.isBufferClearingOngoing()&&t.isRestartThresholdReached()&&t.maybeEndStalling(),t.hasFinishedLoadingBufferBlock(i)?(t.logger.debug("Finished loading BB ".concat(t.currentPlayingBufferBlockId,": signaling EOS")),null===(e=t.getSourceStore())||void 0===e||e.dispatch((0,T.setLoadingRangeFinished)(!0)),t.hasTemporarilyReopenedMsePriorToBufferBlockSwitch?t.logger.debug("Skipped EOS signaling as a BufferBlock switch is just being executed"):t.currentBufferBlockSwitchPromise&&(0,R.isSwitchingBufferBlocks)(t.getSourceStore())?(t.logger.debug("delaying EOS signal until ongoing period switch is completed"),t.currentBufferBlockSwitchPromise.then((function(){t.setEndOfStream(!0)}))):t.setEndOfStream(!0)):null===(n=t.getSourceStore())||void 0===n||n.dispatch((0,T.setLoadingRangeFinished)(!1))}))},e.prototype.canClearBackwardBuffer=function(){var e,t,i=this.context.serviceManager.get(c.ServiceName.PlayerStateService);if(!i)return!1;var n=null!==(e=i.seekingOrTimeshifting)&&void 0!==e&&e,r=null===(t=this.getSourceStore())||void 0===t?void 0:t.getState(),o=r&&(0,b.getIsTimestampRollingOver)(r);return!(this.isEndOfStreamReached||(0,R.isSwitchingBufferBlocks)(this.getSourceStore())||n||o)},e.prototype.checkIfEndOfBufferReached=function(e){var t=this.renderer.getEndOfBufferTime();if(void 0===t)this.renderer.isPaused()||this.startStalling();else{var i=t-e,n=this.renderer.getCurrentTime(!0);this.shouldHandleEndOfBuffer(n,i)&&this.handleEndOfBuffer(n)}},e.prototype.shouldHandleEndOfBuffer=function(e,t){return!(0,R.isSwitchingBufferBlocks)(this.getSourceStore())&&(t<=e||this.manifestService.isLive()&&0===e)},e.prototype.resetCurrentTimeOnSegment=function(){var e,t=!0;this.dataSegmentsPushedMap.forEach((function(i,n){C.MimeTypeHelper.isVideo(n)&&(t=i,e=n)})),t||(this.context.videoElement.currentTime=this.context.videoElement.currentTime,this.dataSegmentsPushedMap.set(e,!0))},e.prototype.handleEndOfBuffer=function(e){var t,i,n=this.isAtEndOfBufferBlock(),r=null===(t=this.segmentStore)||void 0===t?void 0:t.getNextBufferBlock(this.currentPlayingBufferBlockId),o=Boolean(r);if(n&&o&&(null===(i=this.segmentStore)||void 0===i?void 0:i.areBufferBlockMediaTypesFinal(r))){var a=r.getMinCommonStartTime(),s=isFinite(a)&&a>=e?a:e;this.tryToSwitchBufferBlock(s,r)}else this.getRemainingTime()>0&&!this.finishPlayback&&(this.startStalling(),this.resetCurrentTimeOnSegment())},e.prototype.isAtEndOfBufferBlock=function(){var e,t=null===(e=this.segmentStore)||void 0===e?void 0:e.getBufferBlock(this.currentPlayingBufferBlockId);return!!t&&(!t.getNextSegment()||!!this.segmentStore&&this.segmentStore.hasStartedLoadingNextBufferBlock(t.getId()))},e.prototype.readyToSeek=function(){return this.currentBufferBlockSwitchPromise?(this.logger.debug("Awaiting ongoing buffer block switch before seek"),this.currentBufferBlockSwitchPromise.then((function(){})).catch((function(){}))):Promise.resolve()},e.prototype.shouldBuffersBeClearedOnTimeshift=function(e){var t=this.renderer.getCurrentTime();return!this.isInBufferedRange(e)||e<t},e.prototype.maybeClearBuffers=function(e,t){return void 0===t&&(t=!1),t||this.shouldBuffersBeClearedOnTimeshift(e)?this.clearBuffersOnTimeshift():Promise.resolve()},e.prototype.setTimeshiftCancelState=function(e){this.isTimeshiftCancelled=e},e.prototype.clearBuffersOnTimeshift=function(){var e=this;return this.context.logger.debug("Clearing buffer on timeshift"),this.clearBuffers().then((function(){return e.isTimeshiftCancelled?(e.isTimeshiftCancelled=!1,Promise.reject("cancelled timeshift")):Promise.resolve()}))},e.prototype.setCurrentTime=function(e,t,i){var n=this;void 0===t&&(t=!1),void 0===i&&(i=!1);var r=this.segmentStore.getBufferBlockForPlaybackTime(e),o=this.segmentStore.getBufferBlock(this.currentPlayingBufferBlockId),a=Boolean(!r||r.getId()!==o.getId());return this.seekTarget=e,this.isStartup||this.isInBufferedRange(e)||this.startStalling(),t&&this.isBufferAvailable()&&!this.isBufferClearingOngoing()&&this.maybeClearBuffers(e,i).catch((function(e){return n.logger.debug("Error clearing buffers:",e)})),a?r?this.tryToSwitchBufferBlock(e,r):this.delayBufferBlockSwitch(e):(this.delayedBufferBlockSwitchContext=void 0,this.renderer.setCurrentTime(e).then((function(e){return n.endStallingAfterSeek(e)})))},e.prototype.endStallingAfterSeek=function(e){return this.seekTarget=void 0,this.isRestartThresholdReached()?this.isCurrentlyStalled&&(this.logger.debug("End stalling after seek to ".concat(e)),this.maybeEndStalling()):this.logger.debug("Delay ending stall as the restart threshold is not yet reached"),Promise.resolve(e)},e.prototype.bufferBlockSwitchErrorHandler=function(e,t){var i,n,r,o,a,s=null!==(n=null===(i=this.delayedBufferBlockSwitchContext)||void 0===i?void 0:i.time)&&void 0!==n?n:t,u=(0,x.isNumber)(null===(r=this.delayedBufferBlockSwitchContext)||void 0===r?void 0:r.time)&&(null===(o=this.segmentStore)||void 0===o?void 0:o.canSwitchToBufferBlockForTime(s));if(this.logger.debug("Buffer block switch failed:",e),this.currentBufferBlockDeferredReject=new z.DeferredReject,u){this.logger.debug("Trying to continue delayed BufferBlock switch to ".concat(s,"..."));var d=this.findBufferBlockForTime(s);return this.tryToSwitchBufferBlock(s,d)}return null===(a=this.getSourceStore())||void 0===a||a.dispatch((0,T.setBufferBlockSwitchOngoing)(!1)),Promise.resolve(t)},e.prototype.findBufferBlockForTime=function(e){var t,i=this.manifestService.getPeriodIdForTime(e);return null===(t=this.segmentStore)||void 0===t?void 0:t.getTargetBufferBlock(e,i)},e.prototype.tryToSwitchBufferBlock=function(e,t){var i,n,r=this,o=function(){};if(this.currentBufferBlockDeferredReject&&this.currentBufferBlockDeferredReject.reject("BufferBlock switch interrupted by newest one"),this.currentBufferBlockDeferredReject=new z.DeferredReject,null===(i=this.getSourceStore())||void 0===i||i.dispatch((0,T.setBufferBlockSwitchOngoing)(!0)),this.delayedBufferBlockSwitchContext&&(o=this.delayedBufferBlockSwitchContext.deferred.resolve),!(null===(n=this.segmentStore)||void 0===n?void 0:n.canSwitchToBufferBlock(t)))return this.delayBufferBlockSwitch(e);this.hasTemporarilyReopenedMsePriorToBufferBlockSwitch=!0;var a=this.bufferClearingPromise||Promise.resolve();return this.currentBufferBlockSwitchPromise=a.then((function(){return r.setEndOfStream(!1)})).then((function(){return r.delayedBufferBlockSwitchContext=void 0})).then((function(){return r.currentBufferBlockDeferredReject.next((function(){return r.clearRendererBuffers()}))})).then((function(){return r.currentBufferBlockDeferredReject.next((function(){return r.switchOrRestoreBufferBlock(t,e)}))})).then((function(e){return r.hasTemporarilyReopenedMsePriorToBufferBlockSwitch=!1,r.currentBufferBlockDeferredReject=new z.DeferredReject,o(e),e})).catch((function(t){return r.bufferBlockSwitchErrorHandler(t,e)})).finally((function(){r.currentBufferBlockSwitchPromise=void 0})),this.currentBufferBlockSwitchPromise},e.prototype.switchOrRestoreBufferBlock=function(e,t){var i,n;return this.hasStreamEnded()?(null===(i=this.currentBufferBlockDeferredReject)||void 0===i||i.reject("BufferBlock switch interrupted by source unloading"),Promise.reject("Source was unloaded")):(0,E.isTizen2017)()||(null===(n=this.segmentStore)||void 0===n?void 0:n.shouldSwitchBufferBlock(e,this.currentPlayingBufferBlockId))?this.switchBufferBlock(t,e):this.restorePlayback(t,e)},e.prototype.delayBufferBlockSwitch=function(e){(0,R.isSwitchingBufferBlocks)(this.getSourceStore())&&this.currentBufferBlockDeferredReject&&this.currentBufferBlockDeferredReject.reject("BufferBlock switch interrupted by newest one");var t=this.segmentStore.getAvailablePositionForTime(e,this.renderer.getCurrentTime());return this.delayedBufferBlockSwitchContext&&this.delayedBufferBlockSwitchContext.time===e||(this.delayedBufferBlockSwitchContext&&this.delayedBufferBlockSwitchContext.time!==e&&this.delayedBufferBlockSwitchContext.deferred.reject(),this.delayedBufferBlockSwitchContext={time:t,deferred:new P.Deferred}),this.delayedBufferBlockSwitchContext.deferred.promise},e.prototype.restorePlayback=function(e,t){var i,n=this,r=t.getId();return(null===(i=this.segmentStore)||void 0===i?void 0:i.getBufferBlock(r))?(this.isEndOfStreamReached=!1,this.currentPlayingBufferBlockId=r,this.segmentStore.deleteOldBufferBlocks(r),this.hasTemporarilyReopenedMsePriorToBufferBlockSwitch=!1,this.currentDiscoOrPeriodForMediaTypes.clear(),this.currentBufferBlockDeferredReject.next((function(){return n.pushSegmentsToRenderer(1/0)})).then((function(){return n.currentBufferBlockDeferredReject.next((function(){return n.setCurrentTimeOnRenderer(e)}))})).then((function(e){var t;return null===(t=n.getSourceStore())||void 0===t||t.dispatch((0,T.setBufferBlockSwitchOngoing)(!1)),n.resumePlaybackIfNotStalledOrPaused(),n.endStallingAfterSeek(e)}))):(this.logger.debug("Target BufferBlock is no longer available, cancelling BufferBlock switch..."),Promise.reject(Q))},e.prototype.resumePlaybackIfNotStalledOrPaused=function(){var e=this,t=this.getPlayerStateService();t&&(this.isCurrentlyStalled||t.isPaused()||this.renderer.play().catch((function(t){e.logger.debug("play call failed with reason: ".concat(t))})))},e.prototype.shouldRecreateSourceBuffers=function(e,t){var i,n=null===(i=this.segmentStore)||void 0===i?void 0:i.getBufferBlock(this.currentPlayingBufferBlockId);if(!n)return!0;var r=n.getAllKnownPeriodIds().some((function(t){return e.hasTrackedPeriodId(t)})),o=this.currentRendererMediaTypes.length===t.length,a=!(r||o)||!(0,W.canReuseSourceBuffer)(n,e,t);return this.allowUserToOverwriteBufferRecreationDecision(a,n,e)},e.prototype.allowUserToOverwriteBufferRecreationDecision=function(e,t,i){var n,r,o,a,s=null===(n=this.context)||void 0===n?void 0:n.sourceContext.source,u=null===(r=null==s?void 0:s.options)||void 0===r?void 0:r.shouldRecreateSourceBuffersOnPeriodSwitch;if(!u)return e;var d=t.getAllKnownPeriodIds(),c=J(t,null!==(o=d[d.length-1])&&void 0!==o?o:I.DEFAULT_PERIOD_ID),l=null!==(a=i.getAllKnownPeriodIds()[0])&&void 0!==a?a:I.DEFAULT_PERIOD_ID,p=J(i,l),f=(0,k.safeUserCallback)((function(){return u(c,p,e)}),this.context.logger);return"boolean"==typeof f?(this.logger.debug("User overwrote default SourceBuffer recreation strategy from ".concat(e," to ").concat(f)+" for the switch from ".concat(c.periodId," to ").concat(p.periodId,".")),f):e},e.prototype.switchBufferBlock=function(e,t){var i,n,r,o,a,s,u=this;this.logger.debug("Switching buffer block for time ".concat(e));var d=t.getMediaTypesWithoutSubs(),l=this.maybeRecreateSourceBuffers(t,d);if(this.currentRendererMediaTypes=d,null===(i=this.getSourceStore())||void 0===i||i.dispatch((0,T.setLoadingRangeFinished)(!1)),null===(r=null===(n=this.context.serviceManager)||void 0===n?void 0:n.get(c.ServiceName.TimedMetadataService))||void 0===r||r.resetContentBoundaryTracking(),!1===(null===(o=this.getPlayerStateService())||void 0===o?void 0:o.seekingOrTimeshifting)){var p=null!==(a=t.getAllKnownPeriodIds()[0])&&void 0!==a?a:I.DEFAULT_PERIOD_ID;null===(s=this.getSourceStore())||void 0===s||s.dispatch((0,O.periodSwitchStarted)(p))}return this.currentBufferBlockDeferredReject.next((function(){return l})).then((function(){return u.currentBufferBlockDeferredReject.next((function(){return u.restorePlayback(e,t)}))})).then((function(e){return u.currentBufferBlockDeferredReject.next((function(){return u.pushSegmentsToRenderer(1/0).catch((function(){return u.context.logger.debug("Could not push segment into renderer")}))})).then((function(){return e}))}))},e.prototype.maybeRecreateSourceBuffers=function(e,t){return this.shouldRecreateSourceBuffers(e,t)?(this.logger.debug("recreating the buffers with types",t),this.createSourceBuffers(t,e)):Promise.resolve(0)},e.prototype.setCurrentTimeOnRenderer=function(e){var t=this,i=this.delayedBufferBlockSwitchContext;return this.delayedBufferBlockSwitchContext=void 0,this.currentBufferBlockDeferredReject.next((function(){return t.renderer.setCurrentTime(e)})).then((function(e){return i&&i.deferred&&i.deferred.resolve(e),e}))},e.prototype.addMimeTypeAndCodec=function(e,t,i){if(void 0===i&&(i=!1),!C.MimeTypeHelper.isAV(e))return!0;var n=this.renderer.addBuffer(e,t);n&&((0,m.getMetricsState)(this.context.store.getState())[e]||this.context.store.dispatch((0,g.initializeMetricsForMimeType)(e,this.context.settings)),i||this.context.store.dispatch((0,g.addMetricsValue)(e,v.MetricType.StalledSeconds,0)));return n},e.prototype.getCurrentLoadingBufferBlock=function(){var e,t,i,n,r,o=this.getCurrentPlayingBufferBlockId(),a=null===(e=this.segmentStore)||void 0===e?void 0:e.getAllBufferBlocks(),s=null===(t=this.segmentStore)||void 0===t?void 0:t.getFutureBufferBlocks(o),u=null===(i=this.segmentStore)||void 0===i?void 0:i.getBufferBlock(o);if(null==a?void 0:a.length){if(!(null==s?void 0:s.length))return u;for(var d=a.findIndex((function(e){return e.getId()===o})),c=d;c<a.length;c++){var l=a[c];(null===(n=this.segmentStore)||void 0===n?void 0:n.hasStartedLoadingNextBufferBlock(l.getId()))&&(u=null===(r=this.segmentStore)||void 0===r?void 0:r.getNextBufferBlock(l.getId()))}return u}},e.prototype.getCurrentlyPlayingMimeTypes=function(){return this.currentRendererMediaTypes.map((function(e){return e.mimeType}))},e.prototype.getCurrentlyLoadingMimeTypes=function(){var e,t;return null!==(t=null===(e=this.getCurrentLoadingBufferBlock())||void 0===e?void 0:e.getMediaTypes().map((function(e){return e.mimeType})))&&void 0!==t?t:[]},e.prototype.getLowestBufferLevel=function(e,t,i){var n=this;if(!this.isBufferAvailable())return 0;var r=e.map((function(e){return n.getOverallBufferLevel(e,t,f.BufferType.ForwardDuration,i)}));return r.length>0?Math.min.apply(Math,r):0},e.prototype.isStartupThresholdReached=function(){var e=this.bufferSettings.getForwardTargetLevel(),t=Math.min(this.settings.STARTUP_THRESHOLD,e-this.settings.STARTUP_THRESHOLD_DELTA),i=this.getCurrentlyLoadingMimeTypes(),n=this.getLowestBufferLevel(i);return n>=t||n>=this.getRemainingTime()},e.prototype.getRemainingTime=function(){return this.manifestService.isLive()?1/0:this.renderer.getDuration()-this.getCurrentTimeRespectingTarget()},e.prototype.endStartupPhase=function(){if(this.isStartup){var e=Date.now(),t=(0,m.getMetricsState)(this.context.store.getState()),i=(0,m.getMetricsLastEntry)(t,"default",v.MetricType.StartTime).value,n=(0,A.toSeconds)(e-i);this.context.store.dispatch((0,g.addMetricsValue)("default",v.MetricType.StartupTimeSeconds,n)),isNaN(n)||this.logger.debug("start up time: ".concat(n)),this.isStartup=!1,this.maybeEndStalling()}},e.prototype.resetCurrentTimeInBufferedRange=function(e){var t=this.renderer.getCurrentTime(!0);e.isInit()&&C.MimeTypeHelper.isVideo(e.getMimeType())&&this.isCurrentlyStalled&&this.isInBufferedRange(t)&&(this.context.videoElement.currentTime=this.context.videoElement.currentTime)},e.prototype.processDrmInitData=function(e){var t=this;e.getDrmInitData().filter((function(e){var t;return(null===(t=e.kid)||void 0===t?void 0:t.length)>0})).forEach((function(i){return t.manifestService.setRepresentationDrmKid(e.getRepresentationId(),i.kid[0])}))},e.prototype.adjustSegmentPtoToNegativeOffset=function(e){var t=e.getPresentationTimeOffset()+this.negativeDtsCompensationOffset;isNaN(t)||e.setPresentationTimeOffset(t)},e.prototype.addDataSegmentToRenderer=function(e){var t=this;this.negativeDtsCompensationOffset<0&&this.adjustSegmentPtoToNegativeOffset(e);var i=C.MimeTypeHelper.extractContentType(e.getMimeType());if(this.activeInitSegments[i]!==e.getInitSegment()){var n=e.getInitSegment();return this.activeInitSegments[i]=n,Promise.all([n,e].map((function(e){return t.addSegmentToRenderer(e)}))).then()}return this.addSegmentToRenderer(e)},e.prototype.addSegmentToRenderer=function(e){var t=this;return e.hasDrmInitData()&&this.processDrmInitData(e),this.drmConfigValidPromise.catch((function(i){if(e.isEncrypted())throw(null==i?void 0:i.message)!==o.DrmSetupError.CANCELLED&&t.context.eventHandler.fireError(X(i)),"DRMConfig failure";t.logger.debug("Problem ensuring segment, error:",i)})).then((function(){return t.resetCurrentTimeInBufferedRange(e),t.maybeSetTimestampOffsetForDiscoOrPeriod(e),t.renderer.appendData(e).catch((function(e){t.logger.debug("could not append data to renderer",e)}))}))},e.prototype.maybeSetTimestampOffsetForDiscoOrPeriod=function(e){var t,i;if(!e.isInit()){var n=e.getSegmentInfo().discontinuitySequenceNumber,r=e.getPeriodId(),o=void 0!==n?String(n):r,a=e.getMimeType();if(o!==this.currentDiscoOrPeriodForMediaTypes.get(a)){var s=e.getPresentationTimeOffset();(0,x.isNumber)(s)&&isFinite(s)&&this.renderer.setTimestampOffset(a,-s),this.currentDiscoOrPeriodForMediaTypes.set(a,o),null===(i=null===(t=this.context.serviceManager)||void 0===t?void 0:t.get(c.ServiceName.TimedMetadataService))||void 0===i||i.trackContentBoundary(r,n)}}},e.prototype.isRestartThresholdReached=function(){var e=this;if(!this.bufferSettings)return!1;var t=this.settings.RESTART_THRESHOLD;(0,E.isWebOS)()&&(t=Math.max(t,1.5));var i=this.bufferSettings.getForwardTargetLevel(),n=Math.min(t,i-this.settings.RESTART_THRESHOLD_DELTA,this.getRemainingTime()),r=this.getCurrentlyPlayingMimeTypes();return r.every((function(t){return e.renderer.getBufferedRanges(t).length>0}))&&this.getLowestBufferLevel(r)>=n},e.prototype.isAllowedToStall=function(){return!this.isStartup&&!(0,R.isSwitchingBufferBlocks)(this.getSourceStore())},e.prototype.startStalling=function(){this.isAllowedToStall()&&this.bufferStallingService.startStalling()},e.prototype.getPlayerStateService=function(){return this.context.serviceManager.get(c.ServiceName.PlayerStateService)},e.prototype.isStallingAtGap=function(e){var t=this.getPlayerStateService();if(!t)return!1;var i=this.getCurrentlyPlayingMimeTypes(),n=this.getLowestBufferLevel(i,e,!1),r=n>=this.settings.RESTART_THRESHOLD,o=n>=this.getRemainingTime()||this.finishPlayback;return!r&&!o&&!t.seekingOrTimeshifting},e.prototype.maybeEndStalling=function(){if(this.isCurrentlyStalled)if((0,R.isSwitchingBufferBlocks)(this.getSourceStore()))this.logger.debug("Ending stall was aborted due to buffer block switching");else{var e=this.getCurrentTimeRespectingTarget();this.isStallingAtGap(e)?this.hasFutureCommonBuffer(e)?(this.logger.debug("Ending stall at the gap"),this.endStallAtGapCallback()):this.logger.debug("Cannot unstall yet as playback is stalling at a gap and there is no future common buffer"):this.bufferStallingService.endStalling()}},e.prototype.getCurrentTimeRespectingTarget=function(){return void 0!==this.seekTarget?this.seekTarget:this.renderer.getCurrentTime()},e.prototype.getCurrentPlayingBufferBlockId=function(){return this.segmentStore.getCurrentBufferBlockId()},e.prototype.hasPendingSegments=function(e){return e=e||this.getCurrentPlayingBufferBlockId(),this.segmentStore.getFutureBufferBlocks(e).some((function(e){return e.hasSegments()}))},e.prototype.getOverallBufferLevel=function(e,t,i,n){void 0===t&&(t=this.renderer.getCurrentTime()),void 0===i&&(i=f.BufferType.ForwardDuration),void 0===n&&(n=!0),this.isStartup&&(t=this.getCurrentTimeRespectingTarget());var o=this.getBufferedRangesMap()[e];return o&&0!==o.length?(o=n?r.BufferRangeHelper.mergeRanges(o.concat(r.BufferRangeHelper.findGapsInRanges(o)),this.settings.GAP_TOLERANCE):o.filter((function(e){return e.start<=t&&t<=e.end})),r.BufferRangeHelper.getBufferLevel(o,t,i)):0},e.prototype.hasFutureCommonBuffer=function(e){return void 0===e&&(e=this.renderer.getCurrentTime()),r.BufferRangeHelper.getCommonBufferedRanges(this.getBufferedRangesMap()).some((function(t){return t.getStart()>e}))},e.prototype.hasFutureBufferBlockData=function(){var e;return Boolean(null===(e=this.segmentStore)||void 0===e?void 0:e.getNextBufferBlock(this.currentPlayingBufferBlockId))||(0,R.isSwitchingBufferBlocks)(this.getSourceStore())},e.prototype.isInBufferedRange=function(e){return r.BufferRangeHelper.isInBufferedRange(this.getBufferedRangesMap(),e)},e.prototype.isStarted=function(){return this.isAlreadyStarted},e.prototype.hasStreamEnded=function(){return this.hasStreamEndedInternal},e.prototype.subscribeToManifestChange=function(){var e;if(!this.unsubscribeFromManifestChange){var t=this.getSourceStore();if(void 0!==t)void 0!==(null===(e=t.getState())||void 0===e?void 0:e.manifest)&&(this.unsubscribeFromManifestChange=(0,M.subscribe)(t)(w.getManifest,this.updateTotalDuration,(function(e){return(null==e?void 0:e.isInitialized)||!1})))}},e.prototype.stop=function(e){void 0===e&&(e=!1),this.finishPlayback=!0,this.isAlreadyStarted=!1,this.isStartup=!1,e?(this.renderer.end(),this.hasStreamEndedInternal=!0,this.logger.debug("stopped playback"),this.renderer.off(L.MediaElementEvent.stalled,this.onVideoElementStalled),this.renderer.off(L.MediaElementEvent.waiting,this.onVideoElementStalled),this.renderer.off(L.MediaElementEvent.ended,this.onVideoElementEnded),this.renderer.off(L.MediaElementEvent.timeupdate,this.onVideoElementTimeUpdate),this.renderer.off(L.MediaElementEvent.currenttimenotadvancing,this.onTimeNotAdvancing),this.unsubscribeFromManifestChange&&(this.unsubscribeFromManifestChange(),this.unsubscribeFromManifestChange=void 0),void 0!==this.unsubscribeFromStoreStoppedListener&&(this.unsubscribeFromStoreStoppedListener(),this.unsubscribeFromStoreStoppedListener=void 0),this.clearBufferMaxSizeChangedSubscription(),this.dataSegmentsPushedMap.clear()):this.maybeEndStalling()},e.prototype.hasStopped=function(){return this.finishPlayback},e.prototype.clearBufferMaxSizeChangedSubscription=function(){var e=this;Object.keys(this.bufferMaxSizeChangedSubscriptionMap).forEach((function(t){return e.unsubscribeFromBufferMaxSizeChanged(t)}))},e.prototype.restart=function(){this.finishPlayback=!1,this.isAlreadyStarted||(this.isAlreadyStarted=!0,this.renderer.on(L.MediaElementEvent.stalled,this.onVideoElementStalled),this.renderer.on(L.MediaElementEvent.waiting,this.onVideoElementStalled),this.renderer.on(L.MediaElementEvent.ended,this.onVideoElementEnded),this.renderer.on(L.MediaElementEvent.timeupdate,this.onVideoElementTimeUpdate),this.renderer.on(L.MediaElementEvent.currenttimenotadvancing,this.onTimeNotAdvancing)),this.hasStreamEndedInternal=!1},e.prototype.dispose=function(){var e,t;this.stop(!0),this.renderer=null,this.eventHandler=null,this.settings=null,this.bufferSettings=null,this.eventHandler=null,this.segmentStore=void 0,this.activeInitSegments=null,null===(e=this.unsubscribeFromStoreSeekedListener)||void 0===e||e.call(this),null===(t=this.getSourceStore())||void 0===t||t.dispatch((0,T.resetLoadedRanges)()),this.bufferRangesCache.dispose(),this.currentRendererMediaTypes=[],this.clearBufferMaxSizeChangedSubscription(),this.drmConfigValidPromise=null,this.bufferStallingService=(0,l.dispose)(this.bufferStallingService)},e.prototype.changeBufferType=function(e,t){var i,n,r=this.currentRendererMediaTypes.find((function(t){return C.MimeTypeHelper.getMediaType(t.mimeType)===C.MimeTypeHelper.getMediaType(e)}));return r&&(this.currentRendererMediaTypes.splice(this.currentRendererMediaTypes.indexOf(r),1),this.currentRendererMediaTypes.push({mimeType:e,codec:t}),this.unsubscribeFromBufferMaxSizeChanged(r.mimeType),null===(n=this.getSourceStore())||void 0===n||n.dispatch((0,T.resetLoadedRanges)(r.mimeType))),this.subscribeToBufferMaxSizeChanged(e),this.segmentStore.changeMediaType(e,null==r?void 0:r.mimeType),this.activeInitSegments={},r?this.renderer.changeBufferType(e,t):Promise.resolve(((i={})[e]=t,i))},e.prototype.addRequiredMediaType=function(e,t){this.segmentStore.addRequiredMediaType(e,t)},e.prototype.areBufferBlockMediaTypesFinalForPeriod=function(e){var t,i;return null!==(i=null===(t=this.segmentStore)||void 0===t?void 0:t.areBufferBlockMediaTypesFinalForPeriod(e))&&void 0!==i&&i},e.prototype.clearCacheForMimeType=function(e){var t;null===(t=this.getSourceStore())||void 0===t||t.dispatch((0,T.resetLoadedRanges)(e)),this.segmentStore.clearSegments(e)},e.prototype.clearCache=function(){var e,t=this;Object.keys((0,U.getStreamTimeline)(null===(e=this.getSourceStore())||void 0===e?void 0:e.getState())).filter((function(e){return C.MimeTypeHelper.isAV(e)})).forEach((function(e){return t.clearCacheForMimeType(e)}))},e.prototype.isBufferClearingOngoing=function(){return void 0!==this.bufferClearingPromise},e.prototype.clearBuffers=function(){var e=this;return this.bufferClearingPromise?(this.logger.debug("Skipping buffer clearing as it is already ongoing"),this.bufferClearingPromise):(this.startStalling(),this.clearCache(),this.bufferClearingPromise=this.setEndOfStream(!1).then((function(){return e.clearRendererBuffers()})),this.bufferClearingPromise.finally((function(){return e.bufferClearingPromise=void 0})),this.bufferClearingPromise)},e.prototype.clearRendererBuffers=function(){var e=this;if(!this.isBufferAvailable())return Promise.resolve();var t=this.currentRendererMediaTypes.map((function(t){return e.clearBuffer(t.mimeType)}));return Promise.all(t).then((function(){}))},e.prototype.clearBuffer=function(e){var t=this;return this.renderer.removeData(e).catch((function(i){return t.logger.debug("Error clearing ".concat(e," buffer:"),i)}))},e.prototype.isBufferAvailable=function(){return this.currentRendererMediaTypes.length>0},e}();function X(e){var t;return _.ModuleManager.has(B.ModuleName.DRM)?(null==e?void 0:e.message)===o.DrmSetupError.MISSING_CONFIGURATION?new s.PlayerError(a.ErrorCode.DRM_MISSING_CONFIGURATION,{reason:e.message}):new s.PlayerError(a.ErrorCode.DRM_NO_KEY_SYSTEM,{reason:null!==(t=null==e?void 0:e.message)&&void 0!==t?t:e}):new D.PlayerModuleMissingError(B.ModuleName.DRM)}function J(e,t){return{periodId:t,contentInformation:e.getMediaTypes().map((function(t){return{codec:t.codec,mimeType:t.mimeType,isDrmProtected:e.getSegregationCriteria(t.mimeType).encryption===q.EncryptionState.Encrypted}}))}}function Z(e,t){var i=(0,j.getTrackIdentifier)(e.getSegmentInfo()),n=e.getPlaybackTimeRange();n&&(null==t||t.dispatch((0,H.removeStreamTimeRange)(i,n,j.StreamTimeRangeType.Loading))),null==t||t.dispatch((0,T.addLoadedRange)(i,(0,N.segmentToBufferBlockTimeRange)(e)))}function $(e){return e.reduce((function(e,t){var i=e.getPlaybackTime();return t.getPlaybackTime()<i?t:e}),e[0])}t.BufferController=Y},81660:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentManager=t.SegmentService=void 0;var n=i(60997),r=i(79814),o=i(56435),a=i(41661),s=i(46678),u=i(72207),d=i(72477),c=function(){function e(e){var t=this;this.context=e,this.disposeSegmentManager=function(e){t.segmentManagerMap.has(e)&&(t.segmentManagerMap.get(e).dispose(),t.segmentManagerMap.delete(e))},this.segmentManagerMap=new Map}return e.prototype.getSegmentManagerKey=function(e,t){var i=void 0===t?{}:t,n=i.id,o=i.lang;return r.MimeTypeHelper.isSubtitle(e)?[e,n,o].filter(Boolean).join("-"):e},e.prototype.add=function(e,t,i){var n=this.get(e.mimeType,i);return n||(n=this.createSegmentManager(e,t),this.segmentManagerMap.set(this.getSegmentManagerKey(e.mimeType,i),n)),n},e.prototype.createSegmentManager=function(e,t){return new l(this.context,e,t)},e.prototype.get=function(e,t){return this.segmentManagerMap.get(this.getSegmentManagerKey(e,t))},e.prototype.disposeOfType=function(e,t){this.disposeSegmentManager(this.getSegmentManagerKey(e,t))},e.prototype.dispose=function(){this.segmentManagerMap.forEach((function(e){return e.dispose()})),this.segmentManagerMap.clear()},e}();t.SegmentService=c;var l=function(){function e(e,t,i){this.context=e,this.segmentParser=i,this.hasEncryptedSegments=!1,this.loaderPool=new s.SegmentLoaderPool(e,t),this.initSegmentProvider=new d.UrlInitSegmentProvider(e,t.mimeType),this.cache=new a.SegmentCache(e.settings.MAX_NUM_CACHED_SEGMENTS),this.sequence=Promise.resolve(null)}return e.prototype.getSegment=function(e){var t=e.isInitSegment?this.getInitSegment(e).then((function(e){return p(e)})):this.getDataSegment(e);return this.sequence=this.sequence.catch((function(){})).then((function(){return t})),this.sequence},e.prototype.getInitSegment=function(e){var t=this;return this.initSegmentProvider.getSegment(e).then((function(e){var i;return null===(i=t.segmentParser)||void 0===i||i.parseSegment(e),e})).catch((function(e){return t.context.logger.debug("Error during loading an init segment",e),Promise.reject(e)}))},e.prototype.getDataSegment=function(e){var t,i=this;(null===(t=e.key)||void 0===t?void 0:t.method)&&(this.hasEncryptedSegments=!0);var n=this.acquireInitSegment(e);return(this.mustLoadInitSegmentSequentially()?this.loadSegmentsSequentially(n,e):this.loadSegmentsConcurrently(n,e)).then((function(e){var t=e[0],n=e[1];return t.transform((function(e){return i.attachInitSegment(e,n),e}),(function(){}),(function(e){return i.context.logger.debug("Loading failed",e),e.message}))}))},e.prototype.acquireInitSegment=function(e){var t=Promise.resolve();if(e.init){var i=this.initSegmentProvider.getCachedSegment(e);t=i?Promise.resolve(i):this.getInitSegment(e.init)}return t},e.prototype.mustLoadInitSegmentSequentially=function(){var e;return this.context.settings.DISABLE_PARALLEL_SEGMENT_LOADING||Boolean(null===(e=this.context.sourceContext.source.drm)||void 0===e?void 0:e.clearkey)||this.hasEncryptedSegments||(0,o.isTizen2016)()},e.prototype.loadSegmentsSequentially=function(e,t){var i=this;return e.then((function(e){return i.getDataSegmentFromCacheOrLoad(t).then((function(t){return[t,e]}))}))},e.prototype.loadSegmentsConcurrently=function(e,t){return Promise.all([this.getDataSegmentFromCacheOrLoad(t),e])},e.prototype.getDataSegmentFromCacheOrLoad=function(e){var t=this.getCachedSegment(e);return null!=t?(this.context.logger.debug("Returning segment from cache for URL: ".concat(e.url)),Promise.resolve(p(t))):this.loaderPool.load(e)},e.prototype.attachInitSegment=function(e,t){t&&e.setInitSegment(t)},e.prototype.isLoading=function(e){return e?this.loaderPool.getLoadingPeriodIds().includes(e):this.loaderPool.getLoadingPeriodIds().length>0},e.prototype.canLoad=function(){var e=this.loaderPool.isFreeLoaderAvailable();return this.mustLoadInitSegmentSequentially()?e&&!this.initSegmentProvider.isLoading():e},e.prototype.cacheSegment=function(e){this.cache.add(e)},e.prototype.hasCachedSegment=function(e){return Boolean(this.getCachedSegment(e))},e.prototype.getCachedSegment=function(e){return e.isInitSegment?this.initSegmentProvider.getCachedSegment(e):this.cache.get(e)},e.prototype.removeCachedInitSegments=function(e){this.initSegmentProvider.removeCachedSegments(e)},e.prototype.clearCache=function(e){void 0===e&&(e=!0),e&&this.initSegmentProvider.removeCachedSegments((function(){return!0})),this.cache.clear()},e.prototype.cancel=function(){return this.loaderPool.cancelLoading()},e.prototype.dispose=function(){this.cancel(),this.clearCache(!1),this.loaderPool=(0,n.dispose)(this.loaderPool),this.segmentParser=void 0,(0,n.dispose)(this.initSegmentProvider)},e}();function p(e){var t=new u.Stream;return t.add(e),t.end(),t}t.SegmentManager=l},82311:function(e,t,i){var n,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},o=this&&this.__rest||function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(i[n[r]]=e[n[r]])}return i};Object.defineProperty(t,"__esModule",{value:!0}),t.ActiveTracksReducer=void 0,t.getContainerFormat=d,t.getMediaTypes=c;var a=i(21829),s=i(73731),u={};function d(e,t){var i;if(e.activeTracks)return null===(i=Object.values(e.activeTracks).find((function(e){var i;return null===(i=e.mediaTypes)||void 0===i?void 0:i.includes(t)})))||void 0===i?void 0:i.containerFormat}function c(e,t){var i;return e.activeTracks&&(null===(i=e.activeTracks[t.adaptationSetId])||void 0===i?void 0:i.mediaTypes)||[]}function l(e,t){var i,n=t.selectedRepresentationId,o=e[n.adaptationSetId];return r(r({},e),((i={})[n.adaptationSetId]=r(r({},o),{selectedRepresentationId:n}),i))}function p(e,t){var i,n=t.trackId,o=t.mediaTypes,a=e[n.adaptationSetId];return r(r({},e),((i={})[n.adaptationSetId]=r(r({},a),{mediaTypes:o}),i))}function f(e,t){var i,n=t.trackId,o=t.containerFormat,a=e[n.adaptationSetId];return r(r({},e),((i={})[n.adaptationSetId]=r(r({},a),{containerFormat:o}),i))}var h=function(e,t){var i=e,n=t.adaptationSetId.adaptationSetId,a=(i[n],o(i,["symbol"==typeof n?n:n+""]));return r({},a)};t.ActiveTracksReducer=(0,a.default)({},((n={})[s.ActiveTracksActionType.SetSelectedRepresentationId]=function(e,t){return l(e,t.payload)},n[s.ActiveTracksActionType.SetMediaType]=function(e,t){return p(e,t.payload)},n[s.ActiveTracksActionType.SetContainerFormat]=function(e,t){return f(e,t.payload)},n[s.ActiveTracksActionType.RemoveActiveTrack]=function(e,t){var i=t.payload;return h(e,i)},n[s.ActiveTracksActionType.ClearActiveTracks]=function(){return u},n))},84519:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.MPDHandlerFactory=void 0;var n=i(18665),r=i(3941),o=i(91520),a=i(16368),s=i(67550),u=function(){function e(){}return e.createInstance=function(e,t,i,o,a){void 0===o&&(o=r.DEFAULT_PERIOD_ID);var s=e.serviceManager.get(n.ServiceName.ManifestService,e.sourceContext.sourceIdentifier);if(!(null==s?void 0:s.hasAdaptationSets(o)))return null;var u=s.findAdaptationSet(o,t,a);if(!u)return e.logger.debug("Did not find adaptation set for mime type [".concat(t,"] in period ").concat(o)),null;var p=u.Representation&&u.Representation[0],f=p&&p.SegmentTemplate?p.SegmentTemplate:u.SegmentTemplate;return s.isSmoothManifest()?d(e,t,i,f):s.isHlsManifest()?c(e,p):l(e,f,p)},e}();function d(e,t,i,n){return n?new(o.ModuleManager.get(a.ModuleName.Smooth).createSmoothSegmentTemplateMPDHandler())(e,t,i):null}function c(e,t){return t.SegmentList?new s.SegmentListMPDHandler(e):null}function l(e,t,i){var n=o.ModuleManager.get(a.ModuleName.DASH);return t&&t[0].SegmentTimeline?new n.SegmentTimelineMPDHandler(e):t?new n.SegmentTemplateMPDHandler(e):i.SegmentList?new s.SegmentListMPDHandler(e):i.SegmentBase?new n.SegmentBaseMPDHandler(e):null}t.MPDHandlerFactory=u},85157:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentInfoMapActionType=void 0,function(e){e.SetSegmentInfos="@instance/sources/@source/playback/setSegmentInfos",e.UpdateSegmentInfos="@instance/sources/@source/playback/updateSegmentInfos"}(i||(t.SegmentInfoMapActionType=i={}))},85586:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.MediaPlayer=t.LoadState=void 0,t.checkIfGapSkip=K;var r,o=i(92712),a=i(47275),s=i(18665),u=i(60997),d=i(62510),c=i(57620),l=i(15539),p=i(33696),f=i(76650),h=i(28819),g=i(8272),m=i(17990),v=i(58975),S=i(3464),y=i(42283),T=i(79814),b=i(331),M=i(46462),I=i(91520),P=i(16368),R=i(6476),C=i(3337),E=i(38353),A=i(43412),x=i(28171),k=i(43752),L=i(36366),_=i(81660),B=i(38765),D=i(4053),w=i(71827),O=i(58211),N=i(59879),F=i(64330),H=i(64729);!function(e){e.Unloaded="unloaded",e.Preparing="preparing",e.Loading="loading",e.Loaded="loaded"}(r||(t.LoadState=r={}));var U=Object.freeze({id:"auto",bitrate:null}),j=Object.freeze({id:"auto",bitrate:null,width:null,height:null}),q=function(){function e(e){var t=this;this.timeShiftJumpStart=-1,this.ignoreNextVideoError=!1,this.ended=!1,this.isDvrWindowExceeded=!1,this.controlPermitted=!0,this.lastPlayIssuer="api",this.lastPauseIssuer="api",this.onSegmentAvailable=function(e){null!=t.relativeCurrentTimeOffset||e.isInit()||(t.relativeCurrentTimeOffset=t.isLive()?t.calcRelativeCurrentTimeOffsetForLive(e):0)},this.onPlaying=function(){t.isLive()&&t.canResumeLatencyControl()&&t.resumeLatencyControl()},this.videoErrorHandler=function(e){t.ignoreNextVideoError?t.ignoreNextVideoError=!1:(t.logger.debug("Caught a video element error: ",e),t.unload(),t.eventHandler.fireError((0,c.createPlayerErrorFromMediaError)(e)))},this.controlLiveLatency=function(){var e=t.liveLatencyService.getStatus(t.liveLatencyMode,t.getCommonBufferLevel());t.liveLatencyMode!==e.mode&&(t.context.eventHandler.dispatchEvent(d.PlayerEvent.LatencyModeChanged,{from:t.liveLatencyMode,to:e.mode}),t.liveLatencyMode=e.mode),e.mode===d.LatencyMode.Suspended||t.playerStateService.seekingOrTimeshifting||(e.requiredAction===t.lowLatencyModule.LiveLatencyControlAction.Seek?t.goToLiveEdge():e.requiredAction===t.lowLatencyModule.LiveLatencyControlAction.PlaybackRate&&t.setPlaybackSpeed(1,e.actionParam,!1))},this.onPlaybackStarted=function(){t.playerStateService.getIsRendererStalling()?(t.logger.debug("Renderer is currently stalled. Waiting for unstalling before ending stall."),t.clearStallEndedSubscription(),t.unsubscribeFromStallEnded=(0,S.subscribe)(t.context.store)((function(e){return(0,v.getPlayerState)(e)}),(function(){return t.endStall()}),(function(e){return!(0,v.getIsRendererStalled)(e)}))):t.endStall()},this.onVideoElementPlay=function(){t.ended&&(t.logger.debug("Detected replay attempt from video element"),t.context.internalPlayer.play()),t.playerStateService.transitionToPlayState(!0,t.lastPlayIssuer)},this.onVideoElementPaused=function(){t.logger.insane("video element signaled pause in state: ".concat(t.playerStateService.playbackState)),t.playerController.maybeTransitionToPausedState(t.lastPauseIssuer)},this.mapLangObjectToAudioTrack=function(e){return{id:e.id,lang:e.lang,label:e.label,role:e.role,getQualities:function(){return x.MediaPlayerManifestApiFactory.getAudioQualities(t.manifestService,t.getCurrentPlayingPeriodId(),e.id)}}},this.onVideoElementSeeking=function(e){var i=K(t.context,e.time);t.settings.GLOBAL_DISABLE_SEEKING&&!i&&(Math.abs(e.time-t.lastCurrentTimeUpdate)>.05&&(t.logger.debug("Seeking now allowed, seeking back to expected time"),t.context.videoElement.currentTime=t.lastCurrentTimeUpdate))},this.onVideoTimeupdate=function(e){if(!t.ended){var i=0===t.renderer.getCurrentTime(!0),n=t.isPlaying(),r=t.playerStateService.isSeeking(),o=t.isStalled();!n||r||o||i?t.logger.debug("Skipping timeupdate",{isPlaying:n,isSeeking:r,isStalled:o,timeUpdateWithoutBuffer:i,time:e.time}):t.handleTimeChanged(t.getCurrentTime())}},this.onContentPlaybackFinished=function(){t.ended=!0},this.onDvrWindowExceeded=function(){t.context.logger.debug("DVR window exceeded"),t.playerStateService.isPlaying()?t.handleDvrWindowExceeded():t.isDvrWindowExceeded=!0},this.onError=function(e){3001===e.code&&t.unload()},this.fireAudioTracksAdded=function(){for(var e=t.getCurrentTime(),i=0,n=t.getAvailableAudio();i<n.length;i++){var r=n[i];t.context.eventHandler.dispatchEvent(d.PlayerEvent.AudioAdded,{track:r,time:e})}},this.fireAudioTracksRemoved=function(e){for(var i=0,n=t.getAvailableAudio();i<n.length;i++){var r=n[i];t.context.eventHandler.dispatchEvent(d.PlayerEvent.AudioRemoved,{track:r,time:e})}},this.onPeriodSwitch=function(){t.fireAudioTracksRemoved(t.getCurrentTime())},this.onPlaybackSpeedChanged=function(e){t.isLive()&&(t.canResumeLatencyControl()?t.resumeLatencyControl():t.suspendLatencyControl(!1))},this.context=e,this.logger=e.logger,this.config=e.config,this.settings=e.settings,this.renderer=e.renderer,this.eventHandler=e.eventHandler,this.loadState=r.Unloaded,this.context.store.dispatch((0,f.initializeMetricsForMimeType)("default",this.context.settings)),this.playerController=new L.MediaPlayerController(e,this.onSegmentAvailable),I.ModuleManager.has(P.ModuleName.LowLatency)&&(this.lowLatencyModule=I.ModuleManager.get(P.ModuleName.LowLatency)),this.eventHandler.on(d.PlayerEvent.PeriodSwitch,this.onPeriodSwitch),this.eventHandler.on(d.PlayerEvent.PeriodSwitched,this.fireAudioTracksAdded),this.eventHandler.on(d.PlayerEvent.PlaybackSpeedChanged,this.onPlaybackSpeedChanged),this.unsubscribeStorePlayingListener=(0,S.subscribe)(this.context.store)((function(e){return(0,v.getIsPlaying)((0,v.getPlayerState)(e))}),this.onPlaying,(function(e,t){return e&&!t}))}return e.prototype.createServices=function(e,t){var i=t.serviceManager;this.createSynchronizedTimeService(t),this.createManifestService(t),this.createManifestLoadingService(t,e),this.createManifestUpdateSchedulingService(t);var n=I.ModuleManager.get(P.ModuleName.ABR).AdaptationService;i.set(s.ServiceName.AdaptationService,new n(t)),i.set(s.ServiceName.TimedMetadataService,new B.TimedMetadataService(t)),i.set(s.ServiceName.SegmentService,new _.SegmentService(t));var r=new N.StreamTimeService(i,t.sourceContext.sourceIdentifier);i.set(s.ServiceName.StreamTimeService,r),i.set(s.ServiceName.HeartbeatService,new R.HeartbeatService(i,this.playerController),t.sourceContext.sourceIdentifier),this.createSubtitleService(t),this.createLowLatencyService(t)},e.prototype.createSynchronizedTimeService=function(e){var t=e.serviceManager,i=e.sourceContext.sourceIdentifier;if(!t.has(s.ServiceName.SynchronizedTimeService,i)){var n=new F.SynchronizedTimeService(e,e.sourceContext);t.set(s.ServiceName.SynchronizedTimeService,n,i)}},e.prototype.createManifestService=function(e){var t=e.serviceManager,i=e.sourceContext.sourceIdentifier;t.has(s.ServiceName.ManifestService,i)?(this.manifestService=t.get(s.ServiceName.ManifestService,i),this.manifestService.restore()):(this.manifestService=new E.ManifestService(e,e.sourceContext),t.set(s.ServiceName.ManifestService,this.manifestService,i))},e.prototype.createManifestLoadingService=function(e,t){var i=e.serviceManager,n=e.sourceContext.sourceIdentifier;if(!i.has(s.ServiceName.ManifestLoadingService,n)){var r=(0,C.createManifestLoadingService)(t,e,e.sourceContext);i.set(s.ServiceName.ManifestLoadingService,r,n)}},e.prototype.createManifestUpdateSchedulingService=function(e){var t=e.serviceManager,i=e.sourceContext.sourceIdentifier;if(t.has(s.ServiceName.ManifestUpdateSchedulingService,i))t.get(s.ServiceName.ManifestUpdateSchedulingService,i).restore();else{var n=new A.ManifestUpdateScheduler(e,e.sourceContext);t.set(s.ServiceName.ManifestUpdateSchedulingService,n,i)}},e.prototype.createSubtitleService=function(e){var t=e.serviceManager,i=e.sourceContext.sourceIdentifier;if(I.ModuleManager.has(P.ModuleName.Subtitles)&&!t.has(s.ServiceName.SubtitleService,i)){var n=I.ModuleManager.get(P.ModuleName.Subtitles).SubtitleService;t.set(s.ServiceName.SubtitleService,new n(e),i)}this.subtitleService=t.get(s.ServiceName.SubtitleService,i)},e.prototype.createLowLatencyService=function(e){var t,i=e.serviceManager;null===(t=this.liveLatencyService)||void 0===t||t.dispose(),I.ModuleManager.has(P.ModuleName.LowLatency)&&(i.set(s.ServiceName.LiveLatencyService,new this.lowLatencyModule.LiveLatencyService(e)),this.liveLatencyService=i.get(s.ServiceName.LiveLatencyService))},e.prototype.disposeServices=function(e){var t,i,n=this.context.serviceManager;[s.ServiceName.LiveLatencyService,s.ServiceName.TimedMetadataService].forEach((function(e){return(0,a.disposeService)(n,e)}));var r=null===(t=this.context.sourceContext)||void 0===t?void 0:t.sourceIdentifier;r&&((0,a.disposeService)(n,s.ServiceName.HeartbeatService,r),(0,a.disposeService)(n,s.ServiceName.SegmentService,r),e!==l.ADVERTISING_ISSUER_NAME?[s.ServiceName.ManifestService,s.ServiceName.ManifestLoadingService,s.ServiceName.ManifestUpdateSchedulingService,s.ServiceName.SynchronizedTimeService,s.ServiceName.SubtitleService].forEach((function(e){return(0,a.disposeService)(n,e,r)})):(n.get(s.ServiceName.ManifestService,r).suspend(),n.get(s.ServiceName.ManifestUpdateSchedulingService,r).suspend(),null===(i=n.get(s.ServiceName.SubtitleService,r))||void 0===i||i.disableAllSubtitles()))},e.prototype.calcRelativeCurrentTimeOffsetForLive=function(e){var t=null!=e.getSegmentInfo().wallClockTime?e.getPlaybackTime()-(0,b.toSeconds)(e.getSegmentInfo().wallClockTime):0;return this.playerController.getSeekableRange().start+t},e.prototype.play=function(e){return this.lastPlayIssuer=e,this.controlPermitted?this.currentSource?this.playerStateService.isPlaying()?(this.logger.debug("Ignoring play call as play was already triggered or we're already playing"),Promise.resolve()):(this.ended=!1,this.playerStateService.isPaused()&&this.isLive()&&(this.isDvrWindowExceeded||this.getMaxTimeShift()>=0)&&(this.context.logger.debug("Handling DVR window excursion - going to live edge"),this.goToLiveEdge(),this.isDvrWindowExceeded=!1),this.playerController.start(),this.playerStateService.transitionToPendingPlayState(!1,e),this.renderer.play()):Promise.resolve():Promise.reject("Play control not permitted")},e.prototype.goToLiveEdge=function(){var e=this;this.settings.ENABLE_SEEK_FOR_LIVE?this.playerStateService.isSeeking()||this.seek(this.getSeekableRange().end,l.INTERNAL_ISSUER_NAME):this.playerController.timeShift(0,(function(){return e.onTimeShifted()})).catch((function(){return e.onTimeShiftFailed()}))},e.prototype.canResumeLatencyControl=function(){return Boolean(this.liveLatencyService)&&!this.isPaused()&&0===this.getTimeShift()&&1===this.getPlaybackSpeed()&&!this.playerStateService.isTimeShifting()&&!this.playerStateService.isSeeking()},e.prototype.resumeLatencyControl=function(){this.liveLatencyService.resumeControl()},e.prototype.suspendLatencyControl=function(e){void 0===e&&(e=!0);var t=this.liveLatencyMode===d.LatencyMode.Suspended;this.liveLatencyService&&!t&&(this.liveLatencyService.suspendControl(),e&&this.setPlaybackSpeed(1,1,!1))},e.prototype.getCommonBufferLevel=function(){var e=[this.getBufferLevel(p.BufferType.ForwardDuration,p.MediaType.Audio),this.getBufferLevel(p.BufferType.ForwardDuration,p.MediaType.Video)].map((function(e){return e.level})).filter((function(e){return null!==e}));return 0===e.length?0:Math.min.apply(Math,e)},e.prototype.endStall=function(){this.clearStallEndedSubscription(),this.playerStateService.stallExit(),this.playerStateService.transitionToPlayingState(this.lastPlayIssuer),this.ended=!1},e.prototype.preload=function(){this.loadState!==r.Unloaded&&this.playerController&&this.playerController.start()},e.prototype.pause=function(e){this.controlPermitted&&(this.logger.insane("Pause was called via the API"),this.playerStateService.isInitial()||(this.lastPauseIssuer=e,this.isLive()&&this.liveLatencyService&&this.liveLatencyService.isConfigured()&&(this.suspendLatencyControl(),this.controlLiveLatency()),this.playerStateService.isPaused()||this.playerStateService.isStopped()||this.renderer.pause(),this.playerStateService.transitionToPausedState(this.renderer.isPaused(),e)))},e.prototype.mute=function(e){this.isMuted()||(this.renderer.mute(),this.context.eventHandler.dispatchEvent(d.PlayerEvent.Muted,{issuer:e||"api"}))},e.prototype.unmute=function(e){this.isMuted()&&(this.renderer.unmute(),this.context.eventHandler.dispatchEvent(d.PlayerEvent.Unmuted,{issuer:e||"api"}))},e.prototype.maybeAdjustSeekTargetToDiscontinuityStart=function(e,t){var i,n=null===(i=this.getSourceStoreService())||void 0===i?void 0:i.getState();if(!n)return e;for(var r=I.ModuleManager.get(P.ModuleName.HLS).selectors,o=r.getHlsState,a=(0,r.getMergedDiscontinuityTimings)(o(n)),s=Object.values(a),u=0;u<s.length-1;u++){var d=s[u].endTime,c=s[u+1].startTime;if(e>=d-t&&e<c)return this.logger.debug("Adjusting seek target to discontinuity start: ".concat(e," -> ").concat(c)),c}return e},e.prototype.seek=function(e,t){var i,n,r;void 0===t&&(t="");var o=this.config&&this.config.hasOwnProperty("playback")?this.config.playback:{},a=!o.hasOwnProperty("seeking")||o.seeking,s=this.context.store.getState(),u=!!s&&(null!==(r=null===(n=null===(i=(0,v.getPlayerState)(s))||void 0===i?void 0:i.seekingProcess)||void 0===n?void 0:n.isInitial)&&void 0!==r&&r),d=![l.STARTUP_ISSUER_NAME,l.INTERNAL_ISSUER_NAME].includes(t);if(!(u||!this.settings.GLOBAL_DISABLE_SEEKING&&a)||!this.controlPermitted)return!1;var c=this.settings.END_OF_BUFFER_TOLERANCE,p=this.manifestService.isHlsManifest()?this.maybeAdjustSeekTargetToDiscontinuityStart(e,c):(0,H.adjustTargetTimeToPeriodStart)(e,c,this.manifestService,this.logger);return this.ended=!1,this.cancelPotentialOngoingTimeShift(),this.ensureSeekedOrTimeshiftedSubscription(),this.playerStateService.transitionToSeekingState(p,t,d),this.context.store.dispatch((0,m.updateSeekingProcess)({issuer:t,targetTime:p,seekableRange:this.getSeekableRange(),isInitial:u})),!0},e.prototype.cancelPotentialOngoingTimeShift=function(){this.playerStateService.isTimeShifting()&&(this.playerController.getTimeShiftHandler().cancel(),this.playerStateService.transitionToTimeShiftedState(!1))},e.prototype.handleTimeChanged=function(e){e!==this.lastCurrentTimeUpdate&&(this.context.eventHandler.dispatchEvent(d.PlayerEvent.TimeChanged,{time:e}),this.lastCurrentTimeUpdate=e,this.isLive()&&this.liveLatencyService&&this.liveLatencyService.isConfigured()&&this.controlLiveLatency())},e.prototype.setVolume=function(e,t){var i=this.renderer.getVolume();this.renderer.setVolume(e),i!==e&&this.context.eventHandler.dispatchEvent(d.PlayerEvent.VolumeChanged,{targetVolume:e,sourceVolume:i,issuer:t||"api"})},e.prototype.setAudioQuality=function(e){var t,i=this.getAvailableAudioQualities(),n=this.getAudioQuality();(t="auto"===e?U:i.find((function(t){return t.id===e})))&&n.id!==t.id&&(this.setFixedRepresentation(p.MediaType.Audio,e),this.context.eventHandler.dispatchEvent(d.PlayerEvent.AudioQualityChanged,{sourceQuality:n,sourceQualityId:n.id,targetQuality:t,targetQualityId:t.id}))},e.prototype.getAudioQuality=function(){var e=this.getInitializedMimeTypeFromMediaType(p.MediaType.Audio),t=this.context.serviceManager.get(s.ServiceName.AdaptationService),i=t.isAuto(e),n=t.getCurrentRepresentationId(e);return i||null===n?U:this.getAvailableAudioQualities().find((function(e){return e.id===n.representationId}))},e.prototype.setFixedRepresentation=function(e,t){var i=this.getInitializedMimeTypeFromMediaType(e);this.context.serviceManager.get(s.ServiceName.AdaptationService).setFixedRepresentation(i,t)},e.prototype.setVideoQuality=function(e){var t,i=this.getAvailableVideoQualities(),n=this.getVideoQuality();(t="auto"===e?j:i.find((function(t){return t.id===e})))&&n.id!==t.id&&(this.context.eventHandler.dispatchEvent(d.PlayerEvent.VideoQualityChanged,{sourceQuality:n,sourceQualityId:n.id,targetQuality:t,targetQualityId:t.id}),this.setFixedRepresentation(p.MediaType.Video,e))},e.prototype.getVideoQuality=function(){var e=this.getInitializedMimeTypeFromMediaType(p.MediaType.Video),t=this.context.serviceManager.get(s.ServiceName.AdaptationService),i=t.isAuto(e),n=t.getCurrentRepresentationId(e);return i||null===n?j:this.getAvailableVideoQualities().find((function(e){return e.id===n.representationId}))},e.prototype.processSourceContext=function(){var e=this.context.sourceContext;e.source&&e.source.options&&e.source.options.headers&&(this.settings.HTTP_HEADERS=e.source.options.headers)},e.prototype.addReducersToSourceStore=function(e){var t=this.getSourceStoreService();if(void 0!==t){if(e.type===p.StreamType.Hls&&I.ModuleManager.has(P.ModuleName.HLS)&&!t.hasReducer("hls")){var i=I.ModuleManager.get(P.ModuleName.HLS);t.addReducer("hls",i.HlsReducer)}Object.keys(w.engineBitmovinSourceReducers).forEach((function(e){var i=w.engineBitmovinSourceReducers[e];void 0!==i&&t.addReducer(e,i)}))}},e.prototype.addRendererEventListeners=function(){this.renderer.on(M.MediaElementEvent.error,this.videoErrorHandler),this.renderer.on(M.MediaElementEvent.timeupdate,this.onVideoTimeupdate),this.renderer.on(M.MediaElementEvent.seeking,this.onVideoElementSeeking),this.renderer.on(M.MediaElementEvent.playing,this.onPlaybackStarted),this.renderer.on(M.MediaElementEvent.play,this.onVideoElementPlay),this.renderer.on(M.MediaElementEvent.pause,this.onVideoElementPaused)},e.prototype.logBitrateBoundarySettings=function(){this.logger.debug("setting bitrate boundaries for audio to "+this.settings.MIN_SELECTABLE_AUDIO_BITRATE+" - "+this.settings.MAX_SELECTABLE_AUDIO_BITRATE),this.logger.debug("setting bitrate boundaries for video to "+this.settings.MIN_SELECTABLE_VIDEO_BITRATE+" - "+this.settings.MAX_SELECTABLE_VIDEO_BITRATE)},e.prototype.loadInternal=function(e,t){var i,n=this;return this.loadState===r.Unloaded?Promise.reject("Unload during load"):(this.createServices(e,this.context),this.context.sourceContext.source=e.config,(null===(i=this.context.videoElement)||void 0===i?void 0:i.eventHandler)&&this.context.videoElement.eventHandler.reset(),this.currentSource=e,this.ended=!1,this.isDvrWindowExceeded=!1,this.context.store.dispatch((0,m.updateSeekingProcess)({isInitial:!0})),this.logBitrateBoundarySettings(),this.liveLatencyService&&(this.liveLatencyMode=d.LatencyMode.Suspended,this.liveLatencyService.reset()),this.addRendererEventListeners(),this.settings.GLOBAL_DISABLE_SEEKING=t||!1,this.loadState=r.Loading,this.playerController.init(e).then((function(){n.loadState=r.Loaded})))},e.prototype.prepareLoad=function(){var e=this.context.serviceManager.get(s.ServiceName.PlayerStateService);e&&(this.playerStateService=e);var t=Promise.resolve();return this.loadState===r.Loading&&(t=this.unload()),this.playerStateService.reset(),this.loadState=r.Preparing,t},e.prototype.load=function(e,t){var i=this;return"dash"!==e.type&&"hls"!==e.type&&"smooth"!==e.type?Promise.reject("invalid source"):(this.addReducersToSourceStore(e),this.processSourceContext(),this.unsubscribeFromStoreStoppedListener=(0,S.subscribe)(this.context.store)((function(e){return(0,v.getIsStopped)((0,v.getPlayerState)(e))}),this.onContentPlaybackFinished,(function(e){return!0===e})),this.eventHandler.on(d.PlayerEvent.DVRWindowExceeded,this.onDvrWindowExceeded,!0),this.eventHandler.on(d.PlayerEvent.Error,this.onError,!0),this.renderer.ready().then((function(){return i.loadInternal(e,t)})))},e.prototype.getSourceStoreService=function(){return this.context.serviceManager.get(s.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier)},e.prototype.unload=function(e){var t=this;if(this.clearStallEndedSubscription(),this.loadState===r.Unloaded)return Promise.resolve();var i=this.loadState===r.Loaded;this.ended=!1,this.loadState=r.Unloaded,this.ignoreNextVideoError=!0,this.relativeCurrentTimeOffset=null,this.renderer.off(M.MediaElementEvent.error,this.videoErrorHandler),this.renderer.off(M.MediaElementEvent.timeupdate,this.onVideoTimeupdate),this.renderer.off(M.MediaElementEvent.seeking,this.onVideoElementSeeking),this.renderer.off(M.MediaElementEvent.playing,this.onPlaybackStarted),this.renderer.off(M.MediaElementEvent.play,this.onVideoElementPlay),this.renderer.off(M.MediaElementEvent.pause,this.onVideoElementPaused),this.unsubscribeFromStoreStoppedListener(),this.eventHandler.off(d.PlayerEvent.DVRWindowExceeded,this.onDvrWindowExceeded),this.eventHandler.off(d.PlayerEvent.Error,this.onError),this.playerStateService.isPlaying()&&this.renderer.pause(),this.playerStateService.reset();var n=this.context.sourceContext.sourceIdentifier;return this.context.serviceManager.has(s.ServiceName.SynchronizedTimeService,n)&&!V(e)&&this.context.serviceManager.get(s.ServiceName.SynchronizedTimeService,n).reset(),(this.playerController?this.playerController.terminate():Promise.resolve()).then((function(){t.currentSource=void 0,t.disposeServices(e),V(e)||t.getSourceStoreService().dispatch((0,D.clearActiveTracksAction)()),i&&t.eventHandler.dispatchEvent(d.PlayerEvent.SourceUnloaded)}))},e.prototype.isReady=function(){return this.playerController.isInitialized()},e.prototype.isPlaying=function(){return this.playerStateService.isPlaying()},e.prototype.isPaused=function(){return this.playerStateService.isPaused()},e.prototype.hasEnded=function(){return this.ended},e.prototype.isMuted=function(){return this.renderer.isMuted()},e.prototype.isStalled=function(){return this.playerStateService.isStalled},e.prototype.isLive=function(){return this.manifestService.isLive()},e.prototype.getVolume=function(){return this.renderer.getVolume()},e.prototype.getCurrentTime=function(e){return void 0===e&&(e=p.TimeMode.AbsoluteTime),!this.isLive()&&this.hasEnded()?this.getDuration():e===p.TimeMode.RelativeTime?Math.max(0,this.playerController.getCurrentTime()-this.relativeCurrentTimeOffset):this.playerController.getCurrentTime()},e.prototype.getDuration=function(){return this.manifestService.getDuration()},e.prototype.getAvailableVideoQualities=function(){return this.isReady()?this.manifestService.getVideoRepresentations(this.getCurrentPlayingPeriodId()):[]},e.prototype.getAvailableSegments=function(){return this.isReady()?this.playerController.getAvailableSegments():{}},e.prototype.getAvailableAudioQualities=function(){var e=this.getAudio();if(!this.isReady()||!e)return[];var t=this.manifestService.getAudioRepresentations(this.getCurrentPlayingPeriodId());return t[e.id]?t[e.id]:[]},e.prototype.getDroppedVideoFrames=function(){return this.renderer.getDroppedVideoFrames()},e.prototype.setTargetBufferLevel=function(e,t,i){this.context.bufferSettings.setTargetLevel(e,t,i)},e.prototype.getBufferLevel=function(e,t){var i={level:null,targetLevel:this.context.bufferSettings.getTargetLevel(e,t),type:e,media:t},n=this.renderer.getCurrentTime(),r=(0,h.getMetricsState)(this.context.store.getState()),o=Object.keys(r).find((function(e){return T.MimeTypeHelper.isVideo(e)})),a=Object.keys(r).find((function(e){return T.MimeTypeHelper.isAudio(e)}));return t===p.MediaType.Video&&o?i.level=this.playerController.getBufferLevel(o,n,e):t===p.MediaType.Audio&&a&&(i.level=this.playerController.getBufferLevel(a,n,e)),i},e.prototype.getTotalStalledTime=function(){var e=(0,h.getMetricsState)(this.context.store.getState());for(var t in e)if(e[t]){var i=(0,h.getMetricsLastEntry)(e,t,g.MetricType.StalledSeconds);if(i&&i.value&&!isNaN(i.value))return Math.round(100*i.value)/100}return 0},e.prototype.getInitializedMimeTypeFromMediaType=function(e){var t=(0,h.getMetricsState)(this.context.store.getState());return Object.keys(t).find((function(t){return t.includes(e)}))},e.prototype.getDownloadedData=function(e){var t=this.getInitializedMimeTypeFromMediaType(e),i=n({},this.getCurrentAdaptationSet(t)),r=n({},this.getCurrentRepresentation(t));return r.id&&(r.id=""+r.id),void 0===r.width&&void 0!==i.width&&(r.width=i.width),r.width=parseInt(""+r.width),void 0===r.height&&void 0!==i.height&&(r.height=i.height),r.height=parseInt(""+r.height),void 0===r.bitrate&&void 0!==i.bitrate&&(r.bitrate=i.bitrate),r.bitrate=parseInt(""+r.bitrate),r.isAuto=this.context.serviceManager.get(s.ServiceName.AdaptationService).isAuto(t),r},e.prototype.getCurrentRepresentation=function(e){if(this.playerController.hasStarted()){var t=(0,h.getMetricsState)(this.context.store.getState());if(t[e])return(0,h.getMetricsLastEntry)(t,e,g.MetricType.DownloadedRepresentation).value}return{}},e.prototype.getCurrentAdaptationSet=function(e){if(this.playerController.hasStarted()){var t=(0,h.getMetricsState)(this.context.store.getState());if(t[e])return(0,h.getMetricsLastEntry)(t,e,g.MetricType.DownloadedAdaptationSet).value}return{}},e.prototype.getDownloadedVideoData=function(){var e=this.getDownloadedData(p.MediaType.Video),t=!0;return e.hasOwnProperty("isAuto")&&(t=e.isAuto),e&&(e.bitrate||e.id)?{id:e.id,bitrate:e.bitrate,height:e.height,width:e.width,isAuto:t}:{id:"",bitrate:0,height:0,width:0,isAuto:!0}},e.prototype.getDownloadedAudioData=function(){var e=this.getDownloadedData(p.MediaType.Audio),t=!0;return e.hasOwnProperty("isAuto")&&(t=e.isAuto),e&&(e.bitrate||e.id)?{id:e.id,bitrate:e.bitrate,isAuto:t}:{id:"",bitrate:0,isAuto:!0}},e.prototype.getPlaybackVideoData=function(){var e=this.playerController.getPlaybackRepresentation(p.MediaType.Video)||{},t=isNaN(e._height)?0:Number(e._height),i=isNaN(e._width)?0:Number(e._width),n={id:e._id+"",bitrate:Number(e._bandwidth)||0,frameRate:e._frameRate,height:t,width:i,uid:e.uid};return e._codecs&&(n.codec=y.CodecStringHelper.getExtractedCodecStrings(e._codecs).video),n},e.prototype.getPlaybackAudioData=function(){var e,t={id:String(void 0),bitrate:Number(void 0)},i=this.playerController.getPlaybackRepresentation(p.MediaType.Audio);return i&&null!==(e=(0,k.representationToQuality)(i,i._mimeType))&&void 0!==e?e:t},e.prototype.setQueryParameters=function(e){this.settings.QUERY_PARAMETERS=e},e.prototype.clearQueryParameters=function(){this.settings.QUERY_PARAMETERS=void 0},e.prototype.restoreAfterTimeShift=function(e){this.playerStateService&&this.playerStateService.isTimeShifting()&&(this.playerStateService.transitionToTimeShiftedState(e),this.timeShiftJumpStart=-1)},e.prototype.onTimeShiftFailed=function(){this.restoreAfterTimeShift(!1)},e.prototype.onTimeShifted=function(e){this.restoreAfterTimeShift(e!==l.STARTUP_ISSUER_NAME)},e.prototype.getMaxTimeShift=function(){if(this.isLive()&&this.isReady())return this.config.playback&&!1===this.config.playback.timeShift?0:this.playerController.getMaxTimeShift()},e.prototype.timeShift=function(e,t){var i=this;if(this.isReady()&&this.getMaxTimeShift()<=0){-1===this.timeShiftJumpStart&&(this.timeShiftJumpStart=this.getCurrentTime(),0===this.timeShiftJumpStart&&(this.timeShiftJumpStart=this.playerController.getTimeShiftLiveEdge())),this.ensureSeekedOrTimeshiftedSubscription();var n=this.playerController.getPlaybackTimeForTimeShiftOffset(e);this.playerStateService.transitionToTimeShiftingState(this.timeShiftJumpStart,n,t||"api",t!==l.STARTUP_ISSUER_NAME),this.playerController.timeShift(e,(function(){return i.onTimeShifted(t)})).catch((function(){return i.onTimeShiftFailed()}))}},e.prototype.getTimeShift=function(){return this.isReady()?this.playerController.getTimeShift():0},e.prototype.addSubtitle=function(e){var t=this;return this.isReady()&&this.subtitleService?Promise.resolve(void 0).then((function(){t.subtitleService.addExternalSubtitle(e)})):Promise.reject(void 0)},e.prototype.removeSubtitle=function(e){this.isReady()&&this.subtitleService&&this.subtitleService.removeSubtitle(e)},e.prototype.enableSubtitle=function(e){return this.isReady()&&this.subtitleService?this.subtitleService.enableSubtitle(e):Promise.resolve(!1)},e.prototype.disableSubtitle=function(e){return this.isReady()&&this.subtitleService?this.subtitleService.disableSubtitle(e):Promise.resolve(!1)},e.prototype.listSubtitles=function(){return this.isReady()&&this.subtitleService&&this.playerController?this.subtitleService.getAvailableSubtitles(this.getCurrentPlayingPeriodId()):[]},e.prototype.setAudio=function(e){var t=this;if(this.isReady()&&this.playerController&&"string"==typeof e){var i=this.getAudio();if(i&&i.id===String(e))return;this.playerController.setAudio(e).then((function(){var e=t.getAudio();e&&i&&e.id!==i.id&&t.context.eventHandler.dispatchEvent(d.PlayerEvent.AudioChanged,{targetAudio:e,sourceAudio:i,time:t.renderer.getCurrentTime()})})).catch((function(e){t.logger.debug("setAudio failed for lang ",e)}))}},e.prototype.getAvailableAudio=function(){return this.isReady()&&this.playerController?this.playerController.getAvailableAudio(this.getCurrentPlayingPeriodId()).map(this.mapLangObjectToAudioTrack):[]},e.prototype.getAudio=function(){if(this.isReady()&&this.playerController){var e=this.playerController.getAudio();if(e)return this.mapLangObjectToAudioTrack(e)}return null},e.prototype.getManifest=function(){var e=this.manifestService.getManifest();if(e)return e._isHls?this.getHlsManifest():e.originalDashManifest},e.prototype.getHlsManifest=function(){return I.ModuleManager.get(P.ModuleName.HLS).selectors.getHlsState(this.getSourceStoreService().getState()).masterPlaylist.string},e.prototype.getBufferedRanges=function(){var e=this.playerController.getBufferedRanges(!0);return o.BufferRangeHelper.getCommonBufferedRanges(e).map((function(e){return{start:e.getStart(),end:e.getEnd()}}))},e.prototype.getSnapshot=function(e,t){if(this.isPlaying()||this.isPaused())return this.renderer.getSnapshotData(e,t)},e.prototype.setPlaybackSpeed=function(e,t,i){void 0===t&&(t=1),void 0===i&&(i=!0),this.renderer.setPlaybackSpeed(e*t),this.playerStateService.playbackSpeed!==this.renderer.getPlaybackSpeed()&&this.playerStateService.setPlaybackSpeed(e,t,i)},e.prototype.getPlaybackSpeed=function(){return this.playerStateService.playbackSpeed},e.prototype.permitControl=function(e){this.controlPermitted=e},e.prototype.getSeekableRange=function(){return this.isLive()&&!this.settings.ENABLE_SEEK_FOR_LIVE||!this.playerController?{start:-1,end:-1}:this.playerController.getSeekableRange()},e.prototype.createManifestApi=function(){return x.MediaPlayerManifestApiFactory.create(this,this.context)},e.prototype.handleDvrWindowExceeded=function(){var e=this.playerController.getTimeShiftHandler(),t=this.getMaxTimeShift()+e.getMinSegmentDuration(),i=this.playerController.getPlaybackTimeForTimeShiftOffset(t),n=e.isTimeInBufferedRange(i);this.context.logger.debug("Handling DVRWindowExceeded - TimeShifting to ".concat(n?t:"live edge")),this.timeShift(n?t:0)},e.prototype.getLatency=function(){return this.liveLatencyService.getLatency()},e.prototype.setTargetLatency=function(e){var t=Math.abs(this.manifestService.getTimeShiftBufferDepthSeconds());e>t&&(e=t,this.logger.debug("Tried to set target latency outside DVR window, corrected to lower bound",t)),this.context.eventHandler.dispatchEvent(d.PlayerEvent.TargetLatencyChanged,{from:this.liveLatencyService.getTargetLatency(),to:e}),this.liveLatencyService.setTargetLatency(e)},e.prototype.getTargetLatency=function(){return this.liveLatencyService.getTargetLatency()},e.prototype.setCatchupConfig=function(e){this.liveLatencyService.setCatchupConfig(e)},e.prototype.getCatchupConfig=function(){return this.liveLatencyService.getCatchupConfig()},e.prototype.setFallbackConfig=function(e){this.liveLatencyService.setFallbackConfig(e)},e.prototype.getFallbackConfig=function(){return this.liveLatencyService.getFallbackConfig()},e.prototype.release=function(e){var t,i=this;return this.eventHandler.off(d.PlayerEvent.PeriodSwitch,this.onPeriodSwitch),this.eventHandler.off(d.PlayerEvent.PeriodSwitched,this.fireAudioTracksAdded),this.eventHandler.off(d.PlayerEvent.PlaybackSpeedChanged,this.onPlaybackSpeedChanged),this.unsubscribeStorePlayingListener(),null===(t=this.unsubscribeToSeekedOrTimeshifted)||void 0===t||t.call(this),this.unload(e).then((function(){return i.playerStateService=null,i.playerController=(0,u.dispose)(i.playerController),i.renderer.release().then((function(){return i.renderer=null}))}))},e.prototype.clearStallEndedSubscription=function(){this.unsubscribeFromStallEnded&&(this.unsubscribeFromStallEnded(),this.unsubscribeFromStallEnded=void 0)},e.prototype.ensureSeekedOrTimeshiftedSubscription=function(){var e=this;!this.unsubscribeToSeekedOrTimeshifted&&this.context.store&&(this.unsubscribeToSeekedOrTimeshifted=(0,v.subscribeToSeekedOrTimeshifted)(this.context.store,(function(){var t;null===(t=e.unsubscribeToSeekedOrTimeshifted)||void 0===t||t.call(e),e.unsubscribeToSeekedOrTimeshifted=void 0,e.canResumeLatencyControl()?e.resumeLatencyControl():e.suspendLatencyControl()})))},e.prototype.getCurrentPlayingPeriodId=function(){return(0,O.getPlayingPeriodId)(this.getSourceStoreService().getState())},e.prototype.updateCallback=function(e){},e.prototype.onFullscreenEnter=function(){},e.prototype.onFullscreenExit=function(){},e.prototype.isAirplayAvailable=function(){return!1},e.prototype.isAirplayActive=function(){return!1},e.prototype.showAirplayTargetPicker=function(){},e}();function V(e){return e===l.ADVERTISING_ISSUER_NAME||e===l.ADVERTISING_RESTORE_ISSUER_NAME}function K(e,t){if(!I.ModuleManager.has(P.ModuleName.RendererMse)||!e.videoElement)return!1;for(var i=I.ModuleManager.get(P.ModuleName.RendererMse).Ranges,n=e.settings.MIN_SIZE_FOR_GAP_SKIPPING,r=i.findGaps(e.videoElement.buffered,n),o=.1,a=!1,s=0;s<r.length;s++){var u=r.end(s)+i.TIME_FUDGE_FACTOR;Math.abs(u-t)<=o&&(a=!0)}return a}t.MediaPlayer=q},86674:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.calculateDownloadRates=r,t.filterValuesExceedingAverageByMargin=o,t.calcAverage=a,t.bpsToMbps=s;var n=i(40392);function r(e){return e.map((function(e){return(0,n.bytesToBits)(e.bytes)/e.downloadDuration})).filter((function(e){return e<1/0}))}function o(e){var t=a(e)*1.1;return e.filter((function(e){return e<=t}))}function a(e){return e.reduce((function(e,t){return e+t}),0)/e.length}function s(e){return e/1024/1024}},87062:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.isLowLatencyConfigured=r;var n=i(18665);function r(e){var t=e.serviceManager.get(n.ServiceName.LiveLatencyService);return(null==t?void 0:t.isConfigured())||!1}},89010:function(e,t,i){var n=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.StringTimeProvider=t.HttpHeadTimeProvider=t.HttpGetTimeProvider=t.HttpTimeProvider=t.ServerTimeProvider=void 0;var r=i(88005),o=i(67345),a=function(){function e(e){this.timeDecoder=e}return e}();t.ServerTimeProvider=a;var s=function(e){function t(t,i){var n=e.call(this,i)||this;return n.context=t,n}return n(t,e),t.prototype.sendSingleRequest=function(e){var t=this,i=e.shift();return i?(this.currentContentLoader=new r.DefaultContentLoader(this.context,{requestType:o.HttpRequestType.TIME_SYNC,maxRetries:1}),this.currentContentLoader.load(i,this.requestMethod).then((function(e){var i=t.timeDecoder.decode(t.processResponse(e));return t.currentContentLoader=void 0,isNaN(i)?Promise.reject(new Error("Failed to decode time")):Promise.resolve(i)})).catch((function(i){return t.context.logger.debug("could not load UTC time url",i),t.sendSingleRequest(e)}))):Promise.reject(new Error("No time source left to try"))},t.prototype.requestTime=function(e){var t=this,i=e.match(/\S+/g);return i?this.sendSingleRequest(i).then((function(e){return t.currentContentLoader=void 0,e})):Promise.reject(new Error("No time source left to try"))},t.prototype.cancelRequest=function(){this.currentContentLoader&&this.currentContentLoader.cancel()},t}(a);t.HttpTimeProvider=s;var u=function(e){function t(t,i){var n=e.call(this,t,i)||this;return n.requestMethod=o.HttpRequestMethod.GET,n}return n(t,e),t.prototype.processResponse=function(e){return e.body},t}(s);t.HttpGetTimeProvider=u;var d=function(e){function t(t,i){var n=e.call(this,t,i)||this;return n.requestMethod=o.HttpRequestMethod.HEAD,n}return n(t,e),t.prototype.processResponse=function(e){var t=Object.keys(e.headers).find((function(e){return"date"===e.toLocaleLowerCase()}));return t&&e.headers[t]||""},t}(u);t.HttpHeadTimeProvider=d;var c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.requestTime=function(e){var t=this.timeDecoder.decode(e);return isNaN(t)?Promise.reject(new Error("Failed to decode time")):Promise.resolve(t)},t.prototype.cancelRequest=function(){},t}(a);t.StringTimeProvider=c},92103:function(e,t){function i(e,t){if(void 0!==e.mediaSequence&&void 0!==t.mediaSequence)return e.mediaSequence===t.mediaSequence;var i=r(e,t),n=a(e.byteRange,t.byteRange),o=e.discontinuitySequenceNumber===t.discontinuitySequenceNumber;return i&&n&&o}function n(e,t){var i=(null==e?void 0:e.url)===(null==t?void 0:t.url),n=a(null==e?void 0:e.byteRange,null==t?void 0:t.byteRange);return i&&n}function r(e,t){var i=o(e),n=o(t);return i.some((function(e){return n.includes(e)}))}function o(e){return[e.url,e.mediaURL].filter(Boolean)}function a(e,t){return null==e?null==t:null!=t&&e.start===t.start&&e.end===t.end}Object.defineProperty(t,"__esModule",{value:!0}),t.isIdenticalSegmentInfo=i,t.isIdenticalInitSegmentInfo=n},92395:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentPrefetchingService=void 0;var n=i(13788),r=i(28499),o=function(){function e(){this.segmentPrefetcher=new r.SegmentPrefetcher(this),this.prefetchedSegments=new Map}return e.prototype.updatePlayerContext=function(e){this.context=e,this.segmentPrefetcher.updateContext(this.context)},e.prototype.setDecrypter=function(e,t){this.segmentPrefetcher.setDecrypter(e,t)},e.prototype.removeDecrypter=function(e){this.segmentPrefetcher.removeDecrypter(e)},e.prototype.getDecrypter=function(e){return this.segmentPrefetcher.getDecrypter(e)},e.prototype.hasMp4Decrypter=function(e){return Boolean(this.getDecrypter(e))},e.prototype.setShouldDownloadBeCancelledCallback=function(e,t){this.segmentPrefetcher.setShouldDownloadBeCancelledCallback(e,t)},e.prototype.removeShouldDownloadBeCancelledCallback=function(e){this.segmentPrefetcher.removeShouldDownloadBeCancelledCallback(e)},e.prototype.fetch=function(e){var t=this;return this.segmentPrefetcher.prefetchSegment(e).then((function(){var i=t.prefetchedSegments.get(e.mimeType).get((0,n.getSegmentInfoId)(e));return i?Promise.resolve(i.loadingPromise):Promise.reject()}))},e.prototype.fetchAll=function(e){this.segmentPrefetcher.prefetchSegments(e)},e.prototype.onPrefetch=function(e){var t=e.segmentInfo.mimeType;this.prefetchedSegments.has(t)||this.prefetchedSegments.set(t,new Map),this.prefetchedSegments.get(t).set((0,n.getSegmentInfoId)(e.segmentInfo),e)},e.prototype.hasPrefetchedSegment=function(e){return Boolean(this.findPrefetchedSegment(e))},e.prototype.getPrefetchedSegment=function(e){var t=this.findPrefetchedSegment(e);return t?t.loadingPromise:null},e.prototype.findPrefetchedSegment=function(e){return this.prefetchedSegments.has(e.mimeType)&&this.prefetchedSegments.get(e.mimeType).get((0,n.getSegmentInfoId)(e))||null},e.prototype.getPrefetchedSegments=function(){return this.prefetchedSegments},e.prototype.removePrefetchedSegment=function(e){this.prefetchedSegments.has(e.mimeType)&&this.prefetchedSegments.get(e.mimeType).delete((0,n.getSegmentInfoId)(e))},e.prototype.clearPrefetchingQueue=function(e){this.segmentPrefetcher.clearPrefetchingQueue(e)},e.prototype.reset=function(){this.segmentPrefetcher.reset(),this.prefetchedSegments.clear()},e.prototype.dispose=function(){this.reset(),this.segmentPrefetcher.dispose(),this.segmentPrefetcher=null,this.prefetchedSegments=null},e}();t.SegmentPrefetchingService=o},93109:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.isPlayingLowLatencyHls=s;var n=i(27279),r=i(91520),o=i(16368),a=i(87062);function s(e){var t;if(!(0,a.isLowLatencyConfigured)(e)||!e.settings.LL_HLS)return!1;var i=(0,n.getSourceState)(e);if(!i)return!1;var s=r.ModuleManager.get(o.ModuleName.HLS,!1);if(!s)return!1;var u=s.selectors,d=u.getHlsState,c=u.getIsLowLatencyPlaylist,l=d(i);return!!l&&(null!==(t=c(l))&&void 0!==t&&t)}},93326:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getMostRecentlyRefreshedQuality=t.getSegmentInfos=t.getSegmentMapEntry=void 0;var i=function(e,t){return null==e?void 0:e.segmentInfoMap[t]};t.getSegmentMapEntry=i;var n=function(e,i){var n,r;return null!==(r=null===(n=(0,t.getSegmentMapEntry)(e,i))||void 0===n?void 0:n.segmentInfos)&&void 0!==r?r:[]};t.getSegmentInfos=n;var r=function(e){if(e)return Object.values(e.segmentInfoMap).filter((function(e){return null!=e.qualityInfo.lastUpdateTimestamp})).sort((function(e,t){var i=e.qualityInfo.lastUpdateTimestamp;return t.qualityInfo.lastUpdateTimestamp-i}))[0]};t.getMostRecentlyRefreshedQuality=r},94938:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.orderAdaptationSetsByCodec=a,t.sortAdaptationSetsByBestCodec=u,t.getCodecsFromAdaptationSet=d,t.getMimeTypeForAdaptationSet=c,t.removeRepresentations=l;var r=i(79814),o=i(38925);function a(e,t){return(e=n({},e)).Period=e.Period.map((function(e){if(!e.AdaptationSet)return n({},e);var i=s(e.AdaptationSet),r=i.videoSets,o=i.audioSets,a=i.otherSets,d=u(r,t.video),c={};return o.forEach((function(e){var t=e._lang||"none";c[t]=c[t]||[],c[t].push(e)})),Object.keys(c).forEach((function(e){d=d.concat(u(c[e],t.audio))})),n(n({},e),{AdaptationSet:d.concat(a)})})),e}function s(e){return e.reduce((function(e,t){var i=c(t);return r.MimeTypeHelper.isVideo(i)?e.videoSets.push(t):r.MimeTypeHelper.isAudio(i)?e.audioSets.push(t):e.otherSets.push(t),e}),{videoSets:[],audioSets:[],otherSets:[]})}function u(e,t){var i=function(e){for(var i=0;i<t.length;i++)if(0===e.indexOf(t[i]))return i;return t.length+1};return e.sort((function(e,t){return i(d(e))-i(d(t))}))}function d(e){var t;if((null===(t=null==e?void 0:e._codecs)||void 0===t?void 0:t.length)>0)return e._codecs;if(e.Representation&&e.Representation.length>0){var i=e.Representation[e.Representation.length-1];if(i._codecs)return i._codecs}return""}function c(e){if(e._mimeType)return e._mimeType;if(Array.isArray(e.Representation)){var t=e.Representation.find((function(e){return e._mimeType}));if(t)return t._mimeType}return""}function l(e,t){if(!(e.Period&&e.Period.length>0)||0===t.length)return e;var i=e.Period.map((function(e){return p(e,t)})).filter((function(e){return e.AdaptationSet.length>0}));return n(n({},e),{Period:i})}function p(e,t){var i=e.AdaptationSet.map((function(e){return f(e._internalId,e,t)})).filter((function(e){return e.Representation.length>0}));return n(n({},e),{AdaptationSet:i})}function f(e,t,i){var r=t.Representation.filter((function(t){return!i.some((function(i){return i.equals(new o.RepresentationId(e,t._id))}))}));return n(n({},t),{Representation:r})}},96280:function(e,t,i){var n,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},o=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentInfoMapReducer=void 0;var a=i(21829),s=i(41661),u=i(85157),d={};function c(e,t,i){var n;return r(r({},e),((n={})[t]=i,n))}function l(e,t,i){var n;if(!e[t]||0===i.segmentInfos.length)return c(e,t,i);var a=e[t].segmentInfos,u=a.findIndex((function(e){return(0,s.areSegmentsIdentical)(e,i.segmentInfos[0])}));if(-1===u)return c(e,t,i);var d,l=a.length-u,p=i.segmentInfos.length-l;if(0!==u||0!==p){var f=p>0?i.segmentInfos.slice(-p):[];d=o(o([],a.slice(u),!0),f,!0)}else d=a;var h=r(r(r({},e[t]),i),{segmentInfos:d});return r(r({},e),((n={})[t]=h,n))}t.SegmentInfoMapReducer=(0,a.default)(d,((n={})[u.SegmentInfoMapActionType.SetSegmentInfos]=function(e,t){return c(e,t.payload.qualityPath,t.payload.updatedEntry)},n[u.SegmentInfoMapActionType.UpdateSegmentInfos]=function(e,t){return l(e,t.payload.qualityPath,t.payload.updatedEntry)},n))},96707:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.createSegmentParser=a;var n=i(91520),r=i(16368),o=i(73543);function a(e,t){if(t===o.ContainerFormat.MP4&&n.ModuleManager.has(r.ModuleName.ContainerMP4)){var i=n.ModuleManager.get(r.ModuleName.ContainerMP4);return new i.MP4Parser(e,new i.MP4EncryptionParser(e))}if(t===o.ContainerFormat.WEBM&&n.ModuleManager.has(r.ModuleName.ContainerWebM))return new(0,n.ModuleManager.get(r.ModuleName.ContainerWebM).WebMParser)}},96953:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.PeriodId=void 0;var i=function(){function e(e){this._periodId=e}return Object.defineProperty(e.prototype,"periodId",{get:function(){return this._periodId},enumerable:!1,configurable:!0}),e.prototype.equals=function(e){return null!=e&&this.periodId===e.periodId},e.prototype.key=function(){return this.periodId},e}();t.PeriodId=i},98086:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.PlayingTracksActionKey=void 0,function(e){e.SetPlayingPeriodId="@instance/sources/@source/playingTracks/setPlayingPeriodId",e.FinishedPeriodSwitch="@instance/sources/@source/playingTracks/periodSwitchFinished",e.StartedPeriodSwitch="@instance/sources/@source/playingTracks/periodSwitchStarted"}(i||(t.PlayingTracksActionKey=i={}))},99162:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractUpdater=t.REPRESENTATION_UPDATE_CANCEL=void 0,t.calculateUpdateDelayInMillis=c;var n=i(18665),r=i(62510),o=i(10981),a=i(331);t.REPRESENTATION_UPDATE_CANCEL="CANCEL";var s=2147483647,u=function(){function e(e,t){var i=this;this.context=e,this.updateCallbacks=t,this.onPlayingEvent=function(){var e,t;!1===(null===(e=i.waitForPlaybackDeferred)||void 0===e?void 0:e.hasSettled)&&(i.context.logger.debug("Scheduling delayed manifest update as playback has started"),null===(t=i.waitForPlaybackDeferred)||void 0===t||t.resolve(),i.waitForPlaybackDeferred=void 0)},this.handlePayloadUpdateError=function(e){i.publishErrorResponse(e),i.clearTimeout()},this.processPayload=function(e){i.success(e),i.clearTimeout(),i._isStopped||i.scheduleUpdate()},this._isStopped=!1,e.eventHandler.on(r.PlayerEvent.Playing,this.onPlayingEvent,!0)}return e.prototype.cancel=function(){var e;this._updateDeferred&&(this._updateDeferred.reject(t.REPRESENTATION_UPDATE_CANCEL),this.updateCallbacks.error(t.REPRESENTATION_UPDATE_CANCEL),null===(e=this.waitForPlaybackDeferred)||void 0===e||e.reject(t.REPRESENTATION_UPDATE_CANCEL),this.waitForPlaybackDeferred=void 0),this.clearTimeout()},e.prototype.clearTimeout=function(){window.clearTimeout(this.updateTimeoutId),this.updateTimeoutId=null},e.prototype.stop=function(){null!==this.updateTimeoutId&&(this._isStopped=!0,this.cancel())},e.prototype.start=function(){this._isStopped=!1,this.scheduleUpdate()},e.prototype.scheduleUpdate=function(e){void 0===e&&(e=!1),this.updateTimeoutId||this._isStopped||this.initializeUpdate(e)},e.prototype.initializeUpdate=function(e){var t=this,i=c(this.getLastReloadTimestamp(),this.getReloadIntervalInSeconds());if(!(i>s)){var n=new o.Deferred,r={isForcedUpdate:e,wasInvalidated:function(){return n!==t._updateDeferred}};n.promise.catch((function(){})),this._updateDeferred=n,this.updateTimeoutId=window.setTimeout((function(){return t.payloadUpdate(r)}),i)}},e.prototype.payloadUpdate=function(e){var t=this;this.maybeDelayUntilPlaybackStarted(e.isForcedUpdate).then((function(){return t.update()})).then((function(i){e.wasInvalidated()||t.processPayload(i)})).catch((function(i){e.wasInvalidated()||t.handlePayloadUpdateError(i)}))},e.prototype.maybeDelayUntilPlaybackStarted=function(e){var t;return!(null===(t=this.context.settings)||void 0===t?void 0:t.POSTPONE_MANIFEST_UPDATES_UNTIL_PLAYBACK_START)||e||!d(this.context)||this.isLowLatency()?Promise.resolve():(this.context.logger.debug("Delaying manifest update until playback has started"),this.waitForPlaybackDeferred=this.waitForPlaybackDeferred||new o.Deferred,this.waitForPlaybackDeferred.promise)},e.prototype.getSourceStoreService=function(){return this.context.serviceManager.get(n.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier)},e.prototype.isLowLatency=function(){var e,t,i;return null===(i=null===(t=null===(e=this.getSourceStoreService())||void 0===e?void 0:e.getState())||void 0===t?void 0:t.hls)||void 0===i?void 0:i.isLowLatencyPlaylist},e.prototype.publishErrorResponse=function(e){this._updateDeferred.reject(e),this.updateCallbacks.error(e)},e.prototype.success=function(e){this._updateDeferred.resolve(e),this.updateCallbacks.success(e)},e.prototype.dispose=function(){this.context.eventHandler.off(r.PlayerEvent.Playing,this.onPlayingEvent,!0)},Object.defineProperty(e.prototype,"isStopped",{get:function(){return this._isStopped},enumerable:!1,configurable:!0}),e.prototype.getPayload=function(){return this._updateDeferred.promise},e}();function d(e){var t,i;return!0===(null===(i=null===(t=null==e?void 0:e.serviceManager)||void 0===t?void 0:t.get(n.ServiceName.PlayerStateService))||void 0===i?void 0:i.isPlay())}function c(e,t){if(isNaN(t))return 0;var i=(0,a.toSeconds)(Date.now()-e);return i>=t?0:(0,a.toMilliSeconds)(t-i)}t.AbstractUpdater=u}},function(e){return function(t){return e(e.s=t)}(44910)}])}));
})();
