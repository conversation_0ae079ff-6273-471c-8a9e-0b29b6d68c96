/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports["engine-native"]=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player["engine-native"]=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[855],{526:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.AppleAirplayService=t.isAirplaySupported=void 0;var n,r=i(62510),a=i(46462);!function(e){e.Available="available",e.NotAvailable="not-available"}(n||(n={}));var o=function(){return Boolean(window.WebKitPlaybackTargetAvailabilityEvent)};t.isAirplaySupported=o;var s=function(){function e(e,t){var i=this;this.videoElement=e,this.eventHandler=t,this.airplayAvailable=!1,this.onWebkitPlaybackTargetAvailabilityChanged=function(e){switch(e.availability){case n.Available:i.airplayAvailable=!0,i.eventHandler.dispatchEvent(r.PlayerEvent.AirplayAvailable);break;case n.NotAvailable:i.airplayAvailable=!1}},this.onWebkitPlaybackTargetIsWirelessChanged=function(){i.eventHandler.dispatchEvent(r.PlayerEvent.AirplayChanged,{airplayEnabled:i.isAirplayActive(),time:i.videoElement.currentTime})},this.initAirplay()}return e.prototype.initAirplay=function(){this.videoElement.setAttribute("x-webkit-airplay","allow"),this.videoElement.addEventListener(a.MediaElementEvent.webkitplaybacktargetavailabilitychanged,this.onWebkitPlaybackTargetAvailabilityChanged),this.videoElement.addEventListener(a.MediaElementEvent.webkitcurrentplaybacktargetiswirelesschanged,this.onWebkitPlaybackTargetIsWirelessChanged)},e.prototype.isAirplayAvailable=function(){return this.airplayAvailable},e.prototype.isAirplayActive=function(){return this.videoElement.isAirplayActive()},e.prototype.showAirplayTargetPicker=function(){this.isAirplayAvailable()&&(this.videoElement.webkitShowPlaybackTargetPicker(),this.eventHandler.dispatchEvent(r.PlayerEvent.ShowAirplayTargetPicker))},e.prototype.dispose=function(){this.videoElement.removeEventListener(a.MediaElementEvent.webkitplaybacktargetavailabilitychanged,this.onWebkitPlaybackTargetAvailabilityChanged),this.videoElement.removeEventListener(a.MediaElementEvent.webkitcurrentplaybacktargetiswirelesschanged,this.onWebkitPlaybackTargetIsWirelessChanged)},e}();t.AppleAirplayService=s},9183:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.HlsDownloadService=void 0;var n=i(63546),r=i(35148),a=i(62510),o=i(88005),s=i(67345),l=function(){function e(e){var t=this;this.useCredentialsForManifestRequests=!1,this.downloadFailureHandler=function(e){t.context.eventHandler.dispatchEvent(a.PlayerEvent.Warning,new n.PlayerWarning(r.WarningCode.NETWORK_COULD_NOT_LOAD_MANIFEST)),t.context.logger.debug("Could not load manifest, got HTTP status code "+e.status)},this.context=e,this.init()}return e.prototype.init=function(){this.initCache(),this.initUseCredentialsForManifest()},e.prototype.initCache=function(){this.requestCache={}},e.prototype.initUseCredentialsForManifest=function(){var e=this.context.sourceContext.source&&this.context.sourceContext.source.options||{};this.useCredentialsForManifestRequests="boolean"==typeof e.hlsManifestWithCredentials?e.hlsManifestWithCredentials:!0===e.manifestWithCredentials},e.prototype.downloadPlaylist=function(e,t,i){return void 0===i&&(i=!0),i&&this.requestCache.hasOwnProperty(e)?Promise.resolve(this.requestCache[e]):this.download(t,e)},e.prototype.download=function(e,t){var i=this,n=this.getContentLoader(e);return n.load(t,s.HttpRequestMethod.GET,s.HttpResponseType.TEXT,null,{},this.useCredentialsForManifestRequests).then((function(e){return n.dispose(),i.requestCache[t]=e,e})).catch((function(e){return n.dispose(),Promise.reject(e)}))},e.prototype.getContentLoader=function(e){return new o.DefaultContentLoader(this.context,{onFailure:this.downloadFailureHandler,maxRetries:this.context.settings.MAX_MPD_RETRIES,requestType:e})},e.prototype.dispose=function(){},e}();t.HlsDownloadService=l},42966:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.NativeModuleDefinition=void 0;var n=i(16368),r=i(72273),a=i(96926);t.NativeModuleDefinition={name:n.ModuleName.EngineNative,module:{NativePlayer:r.NativePlayer,technologyChecker:new a.TechnologyChecker}},t.default=t.NativeModuleDefinition},55194:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.NativePlayerManifestApiFactory=void 0;var n=i(42283),r=function(){function e(){}return e.create=function(t){var i=t.getParsedManifest();return t.isValidMasterPlaylist(i)?new(function(){function i(){this.hls=e.createHlsApi(t)}return i}()):null},e.createHlsApi=function(t){var i=t.getParsedManifest(),n=i.media.filter((function(e){return"AUDIO"===e.attributes.TYPE})),r=i.media.filter((function(e){return"SUBTITLES"===e.attributes.TYPE||"CLOSED-CAPTIONS"===e.attributes.TYPE}));return new(function(){function a(){this.properties=t.getParsedManifest().tags}return a.prototype.getVideoTracks=function(){return e.getVideoTracks(i.playlists)},a.prototype.getAudioTracks=function(){return e.getAudioTracks(n,i.playlists)},a.prototype.getTextTracks=function(){return e.getTextTracks(r)},a}())},e.getAudioTracks=function(e,t){var i=e.reduce((function(e,t){return e[t.attributes.LANGUAGE]||(e[t.attributes.LANGUAGE]=t.attributes.NAME),e}),{}),r=Object.keys(i).map((function(t,n){return{id:"audio-".concat(n),label:i[t],lang:t,getQualities:function(){return e.filter((function(e){return e.attributes.LANGUAGE===t})).map((function(e,t){return{id:t+"",bitrate:0,label:e.attributes.NAME}}))}}})),a=function(e){return e.attributes.CODECS&&n.CodecStringHelper.getMimeTypeForCodecString(e.attributes.CODECS).includes("audio")};return t.some(a)&&r.push({id:"audio-".concat(r.length),label:"und",lang:"",getQualities:function(){return t.filter(a).map((function(e,t){return{id:t+"",bitrate:e.attributes.BANDWIDTH,codec:n.CodecStringHelper.getExtractedCodecStrings(e.attributes.CODECS).audio}}))}}),r},e.getVideoTracks=function(t){var i=function(e){return!e.attributes.CODECS||n.CodecStringHelper.getMimeTypeForCodecString(e.attributes.CODECS).includes("video")},r=t.filter(i);return[{id:"video-0",label:"und",getQualities:function(){return e.getVideoQualities(r)}}]},e.getTextTracks=function(e){return e.map((function(e,t){return{id:"text-".concat(t),label:e.attributes.NAME,lang:e.attributes.LANGUAGE,isFragmented:!0,kind:"CLOSED-CAPTIONS"===e.attributes.TYPE?"caption":"subtitle",url:e.attributes.URI}}))},e.getVideoQualities=function(e){return e.map((function(e){var t=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width,i=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.height,r={id:i?"".concat(i,"_").concat(e.attributes.BANDWIDTH):"".concat(e.attributes.BANDWIDTH),bitrate:e.attributes.BANDWIDTH,width:t||0,height:i||0};if(e.attributes.CODECS){var a=n.CodecStringHelper.getExtractedCodecStrings(e.attributes.CODECS);a.video&&(r.codec=a.video)}return r}))},e}();t.NativePlayerManifestApiFactory=r},60012:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.HlsLiveNativeTimeTracker=void 0;var n=i(15539),r=i(331),a=i(54838),o=i(46462),s=2e3,l=function(){function e(e,t){this.nativePlayer=e,this.context=t,this.timeAdjustment=0,this.unloadTimestamp=null,this.startDate=null}return e.prototype.onUnload=function(e){e===n.ADVERTISING_ISSUER_NAME&&this.context.settings.RESUME_LIVE_CONTENT_AT_PREVIOUS_POSITION_AFTER_AD_BREAK&&null==this.unloadTimestamp&&(this.nativePlayer.isLive()?(this.unloadTimestamp=Date.now(),this.timeAdjustment=this.nativePlayer.getSeekableRange().start,this.startDate=this.nativePlayer.getStartDate()):this.reset())},e.prototype.reset=function(){this.unloadTimestamp=null,this.timeAdjustment=0,this.startDate=null},e.prototype.adjustTargetTime=function(e,t){var i=this;return t!==n.ADVERTISING_RESTORE_ISSUER_NAME||null==this.unloadTimestamp?(this.context.logger.debug("No adjustments are made",this.unloadTimestamp),Promise.resolve(e)):(null!=this.startDate?this.waitForStartDate():Promise.resolve(null)).then((function(t){if(null!=t){i.context.logger.debug("StartDate progressed from ".concat(i.startDate," to: ").concat(t," during the ad"));var n=a.Util.timeInSeconds(t)-a.Util.timeInSeconds(i.startDate);i.timeAdjustment=n}else{i.context.logger.debug("Adjusting time based on seekable range");var o=(0,r.toSeconds)(Date.now()-i.unloadTimestamp);i.timeAdjustment+=o}var s=e-i.timeAdjustment;return i.context.logger.debug("Adjusting target time from ".concat(e," by ").concat(i.timeAdjustment," to ").concat(s)),i.reset(),s}))},e.prototype.waitForStartDate=function(){var e=this,t=this.getValidDateTime();return t?Promise.resolve(t):new Promise((function(t){var i=null,n=function(){t(e.getValidDateTime()),e.context.videoElement.removeEventListener(o.MediaElementEvent.loadeddata,n),clearTimeout(i)};e.context.logger.debug("Waiting for video.startDate to be valid"),e.context.videoElement.addEventListener(o.MediaElementEvent.loadeddata,n),i=setTimeout(n,s)}))},e.prototype.getValidDateTime=function(){var e=this.nativePlayer.getStartDate();return e&&!isNaN(e.getTime())?e:null},e}();t.HlsLiveNativeTimeTracker=l},62545:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.AssetAvailabilityChecker=void 0;var n=i(88005),r=i(67345),a=i(331),o=i(46462),s=function(){function e(e,t,i){var s=this;this.context=e,this.source=t,this.mediaElement=i,this.scheduleAvailabilityCheck=function(){s.mediaElement.removeEventListener(o.MediaElementEvent.loadstart,s.scheduleAvailabilityCheck);var e=(0,a.toMilliSeconds)(s.context.settings.XHR_TIMEOUT)/2;s.assetAvailabilityCheckTimeoutID=window.setTimeout((function(){s.context.logger.debug("Asset loading has not resolved after ".concat((0,a.toSeconds)(e),"s. Checking accessibility."));var t=!1,i=function(e){var i;e.loadedBytes>0&&(t=!0,null===(i=s.contentLoader)||void 0===i||i.cancel())};s.contentLoader=new n.DefaultContentLoader(s.context,{onProgress:i,requestType:r.HttpRequestType.INTERNAL}),s.contentLoader.load(s.source.src).catch((function(e){if(!t){s.context.logger.debug("Forcing error event as the asset was not accessible.",e);var i=new Event(o.MediaElementEvent.error,{bubbles:!1,cancelable:!1});s.source.dispatchEvent(i)}}))}),e)},i.addEventListener(o.MediaElementEvent.loadstart,this.scheduleAvailabilityCheck)}return e.prototype.dispose=function(){this.assetAvailabilityCheckTimeoutID&&window.clearTimeout(this.assetAvailabilityCheckTimeoutID),this.mediaElement.removeEventListener(o.MediaElementEvent.loadstart,this.scheduleAvailabilityCheck),this.contentLoader&&(this.contentLoader.cancel(),this.contentLoader.dispose())},e}();t.AssetAvailabilityChecker=s},69860:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.HlsDownloadErrorHandler=void 0;var n=i(25550),r=i(28764),a=i(88005),o=i(23234),s=i(67345),l=i(13533),d=i(46462),u=function(){function e(e,t){var i=this;this.fakeProgress=function(){for(var e=i.video.buffered,t="",n=0;n<e.length;n++)t+="".concat(e.start(n)," - ").concat(e.end(n),", ");t===i.lastProgressString?i.numProgressNotChanged++:i.numProgressNotChanged=0,i.numProgressNotChanged>=10&&i.onStalled(),i.lastProgressString=t},this.fakeWaiting=function(){var e=o.TimingUtil.getHiResTimestamp(),t=!i.video.paused&&!i.video.seeking&&!i.video.ended;e-i.lastTimeUpdateTimeStamp>5&&t&&i.onWaiting()},this.onStalled=function(){i.stalledTimeStamp=o.TimingUtil.getHiResTimestamp(),i.checkStalledState()},this.onProgress=function(){i.stalledTimeStamp=-1/0,clearTimeout(i.downloadErrorCheckTimeoutId)},this.onWaiting=function(){i.waitingEventTimeStamp=o.TimingUtil.getHiResTimestamp(),i.checkStalledState()},this.onTimeUpdate=function(){i.waitingEventTimeStamp=-1/0,i.lastTimeUpdateTimeStamp=o.TimingUtil.getHiResTimestamp(),clearTimeout(i.downloadErrorCheckTimeoutId)},this.checkForDownloadError=function(){if(0!==i.video.buffered.length){var e=i.video.currentTime,t=i.video.buffered.end(i.video.buffered.length-1),o=e<=t&&e+.5>t,l=i.video.paused,d=i.video.networkState===HTMLMediaElement.NETWORK_LOADING,u=i.video.videoWidth,c=i.video.videoHeight;!l&&o&&d&&i.manifestController.getCurrentSegments(t,u,c,2).then((function(e){var t=new a.DefaultContentLoader(i.context),o=function(i,n){return void 0===i&&(i=!0),e.length>0&&i?t.load(e.pop(),s.HttpRequestMethod.HEAD).then((function(e){return o(e.status>=200&&e.status<300,e)})):i?Promise.resolve():Promise.reject(n)};o().catch((function(e){var t=e.url,a=e.status,o=e.statusText;i.context.eventHandler.fireError(new r.PlayerError(n.ErrorCode.NETWORK_ERROR,{url:t,statusCode:a,statusText:o},"Failed to load segment ".concat(t,", statusCode: ").concat(a,", statusText: ").concat(o)))}))}))}},this.context=e,this.video=e.videoElement,this.manifestController=t,this.playbackAdvancing=!1,this.stalledTimeStamp=-1/0,this.waitingEventTimeStamp=-1/0,this.downloadErrorCheckTimeoutId=-1,this.lastProgressString="",this.numProgressNotChanged=0,this.lastTimeUpdateTimeStamp=-1/0,this.video.addEventListener(d.MediaElementEvent.waiting,this.onWaiting),this.video.addEventListener(d.MediaElementEvent.stalled,this.onStalled),this.video.addEventListener(d.MediaElementEvent.progress,this.onProgress),this.video.addEventListener(d.MediaElementEvent.timeupdate,this.onTimeUpdate),(0,l.getCapabilities)().isIOS&&(this.fakeEventsIntervalId=window.setInterval((function(){i.fakeProgress(),i.fakeWaiting()}),500))}return e.prototype.checkStalledState=function(){var e=Math.abs(this.waitingEventTimeStamp-this.stalledTimeStamp);clearTimeout(this.downloadErrorCheckTimeoutId),isFinite(e)&&(this.downloadErrorCheckTimeoutId=window.setTimeout(this.checkForDownloadError,1e3*(5-Math.min(5,e))))},e.prototype.dispose=function(){clearTimeout(this.fakeEventsIntervalId),clearTimeout(this.downloadErrorCheckTimeoutId),this.video.removeEventListener(d.MediaElementEvent.waiting,this.onWaiting),this.video.removeEventListener(d.MediaElementEvent.stalled,this.onStalled),this.video.removeEventListener(d.MediaElementEvent.progress,this.onProgress),this.video.removeEventListener(d.MediaElementEvent.timeupdate,this.onTimeUpdate),this.video=null,this.manifestController=null},e}();t.HlsDownloadErrorHandler=u},70539:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.FallbackSegmentTimelineController=void 0;var n=i(68329),r=function(){function e(e){this.videoElement=e,this.shouldCalculatePlaybackTimeForLive=!0,this.timelines={}}return e.prototype.isLiveStream=function(){return this.videoElement.duration===1/0},e.prototype.switchTimeline=function(e){this.timelines[e]||(this.currentActiveTimelineId=e,this.timelines[e]=[])},e.prototype.updateTimeline=function(e,t){var i=this,r=this.timelines[e],a=n.updateTimeline(r,t),o=a.findIndex((function(e){return e.hasOwnProperty("playbackTime")}));-1!==o&&n.populateCurrentTimelineWithPlaybackTime(a,o,a[o].playbackTime),this.timelines[e]=a;var s=a.reduce((function(e,t){return i.isLiveStream()&&!i.shouldCalculatePlaybackTimeForLive||(t.playbackTime=e),e+t.duration}),0);return this.shouldCalculatePlaybackTimeForLive&&(this.shouldCalculatePlaybackTimeForLive=!1),s},e.prototype.getPlayingSegment=function(e){for(var t=this.getCurrentTimeline(),i=0;i<t.length;i++){var n=t[i];if(e>=n.playbackTime&&e<=n.playbackTime+n.duration)return t[i]}return null},e.prototype.addDownloadedSegments=function(e){},e.prototype.getCurrentTimeline=function(){return this.timelines[this.currentActiveTimelineId]},e.prototype.getAllTimelines=function(){return this.timelines},e.prototype.reset=function(){this.timelines[this.currentActiveTimelineId].forEach((function(e){return e.consumed=!1}))},e.prototype.dispose=function(){this.timelines={},this.videoElement=null},e}();t.FallbackSegmentTimelineController=r},70661:function(e,t,i){var n=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.FallbackHlsManifestController=void 0;var r,a=i(63546),o=i(35148),s=i(62510),l=i(67345),d=i(331),u=i(91520),c=i(16368),h=i(77117),v=i(70539),g=i(9183),p=i(76250),m=function(e){function t(t){var i=e.call(this,t)||this;return i.onLoadPlaylistError=function(e){return i.context.logger.debug("Error while loading the playlist ",e),null},r=u.ModuleManager.get(c.ModuleName.HLS).PlaylistUtils,i.hlsDownloadService=new g.HlsDownloadService(i.context),i.segmentTimelineController=new v.FallbackSegmentTimelineController(t.videoElement),i.context.logger.debug("Initializing controller for native HLS parsing"),i}return n(t,e),t.prototype.init=function(){return Promise.resolve()},t.prototype.getDownloadedVideoData=function(){return{id:"not available",bitrate:0,height:0,width:0,isAuto:!0}},t.prototype.getPlayingVideoData=function(){return null},t.prototype.downloadMasterManifest=function(e){var t=this;return this.hlsDownloadService.downloadPlaylist(e,l.HttpRequestType.MANIFEST_HLS_MASTER).then((function(e){var i=t.registerMasterManifest(e,e.url),n=t.parsedManifest.playlists[0].uri;return t.registerFirstVariantPlaylist(n).then((function(n){return t.overrideManifestUrl(i,e.url,n)}))}))},t.prototype.processMasterManifest=function(e,t){return p.replaceManifestUrls(t,e,this.settings.QUERY_PARAMETERS)},t.prototype.registerFirstVariantPlaylist=function(e){var t=this;return this.hlsDownloadService.downloadPlaylist(e,l.HttpRequestType.MANIFEST_HLS_VARIANT).then(this.registerVariantPlaylist).then((function(i){return r.isLive(i)&&t.scheduleReload(i,e),i}))},t.prototype.overrideManifestUrl=function(e,t,i){return"createObjectURL"in window.URL?this.getManifestUrl(e,i):(this.context.eventHandler.dispatchEvent(s.PlayerEvent.Warning,new a.PlayerWarning(o.WarningCode.SETUP_CREATE_OBJECT_URL_MISSING)),Promise.resolve(t))},t.prototype.getManifestUrl=function(e,t){var i=this;return r.isLive(t)?Promise.resolve(this.createDataUrlForManifest(e)):Promise.all(this.getMediaPlaylistPromises(e)).then((function(t){return t.filter((function(e){return Boolean(e)})).forEach((function(t){e=e.replace(t.mediaUrl,t.blobUrl)})),i.createDataUrlForManifest(e)}))},t.prototype.getMediaPlaylistPromises=function(e){var t=this;return e.split("\n").map((function(e){return p.parseUrlFromLine(e)})).filter((function(e){return null!==e})).map((function(e){return t.getMediaPlaylistBlobUrlMapping(e)}))},t.prototype.getMediaPlaylistBlobUrlMapping=function(e){var t=this;return this.hlsDownloadService.downloadPlaylist(e,l.HttpRequestType.MANIFEST_HLS_VARIANT).then((function(i){var n=p.replaceManifestUrls(e,i.body,t.settings.QUERY_PARAMETERS);return{blobUrl:t.createBlobForManifest(n),mediaUrl:e}})).catch(this.onLoadPlaylistError)},t.prototype.loadPlaylist=function(e){var t=this;return this.hlsDownloadService.downloadPlaylist(e.uri,l.HttpRequestType.MANIFEST_HLS_VARIANT).then((function(i){return{width:e.width,height:e.height,timeline:t.parseTimelineEntries(i.body)}})).catch(this.onLoadPlaylistError)},t.prototype.reload=function(e){var t=this;return function(){if(!t.hlsDownloadService)return function(){};t.hlsDownloadService.downloadPlaylist(e,l.HttpRequestType.MANIFEST_HLS_VARIANT,!1).then(t.registerVariantPlaylist).then((function(e){return r.isLive(e)&&t.scheduleReload(e,t.parsedManifest.playlists[0].uri),e})).catch(t.onLoadPlaylistError)}},t.prototype.scheduleReload=function(e,t){this.reloadTimerId=setTimeout(this.reload(t),(0,d.toMilliSeconds)(e.targetDuration))},t.prototype.createBlobForManifest=function(e){var t=new Blob([e],{type:"application/x-mpegURL"});return window.URL.createObjectURL(t)},t.prototype.createDataUrlForManifest=function(e){return"data:application/x-mpegURL,"+encodeURIComponent(e)},t.prototype.getVariantPlaylistInfos=function(){return this.parsedManifest.playlists.map((function(e){return{uri:e.uri,width:e.attributes&&e.attributes.RESOLUTION?e.attributes.RESOLUTION.width:-1,height:e.attributes&&e.attributes.RESOLUTION?e.attributes.RESOLUTION.height:-1}}))},t.prototype.loadAllVariantPlaylists=function(){var e=this,t=this.getVariantPlaylistInfos();t.reverse();var i=t.map((function(t){return e.loadPlaylist(t)}));return Promise.all(i).then((function(t){var i=e.getPlaybackTimeOfFirstSegmentInTimeline(t);return i>=0&&t.forEach((function(e){e.timeline.reduce((function(e,t){return t.playbackTime=e,e+t.duration}),i)})),t}))},t.prototype.getCurrentSegments=function(e,t,i,n){return void 0===n&&(n=1),this.loadAllVariantPlaylists().then((function(r){var a=r.find((function(e){return e.width===t&&e.height===i}));return f(a?[a]:r,n,e)}))},t.prototype.dispose=function(){e.prototype.dispose.call(this),clearTimeout(this.reloadTimerId),this.reloadTimerId=null},t}(h.AbstractHlsManifestController);function f(e,t,i){for(var n=[],r=0,a=0;a<e.length;a++){var o=e[a];r=0;for(var s=0;s<o.timeline.length;s++){var l=o.timeline[s];if(l.playbackTime+l.duration>=i&&(n.push(l.uri),r++),r>t)break}}return n}t.FallbackHlsManifestController=m},72273:function(e,t,i){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)},r=this&&this.__decorate||function(e,t,i,n){var r,a=arguments.length,o=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,i,n);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(o=(a<3?r(o):a>3?r(t,i,o):r(t,i))||o);return a>3&&o&&Object.defineProperty(t,i,o),o};Object.defineProperty(t,"__esModule",{value:!0}),t.NativePlayer=void 0;var a=i(92712),o=i(25550),s=i(28764),l=i(63546),d=i(35148),u=i(18665),c=i(60997),h=i(62510),v=i(57620),g=i(15539),p=i(88005),m=i(16937),f=i(33696),y=i(17990),T=i(58975),S=i(13533),E=i(3464),b=i(81361),k=i(10981),P=i(79814),M=i(56435),C=i(331),A=i(70016),L=i(76885),D=i(54838),I=i(61878),w=i(46462),R=i(91520),H=i(16368),O=i(67345),N=i(526),_=i(62545),x=i(69860),V=i(60012),U=i(91229),F=i(55194),W=2,j=.1,B=.25,Q="not available",G=.75,q=2,K=0,X=function(){function e(e){var t=this;this.numTimeChanged=0,this.loaded=!1,this.audio=null,this.ended=!1,this.ignoreNextVideoError=!1,this.controlPermitted=!0,this.seekingTimeoutId=-1,this.nativeSeekCalled=!1,this.isIllicitlySeeking=!1,this.ignoreNextDelta=!1,this.numSeekingCalled=0,this.timeShiftJumpStartPosition=-1,this.seekTargetTime=-1,this.textTrackController=null,this.lastPlayIssuer="api",this.lastPauseIssuer="api",this.lastSeekIssuer="api",this.sourceLoadRetryCount=0,this.lastVerifiedPlaybackPosition=-1,this.lastLegalPlaybackPosition=0,this.restrictedInlinePlayback=!1,this.lastTime=0,this.availableVideoStreams=[],this.allowedToThrowEvents=!0,this.hasSourceBeenLoaded=!1,this.hasDataBeenLoaded=!1,this.sourceElementEventListeners=[],this.restoringPlaybackPosAfterQualitySwitch=!1,this.isInitialSeek=!1,this.lastDelayedSeekHandlers={},this.isProgressiveQualityChange=!1,this.isSourceBeingRedirected=!1,this.isInNativeFullscreen=!1,this.onAddTrack=function(e){if(t.isReady()){var i=e.track;t.eventCallback(h.PlayerEvent.AudioAdded,{track:t.convertAudioTrackNativeToApi(i)}),i.enabled&&t.loaded&&t.dispatchAudioChangedEvent(null,i)}},this.onRemoveTrack=function(e){t.isReady()&&t.eventCallback(h.PlayerEvent.AudioRemoved,{track:t.convertAudioTrackNativeToApi(e.track)})},this.onWebkitPresentationModeChanged=function(){var e,i,n=t.video.getViewModeFromPresentationMode();n===f.ViewMode.PictureInPicture||n===f.ViewMode.Fullscreen&&(0,S.getCapabilities)().isIOS?null===(e=t.textTrackController)||void 0===e||e.enableNative():null===(i=t.textTrackController)||void 0===i||i.disableNative(),t.context.store.dispatch((0,y.setPlayerViewMode)(n))},this.onDurationChange=function(){(0,v.maybeFireDurationChangedEvent)(t.lastDuration,t.getDuration(),t.eventHandler),t.lastDuration=t.getDuration(),t.hasDataBeenLoaded&&t.maybeResolveDeferredLoading()},this.videoOnPlay=function(){t.stateManager.stallExit(),t.restrictedInlinePlayback&&t.audio&&t.setVideoTime(),t.ended=!1,t.stateManager.transitionToPlayState(!0,t.lastPlayIssuer)},this.videoOnPlaying=function(){t.stateManager.isStalled?(t.stateManager.stallExit(),t.stateManager.transitionToPlayingState(t.lastPlayIssuer)):t.stateManager.isStopped()?t.onPlaybackStarted():t.stateManager.isPlaying()||(t.stateManager.transitionToPlayState(!0,t.lastPlayIssuer),t.stateManager.transitionToPlayingState(t.lastPlayIssuer))},this.loadedDataHandler=function(){var e;clearTimeout(t.loadedDataFallbackTimeoutID),null===(e=t.assetAvailabilityChecker)||void 0===e||e.dispose(),t.hasSourceBeenLoaded||clearTimeout(t.loadTimeoutID),t.hasSourceBeenLoaded=!0,t.isProgressiveQualityChange=!1,t.allowedToThrowEvents=!0,t.hasDataBeenLoaded=!0,t.maybeResolveDeferredLoading()},this.eventCallback=function(e,i,n,r){void 0===i&&(i={}),void 0===n&&(n=!1),void 0===r&&(r=!1),t.eventHandler&&(t.allowedToThrowEvents||n)&&t.eventHandler.dispatchEvent(e,i,r)},this.onTimeUpdate=function(e){var i=t.getElement(),n=i.currentTime;(-1!==t.lastVerifiedPlaybackPosition||n!==t.lastVerifiedPlaybackPosition)&&!t.isIllicitlySeeking&&(t.isIllegalSeek(n)?(Promise.resolve(t.video.play()).catch((function(e){t.logger.insane("Play promise rejected:",e)})),t.ignoreNextDelta=!0,i.currentTime=t.lastLegalPlaybackPosition):t.handleTimeUpdateEvent(i,n),t.nativeSeekCalled=!1,t.currentVideoStream&&(t.currentVideoStream.width=t.video.videoWidth,t.currentVideoStream.height=t.video.videoHeight))},this.onPlaybackStarted=function(){t.isPlaying()||(t.ended=!1,t.startManifestTimeoutTimer(),t.stateManager.transitionToPlayingState(t.lastPlayIssuer))},this.onContentPlaybackFinished=function(){t.ended=!0},this.getCurrentTime=function(e){void 0===e&&(e=f.TimeMode.AbsoluteTime);var i=t.getStartDate();return e===f.TimeMode.AbsoluteTime&&i?D.Util.timeInSeconds(i)+t.getElement().currentTime:t.getElement().currentTime},this.getAudioQuality=this.getPlaybackAudioData,this.getVideoQuality=this.getPlaybackVideoData,this.videoOnEnded=function(){var e,i=t.getElement(),n=Math.abs(i.currentTime-t.lastLegalPlaybackPosition),r=t.settings.GLOBAL_DISABLE_SEEKING,a=null!==(e=t.currentDuration)&&void 0!==e?e:i.duration;r&&(n>G||i.currentTime<a-G)?(Promise.resolve(t.video.play()).catch((function(e){t.logger.insane("Play promise rejected:"+e)})),t.ignoreNextDelta=!0,t.video.currentTime=t.lastLegalPlaybackPosition):(t.lastLegalPlaybackPosition=0,t.stateManager.isPlaying()&&t.stateManager.transitionToPausedState(!0,t.lastPauseIssuer),t.stateManager.transitionToStoppedState())},this.videoOnSeeked=function(){t.resetTimeShift(),t.numSeekingCalled=0,!t.settings.GLOBAL_DISABLE_SEEKING||t.restoringPlaybackPosAfterQualitySwitch||t.isInitialSeekToStartTime?(t.lastVerifiedPlaybackPosition=t.getElement().currentTime,t.isInitialSeekToStartTime=!1,t.stateManager.isSeeking()&&(t.textTrackController&&t.textTrackController.seek(),t.maybeHandleSeekTargetChangeDuringStartup())):t.isIllicitlySeeking=!1,t.finishedSeekedTransitionState()},this.videoOnSeeking=function(){if(t.settings.GLOBAL_DISABLE_SEEKING&&!t.isInitialSeekToStartTime)t.isIllicitlySeeking=!t.restoringPlaybackPosAfterQualitySwitch,t.numSeekingCalled++,t.numSeekingCalled>1&&(t.ignoreNextDelta=!1);else if(!t.stateManager.isSeeking()&&!t.stateManager.isTimeShifting()){var e=t.getElement().currentTime;t.stateManager.transitionToSeekingIOSState(e,g.INTERNAL_ISSUER_NAME),t.lastVerifiedPlaybackPosition=e}},this.onTimeChanged=function(){void 0===t.maxTimeShift&&(t.maxTimeShift=t.getElement().currentTime)},this.delayedSeek=function(){t.numTimeChanged++,t.numTimeChanged<t.settings.IOS_MIN_TIMEUPDATES_AFTER_AD||(t.eventHandler.off(h.PlayerEvent.TimeChanged,t.delayedSeek),t.restoringPlaybackPosAfterQualitySwitch=!0,t.triggerSeek({time:t.currentTimeWas,issuer:"api",force:!0}))},this.context=e,this.isAtLiveEdge=!1,this.logger=e.logger,this.video=e.videoElement,this.config=e.config,this.eventHandler=e.eventHandler,this.settings=e.settings,this.hlsSeekingGlitchEventHandlers=[],this.downloadErrorHandler=null,this.currentVol=this.video.volume,this.originalCrossOriginAttributeValue=this.video.getAttribute("crossOrigin"),this.currentTimeShift=-1/0,(0,N.isAirplaySupported)()&&(this.airplayService=new N.AppleAirplayService(this.video,this.eventHandler)),this.hlsLiveTimeTracker=new V.HlsLiveNativeTimeTracker(this,e),this.elementEvents=[{event:w.MediaElementEvent.pause,handler:function(){t.resetTimeShift(),t.ended||t.stateManager.transitionToPausedState(!0,t.lastPauseIssuer,!0)}},{event:w.MediaElementEvent.ended,handler:function(){t.videoOnEnded()}},{event:w.MediaElementEvent.play,handler:this.videoOnPlay},{event:w.MediaElementEvent.canplay,handler:function(){t.stateManager.stallExit()}},{event:w.MediaElementEvent.seeking,handler:function(){t.nativeSeekCalled=!0,t.videoOnSeeking(),t.getCurrentTime()>=t.getDuration()&&(t.videoOnSeeked(),t.videoOnEnded())}},{event:w.MediaElementEvent.seeked,handler:function(){t.videoOnSeeked(),t.restrictedInlinePlayback&&t.audio&&t.setVideoTime()}},{event:w.MediaElementEvent.durationchange,handler:this.onDurationChange},{event:w.MediaElementEvent.loadeddata,handler:this.loadedDataHandler},{event:w.MediaElementEvent.loadedmetadata,handler:function(){t.loadedDataFallbackTimeoutID=window.setTimeout(t.loadedDataHandler,200)}},{event:w.MediaElementEvent.playing,handler:this.videoOnPlaying},{event:w.MediaElementEvent.waiting,handler:function(){t.resetTimeShift(),t.stateManager.stallEnter()}},{event:w.MediaElementEvent.timeupdate,handler:this.onTimeUpdate},{event:w.MediaElementEvent.error,handler:function(e){t.hasSourceBeenLoaded||1!==t.availableVideoStreams.length||t.type!==f.StreamType.Progressive?(t.loadingDeferred.reject(),t.ignoreNextVideoError?(t.logger.debug("Ignoring video element error",e.errorObject),t.ignoreNextVideoError=!1):(t.logger.debug("video element has thrown an error",e.errorObject),clearTimeout(t.loadTimeoutID),t.eventHandler.fireError((0,v.createPlayerErrorFromMediaError)(e,!0)))):t.handleStreamErrorEvent(o.ErrorCode.SOURCE_PROGRESSIVE_STREAM_ERROR,t.video.getWrappedElement().src,e)}},{event:w.MediaElementEvent.webkitpresentationmodechanged,handler:this.onWebkitPresentationModeChanged},{elementSelector:function(e){return e.audioTracks},event:"addtrack",handler:this.onAddTrack},{elementSelector:function(e){return e.audioTracks},event:"removetrack",handler:this.onRemoveTrack}]}return e.prototype.shouldAwaitDurationAvailability=function(){return!(0,M.isWebOS)()&&!this.getDuration()},e.prototype.shouldAwaitSeekableRange=function(){var e,t;if(!this.isHlsLive()||(null===(t=null===(e=this.config)||void 0===e?void 0:e.tweaks)||void 0===t?void 0:t.prevent_video_element_preloading)||(0,M.isWebOS)()||this.isSourceBeingRedirected)return!1;var i=this.video.readyState>=HTMLMediaElement.HAVE_CURRENT_DATA;return!Z(this.getElement().seekable)&&!i},e.prototype.maybeResolveDeferredLoading=function(){this.shouldAwaitDurationAvailability()||this.shouldAwaitSeekableRange()?this.logger.debug("Cannot resolve deferred loading yet, waiting for required data to be loaded ",{duration:String(this.getDuration()),seekableRangeAvailable:Z(this.getElement().seekable),videoElementReadyState:this.getElement().readyState}):this.loadingDeferred.resolve()},e.prototype.handleTimeUpdateEvent=function(e,t){if(this.ignoreNextDelta=!1,this.currentDuration=e.duration,t>this.currentDuration+q)return this.context.logger.debug("CurrentTime ".concat(t," exceeds video duration (").concat(this.currentDuration,"), reverting current time")),void(e.currentTime=this.currentDuration-.1);(this.lastLegalPlaybackPosition=t,this.stateManager.stallExit(),this.stateManager.isSeeking()||this.stateManager.isTimeShifting()||!this.isPlaying())||(null!=this.getStartDate()?this.eventCallback(h.PlayerEvent.TimeChanged,{time:this.getCurrentTime(),relativeTime:t}):this.eventCallback(h.PlayerEvent.TimeChanged,{time:this.getCurrentTime()}))},e.prototype.isIllegalSeek=function(e){if(!this.settings.GLOBAL_DISABLE_SEEKING||!this.nativeSeekCalled)return!1;var t=-1!==this.lastVerifiedPlaybackPosition&&e>this.lastVerifiedPlaybackPosition+G,i=Math.abs(e-this.lastLegalPlaybackPosition);return(!this.ignoreNextDelta||t)&&i>G},e.prototype.getElement=function(){return this.restrictedInlinePlayback&&this.audio?this.audio:this.video},e.prototype.setVideoTime=function(){var e=Date.now();(0,C.toSeconds)(e-this.lastTime)>=1/30&&(this.video.currentTime=this.audio.currentTime,this.lastTime=e),this.isPlaying()&&window.requestAnimationFrame(this.setVideoTime)},e.prototype.maybeGoToLiveEdgeOnPlay=function(){if(this.isLive()&&!this.isTimeShiftingAllowed()){var e=this.getSeekableRangeInternal();e.end>=0&&this.seekForLive(e.end,g.STARTUP_ISSUER_NAME)}},e.prototype.resolveDelayedSeek=function(){void 0!==this.delayedSeekTarget&&(this.clearDelayedSeekHandlers(),this.seek(this.delayedSeekTarget,"api-play"))},e.prototype.play=function(e){return this.controlPermitted?(this.lastPlayIssuer=e,this.resolveDelayedSeek(),this.maybeGoToLiveEdgeOnPlay(),this.getElement().play()):Promise.reject("Play control not permitted")},e.prototype.getStartDate=function(){if(!this.isLive())return null;var e=this.getElement().getWrappedElement(),t="function"==typeof e.getStartDate?e.getStartDate():null;return t&&!isNaN(t.getTime())?t:null},e.prototype.preload=function(){this.getElement().preload="auto"},e.prototype.pause=function(e){this.lastPauseIssuer=e;var t=this.getElement();this.controlPermitted&&this.loaded&&(this.stateManager.transitionToPausedState(!1,this.lastPauseIssuer),t.pause())},e.prototype.mute=function(e){this.getElement().muted||(this.getElement().muted=!0,this.eventCallback(h.PlayerEvent.Muted,{issuer:e||"api"}))},e.prototype.unmute=function(e){this.getElement().muted&&(this.getElement().muted=!1,this.eventCallback(h.PlayerEvent.Unmuted,{issuer:e||"api"}))},e.prototype.getActualSeekTarget=function(e){var t=this.getStartDate();return t?e-(0,C.toSeconds)(t.getTime()):e},e.prototype.seek=function(e,t){var i=this;return!(!this.isSeekingAllowed(e)||this.getCurrentTime()===e)&&(this.hlsLiveTimeTracker.adjustTargetTime(e,t).then((function(e){i.lastSeekIssuer=t,i.triggerSeekEvent(e,t);var n=i.getActualSeekTarget(e);return i.triggerSeek({time:n,issuer:t})})),this.hasDataBeenLoaded&&this.getDuration()>0)},e.prototype.seekForLive=function(e,t){this.timeShiftJumpStartPosition=this.getCurrentTime(),this.lastSeekIssuer=t,this.triggerTimeShiftEvent(e,t),this.triggerSeekForLive({time:e,issuer:t})},e.prototype.isSeekingAllowed=function(e){this.isInitialSeekToStartTime=this.isInitialSeek&&this.hasStartTime(e);var t=!this.settings.GLOBAL_DISABLE_SEEKING&&this.isSeekingEnabled();return(this.isInitialSeekToStartTime||t)&&this.controlPermitted},e.prototype.isTimeShiftingAllowed=function(){var e=!(this.config.playback&&this.config.playback.hasOwnProperty("timeShift"))||Boolean(this.config.playback.timeShift);return this.isLive()&&e},e.prototype.triggerSeek=function(e){return!this.maybeDelaySeek(e)&&(this.stateManager.transitionToSeekingState(e.time,e.issuer,!1),this.finishSeek(e),!0)},e.prototype.triggerSeekForLive=function(e){this.maybeDelaySeek(e,!0)||(this.stateManager.transitionToTimeShiftingState(this.getCurrentTime(),e.time,e.issuer,!1),e.time<0&&(e.time=this.getSeekableRangeInternal().end+e.time,this.resetTimeShift()),this.finishSeek(e))},e.prototype.maybeDelaySeek=function(e,t){return void 0===t&&(t=!1),this.stateManager.isSeeking()&&"api-play"!==e.issuer?(this.logger.debug("Seeking already in progress, delaying seek for time",e.time),this.scheduleDelayedSeek(e,t,[w.MediaElementEvent.seeked]),!0):this.hasDataBeenLoaded?0===this.getDuration()?(this.logger.debug("Delaying the seek until stream duration is known"),this.scheduleDelayedSeek(e,t,[w.MediaElementEvent.durationchange]),!0):this.video.seekable.length<1&&(this.logger.debug("Delaying the seek as the seekable range is not yet known"),this.scheduleDelayedSeek(e,t,[w.MediaElementEvent.timeupdate,w.MediaElementEvent.canplay]),!0):(this.logger.debug("Delaying the seek until data is loaded"),this.scheduleDelayedSeek(e,t,[w.MediaElementEvent.loadeddata]),!0)},e.prototype.finishSeek=function(e){var t=e.issuer===g.ADVERTISING_ISSUER_NAME,i=this.getSeekableRangeInternal();this.logger.debug("Seek to ".concat(e.time,", seekable range:"),i);var n=this.adjustSeekTimeForBoundaries(e.time,i,t);this.isInitialSeek=!1,this.ignoreNextDelta=this.ignoreNextDelta||this.isInitialSeekToStartTime||e.force;var r=parseFloat(n.toFixed(W));this.seekTargetTime=r,this.logger.debug("Setting time on video element from ".concat(this.getElement().currentTime," to ").concat(r)),this.getElement().currentTime=r},e.prototype.adjustSeekTimeForBoundaries=function(e,t,i){void 0===i&&(i=!1);var n=$(e,t);return i?e>=t.end&&(n=t.end-j):n=Math.min(n,t.end-this.settings.SEEK_TO_END_OFFSET),n!==e&&this.logger.debug("Adjusted seek target from ".concat(e," to ").concat(n)),n},e.prototype.isSeekingEnabled=function(){var e=this.config&&this.config.hasOwnProperty("playback")?this.config.playback:{};return!e.hasOwnProperty("seeking")||e.seeking},e.prototype.hasStartTime=function(e){var t=this.context.sourceContext.source?this.context.sourceContext.source:{};return t.hasOwnProperty("options")&&t.options.hasOwnProperty("startTime")&&e===t.options.startTime},e.prototype.triggerSeekEvent=function(e,t){t!==g.STARTUP_ISSUER_NAME&&this.eventCallback(h.PlayerEvent.Seek,{position:this.getCurrentTime(),seekTarget:e,issuer:t||"api"})},e.prototype.triggerTimeShiftEvent=function(e,t){t!==g.STARTUP_ISSUER_NAME&&this.eventCallback(h.PlayerEvent.TimeShift,{target:e<0?this.timeShiftJumpStartPosition+e:e,position:this.timeShiftJumpStartPosition,issuer:t||"api"})},e.prototype.waitForOnSeeked=function(e){var t=this;return new Promise((function(i){var n=function(){e.removeEventListener(w.MediaElementEvent.seeked,n),i()};t.hlsSeekingGlitchEventHandlers.push({el:e,ev:w.MediaElementEvent.seeked,fn:n}),e.addEventListener(w.MediaElementEvent.seeked,n)}))},e.prototype.waitForOnProgress=function(e,t){var i=this;return new Promise((function(n,r){var a=0,o=function(){var s=e.buffered,l=s.length>0?s.end(s.length-1):-1/0;(a+=isFinite(l)?1:0)>i.settings.SAFARI_NUM_PROGRESS_EVENTS_AFTER_SEEK_GLITCH&&l<t?(e.removeEventListener(w.MediaElementEvent.progress,o),r(null)):l>=t&&(e.removeEventListener(w.MediaElementEvent.progress,o),n())};i.hlsSeekingGlitchEventHandlers.push({el:e,ev:w.MediaElementEvent.progress,fn:o}),e.addEventListener(w.MediaElementEvent.progress,o)}))},e.prototype.verifyHLSPlaybackBuffer=function(e,t){var i=this;return this.type!==f.StreamType.Hls?Promise.resolve():this.waitForOnSeeked(e).then((function(){return i.waitForOnProgress(e,t)})).catch((function(){throw i.logger.debug("seek to target time (".concat(t,") failed - trying to recover by seeking to ").concat(t+1)),null}))},e.prototype.clearDelayedSeekHandlers=function(){var e=this;Object.keys(this.lastDelayedSeekHandlers).forEach((function(t){e.getElement().removeEventListener(t,e.lastDelayedSeekHandlers[t])})),this.lastDelayedSeekHandlers={}},e.prototype.scheduleDelayedSeek=function(e,t,i){var n=this,r=this.getElement();this.clearDelayedSeekHandlers(),this.delayedSeekTarget=e.time;var a=function(i){n.clearDelayedSeekHandlers();var o={time:(0,A.isNumber)(i)?i:e.time,issuer:e.issuer,force:e.force};n.verifyHLSPlaybackBuffer(r,e.time).catch((function(){return a(e.time+1)})),n.delayedSeekTarget=void 0,t?n.triggerSeekForLive(o):n.triggerSeek(o)};i.forEach((function(e){r.addEventListener(e,a),n.lastDelayedSeekHandlers[e]=a}))},e.prototype.setAudio=function(e){var t=b.ArrayHelper.toArray(this.video.audioTracks).find((function(t){return t.id===e}));t&&this.enableAudioTrack(t)},e.prototype.dispatchAudioChangedEvent=function(e,t){this.eventCallback(h.PlayerEvent.AudioChanged,{targetAudio:{id:t.id,label:t.label,lang:t.language},sourceAudio:e,time:this.video.currentTime})},e.prototype.enableAudioTrack=function(e){if(!e.enabled){var t=b.ArrayHelper.toArray(this.video.audioTracks).find((function(e){return e.enabled})),i=null;t&&(t.enabled=!1,i=this.convertAudioTrackNativeToApi(t)),e.enabled=!0,this.dispatchAudioChangedEvent(i,e)}},e.prototype.setVolume=function(e,t){var i=this.getElement(),n=this.currentVol,r=Math.min(e/100,1);n!==r&&(this.currentVol=r,i.volume=r,this.eventCallback(h.PlayerEvent.VolumeChanged,{targetVolume:100*r,sourceVolume:100*n,issuer:t||"api"}))},e.prototype.startManifestTimeoutTimer=function(){var e=this;clearTimeout(this.loadTimeoutID),this.hasSourceBeenLoaded||(this.loadTimeoutID=setTimeout((function(){e.loadTimeoutID=null,!e.hasSourceBeenLoaded&&e.eventHandler&&(e.type===f.StreamType.Hls?e.eventHandler.fireError(new s.PlayerError(o.ErrorCode.NETWORK_MANIFEST_DOWNLOAD_TIMEOUT,{sourceUrl:e.sourceConfig.url},"Failed to load the HLS playlist: the request timed out.")):e.type===f.StreamType.Progressive&&e.eventHandler.fireError(new s.PlayerError(o.ErrorCode.NETWORK_PROGRESSIVE_STREAM_DOWNLOAD_TIMEOUT,{sourceUrl:e.sourceConfig.url},"Failed to load the progressive source: the request timed out.")))}),(0,C.toMilliSeconds)(this.settings.XHR_TIMEOUT/2)))},e.getContentDomain=function(e){var t=document.createElement("a");return t.href=e,""===t.host&&(t.href=t.href),t.hostname},e.prototype.trackLoading=function(e){var t;(0,M.isTizen)()&&(null===(t=this.assetAvailabilityChecker)||void 0===t||t.dispose(),this.assetAvailabilityChecker=new _.AssetAvailabilityChecker(this.context,e,this.video))},e.prototype.prepareLoad=function(){return Promise.resolve()},e.prototype.load=function(t,i,n){var r=this;this.stateManager=this.context.serviceManager.get(u.ServiceName.PlayerStateService),this.stateManager.transitionToSeekedState(!1),this.video.setAttribute("preload","metadata"),this.type=this.context.sourceContext.technology.streaming,this.sourceConfig=t,t.type&&(this.type=t.type),!0===this.loaded&&this.unload().catch((function(){})),this.hasSourceBeenLoaded=!1,this.hasDataBeenLoaded=!1,this.getElement().nativeMediaEventListener.reset(),this.isInitialSeek=!0,this.ignoreNextVideoError=!1,this.sourceLoadRetryCount=0,this.delayedSeekTarget=void 0,this.isSourceBeingRedirected=!1,t.hasOwnProperty("vr")&&t.vr&&Boolean(t.vr.restrictedInlinePlayback)?(this.restrictedInlinePlayback=!0,this.audio=D.Util.getVRAudioElement(),this.addEventListeners(this.audio)):(this.audio=null,this.restrictedInlinePlayback=!1,this.addEventListeners(this.video)),this.eventHandler.on(h.PlayerEvent.TimeChanged,this.onTimeChanged),this.unsubscribeFromStoreStoppedListener=(0,E.subscribe)(this.context.store)((function(e){return(0,T.getIsStopped)((0,T.getPlayerState)(e))}),this.onContentPlaybackFinished,(function(e){return!0===e})),this.settings.GLOBAL_DISABLE_SEEKING=i||!1,this.seekingTimeoutId>-1&&(clearTimeout(this.seekingTimeoutId),this.seekingTimeoutId=-1);var a="";t&&t.url&&("string"==typeof t.url?a=t.url:t.url.length>0&&(a=t.url[0].url));var o=window.location.hostname===e.getContentDomain(a);t.hasOwnProperty("vr")&&Boolean(t.vr)&&!o&&(this.originalCrossOriginAttributeValue=this.video.getAttribute("crossOrigin"),this.video.setAttribute("crossOrigin","anonymous")),this.video.eventHandler.reset(),this.isProgressiveQualityChange||(this.textTrackController=this.createTextTrackController()),this.loadingDeferred=new k.Deferred;var s=this.loadSource(t,n);return this.loadingDeferred.promise.then((function(){r.context.sourceContext.source=t.config})).then((function(){return s})).then((function(){return r.initializeLoadedState(t)}))},e.prototype.maybeHandleRedirectedSource=function(e){var t=this;(0,M.isSafari17orIOS17)()&&fetch(e.url,{method:O.HttpRequestMethod.GET,headers:{Range:"bytes=0-0"}}).then((function(e){e.redirected&&(t.logger.debug("Detected source redirection."),t.hasDataBeenLoaded&&t.loadingDeferred.resolve(),t.isSourceBeingRedirected=!0)})).catch((function(){}))},e.prototype.loadSource=function(e,t){var i=this;return this.type===f.StreamType.Hls?(this.maybeHandleRedirectedSource(e),this.loadHls(e)):this.type===f.StreamType.Progressive?this.loadProgressive(e,t).then((function(){return i.loadVideoElement()})):Promise.resolve()},e.prototype.initializeLoadedState=function(e){this.currentSource=e,this.loaded=!0,this.lastLegalPlaybackPosition=0,this.ignoreNextDelta=!1},e.prototype.loadVideoElement=function(){this.config.tweaks.prevent_video_element_preloading||this.video.load()},e.prototype.setupVideoElement=function(t,i){var n=this;if(t.config.drm&&t.config.drm.fairplay){var r=R.ModuleManager.get(H.ModuleName.DRM).FairplayHandler;this.fpsHandler=new r(this.context,this.video,t.config.drm.fairplay)}var a=e.createSourceElement(i,null);this.appendAndSetVideoStream(a,"0",t),this.trackLoading(a);var s={element:a,handler:function(e){return n.handleStreamErrorEvent(o.ErrorCode.SOURCE_HLS_STREAM_ERROR,a.src,e)}};this.sourceElementEventListeners.push(s),s.element.addEventListener(w.MediaElementEvent.error,s.handler)},e.prototype.setSourceAndLoadVideoElement=function(e,t){this.setupVideoElement(e,t),this.loadVideoElement()},e.prototype.setVideoForServiceWorker=function(e,t){var i=this,n=L.URLHelper.appendQueryParametersToUrl(t,{"bitmovin-player-id":this.context.internalPlayerID});return this.setSourceAndLoadVideoElement(e,n),this.hlsManifestController.resolveMasterManifest(e.url).then((function(){})).catch((function(e){i.logger.debug("Couldn't access master playlist with ServiceWorker: ".concat(e))}))},e.prototype.setVideoForNativeHLSParsing=function(e,t){var i=this;return U.createHlsManifestController(this.context).then((function(n){return i.hlsManifestController=n,i.settings.NATIVE_HLS_DOWNLOAD_ERROR_HANDLING&&(i.downloadErrorHandler=new x.HlsDownloadErrorHandler(i.context,n)),U.shouldUseServiceWorker(i.context)?i.setVideoForServiceWorker(e,t):i.hlsManifestController.resolveMasterManifest(e.url).then((function(t){0!==t.indexOf("data")&&0!==t.indexOf("blob")||i.setSourceAndLoadVideoElement(e,t)})).catch((function(e){i.logger.debug("Couldn't access master playlist for native HLS parsing: ".concat(e))}))}))},e.prototype.loadHls=function(e){var t;if("string"!=typeof e.url){var i=new s.PlayerError(o.ErrorCode.SOURCE_INVALID,{given:e.url},"Cannot load the HLS playlist: the source URL should be a string.");return null===(t=this.eventHandler)||void 0===t||t.fireError(i),this.loadingDeferred.reject(i),Promise.resolve()}var n=this.adjustToFullUrlWithQueryParams(e.url);return this.settings.NATIVE_HLS_PARSING?this.setVideoForNativeHLSParsing(e,n):(this.setSourceAndLoadVideoElement(e,n),Promise.resolve())},e.prototype.loadProgressive=function(e,t){if(this.availableVideoStreams=[],Array.isArray(e.url))this.loadProgressiveMultiSource(e.url,t);else{var i={url:e.url,bitrate:0};this.video.src=this.adjustToFullUrlWithQueryParams(i.url),this.addStreamToAvailableOnes("0",i),this.setCurrentVideoStream("0",i),this.trackLoading(this.video.getWrappedElement())}return Promise.resolve()},e.prototype.loadProgressiveMultiSource=function(e,t){var i=this,n=!1;t=t||z(e);var r=this.loadAllSupportedProgressiveSources(e,t,!n);if(r){var a={element:r,handler:function(e){return i.handleStreamErrorEvent(o.ErrorCode.SOURCE_PROGRESSIVE_STREAM_ERROR,r.src,e)}};this.sourceElementEventListeners.push(a),a.element.addEventListener(w.MediaElementEvent.error,a.handler),this.restrictedInlinePlayback&&this.audio&&this.audio.load(),this.trackLoading(r)}},e.prototype.handleStreamErrorEvent=function(e,t,i){var n,r=this;clearTimeout(this.loadTimeoutID),null===(n=this.assetAvailabilityChecker)||void 0===n||n.dispose(),this.loaded=!0,this.logger.debug("Caught a stream error event (".concat(e,", ").concat(t,")"),i),new p.DefaultContentLoader(this.context).load(t).then((function(e){return{status:e.status,statusText:e.statusText,error:void 0}})).catch((function(e){return{error:e,status:(null==e?void 0:e.status)||0,statusText:(null==e?void 0:e.statusText)||""}})).then((function(n){var a,o=n.error,l=n.status,d=n.statusText,u=(null==i?void 0:i.message)||i,c=function(e,t){r.loadingDeferred.reject(new s.PlayerError(e,t,"The video element has thrown an error while loading the stream."))};l!==200?(a={event:i,error:o,status:l,statusText:d,src:t,extraMessage:u},r.logger.debug("last/only source has thrown an error ".concat(l," - ").concat(d),a),c(e,a)):(a={event:i,error:o,src:t,extraMessage:u},r.logger.debug("last/only source has thrown an error, but we are able to download the asset during the error handling.",a),r.sourceConfig&&r.sourceLoadRetryCount<r.settings.MAX_RETRIES?(r.sourceLoadRetryCount++,r.logger.debug("Reloading the source: attempt ".concat(r.sourceLoadRetryCount," of ").concat(r.settings.MAX_RETRIES,".")),r.loadSource(r.sourceConfig).catch((function(){return c(e,a)}))):c(e,a))}))},e.prototype.loadAllSupportedProgressiveSources=function(t,i,n){for(var r,a=[],o=-1,s=null,l=null,d=0;d<t.length;d++){var u=t[d];if(!this.shouldIgnoreSourceObject(u,l,n)){if(r=e.createSourceElement(this.adjustToFullUrlWithQueryParams(u.url),u.type),l=l||u.type,this.addStreamToAvailableOnes(String(d),u),n){if(a.indexOf(u.type)>-1){this.logger.debug("Stream of mime type ".concat(u.type," already added.. continuing"));continue}i===String(d)?(s=r,o=u.bitrate,this.appendAndSetVideoStream(r,String(d),u),a.push(u.type)):o===u.bitrate&&(s=r,this.appendVideoStream(r),a.push(u.type))}else s=r,this.appendAndSetVideoStream(r,String(d),u);this.restrictedInlinePlayback&&this.audio&&(this.audio.innerHTML=this.video.innerHTML)}}return s},e.prototype.shouldIgnoreSourceObject=function(t,i,n){return!e.isValidProgressiveSourceObject(t)||!!n&&(!this.isMimeTypeSupported(t.type)||!!i&&i!==t.type)},e.isValidProgressiveSourceObject=function(e){return"object"==typeof e&&(e&&e.url&&""!==e.url)},e.prototype.appendAndSetVideoStream=function(e,t,i){this.video.appendChild(e),this.setCurrentVideoStream(t,i)},e.prototype.appendVideoStream=function(e){this.video.appendChild(e)},e.prototype.addStreamToAvailableOnes=function(e,t){var i={id:e};t.bitrate&&(i.bitrate=t.bitrate),i.label=String(t.label||t.bitrate||"unknown"),this.availableVideoStreams.push(i)},e.prototype.setCurrentVideoStream=function(e,t){this.currentVideoStream={id:e},t.bitrate&&(this.currentVideoStream.bitrate=t.bitrate),this.currentVideoStream.label=""+(t.label||t.bitrate||"unknown")},e.prototype.isMimeTypeSupported=function(e){return!(this.video.canPlayType(e).length<=0)||(this.logger.debug("Mime type"+e+" not supported.. continuing"),!1)},e.createSourceElement=function(e,t){var i=document.createElement("source");return i.src=e,t&&(t.indexOf("/")<0&&(t="video/"+t),i.type=t),i},e.prototype.unload=function(e,t){var i,n;return void 0===e&&(e="api"),void 0===t&&(t=!1),this.hlsLiveTimeTracker.onUnload(e),this.clearDelayedSeekHandlers(),this.loadingDeferred&&!t&&this.loadingDeferred.reject(),clearTimeout(this.loadedDataFallbackTimeoutID),clearTimeout(this.loadTimeoutID),null===(i=this.assetAvailabilityChecker)||void 0===i||i.dispose(),this.ended=!1,this.fpsHandler=(0,c.dispose)(this.fpsHandler),this.downloadErrorHandler=(0,c.dispose)(this.downloadErrorHandler),this.hlsManifestController=(0,c.dispose)(this.hlsManifestController),this.hlsSeekingGlitchEventHandlers.forEach((function(e){return e.el.removeEventListener(e.ev,e.fn)})),this.hlsSeekingGlitchEventHandlers=[],this.isProgressiveQualityChange||(null===(n=this.textTrackController)||void 0===n||n.signalSourceChange(),this.textTrackController=(0,c.dispose)(this.textTrackController)),this.clearVideoElement(),this.clearAudioElement(),this.pause(),this.ignoreNextVideoError=!0,this.clearEventListeners(),this.unsubscribeFromStoreStoppedListener&&this.unsubscribeFromStoreStoppedListener(),this.allowedToThrowEvents&&(this.currentVideoStream=null),this.loaded&&(this.loaded=!1,t||(this.eventCallback(h.PlayerEvent.SourceUnloaded,{oldSource:this.currentSource}),this.lastDuration=null)),Promise.resolve()},e.prototype.clearAudioElement=function(){if(this.restrictedInlinePlayback&&this.audio){for(this.audio.removeAttribute("src");this.audio.firstChild;)this.audio.removeChild(this.audio.firstChild);this.audio.load()}},e.prototype.clearEventListeners=function(){var e=this;this.restrictedInlinePlayback?this.audio&&this.removeEventListeners(this.audio):this.removeEventListeners(this.video),this.clearDelayedSeekHandlers(),this.eventHandler.off(h.PlayerEvent.TimeChanged,this.onTimeChanged),this.eventHandler.off(h.PlayerEvent.TimeChanged,this.delayedSeek),this.sourceElementEventListeners.forEach((function(t){try{t.element.removeEventListener(w.MediaElementEvent.error,t.handler)}catch(t){e.logger.debug("Error while removing event listener",t)}}))},e.prototype.clearVideoElement=function(){I.VideoElementUtil.removeSource(this.video),null!=this.originalCrossOriginAttributeValue?this.video.setAttribute("crossOrigin",this.originalCrossOriginAttributeValue):this.video.removeAttribute("crossOrigin"),this.video.load()},e.prototype.isReady=function(){return this.restrictedInlinePlayback&&this.audio&&"readyState"in this.audio?this.audio.readyState!==HTMLMediaElement.HAVE_NOTHING:!("readyState"in this.video)||this.video.readyState!==HTMLMediaElement.HAVE_NOTHING},e.prototype.isLive=function(){return this.getElement().duration===1/0},e.prototype.isPlaying=function(){return this.stateManager.isPlaying()},e.prototype.isPaused=function(){return this.stateManager.isPaused()},e.prototype.hasEnded=function(){return this.ended},e.prototype.isMuted=function(){return this.getElement().muted},e.prototype.isStalled=function(){return this.stateManager.isStalled},e.prototype.getVolume=function(){return 100*this.getElement().volume},e.prototype.getDuration=function(){return this.getElement().duration},e.prototype.convertAudioTrackNativeToApi=function(e){var t={id:e.id||String(b.ArrayHelper.toArray(this.video.audioTracks).indexOf(e)),label:e.label,lang:e.language,getQualities:null},i=this.getLabelForAudio(t);return i&&"string"==typeof i&&(t.label=i),t},e.prototype.getAudio=function(){var e=b.ArrayHelper.toArray(this.video.audioTracks).find((function(e){return e.enabled}));return e?this.convertAudioTrackNativeToApi(e):null},e.prototype.getAvailableAudio=function(){var e=this;return b.ArrayHelper.toArray(this.video.audioTracks).map((function(t){return e.convertAudioTrackNativeToApi(t)}))},e.prototype.getLabelForAudio=function(e){var t={id:e.id,lang:e.lang,mimeType:P.MimeType.AudioMp4};return this.getLabelingFunctionForAudio()(t)},e.prototype.getLabelingFunctionForAudio=function(){var t=this.context.sourceContext,i=t&&t.source&&t.source.labeling;return i&&i[this.type]&&i[this.type].tracks?"function"!=typeof i[this.type].tracks?e.getDefaultLabelForAudio:i[this.type].tracks:e.getDefaultLabelForAudio},e.getDefaultLabelForAudio=function(){return null},e.prototype.getDownloadedVideoData=function(){return this.type===f.StreamType.Progressive?this.currentVideoStream:this.hlsManifestController?this.hlsManifestController.getDownloadedVideoData():{id:Q,bitrate:0,height:0,width:0,isAuto:!0}},e.prototype.getDownloadedAudioData=function(){return{id:Q,bitrate:0,isAuto:this.type===f.StreamType.Progressive}},e.prototype.createVideoQualityObject=function(e){var t={};return t.id=e.id,t.label=e.label,e.width>1?t.width=e.width:this.currentVideoStream&&this.currentVideoStream.id===e.id&&(this.currentVideoStream.width>1?t.width=this.currentVideoStream.width:!isNaN(this.video.videoWidth)&&this.video.videoWidth>1&&(t.width=this.video.videoWidth)),e.height>1?t.height=e.height:this.currentVideoStream&&this.currentVideoStream.id===e.id&&(this.currentVideoStream.height>1?t.height=this.currentVideoStream.height:!isNaN(this.video.videoHeight)&&this.video.videoHeight>1&&(t.height=this.video.videoHeight)),void 0!==e.bitrate&&"unknown"!==e.bitrate&&(t.bitrate=e.bitrate),t},e.prototype.getPlaybackVideoData=function(){if(this.hlsManifestController){var e=this.hlsManifestController.getPlayingVideoData();if(e)return e}if(this.currentVideoStream){var t=this.createVideoQualityObject(this.currentVideoStream);return"progressive"!==this.type&&(t.id=Q),t}return{id:Q,bitrate:0,height:this.video.videoHeight,width:this.video.videoWidth}},e.prototype.getPlaybackAudioData=function(){return{id:Q,bitrate:0}},e.prototype.getMaxTimeShift=function(){if(!this.isTimeShiftingAllowed())return 0;var e=this.getSeekableRangeInternal();return this.isHlsLive()&&e.start>=0?e.start-e.end:0},e.prototype.isHlsLive=function(){return this.type===f.StreamType.Hls&&this.isLive()},e.prototype.timeShift=function(e,t){if(this.isTimeShiftingAllowed()&&this.isTimeShiftNecessary(e,t))if(0===e){var i=this.getSeekableRangeInternal();i.end>=0&&this.seekForLive(i.end)}else this.seekForLive(e,t)},e.prototype.isTimeShiftNecessary=function(e,t){if(t===g.STARTUP_ISSUER_NAME&&!J(this.context.sourceContext.source.options))return!1;var i=Math.abs(Math.abs(e)-Math.abs(this.getTimeShift())),n=this.isOffsetConsideredAsLiveEdge(e);return i>B&&!(this.isAtLiveEdge&&n)},e.prototype.getTimeShift=function(){if(this.shouldReturnDefaultTimeshiftValue())return K;var e=this.getSeekableRangeInternal();if(e.start>=0&&this.needToUpdateTimeshiftValue()){var t=$(this.getElement().currentTime-e.end,this.getTimeshiftRange());this.isAtLiveEdge=this.isOffsetConsideredAsLiveEdge(t)&&!this.isStalledOrPaused(),this.currentTimeShift=this.isAtLiveEdge?K:t}return isFinite(this.currentTimeShift)?this.currentTimeShift:K},e.prototype.shouldReturnDefaultTimeshiftValue=function(){return!this.isLive()||!this.stateManager.hasBeenPlaying&&!this.stateManager.seekedOrTimeshifted||this.isAtLiveEdge},e.prototype.isStalledOrPaused=function(){return this.isStalled()||this.isPaused()},e.prototype.needToUpdateTimeshiftValue=function(){return!isFinite(this.currentTimeShift)||this.isStalledOrPaused()},e.prototype.getManifest=function(){return this.hlsManifestController?this.hlsManifestController.getManifest():null},e.prototype.getSnapshot=function(e,t){if(this.isPlaying()||this.isPaused()){var i=this.video.videoWidth,n=this.video.videoHeight;if(this.snapshotCanvas||(this.snapshotCanvas=document.createElement("canvas"),this.snapshotCanvas.id="snapshotHiddenCanvas"),!this.snapshotCanvas||!this.snapshotCanvas.getContext||!this.snapshotCanvas.getContext("2d"))return;this.snapshotCanvas.height=n,this.snapshotCanvas.width=i,this.snapshotCanvasContext=this.snapshotCanvas.getContext("2d"),this.snapshotCanvasContext.drawImage(this.video.getWrappedElement(),0,0,i,n);var r=void 0;try{"image/jpeg"===e||"image/webp"===e?(Number(t)===t?(t<0||t>1)&&(t=1):t=1,r=this.snapshotCanvas.toDataURL(e,t)):r=this.snapshotCanvas.toDataURL(e,t)}catch(e){return void(e&&e.message?this.logger.debug("Snapshot acquisition failed: "+e.message):this.logger.debug("Snapshot acquisition failed"))}return{height:n,width:i,data:r}}},e.prototype.setPlaybackSpeed=function(e){this.getElement().playbackRate=e},e.prototype.getPlaybackSpeed=function(){return this.getElement().playbackRate},e.prototype.permitControl=function(e){this.controlPermitted=e},e.prototype.resetTimeShift=function(){this.currentTimeShift=-1/0,this.isTimeShiftingAllowed()&&(this.isAtLiveEdge=!1)},e.prototype.isOffsetConsideredAsLiveEdge=function(e){return Math.abs(e)<2*this.settings.SEEK_TO_END_OFFSET},e.prototype.isSeekTargetChangedDuringStartup=function(){return this.seekTargetTime!==this.lastVerifiedPlaybackPosition&&this.lastSeekIssuer===g.STARTUP_ISSUER_NAME},e.prototype.maybeHandleSeekTargetChangeDuringStartup=function(){this.isSeekTargetChangedDuringStartup()&&(this.context.logger.debug("Seek target changed during startup..."),this.triggerSeekEvent(this.seekTargetTime),this.lastSeekIssuer="api",this.seekTargetTime=-1)},e.prototype.finishedSeekedTransitionState=function(){this.stateManager.isSeeking()&&(this.stateManager.transitionToSeekedState(this.lastSeekIssuer!==g.STARTUP_ISSUER_NAME),this.lastSeekIssuer="api"),this.stateManager.isTimeShifting()&&this.stateManager.transitionToTimeShiftedState(this.lastSeekIssuer!==g.STARTUP_ISSUER_NAME)},e.prototype.addEventListeners=function(e){for(var t=0;t<this.elementEvents.length;t++){var i=this.elementEvents[t].elementSelector?this.elementEvents[t].elementSelector(e):e;i&&i.addEventListener(this.elementEvents[t].event,this.elementEvents[t].handler)}},e.prototype.removeEventListeners=function(e){for(var t=0;t<this.elementEvents.length;t++){var i=this.elementEvents[t].elementSelector?this.elementEvents[t].elementSelector(e):e;i&&i.removeEventListener&&i.removeEventListener(this.elementEvents[t].event,this.elementEvents[t].handler)}},e.prototype.getBufferLevel=function(e){var t={level:null,targetLevel:null,type:e,media:f.MediaType.Video},i=this.getElement();if(!i.buffered||i.buffered.length<1)return t;var n=function(n){for(var r=0;r<i.buffered.length;r++){var a=i.buffered.start(r),o=i.buffered.end(r);a>n||o<=n||(t.level=o-n,e===f.BufferType.BackwardDuration&&(t.level=n-a))}},r=i.currentTime;if(n(r),null==t.level){var a=Y(i,r),o=a.distanceToCurrentTime,s=a.closestBufferStart;o<1&&(this.context.logger.debug("Adjusting buffer level to closest buffer range ",s),n(s))}return t},e.prototype.createTextTrackController=function(){if(R.ModuleManager.has(H.ModuleName.SubtitlesNative))return new(0,R.ModuleManager.get(H.ModuleName.SubtitlesNative).TextTrackController)(this.context,this.video,this.config,this.type);this.logger.log("Unable to create native SubtitleController because the ".concat(H.ModuleName.SubtitlesNative," module is missing"))},e.prototype.removeSubtitle=function(e){e&&this.textTrackController&&this.textTrackController.removeSubtitle(e)},e.prototype.addSubtitle=function(e){return this.textTrackController?(this.removeSubtitle(e.id),e.kind="subtitles",this.textTrackController.addSubtitle(e)):Promise.reject()},e.prototype.listSubtitles=function(){return this.textTrackController?this.textTrackController.getAvailableSubtitles():[]},e.prototype.enableSubtitle=function(e){return this.textTrackController?this.textTrackController.enableSubtitle(e):Promise.resolve(!1)},e.prototype.disableSubtitle=function(e){return this.textTrackController?Promise.resolve(this.textTrackController.disableSubtitle(e)):Promise.resolve(!1)},e.prototype.adjustToFullUrlWithQueryParams=function(e){var t=L.URLHelper.toFullUrl(e);return L.URLHelper.appendQueryParametersToUrl(t,this.settings.QUERY_PARAMETERS)},e.prototype.onFullscreenEnter=function(){this.isInNativeFullscreen=this.video.webkitDisplayingFullscreen,this.textTrackController&&this.isInNativeFullscreen&&this.textTrackController.enableNative()},e.prototype.onFullscreenExit=function(){this.textTrackController&&this.isInNativeFullscreen&&this.textTrackController.disableNative(),this.isInNativeFullscreen=!1},e.prototype.getAvailableVideoQualities=function(){var e=this;return"progressive"!==this.type?[]:this.availableVideoStreams.map((function(t){return e.createVideoQualityObject(t)}))},e.prototype.getBufferedRanges=function(){var e=this;return(0,a.getBufferedRanges)(this.getElement()).map((function(t){return(0,a.convertRangeToTimeMode)(t,e.getStartDate(),f.TimeMode.AbsoluteTime)}))},e.prototype.setVideoQuality=function(e){var t,i=this,r=this.getPlaybackSpeed(),a=this.availableVideoStreams.find((function(t){return t.id===e}));if("progressive"===this.type&&a&&(!this.currentVideoStream||this.currentVideoStream.id!==e)){this.isProgressiveQualityChange=!0,this.currentTimeWas=this.getCurrentTime(),this.availableVideoStreams=[],this.allowedToThrowEvents=!1;var o=this.stateManager.isPlaying()||this.stateManager.isPlay();this.stateManager.stallEnter(),this.unload("api",!0).catch((function(){}));var s=this.createVideoQualityObject(a),u={sourceQuality:this.currentVideoStream,sourceQualityId:this.currentVideoStream?this.currentVideoStream.id:null,targetQuality:s,targetQualityId:s.id};this.eventHandler.one(h.PlayerEvent.StallEnded,(function(){i.allowedToThrowEvents=!0,(0,S.getCapabilities)().isIOS||(0,S.getCapabilities)().isSafari?(i.numTimeChanged=0,i.eventHandler.on(h.PlayerEvent.TimeChanged,i.delayedSeek)):(i.numTimeChanged=1/0,i.delayedSeek()),o&&(i.play(g.INTERNAL_ISSUER_NAME).catch((function(){i.eventCallback(h.PlayerEvent.Warning,new l.PlayerWarning(d.WarningCode.PLAYBACK_COULD_NOT_BE_STARTED))})),i.stateManager.transitionToPlayingState(g.INTERNAL_ISSUER_NAME,!1))})),this.eventCallback(h.PlayerEvent.VideoQualityChanged,n({},u),!0),null===(t=this.airplayService)||void 0===t||t.initAirplay(),this.load(this.sourceConfig,this.settings.GLOBAL_DISABLE_SEEKING,e).then((function(){i.setPlaybackSpeed(r),i.eventCallback(h.PlayerEvent.VideoDownloadQualityChange,n({},u),!0),i.eventCallback(h.PlayerEvent.VideoDownloadQualityChanged,n({},u),!0),i.eventCallback(h.PlayerEvent.VideoPlaybackQualityChanged,{sourceQuality:u.sourceQuality,targetQuality:u.targetQuality},!0)}))}},e.prototype.isAirplayAvailable=function(){var e,t;return null!==(t=null===(e=this.airplayService)||void 0===e?void 0:e.isAirplayAvailable())&&void 0!==t&&t},e.prototype.isAirplayActive=function(){var e,t;return null!==(t=null===(e=this.airplayService)||void 0===e?void 0:e.isAirplayActive())&&void 0!==t&&t},e.prototype.showAirplayTargetPicker=function(){var e;null===(e=this.airplayService)||void 0===e||e.showAirplayTargetPicker()},e.prototype.getTimeshiftRange=function(){return this.isLive()?{start:this.getMaxTimeShift(),end:0}:{start:-1,end:-1}},e.prototype.getSeekableRangeInternal=function(){var e=this.video.seekable;if(e.length>0){var t={start:e.start(0),end:e.end(0)};return t.start=Math.max(0,t.start),t}return{start:-1,end:-1}},e.prototype.getSeekableRange=function(){if(!this.isLive()||this.settings.ENABLE_SEEK_FOR_LIVE){var e=this.getStartDate(),t=this.getSeekableRangeInternal();return e?(0,a.convertRangeToTimeMode)(t,e,f.TimeMode.AbsoluteTime):t}return{start:-1,end:-1}},e.prototype.createManifestApi=function(){return this.hlsManifestController?F.NativePlayerManifestApiFactory.create(this.hlsManifestController):null},e.prototype.release=function(){var e,t=this,i=this.unload("api").catch((function(){}));return clearTimeout(this.loadTimeoutID),i=i.then((function(){t.type=null,t.config=null,t.eventHandler=null,t.settings=null,t.currentTimeWas=null,t.snapshotCanvas=null,t.snapshotCanvasContext=null,t.audio=null,t.video=null,t.lastPlayIssuer=null,t.lastPauseIssuer=null,t.lastSeekIssuer=null,t.currentSource=null,t.sourceConfig=null,t.currentVideoStream=null,t.availableVideoStreams=null,t.sourceElementEventListeners=null,t.delayedSeekTarget=void 0})),null===(e=this.airplayService)||void 0===e||e.dispose(),this.textTrackController&&this.textTrackController.dispose(),this.textTrackController=null,this.fpsHandler&&this.fpsHandler.dispose(),this.fpsHandler=null,this.downloadErrorHandler&&this.downloadErrorHandler.dispose(),this.downloadErrorHandler=null,this.hlsManifestController&&this.hlsManifestController.dispose(),this.hlsManifestController=null,i},e.prototype.setQueryParameters=function(e){this.settings.QUERY_PARAMETERS=e},e.prototype.clearQueryParameters=function(){this.settings.QUERY_PARAMETERS=void 0},e.prototype.setAudioQuality=function(e){},e.prototype.setTargetBufferLevel=function(){},e.prototype.getAvailableAudioQualities=function(){return[]},e.prototype.getDroppedVideoFrames=function(){return 0},e.prototype.getTotalStalledTime=function(){return 0},e.prototype.updateCallback=function(e){},e.prototype.getAvailableSegments=function(){return{}},e.prototype.getLatency=function(){var e=this.video.getWrappedElement();if(!e.getStartDate)return 1/0;var t=e.getStartDate().getTime();if(isNaN(t))return 1/0;var i=(new Date).getTime();return(0,C.toSeconds)(i-t)-this.video.currentTime},e.prototype.setTargetLatency=function(e){},e.prototype.getTargetLatency=function(){return null},e.prototype.setCatchupConfig=function(e){},e.prototype.getCatchupConfig=function(){return null},e.prototype.setFallbackConfig=function(e){},e.prototype.getFallbackConfig=function(){return null},r([(0,m.trackPerformance)("NativePlayer.load",!0)],e.prototype,"load",null),e}();function Y(e,t){for(var i=e.buffered.start(0),n=Math.abs(i-t),r=1;r<e.buffered.length;r++){var a=Math.abs(e.buffered.start(r)-t);a<n&&(n=a,i=e.buffered.start(r))}return{distanceToCurrentTime:n,closestBufferStart:i}}function J(e){return null!=(null==e?void 0:e.startTime)||null!=(null==e?void 0:e.startOffset)}function z(e){var t=e.findIndex((function(e){return!0===e.preferred}));return t>-1?String(t):"0"}function $(e,t){return(e<t.start||e>t.end)&&(e=Math.max(e,t.start),e=Math.min(e,t.end)),e}function Z(e){return e.length>0&&e.end(e.length-1)!==1/0}t.NativePlayer=X},76250:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.replaceManifestUrls=a,t.parseUrlFromLine=o,t.replaceUrlAndAddUriParams=s;var n=i(76885),r=/URI="([^"]+)"/;function a(e,t,i){var n=t.split(/\r?\n/);return n.forEach((function(t,a){var l=o(t);if(l&&t.match(r)){var d=s(e,l,i);n[a]=t.replace(r,(function(){return'URI="'.concat(d,'"')}))}else l&&(n[a]=s(e,l,i))})),n.join("\n")}function o(e){if("#"!==e[0]&&e.trim().length>0)return e;if(e.indexOf("URI")>-1){var t=e.match(r);if(t&&t[1].indexOf("skd://")<0&&t[1].indexOf("skds://")<0)return t[1]}return null}function s(e,t,i){var r=n.URLHelper.concatBaseUrlWithPartial(e,t);return n.URLHelper.appendQueryParametersToUrl(r,i)}},77117:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractHlsManifestController=void 0;var n,r,a=i(18665),o=i(62510),s=i(63331),l=i(54861),d=i(489),u=i(3941),c=i(331),h=i(76885),v=i(54838),g=i(91520),p=i(16368);!function(e){e.CueTag="cueTag",e.Scte35="scte35",e.DateRange="dateRange",e.CustomTags="customTags"}(n||(n={}));var m="#EXTM3U\n#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=100000\n$VARIANT$\n",f=function(){function e(e){var t,i=this;this.context=e,this.discontinuitySequences=[],this.onManifestMetadata=function(e){if(e.hasOwnProperty("metadata")){var t={metadataType:e.metadataType.toUpperCase(),metadata:e.metadata};(e.start||0===e.start)&&(t.start=e.start),e.end&&(t.end=e.end),i.context.eventHandler.dispatchEvent(o.PlayerEvent.Metadata,t)}},this.onTimeChangedHandler=function(e){var t=i.segmentTimelineController.getPlayingSegment(e.relativeTime||e.time);t&&!t.consumed&&(t.consumed=!0,i.triggerMetadata(t),i.triggerSegmentPlayback(t))},this.onTimeShiftedHandler=function(){i.segmentTimelineController.reset()},this.onSeeked=function(){i.segmentTimelineController.reset()},this.registerVariantPlaylist=function(e){var t,n,a=i.parsePlaylist(e.body),o=null===(n=null===(t=i.parsedManifest)||void 0===t?void 0:t.playlists)||void 0===n?void 0:n.find((function(t){var i=h.URLHelper.removeDotSegments(t.uri);return h.URLHelper.removeDotSegments(e.url).includes(i)}));if(i.isValidVariantPlaylist(a)&&o){var l=function(e){var t,n;return null!==(n=null===(t=i.parsedManifest.media)||void 0===t?void 0:t.some((function(t){var i;return null===(i=t.attributes.URI)||void 0===i?void 0:i.includes(e)})))&&void 0!==n&&n},d=function(e){var t,i;return null!==(i=null===(t=e.playlists)||void 0===t?void 0:t.every((function(e){return l(e.uri)})))&&void 0!==i&&i},u=function(e){var t,n;return null!==(n=null===(t=i.parsedManifest.playlists)||void 0===t?void 0:t.some((function(t){return e.uri.includes(t.uri)&&!l(e.uri)})))&&void 0!==n&&n};if(u(o)||d(i.parsedManifest)){i.segmentTimelineController.switchTimeline(o.id),r.extrapolateProgramDateTime(a.segments),i.discontinuitySequences=r.createDiscontinuitySequences(a,i.discontinuitySequences);var c=S(a,o.id,i.config);i.metadataParsedService.expirationTimeInSeconds=i.segmentTimelineController.updateTimeline(o.id,c);var g=i.segmentTimelineController.getCurrentTimeline();if(Boolean(c[0].dateTime)){var p=v.Util.timeInSeconds(c[0].dateTime);i.metadataService.removeUntil(p)}(0,s.extractDateRangeMetadata)(a.dateRange,a.endList?a.programDateTime:void 0,(function(e){i.addDateRangeMetadata(e)}));var m=!(null==a?void 0:a.endList);return i.extractMetadataFromTimeline(g,m),a}}},r=g.ModuleManager.get(p.ModuleName.HLS).PlaylistUtils,this.settings=e.settings,this.config=e.config,this.eventHandler=e.eventHandler,this.originalManifest=null,this.metadataParsedService=e.serviceManager.get(a.ServiceName.MetadataParsedService),this.eventHandler.on(o.PlayerEvent.TimeChanged,this.onTimeChangedHandler),this.eventHandler.on(o.PlayerEvent.TimeShifted,this.onTimeShiftedHandler),this.eventHandler.on(o.PlayerEvent.Seeked,this.onSeeked),this.metadataService=new l.MetadataService(this.context,((t={})[d.TimedMetadataType.Manifest]=this.onManifestMetadata,t[d.TimedMetadataType.DateRange]=this.onManifestMetadata,t))}return e.prototype.triggerSegmentPlayback=function(e){var t={url:e.uri,uid:null,mimeType:"video/mp4",playbackTime:e.playbackTime,duration:e.duration,mediaInfo:{},dateTime:null};e.dateTime&&(t.dateTime=e.dateTime.toISOString()),e.customTags&&e.customTags.length>0&&(t.EXPERIMENTAL=t.EXPERIMENTAL||{},t.EXPERIMENTAL.hlsAttributes=e.customTags.map((function(e){return e.attributes}))),this.context.eventHandler.dispatchEvent(o.PlayerEvent.SegmentPlayback,t)},e.prototype.triggerMetadata=function(e){var t=this;e.metadata&&e.metadata.forEach((function(e){T(e)?t.context.eventHandler.dispatchEvent(o.PlayerEvent.Metadata,{metadataType:o.MetadataType.CUETAG,metadata:e}):t.context.eventHandler.dispatchEvent(o.PlayerEvent.Metadata,{metadataType:e.type,metadata:e})}))},e.prototype.resolveMasterManifest=function(e){return this.parsedManifest?Promise.resolve(this.manifestUrl):(this.manifestUrl=e,this.downloadMasterManifest(e))},e.prototype.registerMasterManifest=function(e,t){var i;this.originalManifest=e.body;var n=this.processMasterManifest(null!==(i=e.body)&&void 0!==i?i:"",t);if(this.parsedManifest=this.parsePlaylist(n),this.isValidMasterPlaylist(this.parsedManifest))return n;if(this.isValidVariantPlaylist(this.parsedManifest)){var r=m.replace("$VARIANT$",t);n=this.processMasterManifest(r,t),this.originalManifest=r,this.parsedManifest=this.parsePlaylist(n)}else this.parsedManifest=null;return n},e.prototype.isValidMasterPlaylist=function(e){return null!=e&&"playlists"in e&&e.playlists.length>0},e.prototype.isValidVariantPlaylist=function(e){return null!=e&&"segments"in e&&e.segments.length>0},e.prototype.parsePlaylist=function(e){var t=g.ModuleManager.get(p.ModuleName.HLS).parsePlaylist;try{return t(e)}catch(e){return null}},e.prototype.addDateRangeMetadata=function(e){var t={metadataType:o.MetadataType.DATERANGE,metadata:e.data,start:e.startTime,end:e.endTime};this.addMetadata(t)},e.prototype.metadataToMetadataParsedEvent=function(e){return{metadataType:e.metadataType,metadata:e.metadata,data:e.metadata,start:e.start,end:e.end}},e.prototype.addMetadata=function(e){var t="DATERANGE"===e.metadataType?d.TimedMetadataType.DateRange:d.TimedMetadataType.Manifest;this.metadataService.addToMetadataParsedService(e.start,this.metadataToMetadataParsedEvent(e),u.DEFAULT_PERIOD_ID),this.metadataService.addToTimeline(t,e.start,e)},e.prototype.extractMetadataFromTimeline=function(e,t){var i=this;e.forEach((function(e){Object.values(n).forEach((function(n){e.hasOwnProperty(n)&&y(n,e,t).forEach((function(e){return i.addMetadata(e)}))}))}))},e.prototype.parseTimelineEntries=function(e){var t=this.parsePlaylist(e),i=function(e){var t;return{uri:e.uri,duration:e.duration,discontinuity:null!==(t=e.discontinuity)&&void 0!==t&&t,dateTime:null,metadata:[],consumed:!1,playbackTime:-1,keys:[]}};return this.isValidVariantPlaylist(t)?t.segments.map(i):[]},e.prototype.getPlaybackTimeOfFirstSegmentInTimeline=function(e){for(var t=0;t<e.length;t++)for(var i=e[t],n=0,r=0;r<i.timeline.length;r++){for(var a=i.timeline[r],o=this.segmentTimelineController.getCurrentTimeline(),s=0;s<o.length;s++){var l=o[s];if(l.uri===a.uri)return l.playbackTime-n}n+=a.duration}return-1},e.prototype.getParsedManifest=function(){return this.parsedManifest},e.prototype.getManifest=function(){return this.originalManifest},e.prototype.detachEventHandlers=function(){this.eventHandler&&(this.eventHandler.off(o.PlayerEvent.TimeChanged,this.onTimeChangedHandler),this.eventHandler.off(o.PlayerEvent.TimeShifted,this.onTimeShiftedHandler),this.eventHandler.off(o.PlayerEvent.Seeked,this.onSeeked))},e.prototype.dispose=function(){this.detachEventHandlers(),this.segmentTimelineController&&(this.segmentTimelineController.dispose(),this.segmentTimelineController=null),this.metadataService&&(this.metadataService.dispose(),this.metadataService=null),this.parsedManifest=null,this.originalManifest=null,this.manifestUrl=null,this.settings=null,this.config=null,this.eventHandler=null},e}();function y(e,t,i){var r=i&&t.dateTime?(0,c.toSeconds)(t.dateTime.getTime()):t.playbackTime;switch(e){case n.CueTag:return[{metadataType:o.MetadataType.CUETAG,metadata:t[n.CueTag],start:r}];case n.Scte35:return[{metadataType:o.MetadataType.SCTE,metadata:t[n.Scte35],start:r}];case n.CustomTags:return[{metadataType:o.MetadataType.CUSTOM,metadata:t[n.CustomTags],start:r,end:r+t.duration}];default:return[]}}function T(e){return e.type.includes("CUE")}function S(e,t,i){for(var n,a=e.segments,o=a.some((function(e){return void 0!==e.dateTime})),s=0;s<a.length;s++){var l=a[s];if(l.variantPlaylistId=t,l.mediaSequenceNumber=e.mediaSequence+s,!o&&(null===(n=i.tweaks)||void 0===n?void 0:n.akamai_datetime_parsing)){var d=r.getProgramDateTimeFromSegmentUrl(l.uri,l.duration);d&&(l.dateTime=d)}}return a}t.AbstractHlsManifestController=f},91229:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.createHlsManifestController=s,t.shouldUseServiceWorker=l;var n=i(91520),r=i(16368),a=i(77117),o=i(70661);function s(e){if(l(e)){var t=new(n.ModuleManager.get(r.ModuleName.ServiceWorkerClient).createServiceWorkerHlsManifestController(a.AbstractHlsManifestController))(e);return t.init().then((function(){return t})).catch((function(){return d(e)}))}return d(e)}function l(e){var t=e.config.location&&e.config.location.serviceworker;return"serviceWorker"in navigator&&t&&n.ModuleManager.has(r.ModuleName.ServiceWorkerClient)}function d(e){var t=new o.FallbackHlsManifestController(e);return t.init().then((function(){return t}))}},96926:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.TechnologyChecker=void 0;var n=i(33696),r=function(){function e(){}return e.prototype.getSupportedTechnologies=function(){return[{player:n.PlayerType.Native,streaming:n.StreamType.Progressive},{player:n.PlayerType.Native,streaming:n.StreamType.Hls},{player:n.PlayerType.Native,streaming:n.StreamType.Dash}]},e}();t.TechnologyChecker=r}},function(e){return function(t){return e(e.s=t)}(42966)}])}));
})();
