/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.smooth=e():(t.bitmovin=t.bitmovin||{},t.bitmovin.player=t.bitmovin.player||{},t.bitmovin.player.smooth=e())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[485],{16580:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.saioProcessor=o,e.saizProcessor=n,e.sencProcessor=s,e.uuidProcessor=a,e.avccProcessor=c,e.esdsProcessor=h;var r=i(81361);function o(){this._procFullBox(),1&this.flags&&(this._procField("aux_info_type","uint",32),this._procField("aux_info_type_parameter","uint",32)),this._procField("entry_count","uint",32),this._procFieldArray("offset",this.entry_count,"uint",1===this.version?64:32)}function n(){this._procFullBox(),1&this.flags&&(this._procField("aux_info_type","uint",32),this._procField("aux_info_type_parameter","uint",32)),this._procField("default_sample_info_size","uint",8),this._procField("sample_count","uint",32),0===this.default_sample_info_size&&this._procFieldArray("sample_info_size",this.sample_count,"uint",8)}function s(){this._procFullBox(),this._procField("sample_count","uint",32),1&this.flags&&this._procField("IV_size","uint",8),this._procEntries("entry",this.sample_count,(function(t){this._procEntryField(t,"InitializationVector","data",8),2&this.flags&&(this._procEntryField(t,"NumberOfEntries","uint",16),this._procSubEntries(t,"clearAndCryptedData",t.NumberOfEntries,(function(t){this._procEntryField(t,"BytesOfClearData","uint",16),this._procEntryField(t,"BytesOfEncryptedData","uint",32)})))}))}function a(){var t=[109,29,155,5,66,213,68,230,128,226,20,29,175,247,87,178],e=[212,128,126,242,202,57,70,149,142,84,38,203,158,70,167,159],i=[162,57,79,82,90,155,79,20,162,68,108,66,124,100,141,244];r.ArrayHelper.comparePrimitiveArrays(this.usertype,t)&&(this._procFullBox(),this._parsing&&(this.type="tfxd"),this._procField("fragment_absolute_time","uint",1===this.version?64:32),this._procField("fragment_duration","uint",1===this.version?64:32)),r.ArrayHelper.comparePrimitiveArrays(this.usertype,e)&&(this._procFullBox(),this._parsing&&(this.type="tfrf"),this._procField("fragment_count","uint",8),this._procEntries("entry",this.fragment_count,(function(t){this._procEntryField(t,"fragment_absolute_time","uint",1===this.version?64:32),this._procEntryField(t,"fragment_duration","uint",1===this.version?64:32)}))),r.ArrayHelper.comparePrimitiveArrays(this.usertype,i)&&(this._parsing&&(this.type="sepiff"),s.call(this))}function c(){this._procField("configuration_version","uint",8),this._procField("avc_profile_indication","uint",8),this._procField("profile_compatibility","uint",8),this._procField("avc_level_indication","uint",8),this._parsing||(this.temp1=this.complete_representation<<7|this.length_size_minus_one),this._procField("temp1","uint",8),this._parsing&&(this.complete_representation=this.temp1>>>7&1,this.length_size_minus_one=3&this.temp1),this._parsing||(this.num_sps&=31),this._procField("num_sps","uint",8),this._parsing&&(this.num_sps&=31),this._procEntries("sps",this.num_sps,(function(t){this._procEntryField(t,"length","uint",16),this._procEntryField(t,"data","data",t.length)})),this._parsing||(this.num_pps&=31),this._procField("num_pps","uint",8),this._parsing&&(this.num_pps&=31),this._procEntries("pps",this.num_pps,(function(t){this._procEntryField(t,"length","uint",16),this._procEntryField(t,"data","data",t.length)}))}function h(){this._procFullBox(),this._procField("reserved","uint",16),this._procField("esId","uint",16),this._procField("streamPriority","uint",8),this.streamPriority&=31,this._procField("reserved2","uint",16),this._procField("decoderConfig","data",17)}},25666:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SmoothToMpdConverter=void 0;var r=i(25550),o=i(28764),n=i(63546),s=i(35148),a=i(62510),c=i(331),h=i(67883),p=i(77870),u=i(38925),l="PT4S",d=function(){function t(t,e,i){this.context=t,this.eventHandler=t.eventHandler,this.updateManifest(e,i)}return t.prototype.getDASHManifest=function(){return this.isParsed||this.convertManifests(),this.dashManifest},t.prototype.updateManifest=function(t,e){this.isParsed=!1,this.dashManifest=null,this.smoothManifest=t,this.groupIndex=1,this.smoothInfo={format:"smooth"},this.shouldUpdatePresentationTimeOffsets=!1,this.requestTimeStamp=e,this.presentationTimeOffsets={}},t.prototype.convertManifests=function(){var e=this;(this.smoothManifest.canPause||this.smoothManifest.canSeek)&&(this.shouldUpdatePresentationTimeOffsets=!0),this.dashManifest={"_xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance",_xmlns:"urn:mpeg:dash:schema:mpd:2011","_xmlns:xlink":"http://www.w3.org/1999/xlink","_xsi:schemaLocation":"urn:mpeg:DASH:schema:MPD:2011 http://standards.iso.org/ittf/PubliclyAvailableStandards/MPEG-DASH_schema_files/DASH-MPD.xsd",_profiles:"urn:mpeg:dash:profile:isoff-live:2011",_type:this.smoothManifest.isLive?"dynamic":"static",_minimumUpdatePeriod:this.smoothManifest.isLive?l:null,_mediaPresentationDuration:t.convertDurationToSeconds(Number(this.smoothManifest.duration)),_timeShiftBufferDepth:-this.smoothManifest.dvrWindowLength,_requestTimestamp:this.requestTimeStamp,originalFormat:this.smoothInfo,Location:[this.smoothManifest.location],Period:[],isInitialized:!1},this.smoothManifest.clips.forEach((function(t,i){var r=e.convertClipToPeriod(t,i);e.dashManifest.Period.push(r),e.smoothManifest.isLive&&(e.dashManifest._availabilityStartTime=e.availabilityStartTime||new Date(e.requestTimeStamp-(0,c.toMilliSeconds)(e.smoothManifest.dvrWindowLength)).toISOString(),e.shouldUpdatePresentationTimeOffsets=!1),e.shouldUpdatePresentationTimeOffsets&&e.unifyPresentationTimeOffsets(r),e.groupIndex=1})),this.smoothManifest.isLive&&(this.availabilityStartTime=this.dashManifest._availabilityStartTime),this.isParsed=!0},t.prototype.unifyPresentationTimeOffsets=function(t){var e=this,i=this.presentationTimeOffsets[Number(t._id)];0!==i.audio&&0!==i.video&&t.AdaptationSet.forEach((function(t){var r=i.video;if(i[t._contentType]!==r){var o=i[t._contentType],n=(o-r)/e.smoothManifest.timeScale;e.context.logger.debug("Updating the presentation time offset of ".concat(t._contentType," ")+"from: ".concat(o," to ").concat(r,". Difference: ").concat(n,"s"))}t.SegmentTemplate[0]._presentationTimeOffset=r}))},t.convertDurationToSeconds=function(t){return"PT"+t+"S"},t.prototype.convertClipToPeriod=function(e,i){var r=this,o=[];e.streamIndexes.forEach((function(t){o.push(r.convertStreamInfoToAdaptationSet(t,i))}));var n=e.ClipBegin/this.smoothManifest.timeScale,s=e.ClipEnd/this.smoothManifest.timeScale-n,a=0;return this.dashManifest.Period.forEach((function(t){a+=t.duration})),{_id:String(i),_start:t.convertDurationToSeconds(a),start:a,_duration:t.convertDurationToSeconds(s),duration:s,AdaptationSet:o}},t.prototype.convertStreamInfoToAdaptationSet=function(e,i){var r,o,n,s=this,a=e.type,c=1/0,u=0,l=[],d=new h.AdaptationSetId(String(i),String(this.groupIndex));e.qualityInformation&&e.qualityInformation.length>0?("text"===(a+=t.buildMimeTypeSuffix(e.qualityInformation[0]))&&(a="application/mp4"),e.qualityInformation.forEach((function(t){var i=s.convertQualityInfoToRepresentation(e,t,a,d);c>t.bitrate&&(c=t.bitrate),u<t.bitrate&&(u=t.bitrate),t.maxHeight&&(o=t.maxHeight),t.maxWidth&&(r=t.maxWidth),t.samplingRate&&(i._audioSamplingRate=t.samplingRate),t.noChannels&&(n=t.noChannels),l.push(i)}))):(c=0,u=1/0),l.sort((function(t,e){return t._bandwidth-e._bandwidth}));var f=[this.convertStreamInfoToSegmentTemplate(e,i)],_={_id:String(this.groupIndex),_internalId:d,_group:String(this.groupIndex++),_contentType:e.type,_mimeType:a,_minBandwidth:c,_maxBandwidth:u,_periodId:String(i),_codecs:"",Representation:l,SegmentTemplate:f};return l.forEach((function(t){return t.SegmentTemplate=_.SegmentTemplate})),e.language&&(_._lang=e.language),r&&(_._maxWidth=r),o&&(_._maxHeight=o),n&&l.forEach((function(t){return t.AudioChannelConfiguration=[{_schemeIdUri:p.DashSchemeUri.AudioChannelConfig,_value:"".concat(n)}]})),_},t.buildMimeTypeSuffix=function(t){switch(t.codec){case"TEXT":case"TTML":return"";default:return"/mp4"}},t.prototype.convertQualityInfoToRepresentation=function(t,e,i,r){var o=[{url:this.convertTemplateUrlToBaseUrl(t.templateUrl)}],n=new u.RepresentationId(r,t.name+"_"+e.bitrate),s={_id:n.representationId,_internalId:n,_requestTimestamp:null,_bandwidth:e.bitrate,_mimeType:i,Uri:o[0].url,_codecs:this.convertCodecInfo(e.codec,e.codecPrivateData),BaseURL:o};return e.maxHeight&&(s._height=e.maxHeight),e.maxWidth&&(s._width=e.maxWidth),s},t.prototype.convertCodecInfo=function(e,i){return i=i?i.toString():"","H264"===e||"AVC1"===e?this.getH264Codec(i):e.indexOf("AAC")>-1?t.getAACCodec(e,i):"TEXT"===e||"TTML"===e?"stpp":(this.context.logger.debug("Unknown codec while converting Smooth Manifest: "+e),this.eventHandler.dispatchEvent(a.PlayerEvent.Warning,new n.PlayerWarning(s.WarningCode.SOURCE_CODEC_OR_FILE_FORMAT_NOT_SUPPORTED)),e)},t.prototype.getH264Codec=function(t){var e=/00000001[0-9]7/.exec(t);if(e&&e.length>0){var i=t.indexOf(e[0])+10;return"avc1."+t.substring(i,i+6)}return this.context.logger.debug("Unable to parse H264-codec information from codecPrivateData: "+t),this.context.eventHandler.fireError(new o.PlayerError(r.ErrorCode.SOURCE_INVALID_H264_CODEC,{codec:"h264",codecPrivateData:t},"Unable to parse H264-codec information from codecPrivateData.")),null},t.getAACCodec=function(t,e){return"mp4a.40."+("AACH"===t?5:void 0===e||""===e?2:(248&parseInt(e.substring(0,2),16))>>3)},t.prototype.convertStreamInfoToSegmentTemplate=function(t,e){var i=this.convertTemplateUrlToBaseUrl(t.templateUrl),r=t.timeScale||this.smoothManifest.timeScale||1e4,o=this.convertFragmentInfoToTimeLine(t,e,r);return{_timescale:String(r),_media:i,SegmentTimeline:[o]}},t.prototype.convertTemplateUrlToBaseUrl=function(t){return t.indexOf("{start_time}")>-1&&(this.shouldUpdatePresentationTimeOffsets=!0),t.replace("{bitrate}","$Bandwidth$").replace("{start time}","$Time$").replace("{start_time}","$Time$")},t.prototype.convertFragmentInfoToTimeLine=function(t,e,i){var r=[];return t.fragmentInfos&&t.fragmentInfos.length>0&&(this.presentationTimeOffsets[e]||(this.presentationTimeOffsets[e]={}),this.presentationTimeOffsets[e][t.type]=t.fragmentInfos[0].startTime,r=t.fragmentInfos.map((function(t){return{_t:String(t.startTime),_d:String(t.duration),_n:t.index}}))),{S:r,totalDuration:r.reduce((function(t,e){var i;return t+Number(e._d)*(Number(null!==(i=e._r)&&void 0!==i?i:"0")+1)}),0)/i}},t.prototype.dispose=function(){this.eventHandler=null,this.smoothManifest=null},t}();e.SmoothToMpdConverter=d},26608:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.InitSegmentGenerator=void 0;var r=i(91520),o=i(16368),n=i(73377),s=i(56855),a=7,c=8,h=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],p=function(){function t(e,i,n){if(this.initInfo=i,this.timescale=this.initInfo.timescale||s.DEFAULT_TIMESCALE_FACTOR,this.trackId=this.initInfo.trackId,n){var a=r.ModuleManager.get(o.ModuleName.DRM),c=new a.PlayReady(e,{});this.contentProtection=n.map((function(e){var i=t.base64ToArrayBuffer(e.content),r=String.fromCharCode.apply(null,i),o=c.getIdentifierFromInitData(r),n=t.base64ToArrayBuffer(o);return a.PlayReady.kidToCENC(n),{header:e,kid:n}}))}}return t.prototype.getParsedData=function(){return this.parsedIsoData||this.createIsoFileWithConent(),this.parsedIsoData},t.prototype.generateArrayBuffer=function(){return this.getParsedData().write()},t.prototype.createIsoFileWithConent=function(){var e=n.ISOBoxer.createFile();t.createFtypBox(e),this.createMoovBox(e),this.parsedIsoData=e},t.createFtypBox=function(t){var e=n.ISOBoxer.createBox("ftyp",t);return e.major_brand="iso6",e.minor_version=1,e.compatible_brands=[],e.compatible_brands[0]="isom",e.compatible_brands[1]="iso6",e.compatible_brands[2]="msdh",e},t.prototype.createMoovBox=function(e){var i=n.ISOBoxer.createBox("moov",e);this.createMvhdBox(i);var r=n.ISOBoxer.createBox("trak",i);this.createTkhdBox(r);var o=n.ISOBoxer.createBox("mdia",r);this.createMdhdBox(o),this.createHdlrBox(o);var s=n.ISOBoxer.createBox("minf",o);switch(this.initInfo.type){case"video":t.appendVmhdBox(s);break;case"audio":t.appendSmhdBox(s)}var a=n.ISOBoxer.createBox("dinf",s);t.appendDrefBox(a);var c=n.ISOBoxer.createBox("stbl",s);n.ISOBoxer.createFullBox("stts",c)._data=[0,0,0,0,0,0,0,0],n.ISOBoxer.createFullBox("stsc",c)._data=[0,0,0,0,0,0,0,0],n.ISOBoxer.createFullBox("stco",c)._data=[0,0,0,0,0,0,0,0],n.ISOBoxer.createFullBox("stsz",c)._data=[0,0,0,0,0,0,0,0,0,0,0,0],this.appendStsdBox(c);var h=n.ISOBoxer.createBox("mvex",i);this.appendTrexBox(h),this.contentProtection&&this.contentProtection.forEach((function(e){t.appendProtectionSystemSpecificHeaderBoxForPR(i,e.header.content)}))},t.prototype.createMvhdBox=function(t){var e=n.ISOBoxer.createFullBox("mvhd",t);return e.version=1,e.creation_time=0,e.modification_time=0,e.timescale=this.timescale,e.duration=Math.round(this.initInfo.periodDuration*this.timescale),e.rate=1,e.volume=1,e.reserved1=0,e.reserved2=[0,0],e.matrix=[1,0,0,0,1,0,0,0,16384],e.pre_defined=[0,0,0,0,0,0],e.next_track_ID=this.trackId+1,e},t.prototype.createTkhdBox=function(t){var e=n.ISOBoxer.createFullBox("tkhd",t);return e.version=1,e.flags=7,e.creation_time=0,e.modification_time=0,e.track_ID=this.trackId,e.reserved1=0,e.duration=Math.round(this.initInfo.periodDuration*this.timescale),e.reserved2=[0,0],e.layer=0,e.alternate_group=0,e.volume=1,e.matrix=[1,0,0,0,1,0,0,0,16384],e.width=this.initInfo.width,e.height=this.initInfo.height,e},t.prototype.createMdhdBox=function(t){var e=n.ISOBoxer.createFullBox("mdhd",t);return e.version=1,e.creation_time=0,e.modification_time=0,e.timescale=this.timescale,e.duration=Math.round(this.initInfo.periodDuration*this.timescale),e.language=this.initInfo.lang||"und",e.pre_defined=0,e},t.prototype.createHdlrBox=function(t){var e=n.ISOBoxer.createFullBox("hdlr",t);switch(e.pre_defined=0,this.initInfo.type){case"video":e.handler_type="vide";break;case"audio":e.handler_type="soun";break;default:e.handler_type="meta"}return e.name=""+this.initInfo.id,e.reserved=[0,0,0],e},t.appendVmhdBox=function(t){var e=n.ISOBoxer.createFullBox("vmhd",t);e.flags=1,e.graphicsmode=0,e.opcolor=[0,0,0]},t.appendSmhdBox=function(t){var e=n.ISOBoxer.createFullBox("smhd",t);e.flags=1,e.balance=0,e.reserved=0},t.appendDrefBox=function(t){var e=n.ISOBoxer.createFullBox("dref",t);e.entry_count=1,e.entries=[];var i=n.ISOBoxer.createFullBox("url ",e,!1);i.location="",i.flags=1,e.entries.push(i)},t.prototype.appendStsdBox=function(t){var e=n.ISOBoxer.createFullBox("stsd",t);switch(e.entries=[],this.initInfo.type){case"video":case"audio":e.entries.push(this.createSampleEntry(e))}e.entry_count=e.entries.length},t.prototype.createSampleEntry=function(t){var e=this.initInfo.codecs.substring(0,this.initInfo.codecs.indexOf("."));switch(e){case"avc1":return this.createAVCVisualSampleEntry(t,e);case"mp4a":return this.createMP4AudioSampleEntry(t,e);default:throw{name:"Unsupported codec",message:"Unsupported codec",data:{codec:e}}}},t.prototype.createAVCVisualSampleEntry=function(e,i){var r;if((r=this.contentProtection?n.ISOBoxer.createBox("encv",e,!1):n.ISOBoxer.createBox("avc1",e,!1)).reserved1=[0,0,0,0,0,0],r.data_reference_index=1,r.pre_defined1=0,r.reserved2=0,r.pre_defined2=[0,0,0],r.height=this.initInfo.height,r.width=this.initInfo.width,r.horizresolution=72,r.vertresolution=72,r.frame_count=1,r.compressorname=[10,65,86,67,32,67,111,100,105,110,103,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],r.depth=24,r.pre_defined3=65535,r.config=this.createAVC1ConfigurationRecord(),this.contentProtection){var o=n.ISOBoxer.createBox("sinf",r);t.appendOriginalFormatBox(o,i),t.appendSchemeTypeBox(o),this.appendSchemeInformationBox(o)}return r},t.prototype.createAVC1ConfigurationRecord=function(){for(var e,i=15,r=[],o=[],n=0,s=0,h=0,p=this.initInfo.codecPrivateData.split("00000001").slice(1),u=0;u<p.length;u++)switch(31&(e=t.hexStringtoBuffer(p[u]))[0]){case a:r.push(e),i+=e.length+2;break;case c:o.push(e),i+=e.length+2}r.length>0&&(n=r[0][1],h=r[0][2],s=r[0][3]);var l=new Uint8Array(i),d=0;l[d++]=(4278190080&i)>>24,l[d++]=(16711680&i)>>16,l[d++]=(65280&i)>>8,l[d++]=255&i,l.set([97,118,99,67],d),d+=4,l[d++]=1,l[d++]=n,l[d++]=h,l[d++]=s,l[d++]=255,l[d++]=224|r.length;for(var f=0;f<r.length;f++)l[d++]=(65280&r[f].length)>>8,l[d++]=255&r[f].length,l.set(r[f],d),d+=r[f].length;l[d++]=o.length;for(f=0;f<o.length;f++)l[d++]=(65280&o[f].length)>>8,l[d++]=255&o[f].length,l.set(o[f],d),d+=o[f].length;return l},t.prototype.createMP4AudioSampleEntry=function(e,i){var r;if((r=this.contentProtection?n.ISOBoxer.createBox("enca",e,!1):n.ISOBoxer.createBox("mp4a",e,!1)).reserved1=[0,0,0,0,0,0],r.data_reference_index=1,r.reserved2=[0,0],r.channelcount=this.initInfo.audioChannels,r.samplesize=16,r.pre_defined=0,r.samplerate=this.initInfo.audioSamplingRate,r.esds=this.createMPEG4AACESDescriptor(),this.contentProtection){var o=n.ISOBoxer.createBox("sinf",r);t.appendOriginalFormatBox(o,i),t.appendSchemeTypeBox(o),this.appendSchemeInformationBox(o)}return r},t.prototype.createMPEG4AACESDescriptor=function(){var e=t.hexStringtoBuffer(this.initInfo.codecPrivateData),i=34+e.length,r=new Uint8Array(i),o=0;return r[o++]=(4278190080&i)>>24,r[o++]=(16711680&i)>>16,r[o++]=(65280&i)>>8,r[o++]=255&i,r.set([101,115,100,115],o),o+=4,r.set([0,0,0,0],o),o+=4,r[o++]=3,r[o++]=20+e.length,r[o++]=(65280&this.trackId)>>8,r[o++]=255&this.trackId,r[o++]=0,r[o++]=4,r[o++]=15+e.length,r[o++]=64,r[o]=20,r[o]|=0,r[o++]|=1,r[o++]=255,r[o++]=255,r[o++]=255,r[o++]=(4278190080&this.initInfo.bandwidth)>>24,r[o++]=(16711680&this.initInfo.bandwidth)>>16,r[o++]=(65280&this.initInfo.bandwidth)>>8,r[o++]=255&this.initInfo.bandwidth,r[o++]=(4278190080&this.initInfo.bandwidth)>>24,r[o++]=(16711680&this.initInfo.bandwidth)>>16,r[o++]=(65280&this.initInfo.bandwidth)>>8,r[o++]=255&this.initInfo.bandwidth,r[o++]=5,r[o++]=e.length,r.set(e,o),r},t.appendOriginalFormatBox=function(e,i){n.ISOBoxer.createBox("frma",e).data_format=t.stringToCharCode(i)},t.appendSchemeTypeBox=function(t){var e=n.ISOBoxer.createFullBox("schm",t);e.flags=0,e.version=0,e.scheme_type=1667591779,e.scheme_version=65536},t.prototype.appendSchemeInformationBox=function(t){var e=n.ISOBoxer.createBox("schi",t);this.appendTrackEncryptionBox(e)},t.appendProtectionSystemSpecificHeaderBoxForPR=function(e,i){var r=n.ISOBoxer.createFullBox("pssh",e),o=t.base64ToArrayBuffer(i);r.flags=0,r.version=0,r.SystemID=new Uint8Array([154,4,240,121,152,64,66,134,171,146,230,91,224,136,95,149]),r.DataSize=o.length,r.Data=o},t.prototype.appendTrackEncryptionBox=function(t){var e=n.ISOBoxer.createFullBox("tenc",t);e.flags=0,e.version=0,e.default_IsEncrypted=1,e.default_IV_size=8,e.default_KID=this.contentProtection?this.contentProtection[0].kid:h},t.prototype.appendTrexBox=function(t){var e=n.ISOBoxer.createFullBox("trex",t);e.track_ID=this.trackId,e.default_sample_description_index=1,e.default_sample_duration=0,e.default_sample_size=0,e.default_sample_flags=0},t.base64ToArrayBuffer=function(t){for(var e=window.atob(t),i=e.length,r=new Uint8Array(i),o=0;o<i;o++)r[o]=e.charCodeAt(o);return r},t.hexStringtoBuffer=function(t){var e,i=new Uint8Array(t.length/2);for(e=0;e<t.length/2;e+=1)i[e]=parseInt(""+t[2*e]+t[2*e+1],16);return i},t.stringToCharCode=function(t){var e,i=0;for(e=0;e<t.length;e+=1)i|=t.charCodeAt(e)<<8*(t.length-e-1);return i},t}();e.InitSegmentGenerator=p},56855:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SmoothStreamingParser=e.DEFAULT_TIMESCALE_FACTOR=void 0;var r=i(25550),o=i(28764),n=i(63546),s=i(35148),a=i(62510),c=i(76885);e.DEFAULT_TIMESCALE_FACTOR=1e7;var h=function(){function t(t,i,r){this.timescaleFactor=e.DEFAULT_TIMESCALE_FACTOR,this.context=t,this.updateManifest(i,r)}return t.prototype.getParsedManifest=function(){return this.isManifestParsed||this.parseRawManifest(),this.parsedManifest},t.prototype.updateManifest=function(t,e){this.rawManifest=t,this.manifestUrl=e,this.isManifestParsed=!1,this.parsedManifest=null},t.prototype.parseRawManifest=function(){var t=this;this.parsedManifest=this.parseSmoothStreamingInformation(this.rawManifest.SmoothStreamingMedia),this.ensureClipArray(),this.parsedManifest.startTime=1/0,this.parsedManifest.duration=0,this.rawManifest.SmoothStreamingMedia.Clip.forEach((function(e){var i=e.StreamIndex;Array.isArray(i)||(i=[i]);var r=-1,o=1/0;i.forEach((function(i){var n=t.parseStreamIndex(i,e);n&&(n.totalTime>r&&(r=n.totalTime),n.startTime<o&&(o=n.startTime))})),-1===e.ClipBegin&&(e.ClipBegin=o*t.timescaleFactor,e.ClipEnd=(o+r)*t.timescaleFactor),t.parsedManifest.startTime=Math.min(t.parsedManifest.startTime,o),t.parsedManifest.duration+=r,t.parsedManifest.clips.push(e)})),this.parseProtectionElement(this.rawManifest.SmoothStreamingMedia)},t.prototype.ensureClipArray=function(){if(this.rawManifest.SmoothStreamingMedia.Clip)Array.isArray(this.rawManifest.SmoothStreamingMedia.Clip)||(this.rawManifest.SmoothStreamingMedia.Clip=[this.rawManifest.SmoothStreamingMedia.Clip]);else{var t={_ClipBegin:-1,_ClipEnd:-1,_Url:this.manifestUrl,streamIndexes:[],StreamIndex:this.rawManifest.SmoothStreamingMedia.StreamIndex};this.rawManifest.SmoothStreamingMedia.Clip=[t],delete this.rawManifest.SmoothStreamingMedia.StreamIndex}this.rawManifest.SmoothStreamingMedia.Clip.forEach((function(t){t.streamIndexes=t.streamIndexes||[],t.ClipBegin=Number(t._ClipBegin),t.ClipEnd=Number(t._ClipEnd),t.Url=String(t._Url)}))},t.prototype.parseSmoothStreamingInformation=function(i){var r={lookaheadCount:i._LookaheadCount,dvrWindowLength:Number(i._DVRWindowLength)||0,location:this.manifestUrl,duration:-1,startTime:-1,timeScale:Number(i._TimeScale||e.DEFAULT_TIMESCALE_FACTOR),isLive:t.parseBoolean(i._IsLive,!1),canPause:t.parseBoolean(i._CanPause,!0),canSeek:t.parseBoolean(i._CanSeek,!0),minorVersion:Number(i._MinorVersion)||2,majorVersion:Number(i._MajorVersion)||2,clips:[]};return this.timescaleFactor=r.timeScale,r.isLive?r.dvrWindowLength=r.dvrWindowLength/this.timescaleFactor:r.dvrWindowLength=0,this.isManifestParsed=!0,r},t.prototype.parseProtectionElement=function(t){if(t.Protection&&t.Protection.ProtectionHeader){var e=t.Protection.ProtectionHeader;Array.isArray(e)||(e=[e]),this.parsedManifest.protection=e.map((function(t){return{systemId:t.SystemID,content:t.__text}}))}},t.prototype.parseStreamIndex=function(t,e){var i=this.parseCommonStreamIndexInformation(t,e.Url);return 0===(i=this.parseQualityLevels(t,i)).qualityInformation.length?(this.context.eventHandler.dispatchEvent(a.PlayerEvent.Warning,new n.PlayerWarning(s.WarningCode.SOURCE_SMOOTH_EMPTY_QUALITY_LEVEL,"StreamIndex does not contain any QualityLevel, will be filtered out")),null):(e.streamIndexes.push(i),i)},t.prototype.parseCommonStreamIndexInformation=function(e,i){var r={name:e._Name||"unnamed"+t.unnamedStreamIndexCounter++,parentStream:e._ParentStream,numberOfChunks:Number(e._Chunks),timeScale:Number(e._TimeScale||e._StreamTimeScale||this.timescaleFactor),type:e._Type,templateUrl:this.convertUrlToAbsolute(e._Url,i),totalTime:0,startTime:0,fragmentInfos:[]};return e._Subtype&&""!==e._Subtype&&(r.subType=e._Subtype),e._Language&&(r.language=e._Language),t.parseCustomAttributes(e,r),this.parseFragmentInformation(e,r),r},t.prototype.convertUrlToAbsolute=function(t,e){var i;return c.URLHelper.isUrlAbsolute(t)?i=t:(i=c.URLHelper.removeLastPathPart(e),i+=t),i},t.parseCustomAttributes=function(t,e){if(t&&t.CustomAttributes){e.customAttributes||(e.customAttributes={});var i=t.CustomAttributes;Array.isArray(i)||(i=[i]),i.forEach((function(t){Object.keys(t).forEach((function(i){e.customAttributes[i]=t[i]}))}))}},t.prototype.parseFragmentInformation=function(t,e){if(!t||!t.c||t.c.length<1)this.context.eventHandler.fireError(new o.PlayerError(r.ErrorCode.SOURCE_MANIFEST_INVALID,void 0,"StreamIndex must contain StreamFragmentElements"));else{for(var i=[].concat(t.c),n=e.timeScale||this.timescaleFactor,s=0,a=0,c=0,h=0;h<i.length;h++){var p=i[h],u=1;p._r&&(u=Number(p._r));for(var l=Number(p._t||s),d=0,f=1;f<=u;f++){var _=p._d,m={duration:_=_?Number(_):this.calcualteMissingFragmentDuration(h,i,s)/u,startTime:l+d,index:c};e.fragmentInfos.push(m),0===h&&1===f&&(e.startTime=s,s=l),d+=_,a+=_,s+=_,c++}}e.totalTime=a/n}},t.prototype.calcualteMissingFragmentDuration=function(t,e,i){var n,s=e[t]._t||i;if(t+1<e.length){var a=e[t+1];s&&a._t&&(n=Number(a._t)-Number(s))}if(t>0&&t-1>0){var c=e[t-1];if(s&&c._t){var h=Number(s)-Number(c._t);n?h!==n&&this.context.logger.debug("To avoid gaps in the timeline returning calculated duration of: "+"".concat(n," instead of: ").concat(h," for fragment at startTime: ").concat(s)):n=Number(s)-Number(c._t)}}if(n)return n;this.context.eventHandler.fireError(new o.PlayerError(r.ErrorCode.SOURCE_MANIFEST_INVALID))},t.prototype.parseQualityLevels=function(e,i){var r=this,o=e.QualityLevel;Array.isArray(o)||(o=[o]);var c=i;return c.qualityInformation=o.map((function(e){var o;if("video"===i.type)o=t.parseVideoQualityInformation(e);else if("audio"===i.type){var c=t.parseAudioQualityInformation(e);o=t.validateAudioQualityInformation(c)}else{if("text"!==i.type)return r.context.eventHandler.dispatchEvent(a.PlayerEvent.Warning,new n.PlayerWarning(s.WarningCode.SOURCE_SMOOTH_UNKNOWN_STREAM_INDEX_TYPE,'Unknown StreamIndex.type: "'.concat(i.type,'", it will be ignored'))),null;o=t.parseTextQualityInformation(e)}return t.parseCustomAttributes(e,o),o})).filter((function(t){return null!=t})),c},t.parseVideoQualityInformation=function(t){return{codec:t._FourCC,codecPrivateData:t._CodecPrivateData,maxHeight:parseInt(t._MaxHeight),maxWidth:parseInt(t._MaxWidth),bitrate:parseInt(t._Bitrate),index:parseInt(t._Index)}},t.parseAudioQualityInformation=function(t){return{codec:t._FourCC,codecPrivateData:t._CodecPrivateData,bitrate:parseInt(t._Bitrate),index:parseInt(t._Index),noChannels:parseInt(t._Channels),samplingRate:parseInt(t._SamplingRate,10),audioTag:parseInt(t._AudioTag)}},t.parseTextQualityInformation=function(t){return{codec:t._FourCC,codecPrivateData:t._CodecPrivateData,bitrate:parseInt(t._Bitrate),index:parseInt(t._Index)}},t.validateAudioQualityInformation=function(e){if(null!==e.codecPrivateData&&""!==e.codecPrivateData)return e;var i,r=t.SAMPLING_FREQUENCY_INDEXES[String(e.samplingRate)];if("AACH"===e.codec){var o=5,n=new Uint8Array(4),s=t.SAMPLING_FREQUENCY_INDEXES[2*e.samplingRate];n[0]=o<<3|r>>1,n[1]=r<<7|e.noChannels<<3|s>>1,n[2]=s<<7|8,n[3]=0,(a=new Uint16Array(2))[0]=(n[0]<<8)+n[1],a[1]=(n[2]<<8)+n[3],i=a[0].toString(16)+a[1].toString(16)}else{var a,c=new Uint8Array(2);o=2;c[0]=o<<3|r>>1,c[1]=r<<7|e.noChannels<<3,(a=new Uint16Array(1))[0]=(c[0]<<8)+c[1],i=a[0].toString(16)}var h=""+i;return h=h.toUpperCase(),e.codecPrivateData=h,e},t.parseBoolean=function(t,e){return void 0===t&&(t=null),void 0===e&&(e=!1),t?"string"==typeof t?"true"===t.toLowerCase():Boolean(t):e},t.prototype.dispose=function(){this.parsedManifest=null,this.rawManifest=null},t.unnamedStreamIndexCounter=0,t.SAMPLING_FREQUENCY_INDEXES={96e3:0,88200:1,64e3:2,48e3:3,44100:4,32e3:5,24e3:6,22050:7,16e3:8,12e3:9,11025:10,8e3:11,7350:12},t}();e.SmoothStreamingParser=h},63894:function(t,e,i){"use strict";var r=this&&this.__extends||function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function r(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(r.prototype=i.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0}),e.createSmoothSegmentTemplateMPDHandler=h;var o=i(91520),n=i(16368),s=i(48727),a=i(73377),c=i(16580);function h(){var t,e=o.ModuleManager.get(n.ModuleName.DASH);return(t=function(e){function i(i,r,o){var n=e.call(this,i)||this;return n.lastRepresentationId=void 0,n.mimeType=r,n.smoothLoader=o,n.trackId=t.trackIDs[r],t.trackIDs[r]=Math.max(t.trackIDs.audioMp4,t.trackIDs.videoMp4)+1,a.ISOBoxer.addBoxProcessor("uuid",c.uuidProcessor),a.ISOBoxer.addBoxProcessor("saio",c.saioProcessor),a.ISOBoxer.addBoxProcessor("saiz",c.saizProcessor),a.ISOBoxer.addBoxProcessor("senc",c.sencProcessor),n}return r(i,e),i.prototype.checkIfSegmentAvailable=function(t){return!0},i.prototype.isExpectedSegmentPlaybackTimeExceedingPeriodDuration=function(t){return!1},i.prototype.generateSmoothInitSegment=function(t,e){if(this.rewriteHeaderInfoIfNecessary(t),this.currentRepresentationId&&!this.currentRepresentationId.equals(this.lastRepresentationId)){this.lastRepresentationId=this.currentRepresentationId;var i=this.manifestService.findAdaptationSet(this.manifestService.getFirstPeriod()._id,this.mimeType),r=a.ISOBoxer.parseBuffer(t.getData());t.setParsedData(r);var c=o.ModuleManager.get(n.ModuleName.ContainerMP4).getTrackId(this.context.logger,r),h=this.manifestService.getRepresentationById(this.currentRepresentationId),p=this.smoothLoader.createInitSegmentForRepresentation(h,i,c),u=p[0],l=p[1],d=new s.Segment(l,this.mimeType,t.getCodec(),t.getPeriodId(),null,s.SegmentInitType.INIT,!0,-1,t.getRepresentationId());d.setParsedData(u),d.setUrl(h.BaseURL[0].url),d.setLastSegment(!1),t.getSegmentInfo()&&d.setSegmentInfo({internalRepresentationId:t.getSegmentInfo().internalRepresentationId,representationId:t.getSegmentInfo().representationId}),null==e||e.parseSegment(d),this.lastRepresentationInitSegment=d}t.setInitSegment(this.lastRepresentationInitSegment)},i.prototype.rewriteHeaderInfoIfNecessary=function(t){t&&!t.isInit()&&this.rewriteDataSegmentBoxes(t)},i.prototype.rewriteDataSegmentBoxes=function(e){var i=a.ISOBoxer.parseBuffer(e.getData()),r=i.fetch("tfhd"),o=this.manifestService.getAdaptationSetIndex(e.getRepresentationId())+1;o>0?this.trackId=o:this.trackId<1&&(this.trackId=1),r.track_ID=this.trackId;var n=i.fetch("tfdt"),s=i.fetch("traf");null===n&&((n=a.ISOBoxer.createFullBox("tfdt",s,r)).version=1,n.flags=0,n.baseMediaDecodeTime=Math.floor(e.getSegmentInfo().mediaPresentationTime));var c=i.fetch("trun"),h=i.fetch("tfxd");h&&(h._parent.boxes.splice(h._parent.boxes.indexOf(h),1),h=null);var p=i.fetch("sepiff");if(null!==p){p.type="senc",p.usertype=void 0;var u=i.fetch("saio");if(null===u){(u=a.ISOBoxer.createFullBox("saio",s)).version=0,u.flags=0,u.entry_count=1,u.offset=[0];var l=a.ISOBoxer.createFullBox("saiz",s);if(l.version=0,l.flags=0,l.sample_count=p.sample_count,l.default_sample_info_size=0,l.sample_info_size=[],2&p.flags)for(var d=0;d<p.sample_count;d+=1)l.sample_info_size[d]=10+6*p.entry[d].NumberOfEntries;else l.default_sample_info_size=8}}r.flags&=16777214,r.flags|=131072,c.flags|=1;var f=i.fetch("moof"),_=f.getLength();c.data_offset=_+8;var m=i.fetch("saio");if(null!==m){var g=t.getBoxOffset(f,"traf"),y=t.getBoxOffset(s,"senc");m.offset[0]=g+y+16}e.setData(i.write()),e.setParsedData(a.ISOBoxer.parseBuffer(e.getData()))},i.getBoxOffset=function(t,e){for(var i=8,r=0;r<t.boxes.length;r++){if(t.boxes[r].type===e)return i;i+=t.boxes[r].size}return i},i.prototype.dispose=function(){e.prototype.dispose.call(this),this.smoothLoader=null},i}(e.SegmentTimelineMPDHandler)).trackIDs={videoMp4:2,audioMp4:1},t}},73377:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ISOBoxer=void 0;var r=i(79500),o=r.createBox("dummy"),n=Object.getPrototypeOf(o).constructor,s=r.createFile(),a=Object.getPrototypeOf(s).constructor;function c(t){return 0===t.length?null:t[0]}function h(t,e){var i=e.indexOf("."),r=[];if(-1===i)r=t.filter((function(t){return t.type===e}));else{var o=e.substring(0,i),n=e.substring(i+1);r=t.filter((function(t){return t.type===o})).map((function(t){return t.list(n)})).reduce((function(t,e){return t.concat(e)}),r)}return r}a.prototype.get=function(t){return c(this.list(t))},a.prototype.list=function(t){return h(this.boxes,t)},n.prototype.list=function(t){return h(this.boxes||this.entries||[],t)},n.prototype.get=function(t){return c(this.list(t))},e.ISOBoxer=r},79500:function(t,e){var i={parseBuffer:function(t){return new r(t).parse()},addBoxProcessor:function(t,e){"string"==typeof t&&"function"==typeof e&&(o.prototype._boxProcessors[t]=e)},createFile:function(){return new r},createBox:function(t,e,i){var r=o.create(t);return e&&e.append(r,i),r},createFullBox:function(t,e,r){var o=i.createBox(t,e,r);return o.version=0,o.flags=0,o},Utils:{}};i.Utils.dataViewToString=function(t,e){var i=e||"utf-8";if("undefined"!=typeof TextDecoder)return new TextDecoder(i).decode(t);var r=[],o=0;if("utf-8"===i)for(;o<t.byteLength;){var n=t.getUint8(o++);n<128||(n<224?(n=(31&n)<<6,n|=63&t.getUint8(o++)):n<240?(n=(15&n)<<12,n|=(63&t.getUint8(o++))<<6,n|=63&t.getUint8(o++)):(n=(7&n)<<18,n|=(63&t.getUint8(o++))<<12,n|=(63&t.getUint8(o++))<<6,n|=63&t.getUint8(o++))),r.push(String.fromCharCode(n))}else for(;o<t.byteLength;)r.push(String.fromCharCode(t.getUint8(o++)));return r.join("")},i.Utils.utf8ToByteArray=function(t){var e,i;if("undefined"!=typeof TextEncoder)e=(new TextEncoder).encode(t);else for(e=[],i=0;i<t.length;++i){var r=t.charCodeAt(i);r<128?e.push(r):r<2048?(e.push(192|r>>6),e.push(128|63&r)):r<65536?(e.push(224|r>>12),e.push(128|63&r>>6),e.push(128|63&r)):(e.push(240|r>>18),e.push(128|63&r>>12),e.push(128|63&r>>6),e.push(128|63&r))}return e},i.Utils.appendBox=function(t,e,i){if(e._offset=t._cursor.offset,e._root=t._root?t._root:t,e._raw=t._raw,e._parent=t,-1!==i)if(null!=i){var r,o=-1;if("number"==typeof i)o=i;else{if("string"==typeof i)r=i;else{if("object"!=typeof i||!i.type)return void t.boxes.push(e);r=i.type}for(var n=0;n<t.boxes.length;n++)if(r===t.boxes[n].type){o=n+1;break}}t.boxes.splice(o,0,e)}else t.boxes.push(e)},e.parseBuffer=i.parseBuffer,e.addBoxProcessor=i.addBoxProcessor,e.createFile=i.createFile,e.createBox=i.createBox,e.createFullBox=i.createFullBox,e.Utils=i.Utils,i.Cursor=function(t){this.offset=void 0===t?0:t};var r=function(t){this._cursor=new i.Cursor,this.boxes=[],t&&(this._raw=new DataView(t))};r.prototype.fetch=function(t){var e=this.fetchAll(t,!0);return e.length?e[0]:null},r.prototype.fetchAll=function(t,e){var i=[];return r._sweep.call(this,t,i,e),i},r.prototype.parse=function(){for(this._cursor.offset=0,this.boxes=[];this._cursor.offset<this._raw.byteLength;){var t=o.parse(this);if(void 0===t.type)break;this.boxes.push(t)}return this},r._sweep=function(t,e,i){for(var o in this.type&&this.type==t&&e.push(this),this.boxes){if(e.length&&i)return;r._sweep.call(this.boxes[o],t,e,i)}},r.prototype.write=function(){var t,e=0;for(t=0;t<this.boxes.length;t++)e+=this.boxes[t].getLength(!1);var i=new Uint8Array(e);for(this._rawo=new DataView(i.buffer),this.bytes=i,this._cursor.offset=0,t=0;t<this.boxes.length;t++)this.boxes[t].write();return i.buffer},r.prototype.append=function(t,e){i.Utils.appendBox(this,t,e)};var o=function(){this._cursor=new i.Cursor};o.parse=function(t){var e=new o;return e._offset=t._cursor.offset,e._root=t._root?t._root:t,e._raw=t._raw,e._parent=t,e._parseBox(),t._cursor.offset=e._raw.byteOffset+e._raw.byteLength,e},o.create=function(t){var e=new o;return e.type=t,e.boxes=[],e},o.prototype._boxContainers=["dinf","edts","mdia","meco","mfra","minf","moof","moov","mvex","stbl","strk","traf","trak","tref","udta","vttc","sinf","schi","encv","enca","meta","grpl","prsl"],o.prototype._boxProcessors={},o.prototype._procField=function(t,e,i){this._parsing?this[t]=this._readField(e,i):this._writeField(e,i,this[t])},o.prototype._procFieldArray=function(t,e,i,r){var o;if(this._parsing)for(this[t]=[],o=0;o<e;o++)this[t][o]=this._readField(i,r);else for(o=0;o<this[t].length;o++)this._writeField(i,r,this[t][o])},o.prototype._procFullBox=function(){this._procField("version","uint",8),this._procField("flags","uint",24)},o.prototype._procEntries=function(t,e,i){var r;if(this._parsing)for(this[t]=[],r=0;r<e;r++)this[t].push({}),i.call(this,this[t][r]);else for(r=0;r<e;r++)i.call(this,this[t][r])},o.prototype._procSubEntries=function(t,e,i,r){var o;if(this._parsing)for(t[e]=[],o=0;o<i;o++)t[e].push({}),r.call(this,t[e][o]);else for(o=0;o<i;o++)r.call(this,t[e][o])},o.prototype._procEntryField=function(t,e,i,r){this._parsing?t[e]=this._readField(i,r):this._writeField(i,r,t[e])},o.prototype._procSubBoxes=function(t,e){var i;if(this._parsing)for(this[t]=[],i=0;i<e;i++)this[t].push(o.parse(this));else for(i=0;i<e;i++)this._rawo?this[t][i].write():this.size+=this[t][i].getLength()},o.prototype._readField=function(t,e){switch(t){case"uint":return this._readUint(e);case"int":return this._readInt(e);case"template":return this._readTemplate(e);case"string":return-1===e?this._readTerminatedString():this._readString(e);case"data":return this._readData(e);case"utf8":return this._readUTF8String();case"utf8string":return this._readUTF8TerminatedString();default:return-1}},o.prototype._readInt=function(t){var e=null,i=this._cursor.offset-this._raw.byteOffset;switch(t){case 8:e=this._raw.getInt8(i);break;case 16:e=this._raw.getInt16(i);break;case 32:e=this._raw.getInt32(i);break;case 64:var r=this._raw.getInt32(i),o=this._raw.getInt32(i+4);e=r*Math.pow(2,32)+o}return this._cursor.offset+=t>>3,e},o.prototype._readUint=function(t){var e,i,r=null,o=this._cursor.offset-this._raw.byteOffset;switch(t){case 8:r=this._raw.getUint8(o);break;case 16:r=this._raw.getUint16(o);break;case 24:r=((e=this._raw.getUint16(o))<<8)+(i=this._raw.getUint8(o+2));break;case 32:r=this._raw.getUint32(o);break;case 64:e=this._raw.getUint32(o),i=this._raw.getUint32(o+4),r=e*Math.pow(2,32)+i}return this._cursor.offset+=t>>3,r},o.prototype._readString=function(t){for(var e="",i=0;i<t;i++){var r=this._readUint(8);e+=String.fromCharCode(r)}return e},o.prototype._readTemplate=function(t){return this._readUint(t/2)+this._readUint(t/2)/Math.pow(2,t/2)},o.prototype._readTerminatedString=function(){for(var t="";this._cursor.offset-this._offset<this._raw.byteLength;){var e=this._readUint(8);if(0===e)break;t+=String.fromCharCode(e)}return t},o.prototype._readData=function(t){var e=t>0?t:this._raw.byteLength-(this._cursor.offset-this._offset);if(e>0){var i=new Uint8Array(this._raw.buffer,this._cursor.offset,e);return this._cursor.offset+=e,i}return null},o.prototype._readUTF8String=function(){var t=this._raw.byteLength-(this._cursor.offset-this._offset),e=null;return t>0&&(e=new DataView(this._raw.buffer,this._cursor.offset,t),this._cursor.offset+=t),e?i.Utils.dataViewToString(e):e},o.prototype._readUTF8TerminatedString=function(){var t=this._raw.byteLength-(this._cursor.offset-this._offset),e=null;if(t>0){var r;for(e=new DataView(this._raw.buffer,this._cursor.offset,t),r=0;r<t&&0!==e.getUint8(r);r++);e=new DataView(this._raw.buffer,this._cursor.offset,r),this._cursor.offset+=Math.min(r+1,t)}return e?i.Utils.dataViewToString(e):e},o.prototype._parseBox=function(){if(this._parsing=!0,this._cursor.offset=this._offset,this._offset+8>this._raw.buffer.byteLength)this._root._incomplete=!0;else{switch(this._procField("size","uint",32),this._procField("type","string",4),1===this.size&&this._procField("largesize","uint",64),"uuid"===this.type&&this._procFieldArray("usertype",16,"uint",8),this.size){case 0:this._raw=new DataView(this._raw.buffer,this._offset);break;case 1:this._offset+this.size>this._raw.buffer.byteLength?(this._incomplete=!0,this._root._incomplete=!0):this._raw=new DataView(this._raw.buffer,this._offset,this.largesize);break;default:this._offset+this.size>this._raw.buffer.byteLength?(this._incomplete=!0,this._root._incomplete=!0):this._raw=new DataView(this._raw.buffer,this._offset,this.size)}this._incomplete||(this._boxProcessors[this.type]&&this._boxProcessors[this.type].call(this),-1!==this._boxContainers.indexOf(this.type)?this._parseContainerBox():this._data=this._readData())}},o.prototype._parseFullBox=function(){this.version=this._readUint(8),this.flags=this._readUint(24)},o.prototype._parseContainerBox=function(){for(this.boxes=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.boxes.push(o.parse(this))},o.prototype.append=function(t,e){i.Utils.appendBox(this,t,e)},o.prototype.getLength=function(){if(this._parsing=!1,this._rawo=null,this.size=0,this._procField("size","uint",32),this._procField("type","string",4),1===this.size&&this._procField("largesize","uint",64),"uuid"===this.type&&this._procFieldArray("usertype",16,"uint",8),this._boxProcessors[this.type]&&this._boxProcessors[this.type].call(this),-1!==this._boxContainers.indexOf(this.type))for(var t=0;t<this.boxes.length;t++)this.size+=this.boxes[t].getLength();return this._data&&this._writeData(this._data),this.size},o.prototype.write=function(){switch(this._parsing=!1,this._cursor.offset=this._parent._cursor.offset,this.size){case 0:this._rawo=new DataView(this._parent._rawo.buffer,this._cursor.offset,this.parent._rawo.byteLength-this._cursor.offset);break;case 1:this._rawo=new DataView(this._parent._rawo.buffer,this._cursor.offset,this.largesize);break;default:this._rawo=new DataView(this._parent._rawo.buffer,this._cursor.offset,this.size)}if(this._procField("size","uint",32),this._procField("type","string",4),1===this.size&&this._procField("largesize","uint",64),"uuid"===this.type&&this._procFieldArray("usertype",16,"uint",8),this._boxProcessors[this.type]&&this._boxProcessors[this.type].call(this),-1!==this._boxContainers.indexOf(this.type))for(var t=0;t<this.boxes.length;t++)this.boxes[t].write();return this._data&&this._writeData(this._data),this._parent._cursor.offset+=this.size,this.size},o.prototype._writeInt=function(t,e){if(this._rawo){var i=this._cursor.offset-this._rawo.byteOffset;switch(t){case 8:this._rawo.setInt8(i,e);break;case 16:this._rawo.setInt16(i,e);break;case 32:this._rawo.setInt32(i,e);break;case 64:var r=Math.floor(e/Math.pow(2,32)),o=e-r*Math.pow(2,32);this._rawo.setUint32(i,r),this._rawo.setUint32(i+4,o)}this._cursor.offset+=t>>3}else this.size+=t>>3},o.prototype._writeUint=function(t,e){if(this._rawo){var i,r,o=this._cursor.offset-this._rawo.byteOffset;switch(t){case 8:this._rawo.setUint8(o,e);break;case 16:this._rawo.setUint16(o,e);break;case 24:i=(16776960&e)>>8,r=255&e,this._rawo.setUint16(o,i),this._rawo.setUint8(o+2,r);break;case 32:this._rawo.setUint32(o,e);break;case 64:r=e-(i=Math.floor(e/Math.pow(2,32)))*Math.pow(2,32),this._rawo.setUint32(o,i),this._rawo.setUint32(o+4,r)}this._cursor.offset+=t>>3}else this.size+=t>>3},o.prototype._writeString=function(t,e){for(var i=0;i<t;i++)this._writeUint(8,e.charCodeAt(i))},o.prototype._writeTerminatedString=function(t){if(0!==t.length){for(var e=0;e<t.length;e++)this._writeUint(8,t.charCodeAt(e));this._writeUint(8,0)}},o.prototype._writeTemplate=function(t,e){var i=Math.floor(e),r=(e-i)*Math.pow(2,t/2);this._writeUint(t/2,i),this._writeUint(t/2,r)},o.prototype._writeData=function(t){if(t)if(this._rawo){if(t instanceof Array){for(var e=this._cursor.offset-this._rawo.byteOffset,i=0;i<t.length;i++)this._rawo.setInt8(e+i,t[i]);this._cursor.offset+=t.length}t instanceof Uint8Array&&(this._root.bytes.set(t,this._cursor.offset),this._cursor.offset+=t.length)}else this.size+=t.length},o.prototype._writeUTF8String=function(t){var e=i.Utils.utf8ToByteArray(t);if(this._rawo)for(var r=new DataView(this._rawo.buffer,this._cursor.offset,e.length),o=0;o<e.length;o++)r.setUint8(o,e[o]);else this.size+=e.length},o.prototype._writeField=function(t,e,i){switch(t){case"uint":this._writeUint(e,i);break;case"int":this._writeInt(e,i);break;case"template":this._writeTemplate(e,i);break;case"string":-1==e?this._writeTerminatedString(i):this._writeString(e,i);break;case"data":this._writeData(i);break;case"utf8":this._writeUTF8String(i)}},o.prototype._boxProcessors.ardi=function(){this._procFullBox(),this._procField("audio_rendering_indication","uint",8)},o.prototype._boxProcessors.avc1=o.prototype._boxProcessors.avc2=o.prototype._boxProcessors.avc3=o.prototype._boxProcessors.avc4=o.prototype._boxProcessors.hvc1=o.prototype._boxProcessors.hev1=o.prototype._boxProcessors.encv=function(){this._procFieldArray("reserved1",6,"uint",8),this._procField("data_reference_index","uint",16),this._procField("pre_defined1","uint",16),this._procField("reserved2","uint",16),this._procFieldArray("pre_defined2",3,"uint",32),this._procField("width","uint",16),this._procField("height","uint",16),this._procField("horizresolution","template",32),this._procField("vertresolution","template",32),this._procField("reserved3","uint",32),this._procField("frame_count","uint",16),this._procFieldArray("compressorname",32,"uint",8),this._procField("depth","uint",16),this._procField("pre_defined3","int",16),this._procField("config","data",-1)},o.prototype._boxProcessors.ctts=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procEntries("entries",this.entry_count,(function(t){this._procEntryField(t,"sample_count","uint",32),this._procEntryField(t,"sample_offset",1===this.version?"int":"uint",32)}))},o.prototype._boxProcessors.dref=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procSubBoxes("entries",this.entry_count)},o.prototype._boxProcessors.elng=function(){this._procFullBox(),this._procField("extended_language","utf8string")},o.prototype._boxProcessors.elst=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procEntries("entries",this.entry_count,(function(t){this._procEntryField(t,"segment_duration","uint",1===this.version?64:32),this._procEntryField(t,"media_time","int",1===this.version?64:32),this._procEntryField(t,"media_rate_integer","int",16),this._procEntryField(t,"media_rate_fraction","int",16)}))},o.prototype._boxProcessors.emsg=function(){this._procFullBox(),1==this.version?(this._procField("timescale","uint",32),this._procField("presentation_time","uint",64),this._procField("event_duration","uint",32),this._procField("id","uint",32),this._procField("scheme_id_uri","string",-1),this._procField("value","string",-1)):(this._procField("scheme_id_uri","string",-1),this._procField("value","string",-1),this._procField("timescale","uint",32),this._procField("presentation_time_delta","uint",32),this._procField("event_duration","uint",32),this._procField("id","uint",32)),this._procField("message_data","data",-1)},o.prototype._boxProcessors.free=o.prototype._boxProcessors.skip=function(){this._procField("data","data",-1)},o.prototype._boxProcessors.frma=function(){this._procField("data_format","uint",32)},o.prototype._boxProcessors.ftyp=o.prototype._boxProcessors.styp=function(){this._procField("major_brand","string",4),this._procField("minor_version","uint",32);var t=-1;this._parsing&&(t=(this._raw.byteLength-(this._cursor.offset-this._raw.byteOffset))/4),this._procFieldArray("compatible_brands",t,"string",4)},o.prototype._boxProcessors.hdlr=function(){this._procFullBox(),this._procField("pre_defined","uint",32),this._procField("handler_type","string",4),this._procFieldArray("reserved",3,"uint",32),this._procField("name","string",-1)},o.prototype._boxProcessors.imda=function(){this._procField("imda_identifier","uint",32),this._procField("data","data",-1)},o.prototype._boxProcessors.kind=function(){this._procFullBox(),this._procField("schemeURI","utf8string"),this._procField("value","utf8string")},o.prototype._boxProcessors.labl=function(){this._procFullBox(),this.is_group_label=!!(1&this.flags),this._procField("label_id","uint",16),this._procField("language","utf8string"),this._procField("label","utf8string")},o.prototype._boxProcessors.mdat=function(){this._procField("data","data",-1)},o.prototype._boxProcessors.mdhd=function(){this._procFullBox(),this._procField("creation_time","uint",1==this.version?64:32),this._procField("modification_time","uint",1==this.version?64:32),this._procField("timescale","uint",32),this._procField("duration","uint",1==this.version?64:32),this._parsing||"string"!=typeof this.language||(this.language=this.language.charCodeAt(0)-96<<10|this.language.charCodeAt(1)-96<<5|this.language.charCodeAt(2)-96),this._procField("language","uint",16),this._parsing&&(this.language=String.fromCharCode(96+(this.language>>10&31),96+(this.language>>5&31),96+(31&this.language))),this._procField("pre_defined","uint",16)},o.prototype._boxProcessors.mehd=function(){this._procFullBox(),this._procField("fragment_duration","uint",1==this.version?64:32)},o.prototype._boxProcessors.meta=function(){this._procFullBox()},o.prototype._boxProcessors.mfhd=function(){this._procFullBox(),this._procField("sequence_number","uint",32)},o.prototype._boxProcessors.mfro=function(){this._procFullBox(),this._procField("mfra_size","uint",32)},o.prototype._boxProcessors.mp4a=o.prototype._boxProcessors.enca=function(){this._procFieldArray("reserved1",6,"uint",8),this._procField("data_reference_index","uint",16),this._procFieldArray("reserved2",2,"uint",32),this._procField("channelcount","uint",16),this._procField("samplesize","uint",16),this._procField("pre_defined","uint",16),this._procField("reserved3","uint",16),this._procField("samplerate","template",32),this._procField("esds","data",-1)},o.prototype._boxProcessors.mvhd=function(){this._procFullBox(),this._procField("creation_time","uint",1==this.version?64:32),this._procField("modification_time","uint",1==this.version?64:32),this._procField("timescale","uint",32),this._procField("duration","uint",1==this.version?64:32),this._procField("rate","template",32),this._procField("volume","template",16),this._procField("reserved1","uint",16),this._procFieldArray("reserved2",2,"uint",32),this._procFieldArray("matrix",9,"template",32),this._procFieldArray("pre_defined",6,"uint",32),this._procField("next_track_ID","uint",32)},o.prototype._boxProcessors.payl=function(){this._procField("cue_text","utf8")},o.prototype._boxProcessors.prft=function(){this._procFullBox(),this._procField("reference_track_ID","uint",32),this._procField("ntp_timestamp_sec","uint",32),this._procField("ntp_timestamp_frac","uint",32),this._procField("media_time","uint",1==this.version?64:32)},o.prototype._boxProcessors.prsl=function(){this._procFullBox(),this._procField("group_id","uint",32),this._procField("num_entities_in_group","uint",32),this._procEntries("entities",this.num_entities_in_group,(function(t){this._procEntryField(t,"entity_id","uint",32)})),4096&this.flags&&this._procField("preselection_tag","utf8string"),8192&this.flags&&this._procField("selection_priority","uint",8),16384&this.flags&&this._procField("interleaving_tag","utf8string")},o.prototype._boxProcessors.pssh=function(){this._procFullBox(),this._procFieldArray("SystemID",16,"uint",8),this._procField("DataSize","uint",32),this._procFieldArray("Data",this.DataSize,"uint",8)},o.prototype._boxProcessors.schm=function(){this._procFullBox(),this._procField("scheme_type","uint",32),this._procField("scheme_version","uint",32),1&this.flags&&this._procField("scheme_uri","string",-1)},o.prototype._boxProcessors.sdtp=function(){this._procFullBox();var t=-1;this._parsing&&(t=this._raw.byteLength-(this._cursor.offset-this._raw.byteOffset)),this._procFieldArray("sample_dependency_table",t,"uint",8)},o.prototype._boxProcessors.sidx=function(){this._procFullBox(),this._procField("reference_ID","uint",32),this._procField("timescale","uint",32),this._procField("earliest_presentation_time","uint",1==this.version?64:32),this._procField("first_offset","uint",1==this.version?64:32),this._procField("reserved","uint",16),this._procField("reference_count","uint",16),this._procEntries("references",this.reference_count,(function(t){this._parsing||(t.reference=(1&t.reference_type)<<31,t.reference|=2147483647&t.referenced_size,t.sap=(1&t.starts_with_SAP)<<31,t.sap|=(3&t.SAP_type)<<28,t.sap|=268435455&t.SAP_delta_time),this._procEntryField(t,"reference","uint",32),this._procEntryField(t,"subsegment_duration","uint",32),this._procEntryField(t,"sap","uint",32),this._parsing&&(t.reference_type=t.reference>>31&1,t.referenced_size=2147483647&t.reference,t.starts_with_SAP=t.sap>>31&1,t.SAP_type=t.sap>>28&7,t.SAP_delta_time=268435455&t.sap)}))},o.prototype._boxProcessors.smhd=function(){this._procFullBox(),this._procField("balance","uint",16),this._procField("reserved","uint",16)},o.prototype._boxProcessors.ssix=function(){this._procFullBox(),this._procField("subsegment_count","uint",32),this._procEntries("subsegments",this.subsegment_count,(function(t){this._procEntryField(t,"ranges_count","uint",32),this._procSubEntries(t,"ranges",t.ranges_count,(function(t){this._procEntryField(t,"level","uint",8),this._procEntryField(t,"range_size","uint",24)}))}))},o.prototype._boxProcessors.stsd=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procSubBoxes("entries",this.entry_count)},o.prototype._boxProcessors.sttg=function(){this._procField("settings","utf8")},o.prototype._boxProcessors.stts=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procEntries("entries",this.entry_count,(function(t){this._procEntryField(t,"sample_count","uint",32),this._procEntryField(t,"sample_delta","uint",32)}))},o.prototype._boxProcessors.subs=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procEntries("entries",this.entry_count,(function(t){this._procEntryField(t,"sample_delta","uint",32),this._procEntryField(t,"subsample_count","uint",16),this._procSubEntries(t,"subsamples",t.subsample_count,(function(t){this._procEntryField(t,"subsample_size","uint",1===this.version?32:16),this._procEntryField(t,"subsample_priority","uint",8),this._procEntryField(t,"discardable","uint",8),this._procEntryField(t,"codec_specific_parameters","uint",32)}))}))},o.prototype._boxProcessors.tenc=function(){this._procFullBox(),this._procField("default_IsEncrypted","uint",24),this._procField("default_IV_size","uint",8),this._procFieldArray("default_KID",16,"uint",8)},o.prototype._boxProcessors.tfdt=function(){this._procFullBox(),this._procField("baseMediaDecodeTime","uint",1==this.version?64:32)},o.prototype._boxProcessors.tfhd=function(){this._procFullBox(),this._procField("track_ID","uint",32),1&this.flags&&this._procField("base_data_offset","uint",64),2&this.flags&&this._procField("sample_description_offset","uint",32),8&this.flags&&this._procField("default_sample_duration","uint",32),16&this.flags&&this._procField("default_sample_size","uint",32),32&this.flags&&this._procField("default_sample_flags","uint",32)},o.prototype._boxProcessors.tfra=function(){this._procFullBox(),this._procField("track_ID","uint",32),this._parsing||(this.reserved=0,this.reserved|=(48&this.length_size_of_traf_num)<<4,this.reserved|=(12&this.length_size_of_trun_num)<<2,this.reserved|=3&this.length_size_of_sample_num),this._procField("reserved","uint",32),this._parsing&&(this.length_size_of_traf_num=(48&this.reserved)>>4,this.length_size_of_trun_num=(12&this.reserved)>>2,this.length_size_of_sample_num=3&this.reserved),this._procField("number_of_entry","uint",32),this._procEntries("entries",this.number_of_entry,(function(t){this._procEntryField(t,"time","uint",1===this.version?64:32),this._procEntryField(t,"moof_offset","uint",1===this.version?64:32),this._procEntryField(t,"traf_number","uint",8*(this.length_size_of_traf_num+1)),this._procEntryField(t,"trun_number","uint",8*(this.length_size_of_trun_num+1)),this._procEntryField(t,"sample_number","uint",8*(this.length_size_of_sample_num+1))}))},o.prototype._boxProcessors.tkhd=function(){this._procFullBox(),this._procField("creation_time","uint",1==this.version?64:32),this._procField("modification_time","uint",1==this.version?64:32),this._procField("track_ID","uint",32),this._procField("reserved1","uint",32),this._procField("duration","uint",1==this.version?64:32),this._procFieldArray("reserved2",2,"uint",32),this._procField("layer","uint",16),this._procField("alternate_group","uint",16),this._procField("volume","template",16),this._procField("reserved3","uint",16),this._procFieldArray("matrix",9,"template",32),this._procField("width","template",32),this._procField("height","template",32)},o.prototype._boxProcessors.trex=function(){this._procFullBox(),this._procField("track_ID","uint",32),this._procField("default_sample_description_index","uint",32),this._procField("default_sample_duration","uint",32),this._procField("default_sample_size","uint",32),this._procField("default_sample_flags","uint",32)},o.prototype._boxProcessors.trun=function(){this._procFullBox(),this._procField("sample_count","uint",32),1&this.flags&&this._procField("data_offset","int",32),4&this.flags&&this._procField("first_sample_flags","uint",32),this._procEntries("samples",this.sample_count,(function(t){256&this.flags&&this._procEntryField(t,"sample_duration","uint",32),512&this.flags&&this._procEntryField(t,"sample_size","uint",32),1024&this.flags&&this._procEntryField(t,"sample_flags","uint",32),2048&this.flags&&this._procEntryField(t,"sample_composition_time_offset",1===this.version?"int":"uint",32)}))},o.prototype._boxProcessors["url "]=o.prototype._boxProcessors["urn "]=function(){this._procFullBox(),"urn "===this.type&&this._procField("name","string",-1),this._procField("location","string",-1)},o.prototype._boxProcessors.vlab=function(){this._procField("source_label","utf8")},o.prototype._boxProcessors.vmhd=function(){this._procFullBox(),this._procField("graphicsmode","uint",16),this._procFieldArray("opcolor",3,"uint",16)},o.prototype._boxProcessors.vttC=function(){this._procField("config","utf8")},o.prototype._boxProcessors.vtte=function(){}},79841:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SmoothModuleDefinition=void 0;var r=i(16368);e.SmoothModuleDefinition={name:r.ModuleName.Smooth,module:function(){return{SmoothStreamingLoader:i(89079).A,createSmoothSegmentTemplateMPDHandler:i(63894).createSmoothSegmentTemplateMPDHandler}},dependencies:[r.ModuleName.EngineBitmovin,r.ModuleName.DASH]},e.default=e.SmoothModuleDefinition},89079:function(t,e,i){"use strict";e.A=void 0;var r=i(25550),o=i(28764),n=i(63546),s=i(35148),a=i(18665),c=i(62510),h=i(88005),p=i(59181),u=i(90637),l=i(36564),d=i(67345),f=i(76650),_=i(8272),m=i(91520),g=i(16368),y=i(26608),v=i(56855),x=i(25666),F=function(){function t(t,e){var i=this;this._hasSetResetRetriesWhenOffline=!1,this.onFailureHandler=function(t,e){if((null==e?void 0:e.name)!==u.RequestError.Canceled){var n=new o.PlayerError(r.ErrorCode.SOURCE_COULD_NOT_LOAD_MANIFEST,{sourceUrl:t.url,statusCode:t.status},"Failed to load the manifest: ".concat(t.status," ").concat(t.statusText,"."),i.sourceContext.sourceIdentifier);0===t.status&&(n=e&&e.name===u.RequestError.TimedOut?new o.PlayerError(r.ErrorCode.NETWORK_MANIFEST_DOWNLOAD_TIMEOUT,{sourceUrl:t.url},"Failed to load the manifest: the request timed out.",i.sourceContext.sourceIdentifier):new o.PlayerError(r.ErrorCode.SOURCE_COULD_NOT_LOAD_MANIFEST,{url:t.url,statusCode:t.status,statusText:t.statusText},"Failed to load the smooth manifest due to CORS restrictions or because the client is offline.",i.sourceContext.sourceIdentifier)),i.context.eventHandler.fireError(n)}},this.load=function(t){return i.manifestUrl=t||i.manifestUrl,i.manifestUrl?i.getManifestHttpResponse(i.manifestUrl).then(i.handleRequestSuccess).catch((function(t){return(null==t?void 0:t.message)===u.RequestError.Canceled?Promise.reject():(i.context.logger.debug("Error while loading smooth manifest",t),Promise.reject(t))})):(i.context.eventHandler.fireError(new o.PlayerError(r.ErrorCode.SOURCE_INVALID,void 0,"Cannot load the manifest: no URL was provided.")),Promise.reject("No manifest url provided"))},this.handleRequestSuccess=function(t){var e;if(!i.loader)return null;if(i.context.serviceManager.maybeCall(a.ServiceName.ManifestCachingService,(function(e){return e.cacheHttpResponse(t,t.url)}),null,i.sourceContext.sourceIdentifier),(0,l.isDownloadTimeInformationValid)(t)&&i.context.store.dispatch((0,f.addMetricsValue)("default",_.MetricType.DownloadInformation,{bytes:t.length,time:t.elapsedTime,timeToFirstByte:t.timeToFirstByte})),null===t.body||t.length<1)return i.context.eventHandler.fireError(new o.PlayerError(r.ErrorCode.SOURCE_MANIFEST_INVALID)),null;i.manifestUrl=t.url;var n=t.body?i.parseXmlResponse(t.body):null;n&&"dynamic"===n._type&&(!0===(null===(e=i.settings)||void 0===e?void 0:e.INFINITE_RETRIES_FOR_LIVE)&&!i._hasSetResetRetriesWhenOffline&&(i.loader.setResetRetriesWhenOffline(!0),i._hasSetResetRetriesWhenOffline=!0));return n||null},this.context=t,this.sourceContext=e,this.settings=t.settings,this.loader=new h.DefaultContentLoader(t,{requestType:d.HttpRequestType.MANIFEST_SMOOTH,onFailure:this.onFailureHandler,maxRetries:this.settings.MAX_MPD_RETRIES,retryDelay:this.settings.MPD_RETRY_DELAY,resetRetriesWhenOffline:!1}),this.parseXML=m.ModuleManager.get(g.ModuleName.XML).parseXML}return t.prototype.dispose=function(){var t;this.smoothStreamingParser&&this.smoothStreamingParser.dispose(),this.smoothStreamingParser=null,this.smoothStreamingConverter&&this.smoothStreamingConverter.dispose(),this.smoothStreamingConverter=null,this.settings=null,null===(t=this.loader)||void 0===t||t.cancel(),this.loader=null,this._hasSetResetRetriesWhenOffline=!1},t.prototype.getManifestHttpResponse=function(t){var e,i=this.context.serviceManager.maybeCall(a.ServiceName.ManifestCachingService,(function(e){return e.getHttpResponse(t)}),null,this.sourceContext.sourceIdentifier);if(i)return Promise.resolve(i);var r=null===(e=this.sourceContext.source.options)||void 0===e?void 0:e.manifestWithCredentials;return this.loader.load(t,d.HttpRequestMethod.GET,null,null,null,r,p.ManifestMimeType.Smooth)},t.prototype.parseXmlResponse=function(t){var e;try{if(null===t)throw"input must not be null";var i=this.parseXML(t,{decodeEntities:!0});if(!1===i.success)throw new Error("manifest parsing failed");e=i.parsed}catch(t){return void this.context.eventHandler.fireError(new o.PlayerError(r.ErrorCode.SOURCE_MANIFEST_INVALID))}if(this.validateManifest(e)){this.smoothStreamingParser?this.smoothStreamingParser.updateManifest(e,this.manifestUrl):this.smoothStreamingParser=new v.SmoothStreamingParser(this.context,e,this.manifestUrl);var n=this.smoothStreamingParser.getParsedManifest();return this.smoothStreamingConverter?this.smoothStreamingConverter.updateManifest(n,Date.now()):this.smoothStreamingConverter=new x.SmoothToMpdConverter(this.context,n,Date.now()),this.smoothStreamingConverter.getDASHManifest()}},t.prototype.createInitSegmentForRepresentation=function(t,e,i){if(!t)return this.context.logger.debug("Cannot create init segment for undefined representation."),void this.context.eventHandler.dispatchEvent(c.PlayerEvent.Warning,new n.PlayerWarning(s.WarningCode.SOURCE_SMOOTH_REPRESENTATION_MISSING));if(this.smoothStreamingParser){var a=this.smoothStreamingParser.getParsedManifest(),h=a.clips[Number(t._internalId.periodId)].streamIndexes.find((function(e){return 0===t._id.indexOf(e.name)}));if(!h)return this.context.logger.log("Cannot generate InitSegment, unable to find smooth StreamIndex for: ",t),this.context.eventHandler.fireError(new o.PlayerError(r.ErrorCode.SOURCE_MANIFEST_INVALID,null,"Unable to generate an InitSegment because StreamIndex disappeared")),null;var p=h.qualityInformation.find((function(e){return e.bitrate===t._bandwidth}));if(!p)return this.context.logger.log("Cannot generate InitSegment, unable to find smooth QualityInfo for: ",t),this.context.eventHandler.fireError(new o.PlayerError(r.ErrorCode.SOURCE_MANIFEST_INVALID,null,"Unable to generate InitSegment because QualityInformation disappeared")),null;var u={type:h.type,timescale:h.timeScale||a.timeScale||v.DEFAULT_TIMESCALE_FACTOR,trackId:i,codecPrivateData:p.codecPrivateData,lang:h.language,bandwidth:p.bitrate,codecs:t._codecs,id:t._id,internalRepresentationId:t._internalId,periodDuration:a.duration};if("video"===h.type){var l=p;u.width=l.maxWidth,u.height=l.maxHeight}else if("audio"===h.type){var d=p;u.audioChannels=d.noChannels,u.audioSamplingRate=d.samplingRate}var f=new y.InitSegmentGenerator(this.context,u,a.protection);return[f.getParsedData(),f.generateArrayBuffer()]}this.context.eventHandler.dispatchEvent(c.PlayerEvent.Warning,new n.PlayerWarning(s.WarningCode.SOURCE_SMOOTH_INVALID_INIT_DATA))},t.prototype.validateManifest=function(t){return!(!t||!t.hasOwnProperty("SmoothStreamingMedia")&&!t.SmoothStreamingMedia)||(this.context.eventHandler.fireError(new o.PlayerError(r.ErrorCode.SOURCE_MANIFEST_INVALID)),!1)},t.prototype.updateRepresentation=function(t){return Promise.resolve(t)},t.prototype.stopRepresentationUpdate=function(t){return Promise.resolve()},t}();e.A=F}},function(t){return function(e){return t(t.s=e)}(79841)}])}));
})();
