/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.hls=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player.hls=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[458],{293:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.HlsBackupStreamsHandler=t.BackupStreamErrors=void 0;var r,a=n(18665),o=n(62510),s=n(23234),u=n(27279),l=n(79814),c=n(65114),d=n(99162),f=n(41735),p=n(77874),g=n(9827),m=n(53950),v=n(79367);!function(e){e.ALL_PENALIZED="ALL_PENALIZED",e.LOADING_FAILED="LOADING_FAILED",e.NO_BACKUP_STREAMS="NO_BACKUP_STREAMS"}(r||(t.BackupStreamErrors=r={}));var S=function(){function e(e,t,n){this.context=e,this.backupStreamInfo=t,this.updateRepresentation=n,this.backupStreamsMap={},this.penalizedStreams={},this.hasSourceLoadingFinished=!1,this.manifestService=e.serviceManager.get(a.ServiceName.ManifestService,e.sourceContext.sourceIdentifier),this.manifestSchedulingService=this.context.serviceManager.get(a.ServiceName.ManifestUpdateSchedulingService,this.context.sourceContext.sourceIdentifier)}return e.prototype.populateBackupStreamsMap=function(e){var t;this.backupStreamsMap=y(this.backupStreamInfo,e),null===(t=(0,u.getSourceStore)(this.context))||void 0===t||t.dispatch((0,v.setContentLocationId)(String(0)))},e.prototype.signalLoadingFinished=function(){this.hasSourceLoadingFinished=!0},e.prototype.switchToBackupStream=function(e,t){var n,i,a=this,s=null!==(i=null===(n=this.backupStreamsMap[e._mimeType])||void 0===n?void 0:n[e._id])&&void 0!==i?i:{};if(Object.keys(s).length<=1)return Promise.reject(r.NO_BACKUP_STREAMS);var l=this.getCurrentRepresentation(e._mimeType),c=(null==l?void 0:l._id)===e._id&&(null==l?void 0:l.Uri)!==e.Uri,f=I(e,t);if(c||!f)return this.context.logger.debug("Ignoring outdated failover request for ".concat(e._mimeType," rep ").concat(e._id),{hasPlaylistUriChanged:c,isSegmentInPlaylist:f}),Promise.resolve([l]);var p=T(s,e.Uri);if(void 0===p)return this.context.logger.debug("Could not find the failing rep in the current list of backup streams.",{id:e._id,uri:e.Uri,backupStreams:s}),Promise.reject(r.NO_BACKUP_STREAMS);this.penalizeLocationId(p),this.depenalizeExpiredStreams();var g=this.getNextLocationId(Object.keys(s),p);return void 0===g?(this.context.logger.debug("No other backup stream is available: all penalized."),Promise.reject(r.ALL_PENALIZED)):(this.context.logger.debug("Starting failover from stream ".concat(p," to ").concat(g)),this.triggerPlaylistRotation(e,p,g).then((function(e){var t,n,i,r=T(s,null!==(n=null===(t=e[0])||void 0===t?void 0:t.Uri)&&void 0!==n?n:"");return a.context.logger.debug("Successfully completed failover to stream index ".concat(r)),void 0!==r&&(null===(i=(0,u.getSourceStore)(a.context))||void 0===i||i.dispatch((0,v.setContentLocationId)(String(r))),a.context.eventHandler.dispatchEvent(o.PlayerEvent.ContentLocationChanged,{sourceLocationId:p,targetLocationId:r,reason:o.ContentLocationChangedReason.Failover})),e})).catch((function(e){return e===d.REPRESENTATION_UPDATE_CANCEL?Promise.reject(e):(a.context.logger.debug("Failover from stream ".concat(p," to ").concat(g," failed")),Promise.reject(r.LOADING_FAILED))})))},e.prototype.penalizeLocationId=function(e){this.context.logger.debug("Penalizing stream ".concat(e," for ").concat(this.context.settings.HLS_BACKUP_STREAM_PENALTY_DURATION,"s")),this.penalizedStreams[e]=s.TimingUtil.getHiResTimestamp()},e.prototype.depenalizeExpiredStreams=function(){var e=this,t=s.TimingUtil.getHiResTimestamp();Object.keys(this.penalizedStreams).forEach((function(n){var i=e.penalizedStreams[n];t-i>=e.context.settings.HLS_BACKUP_STREAM_PENALTY_DURATION&&(e.context.logger.debug("Depenalizing stream ".concat(n)),delete e.penalizedStreams[n])}))},e.prototype.triggerPlaylistRotation=function(e,t,n){var i=this,r=[e],a=e._mimeType===l.MimeType.AudioMp4?this.getCurrentRepresentation(l.MimeType.VideoMp4):this.getCurrentRepresentation(l.MimeType.AudioMp4);a&&this.hasSourceLoadingFinished&&r.push(a);var o=r.map((function(e){return i.rotatePlaylist(e,n).catch((function(n){var r;return n!==d.REPRESENTATION_UPDATE_CANCEL&&(i.context.logger.debug("Failed to rotate URL for rep ".concat(e._id,": ").concat(null!==(r=null==n?void 0:n.message)&&void 0!==r?r:n)),i.revertPlaylistRotation(e,t)),Promise.reject(n)}))}));return Promise.all(o)},e.prototype.rotatePlaylist=function(e,t){var n=this,i=(0,u.getSourceStore)(this.context);if(!i)return Promise.resolve(e);this.rotateAllRepresentations(e,t,i);var r=(0,p.getManifest)(i.getState());if(!r)return Promise.resolve(e);var a=e._internalId,o=(0,c.findRepresentation)(r,a);return e.Uri===(null==o?void 0:o.Uri)?(this.context.logger.debug("Current ".concat(e._mimeType," rep ").concat(e._id," does not need update")),Promise.resolve(e)):(this.context.logger.debug("Rotating URL for rep ".concat(e._id," from ").concat(e.Uri," to ").concat(o.Uri)),this.maybeStopManifestUpdater(e).then((function(){return n.updateRepresentation(o)})).then((function(e){return n.maybeRescheduleUpdate(e),e})))},e.prototype.revertPlaylistRotation=function(e,t){var n=(0,u.getSourceStore)(this.context);n&&(this.rotateAllRepresentations(e,t,n),this.context.logger.debug("Restoring URL for rep ".concat(e._id," to ").concat(e.Uri)),this.maybeRescheduleUpdate(e))},e.prototype.maybeStopManifestUpdater=function(e){var t;return(null===(t=this.manifestService)||void 0===t?void 0:t.isLive())&&this.manifestSchedulingService?this.manifestSchedulingService.stopRepresentationUpdates(e):Promise.resolve()},e.prototype.maybeRescheduleUpdate=function(e){var t,n;(null===(t=this.manifestService)||void 0===t?void 0:t.isLive())&&(null===(n=this.manifestSchedulingService)||void 0===n||n.scheduleRepresentationUpdate(e))},e.prototype.rotateAllRepresentations=function(e,t,n){var r,a=this;null===(r=this.manifestService)||void 0===r||r.getAllRepresentations().filter((function(t){return t._mimeType===e._mimeType})).forEach((function(r){var o,s,u,l,c=null!==(s=null===(o=a.backupStreamsMap[r._mimeType])||void 0===o?void 0:o[r._id])&&void 0!==s?s:{},d=null!==(u=r._name)&&void 0!==u?u:r._mimeType,p=null===(l=c[t])||void 0===l?void 0:l[d];if(p&&r.Uri!==p){var g=i(i({},r),{Uri:p,BaseURL:[{url:h(p,n.getState())}],SegmentList:r.SegmentList?[i(i({},r.SegmentList[0]),{SegmentURL:[]})]:r.SegmentList,_requestTimestamp:r._id!==e._id?0:r._requestTimestamp});n.dispatch((0,f.updateRepresentationAction)(g))}}))},e.prototype.getNextLocationId=function(e,t){for(var n=Number(t)||0,i=1;i<=e.length;i++){var r=(n+i)%e.length;if(void 0===this.penalizedStreams[r])return String(r)}},e.prototype.getCurrentRepresentation=function(e){var t,n,i=this.context.serviceManager.get(a.ServiceName.AdaptationService),r=null!==(t=null==i?void 0:i.getCurrentRepresentationId(e))&&void 0!==t?t:void 0;return null===(n=this.manifestService)||void 0===n?void 0:n.getRepresentationById(r)},e.prototype.getNextBackupUriForCodecProbing=function(e){var t,n,i,r,a,o=function(t){return Object.values(t).includes(e)},s=null!==(t=this.backupStreamInfo.find((function(e){return Object.values(e).some(o)})))&&void 0!==t?t:{},u=null!==(n=Object.keys(s).find((function(e){return o(s[e])})))&&void 0!==n?n:"",l=null!==(i=s[u])&&void 0!==i?i:{},c=null!==(r=Object.keys(l).find((function(t){return l[t]===e})))&&void 0!==r?r:"";return null===(a=E(s,c)[Number(u)+1])||void 0===a?void 0:a[c]},e.prototype.dispose=function(){this.penalizedStreams={}},e}();function h(e,t){var n,i,r,a,o=t&&null!==(a=null===(r=null===(i=null===(n=(0,p.getManifest)(t))||void 0===n?void 0:n.BaseURL)||void 0===i?void 0:i[0])||void 0===r?void 0:r.url)&&void 0!==a?a:"";return m.PlaylistUtils.getBaseUrl(o,e)}function y(e,t){var n,i=((n={})[l.MimeType.AudioMp4]={},n[l.MimeType.VideoMp4]={},n);return t.forEach((function(t){var n,r=t._mimeType===l.MimeType.AudioMp4?null!==(n=t._name)&&void 0!==n?n:l.MimeType.AudioMp4:l.MimeType.VideoMp4,a=e.find((function(e){return Object.values(e).find((function(e){return e[r]===t.Uri}))}));a&&(i[t._mimeType][t._id]=E(a,r))})),i}function T(e,t){return Object.keys(e).find((function(n){return Object.values(e[n]).includes(t)}))}function E(e,t){var n={},i=[];for(var r in e){var a=e[r][t];i.includes(a)||(i.push(a),n[r]=e[r])}return n}function I(e,t){var n,i;if(!t)return!0;var r=null===(i=null===(n=e.SegmentList)||void 0===n?void 0:n[0])||void 0===i?void 0:i.SegmentURL;return!!r&&-1!==(0,g.findSegmentUrlIndexWithinSegmentList)(t,r)}t.HlsBackupStreamsHandler=S},4383:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WebWorkerParser=void 0;var i=function(){function e(e){var t=this;this.isParsing=!1,this.jobQueue=[],this.onWorkerMessage=function(e){var n=e.data;if(["parsedPlaylist","error"].includes(n.action)&&0!==t.jobQueue.length){var i=t.jobQueue.shift();if("error"===n.action&&(null==i||i.rejectFn(n.data)),"parsedPlaylist"===n.action&&(null==i||i.resolveFn(n.data)),t.jobQueue.length>0){var r=t.jobQueue[0];t.parsingWorker.postMessage(r.message)}else t.isParsing=!1}};try{var i=n(80211);this.parsingWorker=new i,this.parsingWorker.onmessage=this.onWorkerMessage}catch(t){e.warn("WebWorkerParser: Could not create web worker",t)}}return e.prototype.parsePlaylist=function(e,t){var n=this;return new Promise((function(i,r){var a={action:"parseHlsPlaylist",payload:{data:e,options:t}};n.jobQueue.push({resolveFn:i,rejectFn:r,message:a}),n.isParsing||(n.isParsing=!0,n.parsingWorker.postMessage(a))}))},e.getInstance=function(t){return e.instance||(e.instance=new e(t)),e.instance},e.instance=null,e}();t.WebWorkerParser=i},13254:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isValidHlsPlaylist=m,t.getPeriod=v,t.parseHlsManifest=S,t.ensureMasterPlaylist=h,t.parsePlaylistInWorker=T,t.createDiscontinuitySequences=E,t.getTracksWithUnknownCodecs=I,t.updateMissingCodecsForPlaylists=_,t.findAllOrMatchingVariants=R;var i=n(16937),r=n(42283),a=n(76885),o=n(77874),s=n(87062),u=n(59692),l=n(53950),c=n(4383);function d(e){return e.playlists.length>0}function f(e){return e.segments.length>0}function p(e){return e.playlists||[]}function g(e){return e.media||[]}function m(e){return(0,l.isMasterPlaylist)(e)&&d(e)||(0,l.isMediaPlaylist)(e)&&f(e)}function v(e){return(0,o.getManifest)(e.getState()).Period[0]}function S(e,t){var n={parsePartTags:(0,s.isLowLatencyConfigured)(e)&&e.settings.LL_HLS};return(e.settings.HLS_PARSE_MANIFEST_IN_WORKER?T(t,e.logger,n):y(t,n)).then((function(t){if("playlists"in t){var n=t.playlists.filter((function(t){return t.attributes.BANDWIDTH>=e.settings.HLS_AUDIO_ONLY_THRESHOLD_BITRATE}));t.playlists=n.length?n:t.playlists}return t}))}function h(e,t,n){return(0,l.isMasterPlaylist)(t)?Promise.resolve(t):(e.logger.debug("HLS manifest is not a master playlist but a variant. Using master playlist template!"),S(e,["#EXTM3U","#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=0",n].join("\n")).then((function(e){if(!(0,l.isMasterPlaylist)(e))throw new Error("Template is not of type HLS MasterPlaylist");return e})))}function y(e,t){try{(0,i.trackPerformanceStart)("HlsPlaylist.parsePlaylist");var n=(0,u.parsePlaylist)(e,t);return(0,i.trackPerformanceEnd)("HlsPlaylist.parsePlaylist"),Promise.resolve(n)}catch(e){return Promise.reject(e)}}function T(e,t,n){return c.WebWorkerParser.getInstance(t).parsePlaylist(e,n)}function E(e,t){var n=l.PlaylistUtils.createDiscontinuitySequences(e,t);return n.forEach((function(e,t){l.PlaylistUtils.extrapolateProgramDateTime(e.segments),e.indexOfFirstSegment=0===t?0:n[t-1].indexOfLastSegment+1,e.indexOfLastSegment=e.indexOfFirstSegment+e.segments.length-1})),n}function I(e){return{audioTracksWithoutCodecs:e.media.filter((function(t){var n,i=t.attributes;if("AUDIO"!==i.TYPE||void 0===i.URI||void 0!==i.CODECS)return!1;var a=e.playlists.find((function(e){return e.attributes.AUDIO===i["GROUP-ID"]}));if(void 0===a||a.uri===i.URI)return!1;var o=null===(n=a.attributes.CODECS)||void 0===n?void 0:n.split(",");return void 0===o||o.every((function(e){return!r.CodecStringHelper.isAudioCodec(e)}))})),videoTracksWithoutCodecs:e.playlists.filter((function(e){return void 0===e.attributes.CODECS}))}}function _(e,t,n){O(e,R(t,n))}function b(e,t){return g(e).find((function(e){var n;return a.URLHelper.isSubUrl(t,null===(n=e.attributes)||void 0===n?void 0:n.URI)}))}function R(e,t){var n,i=p(e),r=i.some((function(e){return a.URLHelper.isSubUrl(t,e.uri)})),o=b(e,t);if(!r&&o){var s=null===(n=o.attributes)||void 0===n?void 0:n["GROUP-ID"];if(s)return i.filter((function(e){var t,n;return(null===(t=e.attributes)||void 0===t?void 0:t.AUDIO)===s||(null===(n=e.attributes)||void 0===n?void 0:n.VIDEO)===s}))}return i}function O(e,t){t.forEach((function(t){return t.attributes.CODECS=M(t.attributes.CODECS,e)}))}function A(e,t,n){return"string"!=typeof e||0===e.length?t:e+n+t}function M(e,t){var n=Object.keys(r.CodecStringHelper.getExtractedCodecStrings(e)),i=r.CodecStringHelper.getExtractedCodecStrings(t),a=Object.keys(i).filter((function(e){return!n.includes(e)})).map((function(e){return i[e]})).join(",");return a&&0!==a.length?A(e,a,","):e}},22675:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.M3u8Loader=t.HlsQueryParamNames=void 0,t.getParsedManifest=D;var r,a=n(25550),o=n(28764),s=n(18665),u=n(60997),l=n(62510),c=n(88005),d=n(90637),f=n(36564),p=n(67345),g=n(57782),m=n(76650),v=n(8272),S=n(79814),h=n(331),y=n(76885),T=n(99162),E=n(13034),I=n(4053),_=n(41735),b=n(93109),R=n(64697),O=n(293),A=n(13254),M=n(73725),N=n(32352),P=n(53950);function D(e,t,n){var i=n.url,r=n.manifestText,a=e.serviceManager.maybeCall(s.ServiceName.ManifestCachingService,(function(e){return e.getParsedManifest(i)}),null,t.sourceIdentifier);return a?Promise.resolve(a):(0,A.parseHlsManifest)(e,r)}!function(e){e.MediaSequenceNumber="_HLS_msn",e.PartIndex="_HLS_part",e.Skip="_HLS_skip"}(r||(t.HlsQueryParamNames=r={}));var C=function(){function e(e,t){var n=this;this.variantUpdateLoaders={},this.updateRepresentation=function(e){var t=n.applyServerControlQueryParams(e,e.Uri);n.context.logger.debug("M3u8Loader: updateRepresentation (".concat(t,")"));var i=n.variantUpdateLoaders[e._id];return n.loadPlaylist(i,t).then((function(t){return n.onMediaLoaded(t,e)})).then((function(e){var t,r,a=n.maybeIsLive(),o=!0===(null===(r=null===(t=n.context)||void 0===t?void 0:t.settings)||void 0===r?void 0:r.INFINITE_RETRIES_FOR_LIVE);return a&&o&&i.setResetRetriesWhenOffline(!0),e})).catch((function(t){return n.handleRepresentationUpdateFailure(t,e)}))},this.onFailureHandler=function(e,t){n.isCancelled||(n.lastDownloadFailureReason=t.name,n.context.logger.debug("Failed to load playlist: ".concat(e.url," - ").concat(t.name)))},this.context=e,this.sourceContext=t,this.registeredOnPlayEventHandlers=[],this.registeredOnPausedEventHandlers=[],this.discontinuitySequences=[],this.isCancelled=!1,this.translatedManifest=new M.M3u8DashManifest(e,t),this.masterPlaylistLoader=this.createLoader(p.HttpRequestType.MANIFEST_HLS_MASTER),this.sourceStore=e.serviceManager.get(s.ServiceName.SourceStoreService,t.sourceIdentifier)}return e.prototype.load=function(e){var t=this;return this.isCancelled=!1,this.lastDownloadFailureReason=null,this.loadPlaylist(this.masterPlaylistLoader,e).then((function(e){return t.parseMasterPlaylist(e)})).then((function(e){return t.translatedManifest.updateBaseUrl(e.url),t.initializeBackupStreamsHandler(e),t.executeCodecVerification(e)})).then((function(e){return t.handleMasterPlaylist(e),t.loadFirstVariants()})).then((function(){return t.translatedManifest.getManifest()})).catch((function(e){var n=e instanceof Error?e.message:void 0;return t.isCancelled||n===d.RequestError.Canceled?Promise.reject():e instanceof o.PlayerError||e instanceof Error?Promise.reject(e):Promise.reject(t.getErrorFromResponse(e))})).finally((function(){var e;return null===(e=t.backupStreamsHandler)||void 0===e?void 0:e.signalLoadingFinished()}))},e.prototype.parseMasterPlaylist=function(e){var t=this;if(this.isCancelled)return Promise.reject(new Error("The loading of the HLS source was cancelled."));var n=e.body;if(void 0===n)return Promise.reject(new Error("Cannot parse the playlist: content is empty."));var r={manifestText:n,url:e.url};return D(this.context,this.sourceContext,r).then((function(n){return(0,A.isValidHlsPlaylist)(n)?(t.maybeCacheManifest(e,n),(0,A.ensureMasterPlaylist)(t.context,n,e.url)):Promise.reject(new Error("Cannot parse master playlist: invalid format."))})).then((function(t){return i(i({},t),{rawString:n,url:e.url})})).catch((function(e){return Promise.reject(new o.PlayerError(a.ErrorCode.SOURCE_MANIFEST_INVALID,{errorMessage:e.message},"HLS manifest is not a valid M3U playlist"))}))},e.prototype.initializeBackupStreamsHandler=function(e){var t=this.translatedManifest.extractBackupStreams(e);t.every((function(e){return Object.keys(e).length<=1}))||(this.backupStreamsHandler=new O.HlsBackupStreamsHandler(this.context,t,this.updateRepresentation))},e.prototype.loadFirstVariants=function(){var e=this,t=this.sourceStore.getState(),n=this.translatedManifest.getManifest().Period[0],i=(0,E.findAdaptationSetOfMimeType)(S.MimeType.VideoMp4,n,{sourceState:t}),r=(0,E.findAdaptationSetOfMimeType)(S.MimeType.AudioMp4,n,{sourceState:t,playbackConfig:this.context.config.playback}),s=[];return i&&s.push(this.loadFirstPlaylist(i)),r&&s.push(this.loadFirstPlaylist(r)),Promise.all(s).catch((function(t){return t instanceof o.PlayerError?Promise.reject(t):e.lastDownloadFailureReason===d.RequestError.TimedOut||t===d.RequestError.TimedOut?Promise.reject(new o.PlayerError(a.ErrorCode.NETWORK_MANIFEST_DOWNLOAD_TIMEOUT,{error:t},"Failed to load the HLS variant playlists: the request timed out.")):Promise.reject(new o.PlayerError(a.ErrorCode.SOURCE_HLS_STREAM_ERROR,{error:t},"There was an error while retrieving the variant playlists."))}))},e.prototype.loadFirstPlaylist=function(e){var t=this,n=this.getStartRepresentation(e.Representation,e._mimeType,e);if(!n){var i="No ".concat(e._mimeType," variants/renditions available");return this.context.logger.log(i),Promise.reject(new o.PlayerError(a.ErrorCode.SOURCE_MANIFEST_INVALID,{reason:i},i))}return this.updateRepresentation(n).then((function(e){return t.sourceStore.dispatch((0,g.setPreferredBitrateForMimeTypeAction)(n._mimeType,n._bandwidth)),t.sourceStore.dispatch((0,I.setRepresentationIdAction)(n._internalId)),e})).catch((function(i){var r,a;return t.context.logger.debug("Representation ".concat(n._id," failed, dropping it"),null!==(r=null==i?void 0:i.message)&&void 0!==r?r:i),e.Representation=e.Representation.filter((function(e){return e!==n})),e.Representation.length>0?t.loadFirstPlaylist(e):(t.context.logger.log("All ".concat(e._mimeType," variants/renditions failed"),null!==(a=null==i?void 0:i.message)&&void 0!==a?a:i),Promise.reject(i))}))},e.prototype.getStartRepresentation=function(e,t,n){return this.context.serviceManager.maybeCall(s.ServiceName.AdaptationService,(function(i){i.setAdaptationSetId(n._mimeType,n._internalId);var r=i.selectRepresentation(t,{},e);return e.find((function(e){return e._internalId.equals(r)}))}))},e.prototype.updateAdaptationSet=function(e){var t=this,n=this.translatedManifest.findAdaptationSet(e);return n?Promise.all(n.Representation.map((function(e){var n=t.createLoader(p.HttpRequestType.MANIFEST_HLS_VARIANT);return t.loadPlaylist(n,e.Uri).then((function(n){return t.onMediaLoaded(n,e)})).then((function(e){t.sourceStore.dispatch((0,_.updateRepresentationAction)(e))}))}))):Promise.reject("Could not find data")},e.prototype.maybeCacheManifest=function(e,t){this.context.serviceManager.maybeCall(s.ServiceName.ManifestCachingService,(function(n){n.cacheHttpResponse(e,e.url),n.cacheParsedManifest(t,e.url)}),null,this.sourceContext.sourceIdentifier)},e.prototype.executeCodecVerification=function(e){var t=this,n=(0,A.getTracksWithUnknownCodecs)(e),i=n.audioTracksWithoutCodecs,r=n.videoTracksWithoutCodecs;return 0===i.length&&0===r.length?Promise.resolve(e):(this.context.logger.debug("Codecs are missing in master playlist - probing them from segments now"),this.probeMissingAudioCodecs(e,i).then((function(){return t.probeMissingVideoCodecs(e,r)})).then((function(){return e})).catch((function(e){var n;if(e instanceof o.PlayerError)return Promise.reject(e);var i="Codec verification failed",r=null!==(n=null==e?void 0:e.message)&&void 0!==n?n:e;t.context.logger.debug(i,e);var s=t.lastDownloadFailureReason===d.RequestError.TimedOut?a.ErrorCode.NETWORK_MANIFEST_DOWNLOAD_TIMEOUT:a.ErrorCode.SOURCE_MANIFEST_INVALID;return Promise.reject(new o.PlayerError(s,{reason:r},i))})))},e.prototype.parseMissingCodecsFromPlaylistResponse=function(e,t){var n=this,i=e.url,r=e.body;if(void 0===r)return Promise.reject("Cannot parse codec, empty manifest response");var a={url:t,manifestText:r};return D(this.context,this.sourceContext,a).then((function(r){var a=(0,P.calculateUpdateIntervalInSeconds)(n.context,r),o=Date.now()+(0,h.toMilliSeconds)(a);n.context.serviceManager.maybeCall(s.ServiceName.ManifestCachingService,(function(t){t.cacheHttpResponse(e,i,o),t.cacheParsedManifest(r,i,o)}),null,n.sourceContext.sourceIdentifier);var u=r.segments,l=!r.endList,c=r.mediaSequence,d=P.PlaylistUtils.getBaseUrl(n.translatedManifest.getBaseUrl(),t);return new R.CodecDetector(n.context).probeCodecs(u,l,d,c)}))},e.prototype.getVideoVariantForCodecDetection=function(e){var t=e.map((function(e,t){return{bitrate:e.attributes.BANDWIDTH,id:t}}));return this.context.serviceManager.maybeCall(s.ServiceName.AdaptationService,(function(n){var i=n.selectQualityBasedOnBitrateMeasurement(t);return e[i.id]}))},e.prototype.probeMissingCodecs=function(e,t){var n=this,i=new c.DefaultContentLoader(this.context,{requestType:p.HttpRequestType.MANIFEST_HLS_VARIANT,onFailure:this.onFailureHandler});return this.loadPlaylistForCodecProbing(i,e).then((function(t){return n.parseMissingCodecsFromPlaylistResponse(t,e)})).then((function(n){return(0,A.updateMissingCodecsForPlaylists)(n,t,e)}))},e.prototype.loadPlaylistForCodecProbing=function(e,t){var n=this;return this.loadPlaylist(e,t).catch((function(i){var r;n.context.logger.debug("Representation failed to load during codec probing ".concat(t));var a=null===(r=n.backupStreamsHandler)||void 0===r?void 0:r.getNextBackupUriForCodecProbing(t);return a?(n.context.logger.debug("Attempting to continue codec probing using backup stream ".concat(a)),n.loadPlaylistForCodecProbing(e,a)):Promise.reject(i)}))},e.prototype.probeMissingAudioCodecs=function(e,t){var n=this,i=t.shift();if(!(null==i?void 0:i.attributes.URI))return Promise.resolve();var r=y.URLHelper.getAbsoluteUrl(i.attributes.URI,this.translatedManifest.getBaseUrl());return this.probeMissingCodecs(r,e).then((function(){return n.probeMissingAudioCodecs(e,t)}))},e.prototype.probeMissingVideoCodecs=function(e,t){if(0===t.length)return Promise.resolve();var n=this.getVideoVariantForCodecDetection(t),i=y.URLHelper.getAbsoluteUrl(n.uri,this.translatedManifest.getBaseUrl());return this.probeMissingCodecs(i,e)},e.prototype.handleMasterPlaylist=function(e){var t,n=this;void 0===this.masterPlaylist&&(this.masterPlaylist=e),this.translatedManifest.onMasterPlaylistAvailable(e);var i=this.translatedManifest.getAllVariantRepresentions();i.forEach((function(e){n.variantUpdateLoaders[e._id]=n.createLoader(p.HttpRequestType.MANIFEST_HLS_VARIANT)})),null===(t=this.backupStreamsHandler)||void 0===t||t.populateBackupStreamsMap(i)},e.prototype.onMediaLoaded=function(e,t){var n=this;if(this.isCancelled)return Promise.reject(T.REPRESENTATION_UPDATE_CANCEL);var i=e.url,u=e.body;if(!u)return Promise.reject(new o.PlayerError(a.ErrorCode.SOURCE_MANIFEST_INVALID,void 0,"Cannot parse the playlist: the body of the response is empty."));var l=Date.now(),c={url:i,manifestText:u};return D(this.context,this.sourceContext,c).then((function(c){var d;c.uri=(0,y.removeQueryParameters)(i,Object.values(r)),c.requestTimestamp=l;var f=N.M3u8Validator.checkForError(c);if(f)return Promise.reject(new o.PlayerError(a.ErrorCode.SOURCE_MANIFEST_INVALID,{M3u8ValidatorError:f},"The parsed media playlist is invalid: ".concat(f)));c.start=null!==(d=c.start)&&void 0!==d?d:n.masterPlaylist.start,n.discontinuitySequences=(0,A.createDiscontinuitySequences)(c,n.discontinuitySequences);var p=n.filterTooSmallPeriods(n.discontinuitySequences,Boolean(c.endList));c.segments=p.flatMap((function(e){return e.segments}));var g=n.translatedManifest.onMediaLoaded(u,t,c);c.endList&&n.context.serviceManager.maybeCall(s.ServiceName.ManifestCachingService,(function(t){t.cacheHttpResponse(e,i),t.cacheParsedManifest(c,i)}),null,n.sourceContext.sourceIdentifier);var S=(0,h.toSeconds)(Date.now()-l);return n.context.store.dispatch((0,m.addMetricsValue)("default",v.MetricType.ProcessingTime,S)),n.context.logger.insane("Parsed playlist in: ".concat(null==S?void 0:S.toFixed(2))),g})).catch((function(e){if(e instanceof o.PlayerError)return Promise.reject(e);var t="Failed to parse the media playlist",n=e instanceof Error?e.message:t;return Promise.reject(new o.PlayerError(a.ErrorCode.SOURCE_MANIFEST_INVALID,{errorMessage:n},t))}))},e.prototype.filterTooSmallPeriods=function(e,t){return e.filter((function(n,i){var r=!t&&i===e.length-1,a=n.segments.reduce((function(e,t){return e+t.duration}),0);return!!(0===i&&a>0||r)||a>0}))},e.prototype.stop=function(){var e=this;this.registeredOnPlayEventHandlers.forEach((function(t){return e.context.eventHandler.off(l.PlayerEvent.Play,t)})),this.registeredOnPausedEventHandlers.forEach((function(t){return e.context.eventHandler.off(l.PlayerEvent.Paused,t)})),this.registeredOnPlayEventHandlers=[],this.registeredOnPausedEventHandlers=[],this.stopLoaders(),this.isCancelled=!0},e.prototype.stopLoaders=function(){var e=this;this.masterPlaylistLoader.cancel(),Object.keys(this.variantUpdateLoaders).forEach((function(t){e.variantUpdateLoaders[t].dispose(),delete e.variantUpdateLoaders[t]}))},e.prototype.stopRepresentationUpdate=function(e){var t,n=this.variantUpdateLoaders[e._id];return(null==n?void 0:n.isLoading())?(this.context.logger.debug("M3u8Loader: stopping ongoing representation update (".concat(e.Uri,")")),n.cancel(),(null!==(t=n.getResponse())&&void 0!==t?t:Promise.resolve()).catch((function(){})).then((function(){}))):Promise.resolve()},e.prototype.applyServerControlQueryParams=function(e,t){var n,i={},a=(0,P.getLastMediaSequenceAndPartIndex)(e);return(0,b.isPlayingLowLatencyHls)(this.context)&&(0,P.canMakeBlockingPlaylistReload)(e,this.context.settings)&&a&&(i[r.MediaSequenceNumber]=String(a.mediaSequence),i[r.PartIndex]=String(a.partIndex+1)),Boolean(null===(n=this.context.config.live)||void 0===n?void 0:n.incrementalManifestUpdates)&&(0,P.canPerformDeltaUpdate)(e)&&(i[r.Skip]="YES"),y.URLHelper.appendQueryParametersToUrl(t,i)},e.prototype.handleRepresentationUpdateFailure=function(e,t){var n,i=this;return this.isCancelled||(null==e?void 0:e.message)===d.RequestError.Canceled?Promise.reject(T.REPRESENTATION_UPDATE_CANCEL):(this.context.logger.debug("M3u8Loader: updateRepresentation failed (".concat(t.Uri,")"),null!==(n=null==e?void 0:e.message)&&void 0!==n?n:e),this.switchToBackupStream(t).then((function(e){var t,n=e[0];return n||Promise.reject(null!==(t=i.lastDownloadFailureReason)&&void 0!==t?t:d.RequestError.Failed)})).catch((function(t){return i.lastDownloadFailureReason===d.RequestError.TimedOut?Promise.reject(d.RequestError.TimedOut):Object.values(O.BackupStreamErrors).includes(t)&&e instanceof o.PlayerError?Promise.reject(e):Promise.reject(t===T.REPRESENTATION_UPDATE_CANCEL?t:d.RequestError.Failed)})))},e.prototype.switchToBackupStream=function(e,t){return this.backupStreamsHandler?this.backupStreamsHandler.switchToBackupStream(e,t):Promise.reject(O.BackupStreamErrors.NO_BACKUP_STREAMS)},e.prototype.loadPlaylist=function(e,t){var n;if(!t){var i=new o.PlayerError(a.ErrorCode.SOURCE_INVALID,void 0,"Cannot load the playlist: no URL was provided.");return this.context.eventHandler.fireError(i),Promise.reject(i)}var r=this.context.serviceManager.maybeCall(s.ServiceName.ManifestCachingService,(function(e){return e.getHttpResponse(t)}),null,this.sourceContext.sourceIdentifier);if(r)return Promise.resolve(r);var u=null===(n=this.sourceContext.source.options)||void 0===n?void 0:n.manifestWithCredentials;return e.load(t,p.HttpRequestMethod.GET,null,null,null,u)},e.prototype.maybeIsLive=function(){return this.context.serviceManager.maybeCall(s.ServiceName.ManifestService,(function(e){return e.isLive()}),!1,this.context.sourceContext.sourceIdentifier)},e.prototype.createLoader=function(e){var t,n,i=this,r=!0===(null===(n=null===(t=this.context)||void 0===t?void 0:t.settings)||void 0===n?void 0:n.INFINITE_RETRIES_FOR_LIVE),a=this.maybeIsLive();return new c.DefaultContentLoader(this.context,{onSuccess:function(e,t){var n,r;i.lastDownloadFailureReason=null,(0,f.isDownloadTimeInformationValid)(e)&&(i.context.logger.debug("Downloaded playlist in: ".concat(null===(n=e.elapsedTime)||void 0===n?void 0:n.toFixed(2),", TTFB: ").concat(null===(r=e.timeToFirstByte)||void 0===r?void 0:r.toFixed(2))),i.context.store.dispatch((0,m.addMetricsValue)("default",v.MetricType.DownloadInformation,{bytes:e.length,time:e.elapsedTime,timeToFirstByte:e.timeToFirstByte})))},onFailure:this.onFailureHandler,maxRetries:this.context.settings.MAX_MPD_RETRIES,requestType:e,resetRetriesWhenOffline:a&&r})},e.prototype.getErrorFromResponse=function(e){if(this.lastDownloadFailureReason===d.RequestError.TimedOut)return new o.PlayerError(a.ErrorCode.NETWORK_MANIFEST_DOWNLOAD_TIMEOUT,L(e),"Failed to load HLS playlist: the request timed out.",this.sourceContext.sourceIdentifier);var t=(null==e?void 0:e.status)===XMLHttpRequest.UNSENT?"Failed to load the HLS playlist due to CORS restrictions or because the client is offline.":"Failed to load HLS playlist: ".concat(null==e?void 0:e.status," ").concat(null==e?void 0:e.statusText,".");return new o.PlayerError(a.ErrorCode.SOURCE_COULD_NOT_LOAD_MANIFEST,L(e),t,this.sourceContext.sourceIdentifier)},e.prototype.dispose=function(){var e,t=this;this.stop(),this.translatedManifest=(0,u.dispose)(this.translatedManifest),this.masterPlaylist=null,this.lastDownloadFailureReason=null,this.masterPlaylistLoader.dispose(),null===(e=this.backupStreamsHandler)||void 0===e||e.dispose(),Object.keys(this.variantUpdateLoaders).forEach((function(e){return t.variantUpdateLoaders[e].dispose()})),this.variantUpdateLoaders=null},e}();function L(e){return{url:e.url,statusCode:e.status,statusText:e.statusText}}t.M3u8Loader=C},26527:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DiscontinuitySequenceNumberTracker=void 0;var n=function(){function e(e){this.mimeTypeFilter=e,this.discoSequenceNumberChangedListeners=[],this.reset()}return e.prototype.notifyListeners=function(e){var t=this;this.discoSequenceNumberChangedListeners.forEach((function(n){return n(t.currentDiscontinuitySequenceNumber,e)}))},e.prototype.trackSegment=function(e){if(this.mimeTypeFilter(e.getMimeType())&&!e.isInit()){var t=e.getSegmentInfo(),n=null==t?void 0:t.discontinuitySequenceNumber,i=isFinite(this.currentDiscontinuitySequenceNumber),r=this.currentDiscontinuitySequenceNumber!==n;i&&r&&this.notifyListeners(n),this.currentDiscontinuitySequenceNumber=n}},e.prototype.subscribe=function(e){this.discoSequenceNumberChangedListeners.push(e)},e.prototype.unsubscribe=function(e){var t=this.discoSequenceNumberChangedListeners.indexOf(e);t<0||this.discoSequenceNumberChangedListeners.splice(t,1)},e.prototype.reset=function(){this.currentDiscontinuitySequenceNumber=-1/0},e.prototype.dispose=function(){this.reset(),this.discoSequenceNumberChangedListeners=[]},e}();t.DiscontinuitySequenceNumberTracker=n},27485:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TextParser=void 0;var n=/[ \t]+/gm,i=function(){function e(e){this.data=e,this.position=0}return e.prototype.isAtEnd=function(){return this.position===this.data.length},e.prototype.skipWhitespace=function(){this.readRegex(n)},e.prototype.readRegex=function(e){var t=this.indexOf(e);if(!this.isAtEnd()&&void 0!==t&&t.position===this.position)return this.position+=t.length,t.results},e.prototype.indexOf=function(e){e.lastIndex=this.position;var t=e.exec(this.data);if(null!==t)return{position:t.index,length:t[0].length,results:t}},e}();t.TextParser=i},31043:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSegmentInfos=a,t.updateSegmentInfos=o;var i=n(15231),r=n(85157);function a(e,t){return(0,i.createAction)(r.SegmentInfoMapActionType.SetSegmentInfos,{qualityPath:e,updatedEntry:t})}function o(e,t){return(0,i.createAction)(r.SegmentInfoMapActionType.UpdateSegmentInfos,{qualityPath:e,updatedEntry:t})}},32352:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.M3u8Validator=t.M3u8ValidatorError=void 0,function(e){e.EmptyPlaylist="EmptyPlaylist"}(n||(t.M3u8ValidatorError=n={}));var i=function(){function e(){}return e.checkForError=function(t){for(var n=0,i=e.mediaPlaylistRules;n<i.length;n++){var r=i[n];if(!r.fn(t))return r.err}return null},e.mediaPlaylistRules=[{fn:function(e){var t;return(null===(t=e.segments)||void 0===t?void 0:t.length)>0},err:n.EmptyPlaylist}],e}();t.M3u8Validator=i},33827:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HlsTimelineTracker=void 0;var i=n(18665),r=n(58975),a=n(27279),o=n(3464),s=n(9827),u=n(34435),l=.001,c=function(){function e(e,t){var n=this;this.gapVerificationOnNextSegment=!1,this.context=e,this.playbackTimeNextSegment=-1,this.allowedGapSizeSec=t,this.playerStateService=this.context.serviceManager.get(i.ServiceName.PlayerStateService),this.unsubscribeFromTargetPlaybackTimeChange=(0,o.subscribe)(this.context.store)((function(e){return(0,r.getTargetPlaybackTime)((0,r.getPlayerState)(e))}),(function(){return n.seekVerificationOnNextSegment=!0}),(function(e){return-1!==e}));var s=(0,a.getSourceStore)(this.context);s&&(this.unsubscribeFromContentLocationId=(0,o.subscribe)(s)((function(e){return e&&(0,u.getContentLocationId)((0,u.getHlsState)(e))}),(function(){return n.gapVerificationOnNextSegment=!0}),(function(e,t){return e!==t})))}return e.prototype.reset=function(){this.playbackTimeNextSegment=-1,this.gapVerificationOnNextSegment=!1},e.prototype.isGapVerificationNeeded=function(){return this.gapVerificationOnNextSegment&&-1!==this.playbackTimeNextSegment},e.prototype.isSeekTargetVerificationNeeded=function(){return this.seekVerificationOnNextSegment&&-1===this.playbackTimeNextSegment},e.prototype.trackPlaybackTime=function(e){var t,n=null===(t=e.getSegmentInfo())||void 0===t?void 0:t.duration,i=e.getDuration();this.playbackTimeNextSegment=e.getPlaybackTime()+Math.max(i,null!=n?n:i)},e.prototype.checkForTimelineGap=function(e,t,n){var i;if(this.playbackTimeNextSegment>=0)return this.gapVerificationOnNextSegment=!1,this.checkGapForConsecutiveSegments(e,t,n);var r=(null===(i=this.playerStateService)||void 0===i?void 0:i.seekingOrTimeshifting)?this.playerStateService.targetPlaybackTime:-1;return this.seekVerificationOnNextSegment&&r>-1?(this.seekVerificationOnNextSegment=!1,this.checkSeekAccuracy(r,e,t,n)):{hasGap:!1}},e.prototype.checkGapForConsecutiveSegments=function(e,t,n){var i=d(e,this.allowedGapSizeSec);if(f(this.playbackTimeNextSegment,i-this.allowedGapSizeSec,i+e.getDuration()))return{hasGap:!1};var r={hasGap:!1},a=e.getPlaybackTime()-this.playbackTimeNextSegment,o=t.SegmentList?t.SegmentList[0].SegmentURL:[],u=(0,s.findSegmentUrlIndexWithinSegmentList)(e.getSegmentInfo(),o),l=u>=0?u:n,c=l;if(a>0)(S=g(c=m(a,o,l)))||(this.context.logger.debug("Could not handle gap on playlist switch, reached lower boundary of segment list"),c=0),r=p(!0,a,S,c);else if(a<0){var S;(S=g(c=v(a,o,l)))||(this.context.logger.debug("Could not handle gap on playlist switch, reached upper boundary of segment list"),c=o.length),r=p(!0,a,S,c)}return c===l&&r.hasGap&&(this.context.logger.debug("Ignoring gap as calculated new segment index did not change"),r.hasGap=!1),r},e.prototype.checkSeekAccuracy=function(e,t,n,i){var r=n.SegmentList?n.SegmentList[0].SegmentURL:[],a=t.getPlaybackTime(),o=t.getPlaybackTime()+t.getDuration();this.context.logger.debug("HLS seek target verification",{seekTarget:e,segmentStartTime:a,segmentEndTime:o});var u,l,c,d=(0,s.findSegmentUrlIndexWithinSegmentList)(t.getSegmentInfo(),r);return d=d>=0?d:i,e<a?((c=g(u=m(l=a-e,r,d)))||(u=0),this.context.logger.debug("Target segment starts too late, corrected segment index from ".concat(d," to ").concat(u)),p(!0,l,c,u)):e>o?((c=g(u=v(l=o-e,r,d)))||(u=r.length),this.context.logger.debug("Target segment ends too early, corrected segment index from ".concat(d," to ").concat(u)),p(!0,l,c,u)):{hasGap:!1}},e.prototype.dispose=function(){var e;null===(e=this.unsubscribeFromContentLocationId)||void 0===e||e.call(this),this.unsubscribeFromTargetPlaybackTimeChange(),this.gapVerificationOnNextSegment=!1,this.seekVerificationOnNextSegment=!1,this.playbackTimeNextSegment=-1},e}();function d(e,t){var n,i=null===(n=e.getSegmentInfo())||void 0===n?void 0:n.startTime,r=e.getPlaybackTime();return void 0===i||Math.abs(r-i)>e.getDuration()+t?r:Math.min(r,i)}function f(e,t,n){return e>=t&&e<=n}function p(e,t,n,i){return{hasGap:e,gapSizeSec:t,couldHandle:n,correctedIndex:i}}function g(e){return e>=0&&isFinite(e)}function m(e,t,n){for(;e>l;){if(n<=0)return-1;e-=t[n-1]._duration,n--}return n}function v(e,t,n){for(;e<-l;)if(e+=t[n]._duration,++n>=t.length)return 1/0;return n}t.HlsTimelineTracker=c},34435:function(e,t){"use strict";var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};function i(e){return e.hls}Object.defineProperty(t,"__esModule",{value:!0}),t.getContentLocationId=t.getPresentationTimeOffset=t.getMergedDiscontinuityTimings=t.getFirstDiscoSequenceTiming=t.getDiscoSequenceTimings=t.getDiscoSequenceTiming=t.getCustomTags=t.getDefaultLanguages=t.getEndlist=t.getIsLowLatencyPlaylist=t.getPlaylistType=void 0,t.getHlsState=i,t.computeMergedDiscontinuityTimings=m,t.areDiscontinuitiesMisaligned=v;var r=function(e){return e.playlistType};t.getPlaylistType=r;var a=function(e){return e.isLowLatencyPlaylist};t.getIsLowLatencyPlaylist=a;var o=function(e){return e.endlist};t.getEndlist=o;var s=function(e){return e.defaultLanguages};t.getDefaultLanguages=s;var u=function(e){return e.customTags};t.getCustomTags=u;var l=function(e,t,i){var r,a;return(null===(a=null===(r=e.discontinuitySequenceTiming)||void 0===r?void 0:r[t])||void 0===a?void 0:a[i])?n({},e.discontinuitySequenceTiming[t][i]):null};t.getDiscoSequenceTiming=l;var c=function(e,t){var i;return(null===(i=e.discontinuitySequenceTiming)||void 0===i?void 0:i[t])?n({},e.discontinuitySequenceTiming[t]):null};t.getDiscoSequenceTimings=c;var d=function(e){var n,i=(0,t.getMergedDiscontinuityTimings)(e);return null!==(n=i[Object.keys(i)[0]])&&void 0!==n?n:{startTime:0,endTime:0}};t.getFirstDiscoSequenceTiming=d;var f=function(e){var t;return m(null!==(t=e.discontinuitySequenceTiming)&&void 0!==t?t:{})};t.getMergedDiscontinuityTimings=f;var p=function(e,t){return e.presentationTimeOffsets[t]};t.getPresentationTimeOffset=p;var g=function(e){return e.contentLocationId};function m(e){var t={},n=function(n,i){var r,a,o,s,u,l,c,d,f,p;return{startTime:Math.max(null!==(o=null===(a=null===(r=e[i])||void 0===r?void 0:r[n])||void 0===a?void 0:a.startTime)&&void 0!==o?o:-1/0,null!==(u=null===(s=t[n])||void 0===s?void 0:s.startTime)&&void 0!==u?u:-1/0),endTime:Math.min(null!==(d=null===(c=null===(l=e[i])||void 0===l?void 0:l[n])||void 0===c?void 0:c.endTime)&&void 0!==d?d:1/0,null!==(p=null===(f=t[n])||void 0===f?void 0:f.endTime)&&void 0!==p?p:1/0)}};return Object.keys(e).forEach((function(i){var r;Object.keys(null!==(r=e[i])&&void 0!==r?r:{}).forEach((function(e){t[e]=n(e,i)}))})),t}function v(e,t,n){if(void 0===n&&(n=2),t.length<=1||!e.discontinuitySequenceTiming)return!1;var i=S(e.discontinuitySequenceTiming,t),r=m(i);return 1!==Object.keys(r).length&&!Object.keys(r).every((function(e,a){var o=r[e];return t.every((function(t){var s,u=null===(s=i[t])||void 0===s?void 0:s[e];if(!u)return!0;var l=Math.abs(u.startTime-o.startTime),c=Math.abs(u.endTime-o.endTime);return 0===a?c<=n:a===Object.keys(r).length-1?l<=n:l<=n&&c<=n}))}))}function S(e,t){return t.reduce((function(t,n){return n in e&&(t[n]=e[n]),t}),{})}t.getContentLocationId=g},47967:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.generateRendition=u,t.generateStartAttributes=l,t.generatePartInfoTag=c,t.generateServerControlTag=d,t.generateSkipTag=f,t.generatePartTag=p,t.generatePreloadHintTag=m,t.generateStreamInfTag=v,t.generateVariantPlaylistId=S,t.generateSessionKeyTag=h,t.generateIFrameStreamInfTag=y,t.generateSegmentEntrySkeleton=T,t.generateCueTag=E,t.generateDateRange=I,t.generateKeyTag=_,t.generateByteRangeTag=b,t.generateMapTag=R,t.generateCustomTag=L;var a=n(62510),o=n(91397),s=n(56093);function u(e){var t,n,r;return{name:e.name,attributes:i(i({},e.attributes),{TYPE:null===(t=e.attributes)||void 0===t?void 0:t.TYPE,"GROUP-ID":null===(n=e.attributes)||void 0===n?void 0:n["GROUP-ID"],NAME:null===(r=e.attributes)||void 0===r?void 0:r.NAME})}}function l(e){var t,n;return(null===(t=e.attributes)||void 0===t?void 0:t.hasOwnProperty("TIME-OFFSET"))&&(n={timeOffset:Number(e.attributes["TIME-OFFSET"]),precise:"YES"===e.attributes.PRECISE}),e.attributes&&n&&n.hasOwnProperty("timeOffset")&&(e.attributes["TIME-OFFSET"]=n.timeOffset,e.attributes.PRECISE=n.precise),n}function c(e){return{targetDuration:parseFloat(e["PART-TARGET"])}}function d(e){var t={canBlockReload:!1};return e.attributes?(e.attributes["CAN-SKIP-UNTIL"]&&(t.canSkipUntil=parseFloat(e.attributes["CAN-SKIP-UNTIL"])),void 0!==t.canSkipUntil&&e.attributes["CAN-SKIP-DATERANGES"]&&(t.canSkipDateRanges="YES"===e.attributes["CAN-SKIP-DATERANGES"]),e.attributes["HOLD-BACK"]&&(t.holdBack=parseFloat(e.attributes["HOLD-BACK"])),e.attributes["PART-HOLD-BACK"]&&(t.partHoldBack=parseFloat(e.attributes["PART-HOLD-BACK"])),e.attributes["CAN-BLOCK-RELOAD"]&&(t.canBlockReload="YES"===e.attributes["CAN-BLOCK-RELOAD"]),t):t}function f(e){var t={skippedSegments:0,recentlyRemovedDateranges:[]};return e.attributes?(e.attributes["SKIPPED-SEGMENTS"]&&(t.skippedSegments=parseInt(e.attributes["SKIPPED-SEGMENTS"])),e.attributes["RECENTLY-REMOVED-DATERANGES"]&&(t.recentlyRemovedDateranges=e.attributes["RECENTLY-REMOVED-DATERANGES"].split("\t")),t):t}function p(e,t){var n={uri:(0,o.forceReallocation)(e.URI),duration:parseFloat(e.DURATION)};if("YES"===e.INDEPENDENT&&(n.independent=!0),e.BYTERANGE){var i=e.BYTERANGE.split("@"),r=parseInt(i[0]),a=parseInt(i[1]);isNaN(a)&&(a=(null==t?void 0:t.byteRange)?t.byteRange.end+1:0),n.byteRange={start:a,end:a+r-1}}return"YES"===e.GAP&&(n.gap=!0),n}function g(e){var t=parseInt(e["BYTERANGE-START"]),n=parseInt(e["BYTERANGE-END"]);if(!isNaN(t)||!isNaN(n))return{start:isNaN(t)?0:t,end:isNaN(n)?Math.pow(2,53)-1:n}}function m(e,t){if("PART"===e.TYPE){var n={uri:(0,o.forceReallocation)(e.URI),duration:t,isPreloadHint:!0};return(i=g(e))&&(n.byteRange=i),{part:n}}if("MAP"===e.TYPE){var i,r={url:(0,o.forceReallocation)(e.URI)};return(i=g(e))&&(r.byteRange=i),{init:r}}return{}}function v(e,t){var n,r,a,o,s,u={name:e.name,uri:t,attributes:i(i({},e.attributes),{BANDWIDTH:null===(n=e.attributes)||void 0===n?void 0:n.BANDWIDTH})};return(null===(r=e.attributes)||void 0===r?void 0:r.RESOLUTION)&&(u.attributes.RESOLUTION=A(e)),(null===(a=e.attributes)||void 0===a?void 0:a.BANDWIDTH)&&(u.attributes.BANDWIDTH=parseInt(e.attributes.BANDWIDTH)),(null===(o=e.attributes)||void 0===o?void 0:o["AVERAGE-BANDWIDTH"])&&(u.attributes["AVERAGE-BANDWIDTH"]=parseInt(e.attributes["AVERAGE-BANDWIDTH"])),(null===(s=e.attributes)||void 0===s?void 0:s["PROGRAM-ID"])&&(u.attributes["PROGRAM-ID"]=parseInt(e.attributes["PROGRAM-ID"])),u.id=S(u.attributes),u}function S(e){var t="";return e.RESOLUTION&&(t+=e.RESOLUTION.height+"_"),t+=e.BANDWIDTH}function h(e){O(e)}function y(e){var t,n,i;(null===(t=e.attributes)||void 0===t?void 0:t.BANDWIDTH)&&(e.attributes.BANDWIDTH=parseInt(e.attributes.BANDWIDTH)),(null===(n=e.attributes)||void 0===n?void 0:n["AVERAGE-BANDWIDTH"])&&(e.attributes["AVERAGE-BANDWIDTH"]=parseInt(e.attributes["AVERAGE-BANDWIDTH"],10)),(null===(i=e.attributes)||void 0===i?void 0:i.RESOLUTION)&&(e.attributes.RESOLUTION=A(e))}function T(){return{uri:"",duration:0,mediaSequence:0}}function E(e,t){var n={type:t};if(e.attributes){var i=[];for(var r in e.attributes)if(e.attributes.hasOwnProperty(r)){var a=e.attributes[r]?"".concat(r,"=").concat(e.attributes[r]):r;i.push(a)}n.attributes=i}return n}function I(e){var t={type:a.MetadataType.DATERANGE};if(e.attributes){t.clientAttributes={};var n=e.attributes;for(var i in n)if(n.hasOwnProperty(i))if(i.startsWith("X-")){var r=2;t.clientAttributes[(0,o.kebabCaseToCamelCase)(i.substring(r))]=(0,o.forceReallocation)(n[i])}else t[(0,o.kebabCaseToCamelCase)(i)]=(0,o.forceReallocation)(n[i])}return t.duration&&(t.duration=Number(t.duration)),t.plannedDuration&&(t.plannedDuration=Number(t.plannedDuration)),t.endOnNext&&(t.endOnNext=!0),t}function _(e){if(O(e),e.attributes&&"NONE"!==e.attributes.METHOD&&e.attributes.URI){var t={method:(0,o.forceReallocation)(e.attributes.METHOD)||"AES-128",uri:(0,o.forceReallocation)(e.attributes.URI)};return void 0!==e.attributes.IV&&(t.iv=e.attributes.IV),e.attributes.KEYFORMAT&&(t.keyformat=(0,o.forceReallocation)(e.attributes.KEYFORMAT)),e.attributes.KEYFORMATVERSIONS&&(t.keyformatversions=(0,o.forceReallocation)(e.attributes.KEYFORMATVERSIONS)),e.attributes.KEYID&&(t.keyid=(0,o.forceReallocation)(e.attributes.KEYID)),t}}function b(e,t){var n;if(e.value){var i=e.value.split("@"),r=i[0],a=i[1],o={length:parseInt(r||"0")};if(a)o.offset=parseInt(a);else{var s=t[t.length-1];(null==s?void 0:s.byterange)&&(o.offset=(null!==(n=s.byterange.offset)&&void 0!==n?n:0)+s.byterange.length)}return o}}function R(e,t){var n,i;if(null===(n=e.attributes)||void 0===n?void 0:n.URI){var a={url:(0,o.forceReallocation)(e.attributes.URI)};if(null===(i=e.attributes)||void 0===i?void 0:i.BYTERANGE){var s=e.attributes.BYTERANGE.split("@"),u=s[0],l=s[1];a.byteRange={start:parseInt(l),end:parseInt(l)+parseInt(u)-1}}return t.keys.length>0&&(a.keys=r([],t.keys,!0),t.keyInUse=!0),a}}function O(e){var t;if(null===(t=e.attributes)||void 0===t?void 0:t.IV){e.attributes.IV=e.attributes.IV.toLowerCase();var n=2;"0x"===e.attributes.IV.substring(0,n)&&(e.attributes.IV=e.attributes.IV.substring(n)),e.attributes.IV=new Uint8Array(s.FormatHelper.hexToBytes(e.attributes.IV)).buffer}}function A(e){var t,n={width:0,height:0},i=null===(t=e.attributes)||void 0===t?void 0:t.RESOLUTION.split("x");return i[0]&&(n.width=parseInt(i[0])),i[1]&&(n.height=parseInt(i[1])),n}var M=/(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))/,N=/([^=]*)=(.*)/,P=/^['"](.*)['"]$/,D=/^['"](.*)['"]$/g,C=/^([^:="]*)([:=])?(.*)$/;function L(e){try{e=(0,o.forceReallocation)(e);var t=/^#([^:=]*)([:=])?(.*)$/.exec(e);if(!(null==t?void 0:t[1]))return;var n={name:t[1]},i="";if("="===t[2]&&t[3])i=t[3];else if(":"===t[2]&&t[3]){var r=C.exec(t[3]);r&&"="===r[2]&&r[3]?n.attributes=U(t[3]):i=t[3]}return i&&(n.value=i.replace(D,"$1")),n}catch(e){return}}function U(e){for(var t=e.split(M),n=t.length,i={};n--;)if(""!==t[n]){var r=N.exec(t[n]);if(r){var a=r.slice(1),o=a[0],s=a[1];i[o.trim()]=s.trim().replace(P,"$1")}}return i}},49422:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.HlsParsing=t.M3u8Translator=void 0;var r,a=n(52442),o=n(42283),s=n(3941),u=n(10676),l=n(79814),c=n(331),d=n(76885),f=n(54838),p=n(91520),g=n(16368),m=n(73543),v=n(67883),S=n(83740),h=n(73471),y=n(53950),T=new RegExp(/^data:text\/plain.*;base64.*,(.+)/),E=function(){function e(e,t){this.adaptationsetId=0,this.assumeAudioMuxedIntoVideoTs=!1,this.memoizedParseBase64DataUrl=(0,u.memoize)((function(e){var t=T.exec(e);return null!==t&&2===t.length?t[1]:null})),this.context=e,this.sourceContext=t,this.representationFactory=new h.M3u8RepresentationFactory(e,t)}return e.prototype.updateBaseUrl=function(e){this.baseURL=d.URLHelper.removeLastPathPart(e)},e.prototype.getBaseUrl=function(){var e;return null!==(e=this.baseURL)&&void 0!==e?e:""},e.prototype.createCeaCaptionObjects=function(e,t){for(var n={},i={_schemeIdUri:"urn:scte:dash:cc:cea-"+t+":2015",_value:""},r=0;r<e.length;r++){var a=e[r]["INSTREAM-ID"];n[a]={lang:e[r].LANGUAGE||e[r].NAME||a,label:e[r].NAME||e[r].LANGUAGE||a},i._value.length>0&&(i._value+=";"),i._value+="708"===t?a.substring(7)+"=lang:":a+"=",i._value+=e[r].LANGUAGE}return{closedCaptionLabel:n,accessibility:i}},e.prototype.createCaptionElements=function(e){if(!e)return{closedCaptionLabel:null,accessibility:[]};var t=this.createCeaCaptionObjects(e.cea608,"608"),n=this.createCeaCaptionObjects(e.cea708,"708"),i=[],r=null;return t.accessibility._value&&(i.push(t.accessibility),r=t.closedCaptionLabel),n.accessibility._value&&i.push(n.accessibility),{closedCaptionLabel:r,accessibility:i}},e.prototype.createVideoAdaptationSet=function(e,t,n,i,r){for(var o=[],s=1/0,u=0,l=1/0,c=0,d=1/0,f=0,p=null,g="video/"+i,m=0,S=e;m<S.length;m++){var h=S[m];if(h.uri){var y=this.representationFactory.create(h,a.RepresentationType.VIDEO,i,this.baseURL,this.assumeAudioMuxedIntoVideoTs);o.push(y),s=Math.min(s,y._height),u=Math.max(u,y._height),l=Math.min(l,y._width),c=Math.max(c,y._width),d=Math.min(d,y._bandwidth),f=Math.max(f,y._bandwidth)}}o.sort((function(e,t){return e._bandwidth-t._bandwidth}));var T={Representation:o,ContentComponent:[{_id:"video",_contentType:"video"}],_id:String(this.adaptationsetId),_group:p,_mimeType:g,_internalId:new v.AdaptationSetId(n,String(this.adaptationsetId++)),_minBandwidth:d,_maxBandwidth:f,_minHeight:s,_maxHeight:u,_minWidth:l,_maxWidth:c,isTransmuxingRequired:!0};r&&Object.keys(r).forEach((function(e){r[e].entries.filter((function(e){return!e.URI})).forEach((function(e){T.ContentComponent.push({_id:e.NAME,_contentType:"audio",_lang:e.LANGUAGE})}))}));var E=this.createCaptionElements(t);E.closedCaptionLabel&&(T.ClosedCaptionLabels=E.closedCaptionLabel),E.accessibility.length>0&&(T.Accessibility=E.accessibility);var I=this.getLabelForAdaptationSet(T);return I&&"string"==typeof I&&(T._label=I),T},e.prototype.createAudioAdaptationSet=function(e,t,n,i){for(var r=[],o=1/0,s=0,u=null,l=0,c=e;l<c.length;l++){var d=c[l];if(d.uri){var f=this.representationFactory.create(d,a.RepresentationType.AUDIO,i,this.baseURL,this.assumeAudioMuxedIntoVideoTs);r.push(f)}}r.sort((function(e,t){return e._bandwidth-t._bandwidth}));var p={Representation:r,_id:String(this.adaptationsetId),_group:u,_mimeType:"audio/"+i,_internalId:new v.AdaptationSetId(n,String(this.adaptationsetId++)),_minBandwidth:o,_maxBandwidth:s,isTransmuxingRequired:!0},g=this.createCaptionElements(t);g.closedCaptionLabel&&(p.ClosedCaptionLabels=g.closedCaptionLabel),g.accessibility.length>0&&(p.Accessibility=g.accessibility);var m=this.getLabelForAdaptationSet(p);return m&&"string"==typeof m&&(p._label=m),p},e.prototype.createAudioAdaptationSets=function(e,t,n){var i=this,r=this.context.settings.IGNORE_HLS_AUDIO_GROUPS,o=0,s=[],u=function(t){return{Representation:[],_id:String(i.adaptationsetId),_internalId:new v.AdaptationSetId(e,String(i.adaptationsetId++)),_mimeType:"audio/"+n,_minBandwidth:1/0,_maxBandwidth:-1,_lang:t,_group:String(o++),isTransmuxingRequired:!0}};return Object.keys(t).forEach((function(e){var o,l,c,d=t[e],f=d.entries.find((function(e){return e.LANGUAGE&&"und"!==e.LANGUAGE}));f&&(l=f.LANGUAGE),r||(c=u(l));for(var p=(0,S.uniqueRenditions)(d.entries.filter((function(e){return e.URI}))),g=0,m=p;g<m.length;g++){var v=m[g];r&&(c=u(l));var h=i.representationFactory.create(v,a.RepresentationType.AUDIO,n,i.baseURL,i.assumeAudioMuxedIntoVideoTs);c.Representation.push(h),c._minBandwidth=Math.min(c._minBandwidth,h._bandwidth),c._maxBandwidth=Math.max(c._maxBandwidth,h._bandwidth);var y=v.CHARACTERISTICS;if(y&&0===y.indexOf(i.context.settings.HLS_VR_CHARACTERISTICS_UTI)&&(c.Role=[{_schemeIdUri:i.context.settings.VR_SCHEME_ID_URI,_value:y.substring(i.context.settings.HLS_VR_CHARACTERISTICS_UTI.length)}]),!c._label){var T=i.getLabelForAdaptationSet(c);T&&"string"==typeof T?c._label=T:h._name?c._label=h._name:c._label=e}r&&c.Representation.length>0&&s.push(c),c.Representation.sort((function(e,t){return e._bandwidth-t._bandwidth}))}b(null!==(o=null==c?void 0:c.Representation)&&void 0!==o?o:[]),!r&&c.Representation.length>0&&s.push(c)})),s},e.prototype.createSubtitleAdaptationSets=function(e,t,n){var i=this,r=0;return Object.values(t).filter((function(e){return e.URI})).map((function(t){var o={Representation:[],_id:String(i.adaptationsetId),_internalId:new v.AdaptationSetId(e,String(i.adaptationsetId++)),_mimeType:"application/mp4",_lang:t.LANGUAGE,_group:""+r++,_isFragmented:!0},s=i.representationFactory.create(t,a.RepresentationType.SUBTITLE,n,i.baseURL,i.assumeAudioMuxedIntoVideoTs);o.Representation.push(s);var u=i.getLabelForAdaptationSet(o);return u&&"string"==typeof u?o._label=u:s._name?o._label=s._name:o._label=t.LANGUAGE,o}))},e.prototype.getLabelForAdaptationSet=function(e){var t={mimeType:e._mimeType,lang:e._lang};return e._label&&(t.label=e._label),this.getLabelingFunctionForAdaptationSet()(t)},e.prototype.getLabelingFunctionForAdaptationSet=function(){var e,t=this.context.sourceContext,n=t&&t.source&&t.source.labeling;return(null===(e=null==n?void 0:n.hls)||void 0===e?void 0:e.tracks)?"function"!=typeof n.hls.tracks?this.getDefaultLabelForAdaptationSet:(0,f.safeUserCallback)((function(){return n.hls.tracks}),this.context.logger):this.getDefaultLabelForAdaptationSet},e.prototype.getDefaultLabelForAdaptationSet=function(){return null},e.prototype.extractBackupStreams=function(e){for(var t,n=[],i=null!==(t=this.baseURL)&&void 0!==t?t:"",r=e.playlists,a={},o=0;o<r.length;o++){var s={},u=r[o];this.addBackupStream(s,u,e.media,i);for(var l=o+1;l<r.length;l++){var c=r[l];M(u,c)&&(this.addBackupStream(s,c,e.media,i),c.attributes.AUDIO&&c.attributes.AUDIO!==u.attributes.AUDIO&&(a[c.attributes.AUDIO]=!0),r.splice(l,1),l--)}n.push(s)}return this.context.settings.IGNORE_HLS_AUDIO_GROUPS||(e.media=e.media.filter((function(e){return!a[e.attributes["GROUP-ID"]]}))),n},e.prototype.addBackupStream=function(e,t,n,i){this.context.settings.DISABLE_HLS_BACKUP_STREAM_FAILOVER||(e[String(Object.keys(e).length)]=R(t,n,i))},e.prototype.createDashManifestSkeleton=function(e){var t=e;return this.extractCodecInformation(t),this.playlistPreprocessing(t),this.assumeAudioMuxedIntoVideoTs=this.canAssumeAudioMuxedIntoVideo(t),{BaseURL:[{url:this.baseURL}],Period:[this.createPeriod(t,s.DEFAULT_PERIOD_ID)],_hasIndependentSegments:e.independentSegments,_isHls:!0,isInitialized:!1}},e.prototype.extractCodecInformation=function(e){var t=this;e.playlists.forEach((function(e){if(e.attributes.CODECS){var n=o.CodecStringHelper.getExtractedCodecStrings(e.attributes.CODECS);if(!n.video&&n.unknown&&(e.attributes.RESOLUTION||e.attributes["FRAME-RATE"])){var i=o.CodecStringHelper.extractCodec(n.unknown);t.context.logger.debug("Assuming unknown codec ".concat(i," is video")),o.CodecStringHelper.addCodec("video",i)}}}))},e.prototype.isVariantOfMimeType=function(e,t){return o.CodecStringHelper.getMimeTypeForCodecString(e.attributes.CODECS)===t},e.prototype.removeUnsupportedAudioOnlyVariants=function(e){var t=this,n=e.playlists.some((function(e){return t.isVariantOfMimeType(e,"video")})),i=e.playlists.some((function(e){return t.isVariantOfMimeType(e,"audio")}));n&&i&&(e.playlists=e.playlists.filter((function(e){return t.isVariantOfMimeType(e,"video")})))},e.prototype.playlistPreprocessing=function(e){this.removeUnsupportedAudioOnlyVariants(e)},e.prototype.getVideoCodecForPlaylist=function(e,t){void 0===t&&(t=!1);var n=o.CodecStringHelper.getExtractedCodecStrings(e.attributes.CODECS);return n.video?(o.CodecStringHelper.hasOldAvc1CodecFormat(n.video)&&(n.video=o.CodecStringHelper.convertAvc1ToAvcotiCodecFormat(n.video)),t?n.video:n.video.split(".")[0]):null},e.prototype.getAudioCodecForPlaylist=function(e,t){void 0===t&&(t=!1);var n=o.CodecStringHelper.getExtractedCodecStrings(e.attributes.CODECS);return n.audio?t?n.audio:n.audio.split(".")[0]:null},e.prototype.getAllVideoCodecs=function(e){var t=this;return e.map((function(e){return t.getVideoCodecForPlaylist(e)})).filter((function(e,t,n){return e&&n.indexOf(e)===t}))},e.prototype.getAllAudioCodecs=function(e){var t=this;return e.map((function(e){return t.getAudioCodecForPlaylist(e)})).filter((function(e,t,n){return e&&n.indexOf(e)===t}))},e.prototype.groupPlaylistsByCodec=function(e){var t=this.getAllVideoCodecs(e),n=this.getAllAudioCodecs(e);if(0===t.length&&0===n.length)return{unknown:e};if(0===t.length){var i={};return n.forEach((function(t){i[t]=e.filter((function(e){var n;return null===(n=e.attributes.CODECS)||void 0===n?void 0:n.includes(t)}))})),i}var r={};return t.forEach((function(t){r[t]=e.filter((function(e){var n;return null===(n=e.attributes.CODECS)||void 0===n?void 0:n.includes(t)}))})),r},e.prototype.createPeriod=function(t,n){var i=this,a={_id:n,_duration:"PT0S",duration:0,AdaptationSet:[]},s=function(e){if(null!=e.attributes.CODECS){var t=_(e.attributes.CODECS),n=o.CodecStringHelper.getExtractedCodecStrings(t).video;return!!n&&i.context.renderer.isMediaTypeSupported("video/mp4",n)}return!0},u=r.extractMediaRenditions(t,this.context.settings.IGNORE_HLS_AUDIO_GROUPS,s),l=this.groupPlaylistsByCodec(t.playlists),c=Object.keys(l),d=c.filter((function(e){return o.CodecStringHelper.isVideoCodec(e)})).map((function(e){return i.createVideoAdaptationSet(l[e],u.captions,a._id,m.ContainerFormat.MP4,u.audio)})).filter((function(t){return t.Representation=t.Representation.reduce((function(t,n){var r=e.isRepresentationSupported(n,i.context.renderer),a=Boolean(t.find((function(e){return e.Uri===n.Uri})));return r&&!a&&t.push(n),t}),[]),t.Representation.length>0})),f=this.createAudioAdaptationSets(a._id,u.audio,m.ContainerFormat.MP4).filter((function(t){return t.Representation=t.Representation.filter((function(t){return e.isRepresentationSupported(t,i.context.renderer)})),t.Representation.length>0})).concat(c.filter((function(e){return o.CodecStringHelper.isAudioCodec(e)})).map((function(e){return i.createAudioAdaptationSet(l[e],u.captions,a._id,m.ContainerFormat.MP4)}))),v=this.createSubtitleAdaptationSets(a._id,u.subtitles,m.ContainerFormat.MP4);return a.AdaptationSet=[].concat(d,f,v),p.ModuleManager.has(g.ModuleName.Thumbnail)&&(a.AdaptationSet=p.ModuleManager.get(g.ModuleName.Thumbnail).extendAdaptationSets(a.AdaptationSet,t,this.baseURL)),a},e.isRepresentationSupported=function(e,t){var n=!e._mimeType.includes("audio")&&!e._mimeType.includes("video");if(!e._codecs||n)return!0;var i=o.CodecStringHelper.getExtractedCodecStrings(e._codecs);return Object.keys(i).every((function(e){return o.CodecStringHelper.isSupportedByRenderer(t,e+"/mp4",i[e])}))},e.prototype.canAssumeAudioMuxedIntoVideo=function(e){var t=e.media.filter((function(e){return A(e.attributes)}));return!t.length||t.some((function(e){return!e.attributes.URI}))},e.prototype.parseSegmentEncryptionInfo=function(e,t,n,i,r,a){for(var o,s=function(e){null!==u.memoizedParseBase64DataUrl(e.uri)?(void 0!==t.find((function(t){return!Object.keys(e).some((function(n){return e[n]!==t[n]}))}))||t.push(e),t.length>0&&(o=void 0)):o=(0,y.generateSegmentEncryptionInfo)(e,n,i,a,r)},u=this,l=0,c=null!=e?e:[];l<c.length;l++){s(c[l])}return o},e.prototype.translateSegmentList=function(e,t){var n,r,a,o,s,u=t.segments,l=[],c=[],f=null!==(a=null===(r=null===(n=e.BaseURL)||void 0===n?void 0:n[0])||void 0===r?void 0:r.url)&&void 0!==a?a:"",p="";f&&(p=(0,d.extractOrigin)(f)),this.memoizedParseBase64DataUrl.invalidate();for(var g=0,m=function(n){var r=u[n];f&&(r.uri=d.URLHelper.buildAbsoluteUrl(f,p,r.uri));var a=v.parseSegmentEncryptionInfo(r.keys,c,f,p,n,null===(o=e._hls)||void 0===o?void 0:o.mediaSequence);r.init&&(r.init.url=d.URLHelper.buildAbsoluteUrl(f,p,r.init.url),r.init.key=v.parseSegmentEncryptionInfo(r.init.keys,c,f,p,n,null===(s=e._hls)||void 0===s?void 0:s.mediaSequence));var m={_media:r.uri,_duration:r.duration,_mediaSequence:r.mediaSequence,_discontinuitySequenceNumber:r.discontinuitySequenceNumber,_metadata:[]};N(m,"_discontinuity",r.discontinuity),N(m,"_key",a),N(m,"_init",r.init),N(m,"_dateTime",r.dateTime),N(m,"_byteRange",(0,y.parseSegmentByteRange)(r.byterange)),N(m,"_playbackTime",P(t,g,r.dateTime)),["cueTag","scte35","customTags"].filter((function(e){return r.hasOwnProperty(e)})).forEach((function(e){return m._metadata=m._metadata.concat(r[e])})),r.parts&&(m._parts=r.parts.map((function(e){return i(i({},e),{uri:d.URLHelper.buildAbsoluteUrl(f,p,e.uri)})}))),l.push(m),g+=r.duration},v=this,S=0;S<u.length;S++)m(S);return{entries:l,contentProtectionInfos:this.parseAllContentProtection(c)}},e.prototype.parseAllContentProtection=function(e){var t=this,n=[];return e.forEach((function(e){var i=I(e,t.memoizedParseBase64DataUrl(e.uri));i?n.push(i):t.context.logger.debug("Encountered unknown/unsupported DRM scheme:",e.keyformat)})),n},e.prototype.dispose=function(){this.baseURL=null,this.memoizedParseBase64DataUrl.invalidate()},e}();function I(e,t){return p.ModuleManager.has(g.ModuleName.DRM)?p.ModuleManager.get(g.ModuleName.DRM).ContentProtectionHelper.parseContentProtection(e,t):null}function _(e){return e.split(",").map((function(e){return o.CodecStringHelper.hasOldAvc1CodecFormat(e)?o.CodecStringHelper.convertAvc1ToAvcotiCodecFormat(e):e})).join(",")}function b(e){e.forEach((function(e,t){e._bandwidth=128e3+32e3*t}))}function R(e,t,n){var i={};return i[l.MimeType.VideoMp4]=d.URLHelper.getAbsoluteUrl(e.uri,n),e.attributes.AUDIO&&O(t,e.attributes.AUDIO,n).forEach((function(e){i[e.id]=e.url})),i}function O(e,t,n){return e.filter((function(e){return A(e.attributes)&&e.attributes["GROUP-ID"]===t&&e.attributes.URI})).map((function(e){var t;return{id:e.attributes.NAME,url:d.URLHelper.getAbsoluteUrl(null!==(t=e.attributes.URI)&&void 0!==t?t:"",n)}}))}function A(e){return"AUDIO"===e.TYPE}function M(e,t){var n,i,r,a,o=e.attributes.BANDWIDTH===t.attributes.BANDWIDTH,s=(null===(n=e.attributes.RESOLUTION)||void 0===n?void 0:n.height)===(null===(i=t.attributes.RESOLUTION)||void 0===i?void 0:i.height)&&(null===(r=e.attributes.RESOLUTION)||void 0===r?void 0:r.width)===(null===(a=t.attributes.RESOLUTION)||void 0===a?void 0:a.width);return o&&s}function N(e,t,n){void 0!==n&&(e[t]=n)}function P(e,t,n){var i;return e.endList?(null!==(i=e.startTime)&&void 0!==i?i:0)+t:n?(0,c.toSeconds)(n.getTime()):void 0}t.M3u8Translator=E,function(e){function t(e,t){var n={};return e.forEach((function(e){var i=t[e["GROUP-ID"]]||t[Object.keys(t)[0]];e["AVERAGE-BANDWIDTH"]=i[0]["AVERAGE-BANDWIDTH"],e.BANDWIDTH=i[0].BANDWIDTH,e.CODECS=i[0].CODECS;var r=e["AVERAGE-BANDWIDTH"]||e.BANDWIDTH,a=Object.keys(n).find((function(t){var i,r,a=n[t].entries[0];return(null==a?void 0:a.NAME)===e.NAME&&o.CodecStringHelper.canSwitchBetweenAudioCodecs(null!==(i=null==a?void 0:a.CODECS)&&void 0!==i?i:"",null!==(r=e.CODECS)&&void 0!==r?r:"")}));if(a&&n[a]){var s=n[a];s.entries.push(e),s.MIN_BANDWIDTH=Math.min(s.MIN_BANDWIDTH,r),s.MAX_BANDWIDTH=Math.max(s.MAX_BANDWIDTH,r)}else{var u=e.CODECS?"".concat(e.NAME," (").concat(e.CODECS,")"):e.NAME;n[u]={entries:[e],MIN_BANDWIDTH:r,MAX_BANDWIDTH:r}}})),n}function n(e){var t={};return e.forEach((function(e){return t[e.NAME]=e})),t}function i(e){var t={cea608:[],cea708:[]};return e.forEach((function(e){e["INSTREAM-ID"]&&0===e["INSTREAM-ID"].indexOf("CC")?t.cea608.push(e):e["INSTREAM-ID"]&&0===e["INSTREAM-ID"].indexOf("SERVICE")&&t.cea708.push(e)})),t}function r(e,r,a){var o=e.playlists.filter(a),s=e.media.map((function(e){return e.attributes})),l=u(o),c=s.filter((function(e){var t=l[e["GROUP-ID"]];return A(e)&&(r||t)})),d=s.filter((function(e){return"SUBTITLES"===e.TYPE})),f=s.filter((function(e){return"CLOSED-CAPTIONS"===e.TYPE}));return{audio:t(c,l),subtitles:n(d),captions:i(f)}}function a(e){var t={};return e.forEach((function(e){var n=e.attributes.AUDIO;t[n]||(t[n]=[]);var i={"AVERAGE-BANDWIDTH":e.attributes["AVERAGE-BANDWIDTH"],BANDWIDTH:e.attributes.BANDWIDTH,CODECS:void 0};e.attributes.CODECS&&e.attributes.CODECS.indexOf(",")>-1&&(i.CODECS=l(e)),t[n].push(i)})),t}function s(e){Object.values(e).forEach((function(e){e.sort((function(e,t){return e.BANDWIDTH-t.BANDWIDTH}))}))}function u(e){var t=a(e);return s(t),t}function l(e){return e.attributes.CODECS.split(",").find((function(e){return o.CodecStringHelper.isAudioCodec(e)}))}e.extractMediaRenditions=r,e.getPropertiesForAudioGroups=u}(r||(t.HlsParsing=r={}))},52080:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StartTimeExtrapolationDirection=void 0,t.extrapolateStartTime=u,t.forwardExtrapolateSegmentStartTime=l,t.backwardExtrapolateSegmentStartTime=c,t.getStartTimeExtrapolationReferencePoint=d,t.getStartTimeViaDiscontinuityBoundaries=f,t.findDiscontinuityBoundary=p,t.hasDiscontinuityBoundary=g;var i,r=n(42055),a=n(81361),o=n(331),s=n(93326);function u(e,t,n){void 0===n&&(n=i.Bidirectional),n===i.Backward?c(e,t):(n===i.Forward||c(e,t),l(e,t))}function l(e,t){var n=t.index,i=t.startTime;e[n].startTime=i;for(var r=n+1;r<e.length;r++)e[r].startTime=e[r-1].startTime+e[r-1].duration}function c(e,t){var n=t.index,i=t.startTime;e[n].startTime=i;for(var r=n-1;r>=0;r--)e[r].startTime=e[r+1].startTime-e[r].duration}function d(e,t,n){var i,a;if(t.endList)return{index:0,startTime:(0,r.getStartTimeOffset)(n)};var u=e.segmentInfos[0];if(null==u?void 0:u.dateTime)return{index:0,startTime:(0,o.toSeconds)(u.dateTime.getTime())};var l=(0,s.getMostRecentlyRefreshedQuality)(n);if(l){var c=f(e,l);if(c)return c}return{index:0,startTime:null!==(a=null===(i=null==l?void 0:l.segmentInfos[0])||void 0===i?void 0:i.startTime)&&void 0!==a?a:(0,o.toSeconds)(t.requestTimestamp)-t.totalDuration}}function f(e,t){if(g(e.segmentInfos)&&g(t.segmentInfos)){var n=t.segmentInfos[t.segmentInfos.length-1],i=p(t.segmentInfos,n.discontinuitySequenceNumber),r=t.segmentInfos[i],a=p(e.segmentInfos,r.discontinuitySequenceNumber);if(null!=a)return{index:a,startTime:r.startTime}}}function p(e,t){return g(e)?(0,a.findIndexFromEnd)(e,(function(e){return Boolean(e.isDiscontinuityStart)&&e.discontinuitySequenceNumber===t})):-1}function g(e){if(!e.length)return!1;if(e[0].isDiscontinuityStart)return!0;var t=e[0],n=e[e.length-1];return t.discontinuitySequenceNumber!==n.discontinuitySequenceNumber}!function(e){e.Backward="backward",e.Forward="forward",e.Bidirectional="Bidirectional"}(i||(t.StartTimeExtrapolationDirection=i={}))},52600:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseTag=s;var i=n(27485),r=/^#(EXT[^:]*)(?::(.*))?$/,a=/^(?:"([^"]*)"|([^,=]+))(?:,|$)/g,o=/([^=]+)=*(?:"([^"]*)"|([^",]*))(?:,|$)/g;function s(e){var t,n=r.exec(e);if(!n)throw new Error("Invalid tag "+e);var s,u=n[1],l=n[2],c={};if(l){var d=new i.TextParser(l),f=void 0,p=d.readRegex(a);for(p&&(s=null!==(t=p[1])&&void 0!==t?t:p[2]);f=d.readRegex(o);){var g=f[1],m=f[2]||f[3];if(g.includes(",")){var v=g.split(","),S=v[0];g=v[1].trim(),c[S]=void 0}c[g]=m,d.skipWhitespace()}}return{name:u,attributes:c,value:s}}},52729:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.createNewMapEntry=u,t.updateMapEntry=l;var a=n(70016),o=n(28915),s=n(52080);function u(e,t){var n=e.segments.map((function(e){return f(e,t)}));return{qualityInfo:{lastUpdateTimestamp:Date.now()},segmentInfos:n}}function l(e,t,n){var a=e.segmentInfos,o=a.findIndex((function(e){return d(e,t.segments[0])}));if(-1===o)return u(t,n);var l=a.length-o,p=t.segments.length-l;if(0!==o||0!==p){var g=t.segments.slice(-p).map((function(e){return f(e,n)})),m=c(a);return null!=m&&(0,s.forwardExtrapolateSegmentStartTime)(g,{index:0,startTime:m}),{qualityInfo:i(i({},e.qualityInfo),{lastUpdateTimestamp:Date.now()}),segmentInfos:r(r([],a.slice(o),!0),g,!0)}}return e}function c(e){var t=e[e.length-1];if(void 0!==t.startTime&&void 0!==t.duration)return t.startTime+t.duration}function d(e,t){var n=e.url===t.uri,i=e.discontinuitySequenceNumber===t.discontinuitySequenceNumber,r=!e.byteRange&&!t.byterange,a=e.byteRange&&t.byterange&&e.byteRange.start===t.byterange.offset&&e.byteRange.end===t.byterange.offset+t.byterange.length;return n&&i&&(a||r)}function f(e,t){var n,i={url:e.uri,isInitSegment:!1,internalRepresentationId:t._internalId,isDiscontinuityStart:e.discontinuity,discontinuitySequenceNumber:e.discontinuitySequenceNumber,duration:e.duration,startTime:e.startTime,periodId:t._internalId.periodId,key:e.key,metadata:null!==(n=e.metadata)&&void 0!==n?n:[],mimeType:t._mimeType,bitrate:t._bandwidth,codecs:t._codecs};return e.byterange&&(i.byteRange={start:e.byterange.offset,end:e.byterange.offset+e.byterange.length}),e.init&&(i.init=e.init),e.dateTime&&(i.dateTime=e.dateTime),(0,a.isVideoRepresentation)(t)&&(i.height=t._height,i.width=t._width,i.frameRate=t._frameRate),i.segmentId=(0,o.generateSegmentId)(i),i}},53950:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.PlaylistUtils=t.isMediaPlaylist=t.isMasterPlaylist=void 0,t.calculateUpdateIntervalInSeconds=g,t.parseSegmentByteRange=S,t.generateSegmentEncryptionInfo=h,t.getLastMediaSequenceAndPartIndex=E,t.canMakeBlockingPlaylistReload=I,t.canPerformDeltaUpdate=_,t.calcNumberOfSegmentsToDrop=b,t.addNewSegments=R,t.isDeltaPlaylist=N;var r=n(52442),a=n(91397),o=n(81361),s=n(331),u=n(76885),l=n(93109),c=n(72927),d=function(e){return"playlists"in e};t.isMasterPlaylist=d;var f=function(e){return!(0,t.isMasterPlaylist)(e)};t.isMediaPlaylist=f;var p=function(){function e(){}return e.createDiscontinuitySequences=function(e,t){var n=e.segments.filter((function(e){return e.duration>0})),i=this.getStartingDiscontinuity(e,n,t),r={id:i,segments:[],duration:0},a=[r];return n.forEach((function(e,t){void 0!==e.discontinuity&&(i++,0===t?r.id=i:(r={id:i,duration:0,segments:[]},a.push(r))),e.discontinuitySequenceNumber=i,r.segments.push(e),r.duration+=e.duration})),a},e.getStartingDiscontinuity=function(t,n,i){var r=t.discontinuitySequence;return null==r&&(r=0!==i.length&&e.isLive(t)?this.findCurrentDiscontinuitySequenceNumber(n,i):0),r},e.findCurrentDiscontinuitySequenceNumber=function(e,t){for(var n=[],i=0,r=0;r<t.length-1;r++)i+=t[r].segments.length,n.push(i);var a=[];return e.forEach((function(e,t){void 0!==e.discontinuity&&a.push(t)})),0===n.length?t[0]?t[0].id:0:0===a.length?t[t.length-1].id:a.length!==n.length?a.length>n.length?t[0].id:t[1].id:this.findFirstPeriodByIndexTracking(n,a,t)},e.findFirstPeriodByIndexTracking=function(e,t,n){if(e[0]>t[0])return n[0].id;if(e[0]<t[0]){for(var i=e[0]-t[0],r=0,a=0;a<e.length;a++)e[a]+i<t[0]&&r++;return n[r].id}var o=function(){return e.every((function(e,n){return e===t[n]}))};if(o())return n[0].id;for(var s=1;e.length>0;){e.shift();for(a=0;a<e.length;a++)e[a]--;if(o())break;s++}return n[s].id},e.extrapolateProgramDateTime=function(e){var t=null;if(e.find((function(e,n){return e.dateTime&&(t=n),void 0!==e.dateTime})),null!==t){var n;for(n=t-1;n>=0;n--){var i=e[n+1].dateTime,r=Math.ceil((0,s.toMilliSeconds)(e[n].duration));e[n].dateTime=new Date(i.getTime()-r)}for(n=t+1;n<e.length;n++){var a=e[n-1].dateTime,o=Math.ceil((0,s.toMilliSeconds)(e[n-1].duration));e[n].dateTime=new Date(a.getTime()+o)}}},e.isLive=function(e){return!0!==e.endList},e.getProgramDateTimeFromSegmentUrl=function(e,t){if(e.indexOf("akamaihd.net")>-1){var n=e.match(/segment(\d{9})/);if(n&&2===n.length){var i=parseInt(n[1])*Math.round((0,s.toMilliSeconds)(t));return new Date(i)}}return null},e.correctStartOffsetToBounds=function(e,t){return e>0&&e>t?t:e<0&&Math.abs(e)>t?-t:e},e.getImpreciseStartOffsetFromStartOfPlaylist=function(e){for(var t=e.start.timeOffset,n=0,i=0;i<e.segments.length-1&&t-e.segments[i+1].duration>0;i++){var r=e.segments[i].duration;t-=r,n+=r}return n},e.getImpreciseStartOffsetFromEndOfPlaylist=function(e){for(var t=Math.abs(e.start.timeOffset),n=0,i=e.segments.length-1;i>=0&&v(t,1)>0;i--)t-=e.segments[i].duration,n+=v(e.segments[i].duration,2);return e.totalDuration-n},e.getImpreciseStartOffset=function(t){return t.start.timeOffset>=0?e.getImpreciseStartOffsetFromStartOfPlaylist(t):e.getImpreciseStartOffsetFromEndOfPlaylist(t)},e.getStartOffsetCloseToPlaylistEnd=function(t,n){var i=t.start.timeOffset;return i=(i=e.correctStartOffsetToBounds(i,t.totalDuration))<0?-v(Math.abs(i),2):v(i,2),Math.max(n,i)},e.getStartOffset=function(t){var n=t.start.timeOffset;return(n=e.correctStartOffsetToBounds(n,t.totalDuration))<0&&(n+=t.totalDuration),t.start.precise||(n=e.getImpreciseStartOffset(t)),Number(n.toFixed(2))},e.getBaseUrl=function(e,t){var n=e;return n=u.URLHelper.removeUrlParameters(n),!(t=u.URLHelper.removeLastPathPart(t))||t.indexOf("/")<0?e:u.URLHelper.concatUrlParts(n,t)},e}();function g(e,t){if(!p.isLive(t))return 1/0;var n=t.segments[t.segments.length-1];if(void 0!==n){if((0,l.isPlayingLowLatencyHls)(e)){var i=m(t,n);if(void 0!==i)return i}return Math.max(e.settings.MINIMUM_ALLOWED_UPDATE_PERIOD,n.duration)}return e.settings.MINIMUM_ALLOWED_UPDATE_PERIOD}function m(e,t){var n,i,r=t.parts;return r&&r.length>0?r[r.length-1].duration:null!==(i=null===(n=e.partInfo)||void 0===n?void 0:n.targetDuration)&&void 0!==i?i:void 0}function v(e,t){return Math.floor(e*Math.pow(10,t))/Math.pow(10,t)}function S(e){var t,n;if(e)return{start:null!==(t=e.offset)&&void 0!==t?t:0,end:(null!==(n=e.offset)&&void 0!==n?n:0)+e.length-1}}function h(e,t,n,a,o){if(e.method===r.HlsEncryptionMethod.NONE)return e;var s=new ArrayBuffer(16);return void 0!==a&&new DataView(s).setInt32(12,a+o),i(i({iv:s},e),{uri:u.URLHelper.buildAbsoluteUrl(t,n,e.uri)})}function y(e){var t;return(null===(t=null==e?void 0:e._hls)||void 0===t?void 0:t.requestTimestamp)?(0,s.toSeconds)(Date.now()-e._hls.requestTimestamp):1/0}function T(e,t){var n,i=null===(n=e._hls)||void 0===n?void 0:n.partTargetDuration;return i?i*t.LL_HLS_MAX_PLAYLIST_AGE_FOR_BLOCKING_REQUEST_IN_PART_TARGET_MULTIPLES:-1}function E(e){var t,n,i=null===(t=e.SegmentList)||void 0===t?void 0:t[0].SegmentURL,r=null==i?void 0:i[i.length-1],a=null!==(n=null==r?void 0:r._parts)&&void 0!==n?n:[];if(r&&0!==a.length){if((a=a.filter((function(e){return!e.isPreloadHint}))).length>0)return{mediaSequence:r._mediaSequence,partIndex:a.length-1};var o=null==i?void 0:i[i.length-2];if((null==o?void 0:o._parts)&&0!==o._parts.length)return{mediaSequence:o._mediaSequence,partIndex:o._parts.length-1}}}function I(e,t){var n,i,r=Boolean(null===(i=null===(n=e._hls)||void 0===n?void 0:n.serverControl)||void 0===i?void 0:i.canBlockReload),a=E(e),o=y(e)<=T(e,t);return r&&void 0!==a&&o}function _(e){var t,n,i=null===(n=null===(t=e._hls)||void 0===t?void 0:t.serverControl)||void 0===n?void 0:n.canSkipUntil;return void 0!==i&&!(y(e)>i/2)}function b(e,t,n){return 0===e.length?0:0===t.length?e.length:t[0]._mediaSequence-e[0]._mediaSequence-n}function R(e,t){if(0!==t.length)if(0!==e.length){var n=e[e.length-1],r=t[t.length-1]._mediaSequence-n._mediaSequence,a=(0,o.findFromEnd)(t,(function(e){return e._mediaSequence===n._mediaSequence})),s=i(i({},n),a);if(P(s),e[e.length-1]=s,0!==r){var u=t.slice(-r);u.forEach(P),O(s,u,["_init","_key"]),void 0!==n._playbackTime&&M(u,n._playbackTime+n._duration),e.push.apply(e,u)}}else e.push.apply(e,t)}function O(e,t,n){t.forEach((function(t){n.forEach((function(n){return A(n,e,t)}))}))}function A(e,t,n){t[e]&&!n[e]&&(n[e]=t[e])}function M(e,t){var n=t;e.forEach((function(e){void 0===e._playbackTime&&(e._playbackTime=n),n=e._playbackTime+e._duration}))}function N(e){return e.skippedSegmentsCount>0}function P(e){var t;e._media=(0,a.forceReallocation)(e._media),null===(t=e._metadata)||void 0===t||t.forEach((function(e){return(0,c.forceReallocateStringProps)(e)}))}t.PlaylistUtils=p},55877:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SEGMENT_TAGS=void 0,t.parseVariant=u,t.resolvePlaylistTag=c;var r=n(62510),a=n(56461),o=n(52600),s=n(47967);function u(e,n){void 0===n&&(n={});for(var i={version:1,allowCache:!0,hasM3u:!0,endList:!1,segments:[],dateRange:[],skippedSegmentsCount:0,targetDuration:0,totalDuration:0,mediaSequence:0},r=[],s=0;s<e.length;s++){var u=(0,a.removeTrailingWhitespaces)(e[s]);if(""!==u)if((0,a.isCustomTag)(u))(0,a.handleCustomTag)(i,r,u);else{var d=(0,o.parseTag)(u);if(t.SEGMENT_TAGS.includes(d.name))return(0,a.parseSegments)(e.slice(s),r,i,n),l(i),i;c(i,d,r)}}return l(i),i}function l(e){var t;"EVENT"===e.playlistType&&(e.discontinuitySequence=null!==(t=e.discontinuitySequence)&&void 0!==t?t:0)}function c(e,t,n){switch(t.name){case"EXT-X-VERSION":e.version=Number(t.value);break;case"EXT-X-START":e.start=(0,s.generateStartAttributes)(t);break;case"EXT-X-INDEPENDENT-SEGMENTS":e.independentSegments=!0;break;case"EXT-X-TARGETDURATION":t.value&&(e.targetDuration=parseInt(t.value));break;case"EXT-X-MEDIA-SEQUENCE":t.value&&(e.mediaSequence=parseInt(t.value));break;case"EXT-X-DISCONTINUITY-SEQUENCE":t.value&&(e.discontinuitySequence=parseInt(t.value));break;case"EXT-X-PLAYLIST-TYPE":e.playlistType="EVENT"===t.value?"EVENT":"VOD";break;case"EXT-X-PART-INF":e.partInfo=(0,s.generatePartInfoTag)(t.attributes);break;case"EXT-X-ALLOW-CACHE":e.allowCache="NO"!==t.value;break;case"EXT-X-SERVER-CONTROL":e.serverControl=(0,s.generateServerControlTag)(t);break;case"EXT-X-DATERANGE":e.dateRange.push((0,s.generateDateRange)(t));break;case"EXTM3U":break;default:n.push(i({type:r.MetadataType.CUSTOM},t)),(0,a.addCustomTagToPlaylistTagList)(e,t)}}t.SEGMENT_TAGS=["EXTINF","EXT-X-BYTERANGE","EXT-X-DISCONTINUITY","EXT-X-PROGRAM-DATE-TIME","EXT-X-KEY","EXT-X-GAP","EXT-X-BITRATE","EXT-X-PART","EXT-X-MAP","EXT-X-CUE-OUT-CONT","EXT-X-CUE-OUT","EXT-X-CUE-IN","EXT-X-SCTE35","EXT-X-SKIP"]},56461:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.parseSegments=d,t.addCustomTagToPlaylistTagList=f,t.handleCustomTag=p,t.removeTrailingWhitespaces=T,t.isCustomTag=E;var a=n(52442),o=n(62510),s=n(67550),u=n(52600),l=n(55877),c=n(47967);function d(e,t,n,i){void 0===i&&(i={});for(var o,d=[],f=0,I=(0,c.generateSegmentEntrySkeleton)(),_=r([],t,!0),b=0,R=n.mediaSequence,O={keys:[],keyInUse:!1},A=function(){I=(0,c.generateSegmentEntrySkeleton)(),_=[],b=0},M=function(e){g(I,b,e,o,O,_,R),d.push(I),f+=b,R++,A()},N=function(e){var t,s,f=(0,u.parseTag)(e);switch(f.name){case"EXTINF":b=parseFloat(f.value);break;case"EXT-X-BYTERANGE":I.byterange=(0,c.generateByteRangeTag)(f,d);break;case"EXT-X-DISCONTINUITY":I.discontinuity=!0;break;case"EXT-X-PROGRAM-DATE-TIME":v(I,f);break;case"EXT-X-KEY":if("NONE"===(null===(t=f.attributes)||void 0===t?void 0:t.METHOD)){O.keys=[{method:a.HlsEncryptionMethod.NONE}];break}m(f,O);break;case"EXT-X-MAP":o=(0,c.generateMapTag)(f,O);break;case"EXT-X-DATERANGE":n.dateRange.push((0,c.generateDateRange)(f));break;case"EXT-X-CUE-OUT-CONT":I.cueTag=(0,c.generateCueTag)(f,"CUE-OUT-CONT");break;case"EXT-X-CUE-OUT":I.cueTag=(0,c.generateCueTag)(f,"CUE-OUT");break;case"EXT-X-CUE-IN":I.cueTag=(0,c.generateCueTag)(f,"CUE-IN");break;case"EXT-X-SCTE35":S(I,f);break;case"EXT-X-PART":h(f,I,i);break;case"EXT-X-PRELOAD-HINT":var p=y(f,n.partInfo,i);p.init&&(o=p.init,O.keys.length>0&&(o.keys=r([],O.keys,!0),O.keyInUse=!0)),p.part&&(I.parts=null!==(s=I.parts)&&void 0!==s?s:[],I.parts.push(p.part));break;case"EXT-X-SKIP":var g=(0,c.generateSkipTag)(f);n.skippedSegmentsCount=g.skippedSegments,R+=g.skippedSegments;break;case"EXT-X-ENDLIST":n.endList=!0;break;default:(0,l.resolvePlaylistTag)(n,f,_)}},P=0,D=e;P<D.length;P++){var C=T(D[P]);if(""!==C)!C.startsWith("#")?M(C):E(C)?p(n,_,C):N(C)}I.parts&&I.parts.length>0&&(b=I.parts.reduce((function(e,t){return e+t.duration}),0),M(s.PHANTOM_SEGMENT_URL)),n.segments=d,n.totalDuration=f}function f(e,t){void 0===e.tags&&(e.tags=[]),t&&e.tags.push(t)}function p(e,t,n){t.push({type:o.MetadataType.CUSTOM,attributes:n}),f(e,(0,c.generateCustomTag)(n))}function g(e,t,n,i,a,o,s){e.duration=t,e.uri=n,e.mediaSequence=s,i&&(e.init=i),o.length>0&&(e.customTags=o),a.keys.length>0&&(e.keys=r([],a.keys,!0),a.keyInUse=!0)}function m(e,t){t.keyInUse&&(t.keys=[],t.keyInUse=!1);var n=(0,c.generateKeyTag)(e);void 0!==n&&t.keys.push(n)}function v(e,t){t.value&&(e.dateTime=new Date(t.value))}function S(e,t){var n;void 0===e.scte35&&(e.scte35=[]),e.scte35.push({type:o.MetadataType.SCTE,attributes:i({CUE:null===(n=t.attributes)||void 0===n?void 0:n.CUE},t.attributes)})}function h(e,t,n){var i;if(n.parsePartTags){t.parts=null!==(i=t.parts)&&void 0!==i?i:[];var r=t.parts[t.parts.length-1];t.parts.push((0,c.generatePartTag)(e.attributes,r))}}function y(e,t,n){return n.parsePartTags&&t?(0,c.generatePreloadHintTag)(e.attributes,t.targetDuration):{}}function T(e){return e.replace(/^[ \t]+/g,"")}function E(e){return/^#(?!EXT)/m.test(e)}},59692:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parsePlaylist=c;var i,r=n(56461),a=n(52600),o=n(55877),s=n(47967),u=/\r\n|\r(?=[^\n]|$)/gm,l=/^#EXTM3U($|[ \t\n])/m;function c(e,t){void 0===t&&(t={});var n=-1===e.indexOf("#EXTINF")?i.Master:i.Media,c=e.replace(u,"\n").trim().split(/\n+/m),f=(0,r.removeTrailingWhitespaces)(c[0]);if(!l.test(f))throw new Error("Playlist parsing failed: missing #EXTM3U tag");if(n===i.Media)return(0,o.parseVariant)(c,t);for(var p,g=[],m=[],v=[],S=1,h=!0,y=!1,T=!0,E=0;E<c.length;E++){var I=(0,r.removeTrailingWhitespaces)(c[E]),_=c[E+1];if((0,r.isCustomTag)(I))d(g,I);else if(T)T=!1;else{var b=(0,a.parseTag)(I);switch(b.name){case"EXT-X-STREAM-INF":v.push((0,s.generateStreamInfTag)(b,(0,r.removeTrailingWhitespaces)(_))),T=!0;break;case"EXT-X-MEDIA":var R=(0,s.generateRendition)(b);m.push(R),g.push(R);break;case"EXT-X-VERSION":S=Number(b.value);break;case"EXT-X-START":p=(0,s.generateStartAttributes)(b),g.push(b);break;case"EXT-X-ALLOW-CACHE":h="YES"===b.value||void 0===b.value;break;case"EXT-X-INDEPENDENT-SEGMENTS":y=!0,g.push(b);break;case"EXT-X-I-FRAME-STREAM-INF":(0,s.generateIFrameStreamInfTag)(b),g.push(b);break;case"EXT-X-SESSION-DATA":default:g.push(b);break;case"EXT-X-SESSION-KEY":(0,s.generateSessionKeyTag)(b),g.push(b)}}}var O={version:S,allowCache:h,independentSegments:y,hasM3u:!0,media:m,playlists:v,tags:g=g.concat(v)};return p&&(O.start=p),O}function d(e,t){var n=(0,s.generateCustomTag)(t);n&&e.push(n)}!function(e){e.Master="master",e.Media="media"}(i||(i={}))},64697:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodecDetector=void 0,t.extractCodecFromTsSegment=p,t.extractCodecFromMp4Segment=g,t.mergeSegmentCodecs=v,t.isFmp4Segment=S;var i=n(52442),r=n(28764),a=n(88005),o=n(81361),s=n(76885),u=n(91520),l=n(16368),c=n(41108),d=n(53950),f=function(){function e(e){this.context=e,this.loader=new a.DefaultContentLoader(this.context)}return e.prototype.fetchSegment=function(e){if(!this.context.segmentPrefetchingService)return Promise.reject();var t=this.context.segmentPrefetchingService.hasPrefetchedSegment(e)?this.context.segmentPrefetchingService.getPrefetchedSegment(e):this.context.segmentPrefetchingService.fetch(e);return t||Promise.reject()},e.prototype.probeCodecs=function(e,t,n,a,o){var u=this;void 0===o&&(o=t?e.length-1:0);var l=e.slice(),c=t?l.pop():l.shift();if(!c)return Promise.reject(new Error("Could not load any segment"));var d,f=c.init,p=void 0!==f,g=(0,s.extractOrigin)(n),m=s.URLHelper.buildAbsoluteUrl(n,g,p?f.url:c.uri),v=p?f.keys:c.keys,S=y(null!=v?v:[],n,g,a,o);(null==S?void 0:S.method)===i.HlsEncryptionMethod.AES_128&&(d=S);var T=h(m,c,d);return this.fetchSegment(T).then((function(e){return u.extractCodecsFromSegment(e)})).catch((function(e){if(e instanceof r.PlayerError)return Promise.reject(e);u.context.logger.debug("Codec probing from segment failed",e);var i=t?o-1:o+1;return u.probeCodecs(l,t,n,a,i)})).then((function(e){return u.context.logger.debug("Probed codec is",e),e}))},e.prototype.extractCodecsFromSegment=function(e){return S(e)?g(e):p(e,this.context)},e}();function p(e,t){if(!u.ModuleManager.has(l.ModuleName.ContainerTS))return Promise.reject(new c.PlayerModuleMissingError(l.ModuleName.ContainerTS));var n=m(t);return n.transmuxSegment(e).then((function(t){var n=v(t.transmuxedSegments);if(!n)throw"couldn't detect codec for ".concat(e.getMimeType());return n})).finally((function(){n.dispose()}))}function g(e){if(!u.ModuleManager.has(l.ModuleName.ContainerMP4))return Promise.reject(new c.PlayerModuleMissingError(l.ModuleName.ContainerMP4));var t=u.ModuleManager.get(l.ModuleName.ContainerMP4).getCodecFromSegment(e);return t?Promise.resolve(t):Promise.reject("couldn't detect codec for ".concat(e.getMimeType()))}function m(e){return new(u.ModuleManager.get(l.ModuleName.ContainerTS).WebWorkerTransmuxer)(e,!1)}function v(e){for(var t="",n=0,i=e;n<i.length;n++){var r=i[n];if(!r.getCodec())return null;t+=(""===t?"":",")+r.getCodec()}return t}function S(e){for(var t=e.getData(),n=new DataView(t,0,t.byteLength),i=[],r=0;r+8<n.byteLength;){var a=n.getUint32(r);if(0===a)break;var s=o.ArrayHelper.extractAsciiString(n,r+4,4);i.push(s),r+=a}return e.isInit()?["ftyp","moov"].every((function(e){return i.includes(e)})):["moof","mdat"].every((function(e){return i.includes(e)}))}function h(e,t,n){return{url:e,key:n,byteRange:(0,d.parseSegmentByteRange)(t.byterange),mimeType:"unknown/mp4",internalRepresentationId:null,isInitSegment:Boolean(t.init),preventDownloadCanceling:!0}}function y(e,t,n,i,r){for(var a,o=0,s=e;o<s.length;o++){var u=s[o];a=(0,d.generateSegmentEncryptionInfo)(u,t,n,i,r)}return a}t.CodecDetector=f},64732:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HLSModuleDefinition=void 0;var i=n(16368),r=n(26527),a=n(33827),o=n(22675),s=n(59692),u=n(53950),l=n(79367),c=n(78148),d=n(34435),f={setPresentationTimeOffset:l.setPresentationTimeOffset,setTimestampRolloverPositions:l.setTimestampRolloverPositions},p={areDiscontinuitiesMisaligned:d.areDiscontinuitiesMisaligned,getPresentationTimeOffset:d.getPresentationTimeOffset,getHlsState:d.getHlsState,getPlaylistType:d.getPlaylistType,getIsLowLatencyPlaylist:d.getIsLowLatencyPlaylist,getEndlist:d.getEndlist,getDefaultLanguages:d.getDefaultLanguages,getCustomTags:d.getCustomTags,getDiscoSequenceTiming:d.getDiscoSequenceTiming,getDiscoSequenceTimings:d.getDiscoSequenceTimings,getFirstDiscoSequenceTiming:d.getFirstDiscoSequenceTiming,getMergedDiscontinuityTimings:d.getMergedDiscontinuityTimings};t.HLSModuleDefinition={name:i.ModuleName.HLS,module:function(){return{HlsTimelineTracker:a.HlsTimelineTracker,M3u8Loader:o.M3u8Loader,parsePlaylist:s.parsePlaylist,PlaylistUtils:u.PlaylistUtils,DiscontinuitySequenceNumberTracker:r.DiscontinuitySequenceNumberTracker,HlsReducer:c.HlsReducer,actions:f,selectors:p,canMakeBlockingPlaylistReload:u.canMakeBlockingPlaylistReload}},dependencies:[i.ModuleName.EngineBitmovin]},t.default=t.HLSModuleDefinition},72927:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.forceReallocateStringProps=r;var i=n(91397);function r(e){Object.keys(e).forEach((function(t){"string"==typeof e[t]&&(e[t]=(0,i.forceReallocation)(e[t])),"object"==typeof e[t]&&r(e[t])}))}},73471:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.M3u8RepresentationFactory=void 0;var i=n(52442),r=n(33696),a=n(42283),o=n(26190),s=n(76885),u=n(77870),l=n(53950),c=function(){function e(e,t){this.context=e,this.sourceContext=t}return e.prototype.create=function(e,t,n,r,a){if(void 0===a&&(a=!0),!e)return null;var o=this.createBaseRepresentation(e,r);if(t===i.RepresentationType.AUDIO)o=this.extendToAudioRepresentation(e,o,n,r);else if(t===i.RepresentationType.VIDEO)o=this.extendToVideoRepresentation(e,o,n,r,a);else{if(t!==i.RepresentationType.SUBTITLE)return null;o=this.extendToSubtitleRepresentation(e,o,n,r)}var s=this.getLabelForRepresentation(o);return s&&(o._label=s),o},e.prototype.createBaseRepresentation=function(e,t){var n=e.uri||e.URI;n&&!s.URLHelper.isUrlAbsolute(n)&&(n=s.URLHelper.concatBaseUrlWithPartial(t,n));var i={_id:"",_mimeType:"",_bandwidth:0,_codecs:"",_hls:{requestTimestamp:0},BaseURL:null,SegmentList:[{SegmentURL:[],entries:[],totalDuration:0}],Uri:n};return e.NAME&&(i._label=e.NAME),i},e.prototype.extendToVideoRepresentation=function(e,t,n,i,r){var o=t;if(o._mimeType="video/"+n,o._width=0,o._height=0,o.BaseURL=[{url:l.PlaylistUtils.getBaseUrl(i,e.uri)}],o._bandwidth=e.attributes["AVERAGE-BANDWIDTH"]||e.attributes.BANDWIDTH,o._bandwidth||(o._bandwidth=this.context.settings.MIN_SELECTABLE_VIDEO_BITRATE),o._bandwidth=Number(o._bandwidth),e.attributes.CODECS){var s=a.CodecStringHelper.getExtractedCodecStrings(e.attributes.CODECS);a.CodecStringHelper.hasOldAvc1CodecFormat(s.video)&&(s.video=a.CodecStringHelper.convertAvc1ToAvcotiCodecFormat(s.video)),r?s.video&&s.audio?o._codecs=s.video+","+s.audio:s.video&&!s.audio?o._codecs=s.video:!s.video&&s.audio&&(o._codecs=s.audio):o._codecs=s.video}return e.attributes["FRAME-RATE"]&&(o._frameRate=parseFloat(e.attributes["FRAME-RATE"])),e.attributes.RESOLUTION&&(o._width=e.attributes.RESOLUTION.width,o._height=e.attributes.RESOLUTION.height,o._id+=e.attributes.RESOLUTION.height+"_"),o._id+=e.attributes.BANDWIDTH,o},e.prototype.extendToAudioRepresentation=function(e,t,n,i){var r,a,o,s=t;if(s._id=e.NAME+" "+e["GROUP-ID"]||"",s._mimeType="audio/"+n,s._codecs=e.CODECS||(null===(r=e.attributes)||void 0===r?void 0:r.CODECS),s._name=e.NAME||"",s._groupId=e["GROUP-ID"]||"",s._label=s._name+" "+s._groupId,s._bandwidth=null!==(o=null!==(a=e["AVERAGE-BANDWIDTH"])&&void 0!==a?a:e.BANDWIDTH)&&void 0!==o?o:0,s.BaseURL=[{url:l.PlaylistUtils.getBaseUrl(i,e.URI||e.uri)}],e.CHANNELS){var c=e.CHANNELS.split("/")[0];s.AudioChannelConfiguration=[{_schemeIdUri:u.DashSchemeUri.AudioChannelConfig,_value:c}]}return s},e.prototype.extendToSubtitleRepresentation=function(e,t,n,i){var r=t;return r._id=e.NAME,r._mimeType="application/"+n,r._codecs="wvtt",r._name=e.NAME||"",r.BaseURL=[{url:l.PlaylistUtils.getBaseUrl(i,e.URI)}],r._hls.isForced="YES"===e.FORCED,r},e.prototype.getLabelForRepresentation=function(e){var t={id:e._id,mimeType:e._mimeType,width:e._width,height:e._height,bitrate:e._bandwidth};return(0,o.applyLabeling)({source:this.sourceContext.source,context:this.context,streamType:r.StreamType.Hls,labelingType:o.LabelingType.Qualities},t)},e}();t.M3u8RepresentationFactory=c},73725:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.M3u8DashManifest=void 0;var a=n(60997),o=n(63331),s=n(42055),u=n(81361),l=n(3941),c=n(331),d=n(76885),f=n(54838),p=n(94938),g=n(38353),m=n(41735),v=n(77874),S=n(31043),h=n(18665),y=n(79814),T=n(97662),E=n(52729),I=n(49422),_=n(53950),b=n(52080),R=n(79367),O=n(34435),A=function(){function e(e,t){this.lastStartOffset=-1/0,this.context=e,this.sourceContext=t,this.m3u8Translator=new I.M3u8Translator(e,t),this.totalDurationSec=1/0,this.sourceStore=e.serviceManager.get(h.ServiceName.SourceStoreService,t.sourceIdentifier)}return e.prototype.updateBaseUrl=function(e){this.m3u8Translator.updateBaseUrl(e)},e.prototype.getBaseUrl=function(){return this.m3u8Translator.getBaseUrl()},e.prototype.getCurrentHlsState=function(){return(0,O.getHlsState)(this.sourceStore.getState())},e.prototype.findAdaptationSet=function(e){return this.getManifest().Period[0].AdaptationSet.find((function(t){var n;return t._internalId.adaptationSetId===e||(null===(n=t.ContentComponent)||void 0===n?void 0:n.some((function(t){return t._id===e})))}))},e.prototype.createDashManifestSkeleton=function(e){var t=this.m3u8Translator.createDashManifestSkeleton(e);return t=(0,g.initializeInternalIds)(t),t=(0,p.orderAdaptationSetsByCodec)(t,(0,g.getCodecPriorities)(this.context.config,this.sourceContext.source))},e.prototype.extractBackupStreams=function(e){return this.m3u8Translator.extractBackupStreams(e)},e.prototype.onMasterPlaylistAvailable=function(e){var t=this.createDashManifestSkeleton(e);this.sourceStore.dispatch((0,m.setManifestAction)(t)),this.sourceStore.dispatch((0,R.setDefaultLanguages)(e)),this.sourceStore.dispatch((0,R.setCustomTags)(e)),this.sourceStore.dispatch((0,R.setMasterPlaylistString)(e.rawString))},e.prototype.getManifest=function(){return(0,v.getManifest)(this.sourceStore.getState())},e.prototype.getAllVariantRepresentions=function(){var e=function(e){return y.MimeTypeHelper.isAV(e._mimeType)||y.MimeTypeHelper.isSubtitle(e._mimeType)||y.MimeTypeHelper.isImage(e._mimeType)};return this.getManifest().Period[0].AdaptationSet.filter(e).flatMap((function(e){return e.Representation}))},e.prototype.onMediaLoaded=function(e,t,n){var i=this;this.sourceStore.dispatch((0,R.setMasterPlaylistString)(e)),this.sourceStore.dispatch((0,R.setEndlist)(Boolean(n.endList))),n.playlistType&&!(0,O.getPlaylistType)(this.getCurrentHlsState())&&this.sourceStore.dispatch((0,R.setPlaylistType)(n.playlistType)),void 0===(0,O.getIsLowLatencyPlaylist)(this.getCurrentHlsState())&&this.sourceStore.dispatch((0,R.setIsLowLatencyPlaylist)(void 0!==n.partInfo)),this.calculatePlaylistProgramDateTime(n),this.calculatePlaylistStartTime(n);var r=this.getManifest().Period[0],a=this.getRepresentationsFromUrl(t.Uri,r).map((function(e){return i.updateRepresentation(e,n)}));return y.MimeTypeHelper.isAV(t._mimeType)&&(this.trackPlaylistTiming(n,a),this.updateDashManifestProperties(n)),a.forEach((function(e){var t=i.determineFirstDiscoStartTime(e,n);i.updatePeriodStartAndDuration(r,t,!n.endList),i.ensureContentProtectionSignaledOnAdaptationSet(e),i.context.settings.ENABLE_SEGMENT_INFO_PROVIDER_FROM_STORE&&i.updateSegmentInfoMap(n,e)})),this.context.serviceManager.maybeCall(h.ServiceName.TimedMetadataService,(function(e){return(0,o.extractDateRangeMetadata)(n.dateRange,n.endList?n.programDateTime:void 0,(function(t){e.processDateRangeEvent(t,l.DEFAULT_PERIOD_ID)}))})),a.find((function(e){return e._id===t._id}))},e.prototype.ensureContentProtectionSignaledOnAdaptationSet=function(e){var t,n=null===(t=e.ContentProtection)||void 0===t?void 0:t.map((function(e){return{_schemeIdUri:e._schemeIdUri,pssh:[],pro:[]}})),i=this.findAdaptationSet(e._internalId.adaptationSetId);if(i&&n){var a=n.some((function(e){var t;return!(null===(t=i.ContentProtection)||void 0===t?void 0:t.some((function(t){return t._schemeIdUri===e._schemeIdUri})))}));if(a){var o=i.ContentProtection?(0,f.removeDuplicates)(r(r([],i.ContentProtection,!0),n,!0)):n;this.sourceStore.dispatch((0,m.updateAdaptationSetAction)(e._internalId,{ContentProtection:o}))}}},e.prototype.updateSegmentInfoMap=function(e,t){var n,i,r=t._internalId.key(),a=null===(n=this.sourceStore.getState())||void 0===n?void 0:n.segmentInfoMap[r];a&&(i=(0,E.updateMapEntry)(a,e,t))||(i=(0,E.createNewMapEntry)(e,t),this.initializeStartTimesOnSegmentMapEntry(i,e)),this.sourceStore.dispatch((0,S.setSegmentInfos)(r,i))},e.prototype.initializeStartTimesOnSegmentMapEntry=function(e,t){var n=this.sourceStore.getState();if(n){var i=(0,b.getStartTimeExtrapolationReferencePoint)(e,t,n);(0,b.extrapolateStartTime)(e.segmentInfos,i)}},e.prototype.determineFirstDiscoStartTime=function(e,t){var n,i,r,a=null!==(r=null===(i=null===(n=e.SegmentList)||void 0===n?void 0:n[0])||void 0===i?void 0:i.SegmentURL)&&void 0!==r?r:[];return a.length>0&&null!=a[0]._playbackTime?(0,O.getFirstDiscoSequenceTiming)(this.getCurrentHlsState()).startTime:t.startTime},e.prototype.updatePeriodStartAndDuration=function(e,t,n){var i=0===e.duration?this.totalDurationSec:Math.min(e.duration,this.totalDurationSec),r=n?1/0:i,a={start:t,_start:"PT".concat(t,"S"),duration:r,_duration:"PT".concat(i,"S")};this.sourceStore.dispatch((0,m.updatePeriodTimingAction)(l.DEFAULT_PERIOD_ID,a))},e.prototype.trackPlaylistTiming=function(e,t){var n,i,r,a,o,s=e.totalDuration,u=null!==(n=t.find((function(t){return t.Uri===e.uri})))&&void 0!==n?n:t[0],l=null===(r=null===(i=null==u?void 0:u.SegmentList)||void 0===i?void 0:i[0])||void 0===r?void 0:r.SegmentURL,c=(null!==(a=null==l?void 0:l.length)&&void 0!==a?a:0)>0&&void 0!==(null===(o=null==l?void 0:l[0])||void 0===o?void 0:o._playbackTime);if(l&&c){var d=(0,T.getTimingInfoFromSegmentList)(l);s=d.totalDuration,this.sourceStore.dispatch((0,R.setDiscoTimings)(u._mimeType,d.discoTimings))}this.totalDurationSec=e.endList?Math.min(this.totalDurationSec,s):s},e.prototype.updateDashManifestProperties=function(e){var t,n=!e.endList,i=(0,v.getManifest)(this.sourceStore.getState()),r={_requestTimestamp:e.requestTimestamp,_type:n?"dynamic":"static",_mediaPresentationDuration:n?1/0:"PT".concat(this.totalDurationSec,"S"),_maxSegmentDuration:"PT".concat(e.targetDuration,"S"),_hasIndependentSegments:i._hasIndependentSegments||e.independentSegments};e.start&&(e.start.timeOffset=_.PlaylistUtils.getStartOffsetCloseToPlaylistEnd(e,this.lastStartOffset),this.lastStartOffset=e.start.timeOffset,r._startOffset=_.PlaylistUtils.getStartOffset(e)),n&&(r._minimumUpdatePeriod="PT".concat(e.targetDuration,"S"),r._timeShiftBufferDepth=-this.totalDurationSec,r._availabilityStartTime=null!==(t=i._availabilityStartTime)&&void 0!==t?t:new Date(e.requestTimestamp-(0,c.toMilliSeconds)(this.totalDurationSec)).toISOString()),this.sourceStore.dispatch((0,m.updateManifestAction)(r))},e.prototype.getRepresentationsFromUrl=function(e,t){return t.AdaptationSet.flatMap((function(t){return t.Representation.filter((function(t){return d.URLHelper.isSubUrl(e,t.Uri)}))}))},e.prototype.updateRepresentation=function(e,t){var n=i({},e);return n._requestTimestamp=t.requestTimestamp,n._updateInterval=(0,_.calculateUpdateIntervalInSeconds)(this.context,t),n._hls=i(i({},n._hls),{requestTimestamp:t.requestTimestamp,mediaSequence:t.mediaSequence,serverControl:t.serverControl,loadFailureReason:null}),t.partInfo&&(n._hls.partTargetDuration=t.partInfo.targetDuration),n.SegmentList=[i({},n.SegmentList[0])],n.SegmentList[0].SegmentURL=r([],n.SegmentList[0].SegmentURL,!0),this.updateSegmentList(n,t),this.sourceStore.dispatch((0,m.updateRepresentationAction)(n)),n},e.prototype.updateSegmentList=function(t,n){var i;if(0!==n.segments.length){if(t.SegmentList){this.context.logger.debug("Updating segment list for playlist ".concat(t._id));var a=this.getTranslatedSegmentList(t,n);if(a.contentProtectionInfos.length>0){var o=e.contentProtectionToXmlJson(a.contentProtectionInfos);if((0,_.isDeltaPlaylist)(n)){var s=null!==(i=t.ContentProtection)&&void 0!==i?i:[];t.ContentProtection=(0,f.removeDuplicates)(r(r([],s,!0),o,!0))}else t.ContentProtection=o}var l=t.SegmentList[0].SegmentURL,c=a.entries,d=(0,_.calcNumberOfSegmentsToDrop)(l,c,n.skippedSegmentsCount);(0,u.removeEntriesFromStart)(l,d),(0,_.addNewSegments)(l,c),t.SegmentList[0]._duration=n.targetDuration,t.SegmentList[0]._timescale=1}}else this.context.logger.debug("No segments for representation ".concat(t._id," in loaded playlist"))},e.prototype.getTranslatedSegmentList=function(e,t){if(t.endList){var n=this.context.serviceManager.maybeCall(h.ServiceName.ManifestCachingService,(function(t){return t.getSegmentList(e.Uri)}),null,this.sourceContext.sourceIdentifier);return n||(n=this.m3u8Translator.translateSegmentList(e,t),this.context.serviceManager.maybeCall(h.ServiceName.ManifestCachingService,(function(t){return t.cacheSegmentList(n,e.Uri)}),null,this.sourceContext.sourceIdentifier)),n}return this.m3u8Translator.translateSegmentList(e,t)},e.prototype.calculatePlaylistProgramDateTime=function(e){var t=e.segments[0];(null==t?void 0:t.dateTime)&&(e.programDateTime=(0,c.toSeconds)(t.dateTime.getTime()))},e.prototype.calculatePlaylistStartTime=function(e){var t=e.segments[0];e.endList?(e.startTime=(0,s.getStartTimeOffset)(this.sourceStore.getState()),e.segments[0].startTime=e.startTime):t&&t.dateTime?(e.startTime=(0,c.toSeconds)(t.dateTime.getTime()),e.segments[0].startTime=e.startTime):e.startTime=(0,c.toSeconds)(e.requestTimestamp)-e.totalDuration},e.contentProtectionToXmlJson=function(e){return e.map((function(e){var t={_schemeIdUri:e.schemeIdUri};return e.defaultKid&&(t["_cenc:default_KID"]=e.defaultKid),e.pssh&&(t.pssh=[{__text:e.pssh}]),t}))},e.prototype.dispose=function(){this.m3u8Translator=(0,a.dispose)(this.m3u8Translator)},e}();t.M3u8DashManifest=A},78148:function(e,t,n){"use strict";var i,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},a=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.HlsReducer=void 0,t.extrapolateSegmentStartTime=y;var o=n(21829),s=n(90993);function u(e,t){return e.masterPlaylist.string?e:r(r({},e),{masterPlaylist:{string:t}})}function l(e,t){return r(r({},e),{playlistType:t})}function c(e,t){return r(r({},e),{isLowLatencyPlaylist:t})}function d(e,t){return r(r({},e),{endlist:t})}function f(e,t){var n,i=t.mimeType,a=t.discoTimings;return r(r({},e),{discontinuitySequenceTiming:r(r({},e.discontinuitySequenceTiming),(n={},n[i]=a,n))})}function p(e,t){var n={},i={};return t.media.forEach((function(e){var t=Boolean(e.attributes);if(t){var r=e.attributes,a=r.TYPE,o=r.LANGUAGE;if(Boolean(o)&&null!=o)t&&"YES"===e.attributes.DEFAULT&&!n[a]&&(n[a]={name:e.attributes.NAME,language:o}),"YES"===e.attributes.AUTOSELECT&&!i[a]&&(i[a]={name:e.attributes.NAME,language:o})}})),r(r({},e),{defaultLanguages:r(r({},i),n)})}function g(e,t){var n=t;return r(r({},e),{customTags:n.tags})}function m(e,t){var n,i=t.discoSequenceNumber,a=t.presentationTimeOffset;return r(r({},e),{presentationTimeOffsets:r(r({},e.presentationTimeOffsets),(n={},n[i]=a,n))})}function v(e,t){return r(r({},e),{timestampRolloverPositions:t})}function S(e,t){return r(r({},e),{contentLocationId:t.locationId})}function h(){return{masterPlaylist:{string:""},defaultLanguages:{},discontinuitySequenceTiming:{},presentationTimeOffsets:{},timestampRolloverPositions:{previous:-1,next:-1}}}function y(e,t,n){var i=a([],e,!0);return i[t]=r({},i[t]),i[t].startTime=n,E(i,t),T(i,t),i}function T(e,t){for(var n=t+1;n<e.length;n++)e[n]=r({},e[n]),e[n].startTime=e[n-1].startTime+e[n-1].duration}function E(e,t){for(var n=t-1;n>=0;n--)e[n]=r({},e[n]),e[n].startTime=e[n+1].startTime-e[n].duration}t.HlsReducer=(0,o.default)(h(),((i={})[s.HlsActionType.SetMasterPlaylistString]=function(e,t){return u(e,t.payload)},i[s.HlsActionType.SetPlaylistType]=function(e,t){return l(e,t.payload)},i[s.HlsActionType.SetIsLowLatencyPlaylist]=function(e,t){return c(e,t.payload)},i[s.HlsActionType.SetEndlist]=function(e,t){return d(e,t.payload)},i[s.HlsActionType.SetDefaultLanguages]=function(e,t){return p(e,t.payload)},i[s.HlsActionType.SetCustomTags]=function(e,t){return g(e,t.payload)},i[s.HlsActionType.SetDiscoTimings]=function(e,t){return f(e,t.payload)},i[s.HlsActionType.SetPresentationTimeOffset]=function(e,t){return m(e,t.payload)},i[s.HlsActionType.SetTimestampRolloverPositions]=function(e,t){return v(e,t.payload)},i[s.HlsActionType.SetContentLocationId]=function(e,t){return S(e,t.payload)},i))},79367:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setContentLocationId=t.setTimestampRolloverPositions=t.setPresentationTimeOffset=t.setDiscoTimings=t.setCustomTags=t.setDefaultLanguages=t.setEndlist=t.setIsLowLatencyPlaylist=t.setPlaylistType=t.setMasterPlaylistString=void 0;var i=n(15231),r=n(90993),a=function(e){return(0,i.createAction)(r.HlsActionType.SetMasterPlaylistString,e)};t.setMasterPlaylistString=a;var o=function(e){return(0,i.createAction)(r.HlsActionType.SetPlaylistType,e)};t.setPlaylistType=o;var s=function(e){return(0,i.createAction)(r.HlsActionType.SetIsLowLatencyPlaylist,e)};t.setIsLowLatencyPlaylist=s;var u=function(e){return(0,i.createAction)(r.HlsActionType.SetEndlist,e)};t.setEndlist=u;var l=function(e){return(0,i.createAction)(r.HlsActionType.SetDefaultLanguages,e)};t.setDefaultLanguages=l;var c=function(e){return(0,i.createAction)(r.HlsActionType.SetCustomTags,e)};t.setCustomTags=c;var d=function(e,t){return(0,i.createAction)(r.HlsActionType.SetDiscoTimings,{mimeType:e,discoTimings:t})};t.setDiscoTimings=d;var f=function(e,t){return(0,i.createAction)(r.HlsActionType.SetPresentationTimeOffset,{discoSequenceNumber:e,presentationTimeOffset:t})};t.setPresentationTimeOffset=f;var p=function(e,t){return(0,i.createAction)(r.HlsActionType.SetTimestampRolloverPositions,{next:e,previous:t})};t.setTimestampRolloverPositions=p;var g=function(e){return(0,i.createAction)(r.HlsActionType.SetContentLocationId,{locationId:e})};t.setContentLocationId=g},80211:function(e,t,n){e.exports=function(){return n(90040)('!function(){"use strict";var e={16:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isBoolean=t.isNumber=void 0,t.isDefined=r,t.isVideoRepresentation=a;var n=function(e){return"number"==typeof e};t.isNumber=n;var i=function(e){return"boolean"==typeof e};function r(e){return null!=e}function a(e){return null!=e._height&&null!=e._width}t.isBoolean=i},331:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.toSeconds=i,t.toMilliSeconds=r;var n=1e3;function i(e){return e/n}function r(e){return e*n}},362:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerType=void 0,function(e){e.Html5="html5",e.Native="native",e.WebRtc="webrtc",e.Unknown="unknown"}(n||(t.PlayerType=n={}))},425:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.ReducerTree=void 0,t.isReducer=a,t.isReducerTree=o;var i=n(4210),r=function(){function e(){this.rootNode={}}return e.prototype.has=function(e){return Boolean(this.rootNode[e])},e.prototype.set=function(e,t){return this.rootNode[e]=t,this},e.prototype.get=function(e){return this.rootNode[e]},e.prototype.delete=function(e){delete this.rootNode[e]},e.prototype.keys=function(){return Object.keys(this.rootNode)},e.prototype.getCombinedReducersRecursively=function(e){var t=this,n={};return this.keys().forEach((function(i){var r=t.get(i),o=e.concat(String(i));n[i]=a(r)?r:r.getCombinedReducersRecursively(o)})),0===Object.keys(n).length?function(e){return{}}:(0,i.combineReducers)(n)},e.prototype.buildReducer=function(){return this.getCombinedReducersRecursively([])},e}();function a(e){return Boolean(e)&&"function"==typeof e}function o(e){return Boolean(e)&&e instanceof r}t.ReducerTree=r},618:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.StartOptionsActionKey=void 0,function(e){e.InitStartTimeOffset="@instance/sources/@source/startOptions/initStartTimeOffset",e.SetStartTimeOffset="@instance/sources/@source/startOptions/setStartTimeOffset"}(n||(t.StartOptionsActionKey=n={}))},1108:function(e,t,n){var i=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerModuleMissingError=void 0;var r=n(5550),a=function(e){function t(t,n){return n?e.call(this,r.ErrorCode.MODULE_DEPENDENCY_MISSING,{name:t,dependency:n},"The ".concat(t," module requires the ").concat(n," module to be included as well."))||this:e.call(this,r.ErrorCode.MODULE_MISSING,{name:t},"The ".concat(t," module is required to play this stream."))||this}return i(t,e),t}(n(8764).PlayerError);t.PlayerModuleMissingError=a},1361:function(e,t,n){var i=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.ArrayHelper=void 0,t.invert=o,t.findIndexFromEnd=s,t.findIndexWithin=u,t.findFromEnd=l,t.forEachFromIndex=d,t.forceArray=c,t.contains=f,t.shallowObjectMatcher=p,t.clearArray=g,t.removeEntriesFromStart=S;var r=n(6093),a=function(){function e(){}return e.comparePrimitiveArrays=function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0},e.stringToArrayWithoutEncoding=function(e){for(var t=new Uint8Array(e.length),n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t},e.convertBufferSourceToUTF8=function(t,n){return void 0===n&&(n="utf-8"),t instanceof ArrayBuffer?t=new Uint8Array(t):t instanceof DataView&&(t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),e.convertUint8ArrayToUtf8(t,n)},e.convertUint8ArrayToUtf8=function(e,t){if("undefined"!=typeof TextDecoder)return new TextDecoder(t).decode(e);for(var n=0,i="";n<e.length;){var r=e[n++];if(r>127)if(r>191&&r<224){if(n>=e.length||64&e[n]){i+="�";continue}r=(31&r)<<6|63&e[n++]}else if(r>223&&r<240){if(n+1>=e.length||64&e[n]||64&e[n+1]){i+="�";continue}r=(15&r)<<12|(63&e[n++])<<6|63&e[n++]}else{if(!(r>239&&r<248)){i+="�";continue}if(n+2>=e.length||64&e[n]||64&e[n+1]||64&e[n+2]){i+="�";continue}r=(7&r)<<18|(63&e[n++])<<12|(63&e[n++])<<6|63&e[n++]}r<=65535?i+=String.fromCharCode(r):r<=1114111?(r-=65536,i+=String.fromCharCode(r>>10|55296),i+=String.fromCharCode(1023&r|56320)):i+="�"}return i},e.sort=function(e,t,n){if("string"==typeof t&&"[object Array]"===Object.prototype.toString.call(e)){var i;if(n||(n="asc"),"asc"===n)i=-1;else{if("desc"!==n)return;i=1}if(e.length<2)return e;var r=function(e,n){return e[t]<n[t]?i:e[t]>n[t]?-1*i:0};return e.sort(r)}},e.append=function(t,n){var i=e.getTypedArrayType(t),r=e.getTypedArrayType(n);if(!i&&!r||i!==r)return null;var a=new i((t.byteLength+n.byteLength)/i.BYTES_PER_ELEMENT);return a.set(new i(t),0),a.set(new i(n),t.byteLength/i.BYTES_PER_ELEMENT),a},e.concatBuffers=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(!(e=e.filter((function(e){return e}))).length)return null;var n=e.reduce((function(e,t){return e+t.byteLength}),0),i=new Uint8Array(n);return e.reduce((function(e,t){return i.set(new Uint8Array(t),e),e+t.byteLength}),0),i.buffer},e.getTypedArrayType=function(e){return e instanceof Uint8Array?Uint8Array:e instanceof Uint16Array?Uint16Array:e instanceof Uint32Array?Uint32Array:null},e.getArray=function(t){return e.getTypedArrayType(t)?[].slice.call(t):Array.isArray(t)?t:null},e.union=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=[];return e.filter((function(e){return Boolean(e)})).forEach((function(e){return e.forEach((function(e){n.includes(e)||n.push(e)}))})),n},e.isArrayBuffer=function(e){return Boolean(e)&&e instanceof ArrayBuffer&&void 0!==e.byteLength},e.arrayBufferToAsciiString=function(t,n){return void 0===n&&(n=!0),e.sparseArrayBufferToAsciiString(t,1,0,n)},e.arrayBufferToBase64=function(e){for(var t="",n=new Uint8Array(e),i=0;i<n.length;i++)t+=String.fromCharCode(n[i]);return window.btoa(t)},e.sparseArrayBufferToAsciiString=function(e,t,n,i){void 0===t&&(t=2),void 0===n&&(n=0),void 0===i&&(i=!0);for(var r="",a=i?new Uint8Array(e):new Uint16Array(e),o=n;o<a.length;o+=t)r+=String.fromCharCode(a[o]);return r},e.intersect=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var i=[],r=t.filter((function(e){return Boolean(e)}));return e.union.apply(e,t).forEach((function(e){r.every((function(t){return t.includes(e)}))&&i.push(e)})),i},e.addAndMergeIntersects=function(t,n){var r=t.filter((function(t){return e.intersect(t,n).length>0})),a=t.filter((function(e){return!r.includes(e)}));return 0===r.length?i(i([],t,!0),[n],!1):i([e.union.apply(e,i(i([],r,!1),[n],!1))],a,!0)},e.extractAsciiString=function(e,t,n){for(var i=t+n,r="";t<i;t++)r+=String.fromCharCode(e.getUint8(t));return r},e.extractHexString=function(e,t,n){for(var i=t+n,a="";t<i;t++)a+=r.FormatHelper.intToHex(e.getUint8(t));return a},e.toArray=function(e){try{if(!e||!e.length)return[];for(var t=new Array(e.length),n=0;n<e.length;n++)t[n]=e[n];return t}catch(e){return[]}},e}();function o(e){return function(t,n,i){return!e(t,n,i)}}function s(e,t){var n;for(n=e.length-1;n>=0&&!t(e[n]);n--);return n}function u(e,t,n,i){for(var r=t;r<n;r++)if(i(e[r]))return r;return-1}function l(e,t){return e[s(e,t)]}function d(e,t,n){for(;t<e.length;t++){n(e[t-1],e[t])}}function c(e){return Array.isArray(e)?e:[e]}function f(e,t,n){return void 0===n&&(n=p),null!=e.find((function(e){return n(t,e)}))}function p(e,t){return Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every((function(n){return e[n]===t[n]}))}function g(e){e.splice(0,e.length)}function S(e,t){return t<1?[]:e.splice(0,t)}t.ArrayHelper=a},1397:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.kebabCaseToCamelCase=r,t.forceReallocation=a;var i=n(6435);function r(e){return e.split("-").map((function(e,t){return 0===t?e.toLowerCase():e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()})).join("")}function a(e){return e?(0,i.isFirefox)()?JSON.parse(JSON.stringify(e)):(" "+e).slice(1):""}},1399:function(e,t){var n,i;Object.defineProperty(t,"__esModule",{value:!0}),t.CapabilityKey=t.CapabilityActionType=void 0,function(e){e.InitCapabilities="initcapabilities",e.SetCapability="setcapability"}(n||(t.CapabilityActionType=n={})),function(e){e.isPlayStation5="isPlayStation5",e.isPlayStation4="isPlayStation4",e.isMobile="isMobile",e.isTizen="isTizen",e.isSafari="isSafari",e.isChrome="isChrome",e.isChromium="isChromium",e.isFirefox="isFirefox",e.isFirefoxIOS="isFirefoxIOS",e.isWebOS="isWebOS",e.isVidaa="isVidaa",e.isVizio="isVizio",e.isIOS="isIOS",e.isSafariIOS="isSafariIOS",e.isEdge="isEdge",e.isLegacyEdge="isLegacyEdge",e.isReactNative="isReactNative",e.MSESafari="MSESafari",e.isLocalStorageAvailable="isLocalStorageAvailable",e.isInlinePlaybackRestricted="isInlinePlaybackRestricted",e.IOSVersion="IOSVersion",e.edgeVersion="edgeVersion",e.safariVersion="safariVersion",e.webOSChromiumVersion="webOSChromiumVersion",e.techSupportedByModules="techSupportedByModules",e.techSupportedByPlatform="techSupportedByPlatform",e.techSupportedByModulesOnPlatform="techSupportedByModulesOnPlatform"}(i||(t.CapabilityKey=i={}))},1520:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.ModuleManager=void 0;var i=n(5550),r=n(8764),a=n(1108),o=function(){function e(){}return e.initialize=function(){this.modules||(this.modules={})},e.add=function(e){if(this.initialize(),!e)throw new r.PlayerError(i.ErrorCode.MODULE_INVALID_DEFINITION,void 0,"Cannot add the module: no module definition was provided to the API.");if(!e.name)throw new r.PlayerError(i.ErrorCode.MODULE_INVALID_DEFINITION,void 0,"Cannot add the module: no `name` property was provided to the module definition.");if(!e.module)throw new r.PlayerError(i.ErrorCode.MODULE_INVALID_DEFINITION,void 0,"Cannot add the module: no `module` property was provided to the module definition.");if(e.dependencies){for(var t=0,n=e.dependencies;t<n.length;t++){var o=n[t];if(!this.has(o))throw new a.PlayerModuleMissingError(e.name,o)}if("function"!=typeof e.module)throw new r.PlayerError(i.ErrorCode.MODULE_INVALID_DEFINITION_DEPENDENCY,{name:e.name},"Cannot add the module: modules with dependencies must pass a deferred loading function to the `ModuleDefinition.module` property.")}var s=e.module;"function"==typeof s&&(s=s()),this.modules[e.name]={moduleDefinition:e,module:s},e.hooks&&e.hooks.add&&e.hooks.add(e.module)},e.remove=function(e){var t;if(this.modules){if(!this.modules[e])throw new a.PlayerModuleMissingError(e);var n=this.modules[e].moduleDefinition;(null===(t=n.hooks)||void 0===t?void 0:t.remove)&&n.hooks.remove(n.module),delete this.modules[e]}},e.get=function(e,t){void 0===t&&(t=!0),this.initialize();var n=this.modules[e];if(n&&n.module)return n.module;if(t)throw new a.PlayerModuleMissingError(e)},e.has=function(e){return void 0!==this.get(e,!1)},e.getModuleNames=function(){return Object.getOwnPropertyNames(this.modules)},e.getModuleDefinitions=function(){var e=this;return this.getModuleNames().map((function(t){return e.modules[t]}))},e.callSetupHooks=function(e){this.getModuleDefinitions().filter((function(e){return e.moduleDefinition.hooks&&e.moduleDefinition.hooks.setup})).forEach((function(t){return t.moduleDefinition.hooks.setup(t.module,e)}))},e.callDestroyHooks=function(e){var t=this.getModuleDefinitions().filter((function(e){return e.moduleDefinition.hooks&&e.moduleDefinition.hooks.destroy})).map((function(t){return t.moduleDefinition.hooks.destroy(t.module,e)}));return Promise.all(t).then((function(){}))},e}();t.ModuleManager=o},1715:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.MessageHandler=void 0;var n=function(){function e(e,t){this.runsInWebWorker=e,this.postMessageFunction=t}return e}();t.MessageHandler=n},1829:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.StaticActionTypes=void 0,function(e){e.Default="default"}(n||(t.StaticActionTypes=n={}));var i=function(e,t){return function(i,r){return void 0===i&&(i=e),t[r.type]?t[r.type](i,r):t[n.Default]?t[n.Default](i,r):i}};t.default=i},1921:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.StringStartsWithPolyfill=void 0,t.StringStartsWithPolyfill=function(){return{polyfill:function(){String.prototype.startsWith=function(e,t){var n=t>0?0|t:0;return this.substring(n,n+e.length)===e}}}}()},2055:function(e,t,n){var i,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.StartOptionsReducer=void 0,t.getStartTimeOffset=d;var a=n(1829),o=n(5328),s=n(618),u=o.initialStartOptions,l=function(e,t){var n=t.payload;return r(r({},e),n)};function d(e){return e.startOptions.startTimeOffset}t.StartOptionsReducer=(0,a.default)(u,((i={})[s.StartOptionsActionKey.InitStartTimeOffset]=l,i[s.StartOptionsActionKey.SetStartTimeOffset]=l,i))},2103:function(e,t){function n(e,t){if(void 0!==e.mediaSequence&&void 0!==t.mediaSequence)return e.mediaSequence===t.mediaSequence;var n=r(e,t),i=o(e.byteRange,t.byteRange),a=e.discontinuitySequenceNumber===t.discontinuitySequenceNumber;return n&&i&&a}function i(e,t){var n=(null==e?void 0:e.url)===(null==t?void 0:t.url),i=o(null==e?void 0:e.byteRange,null==t?void 0:t.byteRange);return n&&i}function r(e,t){var n=a(e),i=a(t);return n.some((function(e){return i.includes(e)}))}function a(e){return[e.url,e.mediaURL].filter(Boolean)}function o(e,t){return null==e?null==t:null!=t&&e.start===t.start&&e.end===t.end}Object.defineProperty(t,"__esModule",{value:!0}),t.isIdenticalSegmentInfo=n,t.isIdenticalInitSegmentInfo=i},2442:function(e,t){var n,i,r,a,o,s;Object.defineProperty(t,"__esModule",{value:!0}),t.DrmSetupError=t.TransmuxingRejectionReason=t.SegmentVerificationResult=t.HlsEncryptionMethod=t.SOURCE_BUFFER_APPEND_STATUS=t.RepresentationType=void 0,function(e){e[e.AUDIO=1]="AUDIO",e[e.VIDEO=2]="VIDEO",e[e.SUBTITLE=3]="SUBTITLE"}(n||(t.RepresentationType=n={})),function(e){e[e.FAILURE=0]="FAILURE",e[e.QUOTA_EXCEEDED=1]="QUOTA_EXCEEDED",e[e.SUSPENDED=2]="SUSPENDED"}(i||(t.SOURCE_BUFFER_APPEND_STATUS=i={})),function(e){e.NONE="NONE",e.AES_128="AES-128",e.SAMPLE_AES="SAMPLE-AES",e.SAMPLE_AES_CTR="SAMPLE-AES-CTR"}(r||(t.HlsEncryptionMethod=r={})),function(e){e.Publish="publish",e.Drop="drop",e.Park="park"}(a||(t.SegmentVerificationResult=a={})),function(e){e.INVALID_TS_SEGMENT="Invalid TS segment, could not extract segment information",e.TRANSMUXING_ABORTED="Transmuxing was aborted as it is no longer needed"}(o||(t.TransmuxingRejectionReason=o={})),function(e){e.CANCELLED="Loading was cancelled for the DRM source",e.MISSING_CONFIGURATION="DRM is not configured"}(s||(t.DrmSetupError=s={}))},2510:function(e,t){var n,i,r,a,o,s;Object.defineProperty(t,"__esModule",{value:!0}),t.ContentLocationChangedReason=t.LatencyMode=t.AdInteractionType=t.AdQuartile=t.MetadataType=t.PlayerEvent=void 0,function(e){e.Ready="ready",e.Play="play",e.Playing="playing",e.Paused="paused",e.Seek="seek",e.Seeked="seeked",e.TimeShift="timeshift",e.TimeShifted="timeshifted",e.VolumeChanged="volumechanged",e.Muted="muted",e.Unmuted="unmuted",e.PlayerResized="playerresized",e.PlaybackFinished="playbackfinished",e.Error="error",e.Warning="warning",e.StallStarted="stallstarted",e.StallEnded="stallended",e.AudioChanged="audiochanged",e.AudioAdded="audioadded",e.AudioRemoved="audioremoved",e.VideoQualityChanged="videoqualitychanged",e.AudioQualityChanged="audioqualitychanged",e.VideoDownloadQualityChange="videodownloadqualitychange",e.AudioDownloadQualityChange="audiodownloadqualitychange",e.VideoDownloadQualityChanged="videodownloadqualitychanged",e.AudioDownloadQualityChanged="audiodownloadqualitychanged",e.VideoPlaybackQualityChanged="videoplaybackqualitychanged",e.AudioPlaybackQualityChanged="audioplaybackqualitychanged",e.TimeChanged="timechanged",e.CueParsed="cueparsed",e.CueEnter="cueenter",e.CueUpdate="cueupdate",e.CueExit="cueexit",e.SegmentPlayback="segmentplayback",e.Metadata="metadata",e.MetadataParsed="metadataparsed",e.MetadataChanged="metadatachanged",e.VideoAdaptation="videoadaptation",e.AudioAdaptation="audioadaptation",e.DownloadFinished="downloadfinished",e.SegmentRequestFinished="segmentrequestfinished",e.AdManifestLoaded="admanifestloaded",e.AdStarted="adstarted",e.OverlayAdStarted="overlayadstarted",e.AdQuartile="adquartile",e.AdSkipped="adskipped",e.AdClicked="adclicked",e.AdInteraction="adinteraction",e.AdLinearityChanged="adlinearitychanged",e.AdBreakStarted="adbreakstarted",e.AdBreakFinished="adbreakfinished",e.RestoringContent="restoringcontent",e.AdFinished="adfinished",e.AdError="aderror",e.VRViewingDirectionChange="vrviewingdirectionchange",e.VRViewingDirectionChanged="vrviewingdirectionchanged",e.VRStereoChanged="vrstereochanged",e.CastAvailable="castavailable",e.CastStopped="caststopped",e.CastStart="caststart",e.CastStarted="caststarted",e.CastWaitingForDevice="castwaitingfordevice",e.SourceLoaded="sourceloaded",e.SourceUnloaded="sourceunloaded",e.PeriodSwitch="periodswitch",e.PeriodSwitched="periodswitched",e.DVRWindowExceeded="dvrwindowexceeded",e.SubtitleAdded="subtitleadded",e.SubtitleRemoved="subtitleremoved",e.ShowAirplayTargetPicker="showairplaytargetpicker",e.AirplayAvailable="airplayavailable",e.AirplayChanged="airplaychanged",e.Destroy="destroy",e.PlaybackSpeedChanged="playbackspeedchanged",e.DurationChanged="durationchanged",e.ViewModeChanged="viewmodechanged",e.ModuleReady="moduleready",e.SubtitleEnable="subtitleenable",e.SubtitleEnabled="subtitleenabled",e.SubtitleDisable="subtitledisable",e.SubtitleDisabled="subtitledisabled",e.VideoQualityAdded="videoqualityadded",e.VideoQualityRemoved="videoqualityremoved",e.AudioQualityAdded="audioqualityadded",e.AudioQualityRemoved="audioqualityremoved",e.TargetLatencyChanged="targetlatencychanged",e.LatencyModeChanged="latencymodechanged",e.LicenseValidated="licensevalidated",e.DrmLicenseAdded="drmlicenseadded",e.AspectRatioChanged="aspectratiochanged",e.ContentLocationChanged="contentlocationchanged"}(n||(t.PlayerEvent=n={})),function(e){e.CUETAG="CUETAG",e.DATERANGE="DATERANGE",e.EVENT_STREAM="EVENT-STREAM",e.CUSTOM="CUSTOM",e.SCTE="SCTE",e.ID3="ID3",e.EMSG="EMSG",e.CAST="CAST"}(i||(t.MetadataType=i={})),function(e){e.FIRST_QUARTILE="firstQuartile",e.MIDPOINT="midpoint",e.THIRD_QUARTILE="thirdQuartile"}(r||(t.AdQuartile=r={})),function(e){e.Vpaid="vpaid"}(a||(t.AdInteractionType=a={})),function(e){e.Idle="idle",e.Catchup="catchup",e.Fallback="fallback",e.Suspended="suspended"}(o||(t.LatencyMode=o={})),function(e){e.Failover="failover",e.ContentSteering="contentsteering"}(s||(t.ContentLocationChangedReason=s={}))},2600:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.parseTag=s;var i=n(7485),r=/^#(EXT[^:]*)(?::(.*))?$/,a=/^(?:"([^"]*)"|([^,=]+))(?:,|$)/g,o=/([^=]+)=*(?:"([^"]*)"|([^",]*))(?:,|$)/g;function s(e){var t,n=r.exec(e);if(!n)throw new Error("Invalid tag "+e);var s,u=n[1],l=n[2],d={};if(l){var c=new i.TextParser(l),f=void 0,p=c.readRegex(a);for(p&&(s=null!==(t=p[1])&&void 0!==t?t:p[2]);f=c.readRegex(o);){var g=f[1],S=f[2]||f[3];if(g.includes(",")){var v=g.split(","),m=v[0];g=v[1].trim(),d[m]=void 0}d[g]=S,c.skipWhitespace()}}return{name:u,attributes:d,value:s}}},3091:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.StreamType=void 0,function(e){e.Progressive="progressive",e.Dash="dash",e.Hls="hls",e.Smooth="smooth",e.Whep="whep",e.Unknown="unknown"}(n||(t.StreamType=n={}))},3109:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.isPlayingLowLatencyHls=s;var i=n(7279),r=n(1520),a=n(6368),o=n(7062);function s(e){var t;if(!(0,o.isLowLatencyConfigured)(e)||!e.settings.LL_HLS)return!1;var n=(0,i.getSourceState)(e);if(!n)return!1;var s=r.ModuleManager.get(a.ModuleName.HLS,!1);if(!s)return!1;var u=s.selectors,l=u.getHlsState,d=u.getIsLowLatencyPlaylist,c=l(n);return!!c&&(null!==(t=d(c))&&void 0!==t&&t)}},3326:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getMostRecentlyRefreshedQuality=t.getSegmentInfos=t.getSegmentMapEntry=void 0;var n=function(e,t){return null==e?void 0:e.segmentInfoMap[t]};t.getSegmentMapEntry=n;var i=function(e,n){var i,r;return null!==(r=null===(i=(0,t.getSegmentMapEntry)(e,n))||void 0===i?void 0:i.segmentInfos)&&void 0!==r?r:[]};t.getSegmentInfos=i;var r=function(e){if(e)return Object.values(e.segmentInfoMap).filter((function(e){return null!=e.qualityInfo.lastUpdateTimestamp})).sort((function(e,t){var n=e.qualityInfo.lastUpdateTimestamp;return t.qualityInfo.lastUpdateTimestamp-n}))[0]};t.getMostRecentlyRefreshedQuality=r},3485:function(e,t,n){var i=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.MSESafari=t.getBestTech=t.getTechSupportedByModules=t.getTechSupportedByPlatform=t.getTechSupportedByModulesOnPlatform=t.isLocalStorageAvailable=t.isInlinePlaybackRestricted=t.isFairPlayDrmSupported=t.supports=t.checkPlatformSupports=t.hasMediaSourceSupport=t.FairplayKeySystems=void 0,t.isUsingFileProtocol=_,t.isFetchSupported=D;var r,a=n(1520),o=n(6368),s=n(3696),u=n(3533),l=n(7093),d=n(9814),c=n(6435);!function(e){e.FPS_1="com.apple.fps.1_0",e.FPS_2="com.apple.fps.2_0"}(r||(t.FairplayKeySystems=r={}));var f=[{player:s.PlayerType.Html5,streaming:s.StreamType.Dash},{player:s.PlayerType.Html5,streaming:s.StreamType.Hls},{player:s.PlayerType.Html5,streaming:s.StreamType.Smooth},{player:s.PlayerType.WebRtc,streaming:s.StreamType.Whep},{player:s.PlayerType.Native,streaming:s.StreamType.Hls},{player:s.PlayerType.Native,streaming:s.StreamType.Progressive}],p=!0,g=Boolean(l.default.ArrayBuffer&&l.default.Uint8Array&&l.default.Uint16Array),S=function(){return"function"==typeof document.createElement("video").canPlayType},v=function(){var e=document.createElement("video");return e&&"function"==typeof e.canPlayType&&("maybe"===e.canPlayType("application/vnd.apple.mpegURL")||"maybe"===e.canPlayType("application/x-mpegURL")||"maybe"===e.canPlayType("audio/mpegurl")||"maybe"===e.canPlayType("audio/x-mpegurl"))},m=function(){var e,t,n,i="MediaSource"in l.default&&"function"==typeof(null===(e=l.default.MediaSource)||void 0===e?void 0:e.isTypeSupported),r="WebKitMediaSource"in l.default&&"function"==typeof(null===(t=l.default.WebKitMediaSource)||void 0===t?void 0:t.isTypeSupported),a="ManagedMediaSource"in l.default&&"function"==typeof(null===(n=l.default.ManagedMediaSource)||void 0===n?void 0:n.isTypeSupported);return i||r||a};t.hasMediaSourceSupport=m;var h=function(){var e=l.default.RTCPeerConnection,t=Boolean(e)&&"function"==typeof e.prototype.addTransceiver;return E()&&t},E=function(){var e,t,n,i,r;return Boolean(null!==(r=null!==(i=null!==(n=null!==(t=null===(e=navigator.mediaDevices)||void 0===e?void 0:e.getUserMedia)&&void 0!==t?t:navigator.getUserMedia)&&void 0!==n?n:navigator.webkitGetUserMedia)&&void 0!==i?i:navigator.mozGetUserMedia)&&void 0!==r?r:navigator.msGetUserMedia)},y=function(){var e=[];if(g?e=f.filter((function(e){return(0,t.supports)(e.player,e.streaming)})):e.push({player:s.PlayerType.Native,streaming:s.StreamType.Progressive}),(0,c.isSafari)())for(var n=1;n<e.length;n++)if(e[n].player===s.PlayerType.Native&&e[n].streaming===s.StreamType.Hls){e=e.splice(n,1).concat(e);break}return e};t.checkPlatformSupports=y;var I=function(e,n){var i=(0,t.hasMediaSourceSupport)()?[s.StreamType.Dash,s.StreamType.Hls,s.StreamType.Smooth]:[],r=h()?[s.StreamType.Whep]:[],a=function(){var e=[];return v()&&!(0,c.isEdge)()&&e.push(s.StreamType.Hls),S()&&p&&e.push(s.StreamType.Progressive),e},o=function(e){return e.some((function(e){return e===n}))};switch(e){case s.PlayerType.Html5:return o(i);case s.PlayerType.WebRtc:return o(r);case s.PlayerType.Native:return o(a());default:return!1}};t.supports=I;var T=function(){return Object.values(r).some((function(e){var t;return null===(t=l.default.WebKitMediaKeys)||void 0===t?void 0:t.isTypeSupported(e,d.MimeType.VideoMp4)}))};function _(){return"file:"===l.default.location.protocol}t.isFairPlayDrmSupported=T;var O=function(){var e=(0,c.getIOSVersion)(),t=e<c.IOS_9&&/iPad/i.test(navigator.userAgent),n=e<c.IOS_10&&/iP(hone|od)/i.test(navigator.userAgent);return c.isSafariIOS&&(t||n)};t.isInlinePlaybackRestricted=O;var R=function(){try{return l.default.localStorage&&"function"==typeof localStorage.getItem&&"function"==typeof localStorage.setItem}catch(e){return!1}};t.isLocalStorageAvailable=R;var N=function(){var e=(0,t.getTechSupportedByModules)(),n=(0,t.getTechSupportedByPlatform)(),i=function(e,t){return e.player===t.player&&e.streaming===t.streaming};return n.filter((function(t){return e.filter((function(e){return i(e,t)})).length>0}))};t.getTechSupportedByModulesOnPlatform=N;var b=function(){return(0,t.checkPlatformSupports)()};t.getTechSupportedByPlatform=b;var M=function(){var e=a.ModuleManager.has(o.ModuleName.EngineBitmovin)?a.ModuleManager.get(o.ModuleName.EngineBitmovin).technologyChecker.getSupportedTechnologies():[],t=a.ModuleManager.has(o.ModuleName.EngineNative)?a.ModuleManager.get(o.ModuleName.EngineNative).technologyChecker.getSupportedTechnologies():[],n=a.ModuleManager.has(o.ModuleName.EngineWebRtc)?a.ModuleManager.get(o.ModuleName.EngineWebRtc).technologyChecker.getSupportedTechnologies():[];return i(i(i([],e,!0),t,!0),n,!0)};t.getTechSupportedByModules=M;var A=function(e){var t=(0,u.getCapabilities)(),n=i([],t.techSupportedByPlatform,!0);if(!e||0===e.length)return n;var r=e.filter((function(e){return n.some((function(t,i){var r,a=e.player===t.player&&e.streaming===t.streaming,o=null!==(r=e.exclude)&&void 0!==r&&r;return a&&n.splice(i,1),a&&!o}))})).map((function(e){return{player:e.player,streaming:e.streaming}}));return i(i([],r,!0),n,!0)},P=function(e,t,n){void 0===e&&(e=[]),void 0===t&&(t={});var i=(0,u.getCapabilities)(),r=A(e),a=i.techSupportedByPlatform;return n&&(r=a.filter((function(e){return e.player===n.player&&e.streaming===n.streaming}))),(t.vr&&((0,c.isSafariIOS)()||(0,c.isSafari)())?r.filter((function(e){var t=e.streaming===s.StreamType.Progressive&&e.player===s.PlayerType.Native,n=e.streaming===s.StreamType.Hls&&e.player===s.PlayerType.Native;return t||n&&(!(0,c.isSafariIOS)()||(0,c.getIOSVersion)()>c.IOS_9)})):r).find((function(e){return Boolean(t[e.streaming])}))};function D(){return"function"==typeof l.default.fetch&&"function"==typeof l.default.AbortController}t.getBestTech=P;var C=function(){return(0,c.isSafari)()&&(0,t.hasMediaSourceSupport)()};t.MSESafari=C},3533:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=c,t.getCapabilities=f,t.getReachableDomains=g;var i,r=n(4210),a=n(7722),o=(n(8842),n(3679),n(9210)),s=n(425),u=n(9422),l=new s.ReducerTree;l.set("capabilities",a.CapabilityReducer).set("reachableDomains",o.ReachableDomainsReducer);var d=function(){var e=[(0,u.addStoreServiceDecoration)(l)];return(0,r.createStore)((function(){return{}}),r.compose.apply(void 0,e))};function c(){return i||(i=d()),i}function f(){return p(c().getState())}function p(e){return e.capabilities}function g(){return S(c().getState())}function S(e){return e.reachableDomains}},3553:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentInfoError=void 0,function(e){e.END_OF_STREAM_REACHED="END_OF_STREAM_REACHED",e.PERIOD_COMPLETE="PERIOD_COMPLETE",e.SEGMENT_EXCEEDING_PERIOD_DURATION="SEGMENT_EXCEEDING_PERIOD_DURATION"}(n||(t.SegmentInfoError=n={}))},3679:function(e,t){function n(e){Object.freeze(e);var t="function"==typeof e,i=Object.prototype.hasOwnProperty;return Object.getOwnPropertyNames(e).forEach((function(r){!i.call(e,r)||t&&("caller"===r||"callee"===r||"arguments"===r)||null===e[r]||"object"!=typeof e[r]&&"function"!=typeof e[r]||Object.isFrozen(e[r])||n(e[r])})),e}Object.defineProperty(t,"__esModule",{value:!0}),t.freeze=void 0,t.deepFreeze=n;var i=function(e){return null!==e&&"object"==typeof e},r=function(e){var t=e.getState();i(t)&&n(t)},a=function(e){return r(e),function(t){return function(n){r(e);try{return t(n)}finally{r(e)}}}};t.freeze=a},3696:function(e,t,n){var i=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.TimeMode=t.SupportedTechnologyMode=t.ViewMode=t.PlayerAPINotAvailableError=t.LogLevel=t.BufferType=t.MediaType=t.StreamType=t.PlayerType=void 0;var r=n(5550),a=n(8764),o=n(362);Object.defineProperty(t,"PlayerType",{enumerable:!0,get:function(){return o.PlayerType}});var s,u,l,d=n(3091);Object.defineProperty(t,"StreamType",{enumerable:!0,get:function(){return d.StreamType}}),function(e){e.Audio="audio",e.Video="video",e.Subtitles="subtitles",e.Thumbnails="thumbnails"}(s||(t.MediaType=s={})),function(e){e.ForwardDuration="forwardduration",e.BackwardDuration="backwardduration"}(u||(t.BufferType=u={})),function(e){e.DEBUG="debug",e.LOG="log",e.WARN="warn",e.ERROR="error",e.OFF="off"}(l||(t.LogLevel=l={}));var c,f,p,g=function(e){function t(t){return e.call(this,r.ErrorCode.API_NOT_AVAILABLE,void 0,"Cannot use the `player.".concat(t,"` API: the player has already been destroyed."))||this}return i(t,e),t}(a.PlayerError);t.PlayerAPINotAvailableError=g,function(e){e.Inline="inline",e.Fullscreen="fullscreen",e.PictureInPicture="pictureinpicture"}(c||(t.ViewMode=c={})),function(e){e.Platform="platform",e.Modules="modules",e.PlatformAndModules="platformandmodules"}(f||(t.SupportedTechnologyMode=f={})),function(e){e.RelativeTime="relativetime",e.AbsoluteTime="absolutetime"}(p||(t.TimeMode=p={}))},3801:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.StringIncludesPolyfill=void 0,t.StringIncludesPolyfill=function(){return{polyfill:function(){String.prototype.includes=function(e,t){return"number"!=typeof t&&(t=0),!(t+e.length>this.length)&&-1!==this.indexOf(e,t)}}}}()},4210:function(e,t,n){function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function r(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function a(e){var t=r(e,"string");return"symbol"==i(t)?t:t+""}function o(e,t,n){return(t=a(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){o(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}n.r(t),n.d(t,{__DO_NOT_USE__ActionTypes:function(){return f},applyMiddleware:function(){return I},bindActionCreators:function(){return E},combineReducers:function(){return m},compose:function(){return y},createStore:function(){return g},legacy_createStore:function(){return S}});var d=function(){return"function"==typeof Symbol&&Symbol.observable||"@@observable"}(),c=function(){return Math.random().toString(36).substring(7).split("").join(".")},f={INIT:"@@redux/INIT"+c(),REPLACE:"@@redux/REPLACE"+c(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+c()}};function p(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function g(e,t,n){var i;if("function"==typeof t&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw new Error(l(0));if("function"==typeof t&&void 0===n&&(n=t,t=void 0),void 0!==n){if("function"!=typeof n)throw new Error(l(1));return n(g)(e,t)}if("function"!=typeof e)throw new Error(l(2));var r=e,a=t,o=[],s=o,u=!1;function c(){s===o&&(s=o.slice())}function S(){if(u)throw new Error(l(3));return a}function v(e){if("function"!=typeof e)throw new Error(l(4));if(u)throw new Error(l(5));var t=!0;return c(),s.push(e),function(){if(t){if(u)throw new Error(l(6));t=!1,c();var n=s.indexOf(e);s.splice(n,1),o=null}}}function m(e){if(!p(e))throw new Error(l(7));if(void 0===e.type)throw new Error(l(8));if(u)throw new Error(l(9));try{u=!0,a=r(a,e)}finally{u=!1}for(var t=o=s,n=0;n<t.length;n++){(0,t[n])()}return e}function h(e){if("function"!=typeof e)throw new Error(l(10));r=e,m({type:f.REPLACE})}function E(){var e,t=v;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(l(11));function n(){e.next&&e.next(S())}return n(),{unsubscribe:t(n)}}})[d]=function(){return this},e}return m({type:f.INIT}),(i={dispatch:m,subscribe:v,getState:S,replaceReducer:h})[d]=E,i}var S=g;function v(e){Object.keys(e).forEach((function(t){var n=e[t];if(void 0===n(void 0,{type:f.INIT}))throw new Error(l(12));if(void 0===n(void 0,{type:f.PROBE_UNKNOWN_ACTION()}))throw new Error(l(13))}))}function m(e){for(var t=Object.keys(e),n={},i=0;i<t.length;i++){var r=t[i];0,"function"==typeof e[r]&&(n[r]=e[r])}var a,o=Object.keys(n);try{v(n)}catch(e){a=e}return function(e,t){if(void 0===e&&(e={}),a)throw a;for(var i=!1,r={},s=0;s<o.length;s++){var u=o[s],d=n[u],c=e[u],f=d(c,t);if(void 0===f){t&&t.type;throw new Error(l(14))}r[u]=f,i=i||f!==c}return(i=i||o.length!==Object.keys(e).length)?r:e}}function h(e,t){return function(){return t(e.apply(this,arguments))}}function E(e,t){if("function"==typeof e)return h(e,t);if("object"!=typeof e||null===e)throw new Error(l(16));var n={};for(var i in e){var r=e[i];"function"==typeof r&&(n[i]=h(r,t))}return n}function y(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function I(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),i=function(){throw new Error(l(15))},r={getState:n.getState,dispatch:function(){return i.apply(void 0,arguments)}},a=t.map((function(e){return e(r)}));return i=y.apply(void 0,a)(n.dispatch),u(u({},n),{},{dispatch:i})}}}},4338:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.StringEndsWithPolyfill=void 0,t.StringEndsWithPolyfill=function(){return{polyfill:function(){String.prototype.endsWith=function(e,t){if(null==e)return!1;var n=this.length;return null!=t&&(n=Math.min(t,this.length)),this.substring(n-e.length,n)===e}}}}()},4388:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.DurationConverter=void 0;var n=function(){function e(){}return e.getDurationInSec=function(e){if("string"!=typeof e)return NaN;var t=/^([-])?P(([\\d.]*)Y)?(([\\d.]*)M)?(([\\d.]*)D)?(T)?(([\\d.]*)H)?(([\\d.]*)M)?(([\\d.]*|Infinity)S)?/.exec(e);if(t){var n=Boolean(t[1]),i=12*parseFloat(t[2]||"0")*30*24*60*60,r=30*parseFloat(t[4]||"0")*24*60*60,a=24*parseFloat(t[6]||"0")*60*60,o=60*parseFloat(t[9]||"0")*60,s=60*parseFloat(t[11]||"0"),u=parseFloat(t[13]||"0");if("T"===t[8]&&void 0===(t[9]||t[11]||t[13]))return NaN;var l=i+r+a+o+s+u;return n&&(l=-l),l}return NaN},e}();t.DurationConverter=n},5231:function(e,t){function n(e,t){return{type:e,meta:{},payload:t}}Object.defineProperty(t,"__esModule",{value:!0}),t.createAction=n},5328:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.initialStartOptions=void 0,t.initializeStartTimeOffset=a,t.setStartTimeOffset=o;var i=n(5231),r=n(618);function a(){return o(t.initialStartOptions.startTimeOffset)}function o(e){return(0,i.createAction)(r.StartOptionsActionKey.SetStartTimeOffset,{startTimeOffset:e})}t.initialStartOptions={startTimeOffset:0}},5469:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.installWorkerPolyfills=s;var i=n(8367),r=n(4338),a=n(3801),o=n(1921);function s(){Array.prototype.includes||i.ArrayIncludesPolyfill.polyfill(),String.prototype.includes||a.StringIncludesPolyfill.polyfill(),String.prototype.startsWith||o.StringStartsWithPolyfill.polyfill(),String.prototype.endsWith||r.StringEndsWithPolyfill.polyfill()}},5550:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.ErrorCode=void 0,function(e){e[e.UNKNOWN=1e3]="UNKNOWN",e[e.API_NOT_AVAILABLE=1001]="API_NOT_AVAILABLE",e[e.SETUP_ERROR=1100]="SETUP_ERROR",e[e.SETUP_NO_HTML_ELEMENT=1101]="SETUP_NO_HTML_ELEMENT",e[e.SETUP_MISSING_CONFIGURATION=1102]="SETUP_MISSING_CONFIGURATION",e[e.SETUP_LICENSE_ERROR=1103]="SETUP_LICENSE_ERROR",e[e.SETUP_MISSING_DOMAIN_LICENSE_WHITELIST=1104]="SETUP_MISSING_DOMAIN_LICENSE_WHITELIST",e[e.SETUP_MISSING_DOMAIN_LICENSE_ALLOWLIST=1104]="SETUP_MISSING_DOMAIN_LICENSE_ALLOWLIST",e[e.SETUP_MISSING_LICENSE_WHITELIST=1105]="SETUP_MISSING_LICENSE_WHITELIST",e[e.SETUP_MISSING_LICENSE_ALLOWLIST=1105]="SETUP_MISSING_LICENSE_ALLOWLIST",e[e.SETUP_INVALID_LICENSE_SERVER=1106]="SETUP_INVALID_LICENSE_SERVER",e[e.SETUP_INVALID_IMPRESSION_SERVER=1107]="SETUP_INVALID_IMPRESSION_SERVER",e[e.SETUP_NO_RENDERING_ENGINE=1108]="SETUP_NO_RENDERING_ENGINE",e[e.SETUP_UNSUPPORTED_PROTOCOL=1113]="SETUP_UNSUPPORTED_PROTOCOL",e[e.SOURCE_ERROR=1200]="SOURCE_ERROR",e[e.SOURCE_INVALID=1201]="SOURCE_INVALID",e[e.SOURCE_MANIFEST_INVALID=1202]="SOURCE_MANIFEST_INVALID",e[e.SOURCE_NO_SUPPORTED_TECHNOLOGY=1203]="SOURCE_NO_SUPPORTED_TECHNOLOGY",e[e.SOURCE_STREAM_TYPE_NOT_SUPPORTED=1204]="SOURCE_STREAM_TYPE_NOT_SUPPORTED",e[e.SOURCE_FORCED_TECHNOLOGY_NOT_SUPPORTED=1205]="SOURCE_FORCED_TECHNOLOGY_NOT_SUPPORTED",e[e.SOURCE_NO_STREAM_FOUND_FOR_SUPPORTED_TECHNOLOGY=1206]="SOURCE_NO_STREAM_FOUND_FOR_SUPPORTED_TECHNOLOGY",e[e.SOURCE_EMPTY_SEGMENT=1207]="SOURCE_EMPTY_SEGMENT",e[e.SOURCE_COULD_NOT_LOAD_MANIFEST=1208]="SOURCE_COULD_NOT_LOAD_MANIFEST",e[e.SOURCE_PROGRESSIVE_STREAM_ERROR=1209]="SOURCE_PROGRESSIVE_STREAM_ERROR",e[e.SOURCE_HLS_STREAM_ERROR=1210]="SOURCE_HLS_STREAM_ERROR",e[e.SOURCE_ENCRYPTION_METHOD_NOT_SUPPORTED=1211]="SOURCE_ENCRYPTION_METHOD_NOT_SUPPORTED",e[e.SOURCE_INVALID_H264_CODEC=7043]="SOURCE_INVALID_H264_CODEC",e[e.PLAYBACK_ERROR=1300]="PLAYBACK_ERROR",e[e.PLAYBACK_VIDEO_DECODING_ERROR=1301]="PLAYBACK_VIDEO_DECODING_ERROR",e[e.PLAYBACK_HLS_COULD_NOT_LOAD_TRANSMUXER=1304]="PLAYBACK_HLS_COULD_NOT_LOAD_TRANSMUXER",e[e.NETWORK_ERROR=1400]="NETWORK_ERROR",e[e.NETWORK_MANIFEST_DOWNLOAD_TIMEOUT=1401]="NETWORK_MANIFEST_DOWNLOAD_TIMEOUT",e[e.NETWORK_SEGMENT_DOWNLOAD_TIMEOUT=1402]="NETWORK_SEGMENT_DOWNLOAD_TIMEOUT",e[e.NETWORK_PROGRESSIVE_STREAM_DOWNLOAD_TIMEOUT=1403]="NETWORK_PROGRESSIVE_STREAM_DOWNLOAD_TIMEOUT",e[e.NETWORK_FAILED_CERTIFICATE_REQUEST=1404]="NETWORK_FAILED_CERTIFICATE_REQUEST",e[e.DRM_ERROR=2e3]="DRM_ERROR",e[e.DRM_MISSING_CONFIGURATION=2001]="DRM_MISSING_CONFIGURATION",e[e.DRM_NO_LICENSE_SERVER_URL_PROVIDED=2002]="DRM_NO_LICENSE_SERVER_URL_PROVIDED",e[e.DRM_FAILED_LICENSE_REQUEST=2003]="DRM_FAILED_LICENSE_REQUEST",e[e.DRM_KEY_SIZE_NOT_SUPPORTED=2005]="DRM_KEY_SIZE_NOT_SUPPORTED",e[e.DRM_NO_KEY_SYSTEM=2006]="DRM_NO_KEY_SYSTEM",e[e.DRM_KEY_SESSION_INITIALIZATION_FAILED=2007]="DRM_KEY_SESSION_INITIALIZATION_FAILED",e[e.DRM_MEDIA_KEY_INITIALIZATION_FAILED=2008]="DRM_MEDIA_KEY_INITIALIZATION_FAILED",e[e.DRM_KEY_ERROR=2009]="DRM_KEY_ERROR",e[e.DRM_KEY_SYSTEM_NOT_SUPPORTED=2010]="DRM_KEY_SYSTEM_NOT_SUPPORTED",e[e.DRM_CERTIFICATE_ERROR=2011]="DRM_CERTIFICATE_ERROR",e[e.DRM_PLAYREADY_INVALID_HEADER_KEY_VALUE_PAIR=2012]="DRM_PLAYREADY_INVALID_HEADER_KEY_VALUE_PAIR",e[e.DRM_RESTRICTED_OUTPUT=2013]="DRM_RESTRICTED_OUTPUT",e[e.DRM_INIT_DATA_MISSING=2015]="DRM_INIT_DATA_MISSING",e[e.VR_ERROR=2100]="VR_ERROR",e[e.VR_INCOMPATIBLE_PLAYER_TECHNOLOGY=2101]="VR_INCOMPATIBLE_PLAYER_TECHNOLOGY",e[e.MODULE_ERROR=3e3]="MODULE_ERROR",e[e.MODULE_INVALID_DEFINITION=3001]="MODULE_INVALID_DEFINITION",e[e.MODULE_INVALID_DEFINITION_DEPENDENCY=3002]="MODULE_INVALID_DEFINITION_DEPENDENCY",e[e.MODULE_MISSING=3003]="MODULE_MISSING",e[e.MODULE_DEPENDENCY_MISSING=3004]="MODULE_DEPENDENCY_MISSING",e[e.MODULE_REMOVED=3005]="MODULE_REMOVED",e[e.MODULE_ADVERTISING_ERROR=3100]="MODULE_ADVERTISING_ERROR",e[e.SEGMENT_PSSH_DATA_MISSING=4e3]="SEGMENT_PSSH_DATA_MISSING"}(n||(t.ErrorCode=n={}))},5711:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.calcHash=a;var i=n(1520),r=n(6368);function a(e){return(i.ModuleManager.has(r.ModuleName.Subtitles)?i.ModuleManager.get(r.ModuleName.Subtitles).hashCode:o)(e)}function o(e){var t=0;if(0===e.length)return t;for(var n=0;n<e.length;n++){t=(t<<5)-t+e.charCodeAt(n),t|=0}return t}},5877:function(e,t,n){var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SEGMENT_TAGS=void 0,t.parseVariant=u,t.resolvePlaylistTag=d;var r=n(2510),a=n(6461),o=n(2600),s=n(7967);function u(e,n){void 0===n&&(n={});for(var i={version:1,allowCache:!0,hasM3u:!0,endList:!1,segments:[],dateRange:[],skippedSegmentsCount:0,targetDuration:0,totalDuration:0,mediaSequence:0},r=[],s=0;s<e.length;s++){var u=(0,a.removeTrailingWhitespaces)(e[s]);if(""!==u)if((0,a.isCustomTag)(u))(0,a.handleCustomTag)(i,r,u);else{var c=(0,o.parseTag)(u);if(t.SEGMENT_TAGS.includes(c.name))return(0,a.parseSegments)(e.slice(s),r,i,n),l(i),i;d(i,c,r)}}return l(i),i}function l(e){var t;"EVENT"===e.playlistType&&(e.discontinuitySequence=null!==(t=e.discontinuitySequence)&&void 0!==t?t:0)}function d(e,t,n){switch(t.name){case"EXT-X-VERSION":e.version=Number(t.value);break;case"EXT-X-START":e.start=(0,s.generateStartAttributes)(t);break;case"EXT-X-INDEPENDENT-SEGMENTS":e.independentSegments=!0;break;case"EXT-X-TARGETDURATION":t.value&&(e.targetDuration=parseInt(t.value));break;case"EXT-X-MEDIA-SEQUENCE":t.value&&(e.mediaSequence=parseInt(t.value));break;case"EXT-X-DISCONTINUITY-SEQUENCE":t.value&&(e.discontinuitySequence=parseInt(t.value));break;case"EXT-X-PLAYLIST-TYPE":e.playlistType="EVENT"===t.value?"EVENT":"VOD";break;case"EXT-X-PART-INF":e.partInfo=(0,s.generatePartInfoTag)(t.attributes);break;case"EXT-X-ALLOW-CACHE":e.allowCache="NO"!==t.value;break;case"EXT-X-SERVER-CONTROL":e.serverControl=(0,s.generateServerControlTag)(t);break;case"EXT-X-DATERANGE":e.dateRange.push((0,s.generateDateRange)(t));break;case"EXTM3U":break;default:n.push(i({type:r.MetadataType.CUSTOM},t)),(0,a.addCustomTagToPlaylistTagList)(e,t)}}t.SEGMENT_TAGS=["EXTINF","EXT-X-BYTERANGE","EXT-X-DISCONTINUITY","EXT-X-PROGRAM-DATE-TIME","EXT-X-KEY","EXT-X-GAP","EXT-X-BITRATE","EXT-X-PART","EXT-X-MAP","EXT-X-CUE-OUT-CONT","EXT-X-CUE-OUT","EXT-X-CUE-IN","EXT-X-SCTE35","EXT-X-SKIP"]},6093:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.FormatHelper=void 0;var n=function(){function e(){}return e.hexToBytes=function(e){var t=e.length;1&t&&(e="0"+e,t++);for(var n=new Uint8Array(t>>1),i=0;i<t;i+=2)n[i>>1]=parseInt(e.substring(i,i+2),16);return n},e.bytesToHex=function(e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));for(var t="",n=0;n<e.byteLength;n++){var i=(255&e[n]).toString(16);i.length<2&&(t+="0"),t+=i}return t},e.bytesToUUID=function(e){return this.beautifyUUID(this.bytesToHex(e))},e.beautifyUUID=function(e){return e.slice(0,8)+"-"+e.slice(8,12)+"-"+e.slice(12,16)+"-"+e.slice(16,20)+"-"+e.slice(20)},e.intToHex=function(e){return("00"+e.toString(16)).slice(-2)},e.base64UrlEncode=function(e){return window.btoa(e).replace(/\\+/g,"-").replace(/\\//g,"_").replace(/=/g,"")},e.base64UrlDecode=function(e){return window.atob((e+"===".slice((e.length+3)%4)).replace(/-/g,"+").replace(/_/g,"/"))},e}();t.FormatHelper=n},6368:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.ModuleName=void 0,function(e){e.VR="VR",e.XML="XML",e.Smooth="Smooth",e.AdvertisingCore="AdvertisingCore",e.Advertising="Advertising",e.AdvertisingIma="Advertising",e.AdvertisingBitmovin="Advertising",e.AdvertisingOmSdk="AdvertisingOmSdk",e.Polyfill="Polyfill",e.Patch="Patch",e.RemoteControl="RemoteControl",e.RendererMse="RendererMSE",e.EngineNative="EngineNative",e.EngineBitmovin="EngineBitmovin",e.ABR="ABR",e.Crypto="Crypto",e.HLS="HLS",e.DRM="DRM",e.DASH="DASH",e.ContainerTS="ContainerTS",e.ContainerMP4="ContainerMP4",e.ContainerWebM="ContainerWebM",e.Subtitles="Subtitles",e.SubtitlesTTML="SubtitlesTTML",e.SubtitlesWebVTT="SubtitlesWebVTT",e.SubtitlesCEA608="SubtitlesCEA608",e.SubtitlesNative="SubtitlesNative",e.Style="Style",e.Thumbnail="Thumbnail",e.ThumbnailImp="Thumbnail",e.UI="UI",e.Tizen="Tizen",e.Webos="webos",e.Vidaa="vidaa",e.LowLatency="LowLatency",e.ServiceWorkerClient="ServiceWorkerClient",e.Envivio="Envivio",e.PlayStation5="PlayStation5",e.PlayStation4="PlayStation4",e.EngineWebRtc="EngineWebRtc",e.ReactNative="ReactNative"}(n||(t.ModuleName=n={}))},6435:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.isSafari17orIOS17=t.isSafari15orIOS15=t.getIOSVersion=t.isSafariIOS=t.isIOS=t.isMobile=t.isWebOs2017=t.isWebOs2016=t.isWebOs2020OrOlder=t.getWebOsChromiumVersion=t.isWebOS=t.isVizio=t.getVidaaVersion=t.isVidaa=t.getTizenVersion=t.isTizen2020=t.isTizen2016=t.isTizen2017=t.isTizen2018=t.isTizen2019=t.isTizen=t.getEdgeVersion=t.isEdge=t.getSafariVersion=t.isSafari=t.isFirefoxIOS=t.isFirefox=t.isChromium=t.isChrome=t.WEBOS_2021_CHROMIUM_VERSION=t.WEBOS_2020_CHROMIUM_VERSION=t.WEBOS_2018_2019_CHROMIUM_VERSION=t.WEBOS_2016_2017_CHROMIUM_VERSION=t.TIZEN_2020_MINOR_VERSION=t.TIZEN_2020_MAJOR_VERSION=t.TIZEN_2019_MINOR_VERSION=t.TIZEN_2019_MAJOR_VERSION=t.TIZEN_2018_MINOR_VERSION=t.TIZEN_2018_MAJOR_VERSION=t.TIZEN_2017_MINOR_VERSION=t.TIZEN_2017_MAJOR_VERSION=t.TIZEN_2016_MINOR_VERSION=t.TIZEN_2016_MAJOR_VERSION=t.SAFARI_17_MAJOR_VERSION=t.SAFARI_15_MAJOR_VERSION=t.IOS_17=t.IOS_15=t.IOS_10=t.IOS_9=void 0,t.isLegacyEdge=f,t.isPlayStation5=p,t.isPlayStation4=g,t.isReactNative=S;var i=n(3485);t.IOS_9=9,t.IOS_10=10,t.IOS_15=15,t.IOS_17=17,t.SAFARI_15_MAJOR_VERSION=15,t.SAFARI_17_MAJOR_VERSION=17,t.TIZEN_2016_MAJOR_VERSION=2,t.TIZEN_2016_MINOR_VERSION=4,t.TIZEN_2017_MAJOR_VERSION=3,t.TIZEN_2017_MINOR_VERSION=0,t.TIZEN_2018_MAJOR_VERSION=4,t.TIZEN_2018_MINOR_VERSION=0,t.TIZEN_2019_MAJOR_VERSION=5,t.TIZEN_2019_MINOR_VERSION=0,t.TIZEN_2020_MAJOR_VERSION=5,t.TIZEN_2020_MINOR_VERSION=5,t.WEBOS_2016_2017_CHROMIUM_VERSION=38,t.WEBOS_2018_2019_CHROMIUM_VERSION=53,t.WEBOS_2020_CHROMIUM_VERSION=68,t.WEBOS_2021_CHROMIUM_VERSION=79;var r=function(){return navigator.userAgent.includes("Chrome")&&!(0,t.isEdge)()};t.isChrome=r;var a=function(){return(0,t.isChrome)()||(0,t.isEdge)()&&!f()};t.isChromium=a;var o=function(){return navigator.userAgent.includes("Firefox")};t.isFirefox=o;var s=function(){return navigator.userAgent.includes("FxiOS")&&(0,t.isIOS)()};t.isFirefoxIOS=s;var u=function(){return navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")&&!navigator.userAgent.includes("IEMobile")&&!navigator.userAgent.includes("Edge")&&!(0,t.isTizen)()&&!p()&&!g()||(0,i.isFairPlayDrmSupported)()};t.isSafari=u;var l=function(){var e=/Version\\/(\\d+)\\.?(\\d+)?\\.?(\\d+)?/.exec(navigator.userAgent)||null;if(!e)return{};var t=e[1],n=e[2],i=e[3],r=function(e){return parseInt(e)};return{major:r(t),minor:r(n),patch:r(i)}};t.getSafariVersion=l;var d=function(){return/Edge\\/\\d+/i.test(navigator.userAgent)||/Edg\\/\\d+/i.test(navigator.userAgent)};t.isEdge=d;var c=function(){var e=/Edge\\/(\\d+)\\.?(\\d+)/.exec(navigator.userAgent)||/Edg\\/(\\d+)\\.?(\\d+)/.exec(navigator.userAgent);if(!e||0===e.length)return{major:0,minor:0};var t={};return e[1]&&(t.major=parseInt(e[1])),e[1]&&(t.minor=parseInt(e[2])),t};function f(){var e=79;return(0,t.isEdge)()&&(0,t.getEdgeVersion)().major<e}function p(){return/PlayStation 5/i.test(navigator.userAgent)}function g(){return/PlayStation 4/i.test(navigator.userAgent)}function S(){return"ReactNative"===window.navigator.product}t.getEdgeVersion=c;var v=function(e,n){var i=(0,t.getTizenVersion)();return i.major===e&&i.minor===n},m=function(){return navigator.userAgent.includes("Tizen")};t.isTizen=m;var h=function(){return v(t.TIZEN_2019_MAJOR_VERSION,t.TIZEN_2019_MINOR_VERSION)};t.isTizen2019=h;var E=function(){return v(t.TIZEN_2018_MAJOR_VERSION,t.TIZEN_2018_MINOR_VERSION)};t.isTizen2018=E;var y=function(){return v(t.TIZEN_2017_MAJOR_VERSION,t.TIZEN_2017_MINOR_VERSION)};t.isTizen2017=y;var I=function(){return v(t.TIZEN_2016_MAJOR_VERSION,t.TIZEN_2016_MINOR_VERSION)};t.isTizen2016=I;var T=function(){return v(t.TIZEN_2020_MAJOR_VERSION,t.TIZEN_2020_MINOR_VERSION)};t.isTizen2020=T;var _=function(){var e=/Tizen (\\d).(\\d)/.exec(navigator.userAgent);return(null==e?void 0:e[1])?{major:Number(e[1]),minor:Number(e[2]||0)}:{}};t.getTizenVersion=_;var O=function(){return navigator.userAgent.includes("VIDAA")};t.isVidaa=O;var R=function(){var e=new RegExp(/VIDAA\\/(\\d+)\\.?(\\d+)/).exec(navigator.userAgent);return(null==e?void 0:e[1])?{major:Number(e[1]),minor:Number(e[2])}:{}};t.getVidaaVersion=R;var N=function(){return navigator.userAgent.includes("VIZIO")};t.isVizio=N;var b=function(){return navigator.userAgent.includes("Web0S")||navigator.userAgent.includes("NetCast")};t.isWebOS=b;var M=function(){var e,t=new RegExp(/Chrome\\/(\\d+)\\.?(\\d+)/).exec(navigator.userAgent);return(null==t?void 0:t[1])?{major:Number(t[1]),minor:Number(null!==(e=t[2])&&void 0!==e?e:0)}:{}};t.getWebOsChromiumVersion=M;var A=function(){var e=(0,t.getWebOsChromiumVersion)();return(0,t.isWebOS)()&&void 0!==(null==e?void 0:e.major)&&e.major<=t.WEBOS_2020_CHROMIUM_VERSION};t.isWebOs2020OrOlder=A;var P=function(){return(0,t.getWebOsChromiumVersion)().major===t.WEBOS_2016_2017_CHROMIUM_VERSION&&!("requestMediaKeySystemAccess"in navigator)};t.isWebOs2016=P;var D=function(){return(0,t.getWebOsChromiumVersion)().major===t.WEBOS_2016_2017_CHROMIUM_VERSION&&"requestMediaKeySystemAccess"in navigator};t.isWebOs2017=D;var C=function(){return/Android/i.test(navigator.userAgent)||/IEMobile/i.test(navigator.userAgent)||/Windows Phone 10.0/i.test(navigator.userAgent)||/Safari/i.test(navigator.userAgent)&&/Mobile/i.test(navigator.userAgent)};t.isMobile=C;var L=function(){return/Safari/i.test(navigator.userAgent)&&/Mobile/i.test(navigator.userAgent)&&!/Android/i.test(navigator.userAgent)&&!/Windows Phone 10.0/i.test(navigator.userAgent)};t.isIOS=L;var U=function(){return/Safari/i.test(navigator.userAgent)&&/iP(hone|od|ad)/i.test(navigator.userAgent)};t.isSafariIOS=U;var w=function(){var e=/OS (\\d+)(_\\d+)*/.exec(navigator.userAgent);return e&&e.length>0&&parseInt(e[1],10)||0};t.getIOSVersion=w;var x=function(){return(0,t.isIOS)()&&(0,t.getIOSVersion)()===t.IOS_15},V=function(){return(0,t.isSafari)()&&(0,t.getSafariVersion)().major===t.SAFARI_15_MAJOR_VERSION},k=function(){return V()||x()};t.isSafari15orIOS15=k;var F=function(){return(0,t.isIOS)()&&(0,t.getIOSVersion)()===t.IOS_17},j=function(){return(0,t.isSafari)()&&(0,t.getSafariVersion)().major===t.SAFARI_17_MAJOR_VERSION},B=function(){return j()||F()};t.isSafari17orIOS17=B},6461:function(e,t,n){var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.parseSegments=c,t.addCustomTagToPlaylistTagList=f,t.handleCustomTag=p,t.removeTrailingWhitespaces=y,t.isCustomTag=I;var a=n(2442),o=n(2510),s=n(7550),u=n(2600),l=n(5877),d=n(7967);function c(e,t,n,i){void 0===i&&(i={});for(var o,c=[],f=0,T=(0,d.generateSegmentEntrySkeleton)(),_=r([],t,!0),O=0,R=n.mediaSequence,N={keys:[],keyInUse:!1},b=function(){T=(0,d.generateSegmentEntrySkeleton)(),_=[],O=0},M=function(e){g(T,O,e,o,N,_,R),c.push(T),f+=O,R++,b()},A=function(e){var t,s,f=(0,u.parseTag)(e);switch(f.name){case"EXTINF":O=parseFloat(f.value);break;case"EXT-X-BYTERANGE":T.byterange=(0,d.generateByteRangeTag)(f,c);break;case"EXT-X-DISCONTINUITY":T.discontinuity=!0;break;case"EXT-X-PROGRAM-DATE-TIME":v(T,f);break;case"EXT-X-KEY":if("NONE"===(null===(t=f.attributes)||void 0===t?void 0:t.METHOD)){N.keys=[{method:a.HlsEncryptionMethod.NONE}];break}S(f,N);break;case"EXT-X-MAP":o=(0,d.generateMapTag)(f,N);break;case"EXT-X-DATERANGE":n.dateRange.push((0,d.generateDateRange)(f));break;case"EXT-X-CUE-OUT-CONT":T.cueTag=(0,d.generateCueTag)(f,"CUE-OUT-CONT");break;case"EXT-X-CUE-OUT":T.cueTag=(0,d.generateCueTag)(f,"CUE-OUT");break;case"EXT-X-CUE-IN":T.cueTag=(0,d.generateCueTag)(f,"CUE-IN");break;case"EXT-X-SCTE35":m(T,f);break;case"EXT-X-PART":h(f,T,i);break;case"EXT-X-PRELOAD-HINT":var p=E(f,n.partInfo,i);p.init&&(o=p.init,N.keys.length>0&&(o.keys=r([],N.keys,!0),N.keyInUse=!0)),p.part&&(T.parts=null!==(s=T.parts)&&void 0!==s?s:[],T.parts.push(p.part));break;case"EXT-X-SKIP":var g=(0,d.generateSkipTag)(f);n.skippedSegmentsCount=g.skippedSegments,R+=g.skippedSegments;break;case"EXT-X-ENDLIST":n.endList=!0;break;default:(0,l.resolvePlaylistTag)(n,f,_)}},P=0,D=e;P<D.length;P++){var C=y(D[P]);if(""!==C)!C.startsWith("#")?M(C):I(C)?p(n,_,C):A(C)}T.parts&&T.parts.length>0&&(O=T.parts.reduce((function(e,t){return e+t.duration}),0),M(s.PHANTOM_SEGMENT_URL)),n.segments=c,n.totalDuration=f}function f(e,t){void 0===e.tags&&(e.tags=[]),t&&e.tags.push(t)}function p(e,t,n){t.push({type:o.MetadataType.CUSTOM,attributes:n}),f(e,(0,d.generateCustomTag)(n))}function g(e,t,n,i,a,o,s){e.duration=t,e.uri=n,e.mediaSequence=s,i&&(e.init=i),o.length>0&&(e.customTags=o),a.keys.length>0&&(e.keys=r([],a.keys,!0),a.keyInUse=!0)}function S(e,t){t.keyInUse&&(t.keys=[],t.keyInUse=!1);var n=(0,d.generateKeyTag)(e);void 0!==n&&t.keys.push(n)}function v(e,t){t.value&&(e.dateTime=new Date(t.value))}function m(e,t){var n;void 0===e.scte35&&(e.scte35=[]),e.scte35.push({type:o.MetadataType.SCTE,attributes:i({CUE:null===(n=t.attributes)||void 0===n?void 0:n.CUE},t.attributes)})}function h(e,t,n){var i;if(n.parsePartTags){t.parts=null!==(i=t.parts)&&void 0!==i?i:[];var r=t.parts[t.parts.length-1];t.parts.push((0,d.generatePartTag)(e.attributes,r))}}function E(e,t,n){return n.parsePartTags&&t?(0,d.generatePreloadHintTag)(e.attributes,t.targetDuration):{}}function y(e){return e.replace(/^[ \\t]+/g,"")}function I(e){return/^#(?!EXT)/m.test(e)}},7062:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.isLowLatencyConfigured=r;var i=n(8665);function r(e){var t=e.serviceManager.get(i.ServiceName.LiveLatencyService);return(null==t?void 0:t.isConfigured())||!1}},7093:function(e,t){var n;function i(e){return"Math"in e&&e.Math===Math}function r(){return"object"==typeof window&&window&&i(window)?window:void 0}function a(){return"object"==typeof self&&self&&i(self)?self:void 0}Object.defineProperty(t,"__esModule",{value:!0});var o=null!==(n=r())&&void 0!==n?n:a();t.default=o},7177:function(e,t){function n(e){return null!==e&&"object"==typeof e}function i(e){return void 0!==e&&!e.isDisposed}Object.defineProperty(t,"__esModule",{value:!0}),t.isObject=n,t.isContextAvailable=i},7279:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.getSourceStore=a,t.getSourceState=o;var i=n(8665),r=n(7177);function a(e){var t,n;return(0,r.isContextAvailable)(e)?null===(t=e.serviceManager)||void 0===t?void 0:t.get(i.ServiceName.SourceStoreService,null===(n=e.sourceContext)||void 0===n?void 0:n.sourceIdentifier):void 0}function o(e){var t=a(e);return null==t?void 0:t.getState()}},7485:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.TextParser=void 0;var n=/[ \\t]+/gm,i=function(){function e(e){this.data=e,this.position=0}return e.prototype.isAtEnd=function(){return this.position===this.data.length},e.prototype.skipWhitespace=function(){this.readRegex(n)},e.prototype.readRegex=function(e){var t=this.indexOf(e);if(!this.isAtEnd()&&void 0!==t&&t.position===this.position)return this.position+=t.length,t.results},e.prototype.indexOf=function(e){e.lastIndex=this.position;var t=e.exec(this.data);if(null!==t)return{position:t.index,length:t[0].length,results:t}},e}();t.TextParser=i},7550:function(e,t,n){var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentListMPDHandler=t.PHANTOM_SEGMENT_URL=void 0,t.assignSegmentStartTimesFromHlsRepresentation=_,t.copyPropValues=b,t.getMatchingPart=M;var r=n(2442),a=n(8665),o=n(2055),s=n(1397),u=n(1361),l=n(4388),d=n(331),c=n(16),f=n(1520),p=n(6368),g=n(2103),S=n(3326),v=n(3109),m=n(3553),h=n(8915);t.PHANTOM_SEGMENT_URL="PHANTOM-SEGMENT";var E=function(){function e(e){this.context=e,this.sourceContext=e.sourceContext,this.settings=e.settings,this.logger=e.logger,this.manifestService=e.serviceManager.get(a.ServiceName.ManifestService,this.sourceContext.sourceIdentifier),this.distanceToListStartSeconds=0,this.segmentListMap={},this.timestampOffset=NaN}return Object.defineProperty(e.prototype,"playerStateService",{get:function(){return this.context.serviceManager.get(a.ServiceName.PlayerStateService)},enumerable:!1,configurable:!0}),e.prototype.createSegmentListEntry=function(e,t){var n;if(!e)return null;var a={isInitSegment:!1,url:e._media,duration:e._duration,internalRepresentationId:t,periodId:t.periodId};if(e._key&&e._key.method!==r.HlsEncryptionMethod.NONE&&(a.key=e._key),void 0!==e._playbackTime&&(a.startTime=e._playbackTime),e._dateTime&&(a.dateTime=e._dateTime),e._metadata&&(a.metadata=e._metadata),e._byteRange&&(a.byteRange=e._byteRange),e._init&&(a.init=e._init,(null===(n=e._init.key)||void 0===n?void 0:n.method)===r.HlsEncryptionMethod.NONE&&(a.init=i({},a.init),delete a.init.key)),void 0!==e._mediaSequence&&(a.mediaSequence=e._mediaSequence),void 0!==e._discontinuitySequenceNumber&&(a.discontinuitySequenceNumber=e._discontinuitySequenceNumber),e._parts){var o=a.dateTime;a.parts=e._parts.map((function(e){var t=O(e,a);return o&&(t.dateTime=o,t.startTime=(0,d.toSeconds)(o.getTime()),o=new Date(o.getTime()+(0,d.toMilliSeconds)(e.duration))),t}))}return a.segmentId=(0,h.generateSegmentId)(a),a},e.prototype.removeDroppedOutSegmentsFromList=function(e,t,n){if(t.SegmentURL.length<1)return e.entries=[],e.totalDuration=0,e.entries.length;for(var i=this.createSegmentListEntry(t.SegmentURL[0],n),r=0,a=0,o=0;o<e.entries.length;o++){var s=e.entries[o];if((0,g.isIdenticalSegmentInfo)(s,i))break;r+=s.duration,e.entries.splice(o,1),o--,a++,this.currentRepresentationId.equals(n)&&this.rewindOneSegment()}return e.totalDuration-=r,a},e.prototype.addNewSegmentsToList=function(e,t,n){for(var i=[],r=e.entries[e.entries.length-1],a=0,o=t.SegmentURL.length-1;o>=0;o--){var s=this.createSegmentListEntry(t.SegmentURL[o],n);if(r&&(0,g.isIdenticalSegmentInfo)(r,s)){if(R(r)){var u=N(r,s);e.totalDuration+=u}break}r&&(0,g.isIdenticalInitSegmentInfo)(r.init,s.init)&&(s.init=r.init),isNaN(s.duration)&&(s.duration=e.maximumSegmentDuration),s.duration/=e.timescale,a+=s.duration,i.unshift(s)}return e.totalDuration+=a,e.entries=e.entries.concat(i),i.length},e.prototype.updateSegmentList=function(e,t){var n,i,r=t.SegmentList,a=t._internalId.representationId,o=null===(n=this.currentRepresentationId)||void 0===n?void 0:n.equals(t._internalId),s=null===(i=null==r?void 0:r[0])||void 0===i?void 0:i.SegmentURL.length;if(!o||s){e.Uri&&e.Uri!==t.Uri&&this.resetSegmentListUponFailover(e,null==r?void 0:r[0],o),e.Uri=t.Uri,e.timescale=1,e.maximumSegmentDuration=1,e.startNumber=1;for(var u=function(n){var i=r[n];if(i.hasOwnProperty("_duration")&&(e.maximumSegmentDuration=Number(i._duration)),["_timescale","_startNumber","_presentationTimeOffset"].filter((function(e){return i.hasOwnProperty(e)})).forEach((function(t){return e[t.substr(1)]=i[t]})),l.updateInitInfo(e,i,t),i.hasOwnProperty("SegmentURL")){var o=l.removeDroppedOutSegmentsFromList(e,i,t._internalId),s=l.addNewSegmentsToList(e,i,t._internalId);l.logManifestUpdateStats(t._mimeType,a,o,s),l.manifestService.isHlsManifest()?_(e.entries,t._internalId,l.manifestService):l.assignSegmentStartTimesForDash(e,t._internalId)}},l=this,d=0;d<r.length;d++)u(d)}},e.prototype.resetSegmentListUponFailover=function(e,t,n){var i,r,a,o,s,l;if(this.manifestService.isLive()&&n){var d=null!==(a=null===(r=null===(i=e.entries)||void 0===i?void 0:i[0])||void 0===r?void 0:r.mediaSequence)&&void 0!==a?a:1/0,c=(null!==(s=null===(o=null==t?void 0:t.SegmentURL[0])||void 0===o?void 0:o._mediaSequence)&&void 0!==s?s:0)-d,f=D(e);!isNaN(c)&&c>0&&c<f&&this.rewindSegmentListIndexBy(c)}(0,u.clearArray)(null!==(l=e.entries)&&void 0!==l?l:[])},e.prototype.updateInitInfo=function(e,t,n){var i,r=null===(i=f.ModuleManager.get(p.ModuleName.DASH,!1))||void 0===i?void 0:i.initSegmentInfoSourceDetectors.isInitializationNode;(null==r?void 0:r(t))?e.init=f.ModuleManager.get(p.ModuleName.DASH,!1).initSegmentInfoProviders.provideSegmentInfoFromInitializationNodeProperty(t,n):delete e.init},e.prototype.logManifestUpdateStats=function(e,t,n,i){(n>0||i>0)&&this.logger.debug("[".concat(e,"][").concat(t,"] Updated segment list, removed ").concat(n," segments and added ").concat(i," segments"))},e.prototype.assignSegmentStartTimesForDash=function(e,t){var n=this.getMinStartTime(t),i=0;e.entries.forEach((function(e){var t;e.startTime=n+i,i+=null!==(t=e.duration)&&void 0!==t?t:0}))},e.prototype.extrapolateStartTimesFromDiscontinuityStarts=function(e){var t,n,i,r,a=null===(n=null===(t=this.getSourceStateService())||void 0===t?void 0:t.getState())||void 0===n?void 0:n.hls;if(a){var o=e.getRepresentationId().representationId,s=this.getSegmentListEntries(this.currentRepresentationId),u=f.ModuleManager.get(p.ModuleName.HLS).selectors,l=u.getDiscoSequenceTimings,d=u.getMergedDiscontinuityTimings,c=I(s,null!==(i=l(a,e.getMimeType()))&&void 0!==i?i:d(a));if(!c){var g=null===(r=this.getCurrentPeriod())||void 0===r?void 0:r.start;if(void 0===g)return;c={index:0,discontinuityStartTime:g},this.logger.debug("Could not find discontinuity boundary with start time, using period start time ".concat(g))}this.logger.debug("Assigning segment start times for rep ".concat(o," from discontinuity at index ").concat(c.index," with time ").concat(c.discontinuityStartTime)),s[c.index].startTime=c.discontinuityStartTime;for(var S=c.index-1;S>=0;S--)s[S].startTime=s[S+1].startTime-s[S+1].duration;for(S=c.index+1;S<s.length;S++)s[S].startTime=s[S-1].startTime+s[S-1].duration}},e.prototype.getSourceStateService=function(){return this.context.serviceManager.get(a.ServiceName.SourceStoreService,this.context.sourceContext.sourceIdentifier)},e.prototype.getListIndexForSegment=function(e,t){for(var n=-1,i=this.getSegmentListEntries(t),r=i.length-1;r>=0;r--)if((0,g.isIdenticalSegmentInfo)(i[r],e)){n=r;break}return n},e.prototype.populateSegmentListMap=function(e){var t,n=this,i=null===(t=this.manifestService)||void 0===t?void 0:t.getAdaptationSet(e);i&&i.Representation.forEach((function(e){var t=n.segmentListMap[e._internalId.key()];if(t||(t=n.initializeSegmentList(e._internalId.key())),e.hasOwnProperty("SegmentList")&&(n.updateSegmentList(t,e),f.ModuleManager.has(p.ModuleName.DASH))){var i=f.ModuleManager.get(p.ModuleName.DASH).initSegmentInfoSourceDetectors.isInitializationNode;if(!t.hasOwnProperty("init")&&e.hasOwnProperty("SegmentBase")&&e.SegmentBase&&i(e.SegmentBase[0])){var r=f.ModuleManager.get(p.ModuleName.DASH).initSegmentInfoProviders.provideSegmentInfoFromInitializationNodeProperty;t.init=r(e.SegmentBase[0],e)}}}))},e.prototype.selectNewRepresentationId=function(){var e,t,n=this,i=null===(e=this.context.serviceManager.get(a.ServiceName.SourceStoreService,this.sourceContext.sourceIdentifier).getState().activeTracks)||void 0===e?void 0:e[this.adaptationSetId.adaptationSetId];if((null===(t=null==i?void 0:i.selectedRepresentationId)||void 0===t?void 0:t.periodId)===this.adaptationSetId.periodId)return i.selectedRepresentationId;if(this.currentRepresentationId){var r=this.manifestService.getRepresentation(this.adaptationSetId,this.currentRepresentationId.representationId);if(r)return r._internalId}var o=function(e){return n.isSegmentInfoLoaded(e._internalId)};return(this.getMatchingRepresentationByBandwidth(o)||this.getMatchingRepresentationByBandwidth())._internalId},e.prototype.getMatchingRepresentationByBandwidth=function(e){return this.manifestService.getMatchingRepresentationByBandwidth(this.adaptationSetId,this.getCurrentBandwidth(),e)},e.prototype.shouldUpdateRepresentation=function(){var e=this.adaptationSetId.equals(this.currentRepresentationId),t=!this.currentRepresentationId||Boolean(this.manifestService.getRepresentationById(this.currentRepresentationId));return!this.currentRepresentationId||!e||!t},e.prototype.setAdaptationSetId=function(e){this.adaptationSetId=e,this.populateSegmentListMap(this.adaptationSetId);var t,n=this.manifestService.isLive(),i=0;this.shouldUpdateRepresentation()&&(this.currentRepresentationId=this.selectNewRepresentationId());var r=this.segmentListMap[this.currentRepresentationId.key()];if(r||(r=this.initializeSegmentList(this.currentRepresentationId.key())),r&&n&&r.entries&&r.entries.length>0){var a=r.entries.length-1;this.listIndex>a&&(i=this.listIndex-a),this.listIndex=Math.min(a,this.listIndex||0),this.listIndex=Math.max(0,this.listIndex),t=r.entries[this.listIndex]}if(this.listIndex=this.listIndex||0,n&&(this.distanceToListStartSeconds=this.getSegmentDuration()*this.settings.LIVE_SEGMENT_LIST_START_INDEX_OFFSET),n){if(t&&r){var o=this.getListIndexForSegment(t,this.currentRepresentationId);o>=0?this.listIndex=o+i:this.reset()}else this.reset();var s=this.checkForSegmentInfoError();s?this.rejectPendingSegmentInfoRequest(s):this.resolvePendingSegmentInfoRequests()}else this.resolvePendingSegmentInfoRequests()},e.prototype.initializeSegmentList=function(e){var t={totalDuration:0,entries:[],SegmentURL:[],startNumber:0,init:null,maximumSegmentDuration:1,timescale:1};return this.segmentListMap[e]=t,t},e.prototype.getCurrentBandwidth=function(){var e=0,t=this.getCurrentRepresentation();return t&&(e=t._bandwidth),e},e.prototype.checkForSegmentInfoError=function(){var e;if(this.manifestService.isHlsManifest()){var t=this.manifestService.getRepresentationById(this.currentRepresentationId);if(null===(e=null==t?void 0:t._hls)||void 0===e?void 0:e.loadFailureReason)return t._hls.loadFailureReason}return this.hasNext()||this.manifestService.isLastPeriod(this.currentRepresentationId.periodId)?null:m.SegmentInfoError.PERIOD_COMPLETE},e.prototype.reset=function(){this.settings.ENABLE_SEEK_FOR_LIVE?this.listIndex=0:this.timeShift(0)},e.prototype.syncSegmentStartTimeUsingMediaSequenceNumber=function(e,t){var n,i,r=this.manifestService.getRepresentationById(e),a=this.manifestService.getRepresentationById(t),o=(0,c.isNumber)(null===(n=null==a?void 0:a._hls)||void 0===n?void 0:n.mediaSequence)&&(0,c.isNumber)(null===(i=null==r?void 0:r._hls)||void 0===i?void 0:i.mediaSequence);if(r&&a&&o){var s=a._hls.mediaSequence-r._hls.mediaSequence,l=this.getSegmentListEntries(e),d=this.getSegmentListEntries(t);if(!(s>=l.length)){d[0].startTime=l[s].startTime;var f=function(e,t){(0,c.isNumber)(e.startTime)&&(0,c.isNumber)(e.duration)&&(t.startTime=e.startTime+e.duration)};(0,u.forEachFromIndex)(d,1,f)}}},e.prototype.setRepresentationId=function(e){var t=this.manifestService.getRepresentationById(e),n=this.getSegmentListEntries(this.currentRepresentationId)[this.listIndex];if(t){var i=void 0===this.currentRepresentationId,r=this.currentRepresentationId;if(this.currentRepresentationId=e,this.manifestService.isLive()&&t._hls)if(this.settings.HLS_SYNC_SEGMENT_PLAYBACK_TIME_VIA_MEDIA_SEQUENCE&&this.syncSegmentStartTimeUsingMediaSequenceNumber(r,this.currentRepresentationId),i)this.reset();else if(this.settings.HLS_SYNC_VIA_MEDIA_SEQUENCE){var a=this.manifestService.getRepresentationById(r)._hls.mediaSequence+this.listIndex;this.listIndex=a-t._hls.mediaSequence,this.listIndex=Math.max(this.listIndex,0)}else if(null!=(null==n?void 0:n.startTime)&&!isNaN(n.startTime)){var o=this.getIndexForTime(n.startTime);null===o||isNaN(o)||(this.listIndex=this.adaptListIndexToCorrectIndex(o,n.startTime))}return!0}return!1},e.prototype.adaptListIndexToCorrectIndex=function(e,t){var n=this.getSegmentListEntries(this.currentRepresentationId),i=[n[e],n[e+1]];if(e===n.length-1||i.some((function(e){return!e.startTime})))return e;var r=i.map((function(e){return Math.abs(e.startTime-t)})),a=Math.min.apply(this,r);return e+r.indexOf(a)},e.prototype.getInitSegmentInfo=function(){var e=this.getSegmentList(this.currentRepresentationId);if(null==e?void 0:e.init)return e.init.presentationTimeOffset=this.getTimestampOffset(),e.init;var t=this.getSegmentListEntries(this.currentRepresentationId);if(this.isIndexInBounds(t)&&this.listIndex>-1){var n=i({},t[this.listIndex]);if(n.init){var r=n.init;return r.isInitSegment=!0,r}}return null},e.prototype.getNextSegmentInfo_=function(e){var t;if(!(this.getSegmentListEntries(this.currentRepresentationId).length<1||this.listIndex<0)){var n=this.isTargetTimeOfOngoingSeek(e);return n&&this.logger.debug("".concat(null===(t=this.adaptationSetId)||void 0===t?void 0:t.adaptationSetId," given time ").concat(e," matches seek time, performing precise segment/part search"),e),(0,v.isPlayingLowLatencyHls)(this.context)&&this.setListIndexForTime(e,n),this.getNextSegmentOrPart(e,n)}},e.prototype.isTargetTimeOfOngoingSeek=function(e){var t;return e===(null===(t=this.playerStateService)||void 0===t?void 0:t.targetPlaybackTime)||!1},e.prototype.setListIndexForTime=function(e,t){var n;if(0!==e){if(this.listIndex=this.getIndexForTime(e),!t){var i=this.getSegmentListEntries(this.currentRepresentationId),r=i[this.listIndex];if(!(r.parts&&r.parts.length>0)){var a=this.listIndex;this.listIndex=A(e,this.listIndex,i),this.listIndex!==a&&this.logger.debug("Snapped segment index for time ".concat(e," to next segment boundary at ").concat(null===(n=i[this.listIndex])||void 0===n?void 0:n.startTime))}}}else this.context.logger.debug("Ignoring target time of 0 for LL-HLS")},e.prototype.getNextSegmentOrPart=function(e,t){var n=this.getSegmentListEntries(this.currentRepresentationId);if(this.isIndexInBounds(n)){var i=n[this.listIndex];if(!(i.parts&&i.parts.length>0))return this.getNextFullSegmentInfo();if(R(i))return M(i,e,t);var r=M(i,e,t);return r?i.parts.indexOf(r)>0?r:this.getNextFullSegmentInfo():(this.listIndex++,this.getNextSegmentOrPart(e,t))}},e.prototype.getNextSegmentInfo=function(e){var t=this.getNextSegmentInfo_(e);return t?(this.populateSegmentInfoProps(t),Promise.resolve(t)):this.queueSegmentInfoRequest({time:e})},e.prototype.populateSegmentInfoProps=function(e){return e.presentationTimeOffset=this.getTimestampOffset(),e.startTime&&(e.wallClockTime=(0,d.toMilliSeconds)(e.startTime)),e},e.prototype.getNextFullSegmentInfo=function(){return this.getSegmentListEntries(this.currentRepresentationId)[this.listIndex++]},e.prototype.getSegmentInfos=function(){var e=this,t={},n=this.manifestService.getAdaptationSet(this.adaptationSetId);return n&&n.Representation.forEach((function(n){var i=e.getSegmentListEntries(n._internalId);t[n._id]=i.map((function(e){return{url:e.url,duration:e.duration,startTime:e.startTime}}))})),t},e.prototype.getSubtitleUrl=function(){return this.manifestService.getAvailableSubtitles(this.adaptationSetId.periodId)[0].url},e.prototype.getStartNumber=function(){var e=this.getSegmentList(this.currentRepresentationId);return e?e.startNumber:1},e.prototype.getSegmentDuration=function(){var e=this,t=this.getSegmentList(this.currentRepresentationId),n=this.getSegmentListEntries(this.currentRepresentationId);if(t)return n.length>0&&(0,c.isNumber)(t.totalDuration)?t.totalDuration/n.length:t.maximumSegmentDuration;if(this.manifestService.getAdaptationSet(this.adaptationSetId)){var i=Object.keys(this.segmentListMap).map((function(t){return e.segmentListMap[t]})).find((function(e){return null!==e}));if(i)return i.maximumSegmentDuration}return 1},e.prototype.seekTo=function(e){e>-1&&(this.listIndex=this.getIndexForTime(e))},e.prototype.isIndexInBounds=function(e){return this.listIndex<e.length},e.prototype.hasNext=function(){var e=this.getSegmentListEntries(this.currentRepresentationId);return!this.currentRepresentationId||!e.length||(!this.manifestService.isLive()?this.isIndexInBounds(e):this.hasNextLive(e))},e.prototype.hasNextLive=function(e){return!!this.manifestService.isLastPeriod(this.currentRepresentationId.periodId)||this.isIndexInBounds(e)},e.prototype.setTimestampOffset=function(e,t){void 0===t&&(t=1),this.timestampOffset=e/t},e.prototype.getTimestampOffset=function(){if(!isNaN(this.timestampOffset))return this.timestampOffset;var e=this.getCurrentPeriod(),t=0;if(this.currentRepresentationId){var n=this.getSegmentList(this.currentRepresentationId);n&&!isNaN(n.presentationTimeOffset)&&(t=n.presentationTimeOffset/n.timescale)}return e&&!this.manifestService.isHlsManifest()&&(t-=e.start),t},e.prototype.getIndex=function(){return this.listIndex},e.prototype.setIndex=function(e){if("number"==typeof e)return e<0?(this.logger.debug("Tried to set index at MPDHandler to value smaller 0. Setting to 0."),void(this.listIndex=0)):void(this.listIndex=e);this.logger.debug("Tried to set index at MPDHandler with parameter not being a number!")},e.prototype.queueSegmentInfoRequest=function(e){var t,n=this;if(this.pendingSegmentInfoRequest)return Promise.reject("fail");var r=this.manifestService.getRepresentationById(this.currentRepresentationId);return(null===(t=null==r?void 0:r._hls)||void 0===t?void 0:t.loadFailureReason)?Promise.reject(r._hls.loadFailureReason):(this.logger.debug("No segment info was found for the target time. Waiting for representation update."),new Promise((function(t,r){n.pendingSegmentInfoRequest=i(i({},e),{resolve:t,reject:r})})))},e.prototype.resolvePendingSegmentInfoRequests=function(){if(this.pendingSegmentInfoRequest){var e=this.getNextSegmentInfo_(this.pendingSegmentInfoRequest.time);e?(this.populateSegmentInfoProps(e),this.pendingSegmentInfoRequest.resolve(e),this.pendingSegmentInfoRequest=null):this.manifestService.isLive()||this.hasNext()||this.pendingSegmentInfoRequest.reject(m.SegmentInfoError.END_OF_STREAM_REACHED)}},e.prototype.rejectPendingSegmentInfoRequest=function(e){this.pendingSegmentInfoRequest&&(this.pendingSegmentInfoRequest.reject(e),this.pendingSegmentInfoRequest=null)},e.prototype.getLiveEdgeIndex=function(){var e=this.getSegmentListEntries(this.currentRepresentationId);return e.length>0?e.length-1:0},e.prototype.getIndexForOffset=function(e){for(var t,n=Math.abs(e),i=this.getSegmentDuration(),r=this.getSegmentListEntries(this.currentRepresentationId),a=this.getLiveEdgeIndex(),o=0,s=r.length-1;s>=0&&!((o+=null!==(t=r[s].duration)&&void 0!==t?t:i)>=n);s--)a--;return Math.max(0,a)},e.prototype.timeShift=function(e,t,n){if(this.manifestService.isLive()){var i=this.listIndex;this.listIndex=n&&this.hasStartTimes()?this.getIndexForTime(n):this.getIndexForOffset(e),this.logger.debug("timeShift changes index from ".concat(i," to ").concat(this.listIndex,", based on")+" offset ".concat(e," and target time ").concat(n))}},e.prototype.hasStartTimes=function(){var e,t=null===(e=this.getSegmentListEntries(this.currentRepresentationId)[0])||void 0===e?void 0:e.startTime;return(0,c.isNumber)(t)&&t>=0},e.prototype.updateRepresentation=function(e){return Promise.resolve(e)},e.prototype.isSegmentInfoLoaded=function(e){if(void 0===e&&(e=this.currentRepresentationId),!e)return!1;var t=this.getSegmentListEntries(e),n=this.manifestService.getRepresentationById(e);return!(!t.length||!n)},e.prototype.rewindSegmentListIndexBy=function(e){this.logger.debug("Rewinding segment list index by ".concat(e," segments")),this.listIndex-=e,this.listIndex<0&&(this.logger.debug("List array index is smaller than 0, probably a discontinuity. Using 0."),this.listIndex=0)},e.prototype.rewindOneSegment=function(){return this.listIndex--,!(this.listIndex<0)||(this.logger.debug("List array index is smaller than 0, probably a discontinuity. Using 0."),this.listIndex=0,!1)},e.prototype.getCurrentPeriod=function(){return this.manifestService.getPeriod(this.currentRepresentationId)},e.prototype.getExpectedPresentationTime=function(e){var t=this.getCurrentPeriod().start;return(this.manifestService.hasSinglePeriod()?this.getSeekableRange().start:t)+this.getSegmentListEntries(this.currentRepresentationId).slice(0,e).reduce((function(e,t){return e+t.duration}),0)},e.prototype.getCurrentRepresentation=function(){return this.manifestService.getRepresentationById(this.currentRepresentationId)},e.prototype.getSegmentList=function(e){if(e)return this.segmentListMap[e.key()]},e.prototype.getSegmentListEntries=function(e){var t,n;return e?this.settings.ENABLE_SEGMENT_INFO_PROVIDER_FROM_STORE&&this.manifestService.isHlsManifest()?this.getStoreSegmentInfos(e):null!==(n=null===(t=this.getSegmentList(e))||void 0===t?void 0:t.entries)&&void 0!==n?n:[]:[]},e.prototype.getStoreSegmentInfos=function(e){var t,n=null===(t=this.getSourceStateService())||void 0===t?void 0:t.getState();return(0,S.getSegmentInfos)(n,e.key())},e.prototype.findListIndexWithoutSegmentStartTimes=function(e,t){var n,i;if(!this.adaptationSetId)return 0;for(var r=this.manifestService.getPeriod(this.adaptationSetId),a=null!==(n=null==r?void 0:r.start)&&void 0!==n?n:0,o=this.getSegmentListEntries(this.currentRepresentationId),s=0,u=a;u<=t&&s<o.length;s++)u+=null!==(i=o[s].duration)&&void 0!==i?i:0;return Math.max(s-1,0)},e.prototype.getIndexForTime=function(e){var t=this.getSegmentList(this.currentRepresentationId),n=this.getSegmentListEntries(this.currentRepresentationId);if(!t||0===n.length||!(0,c.isNumber)(t.totalDuration))return 0;var i=this.getMinStartTime(),r=Math.max(i,e),a=t.totalDuration/n.length,o=(0,h.findSegmentIndexForTime)(n,r,a);return null!==o?o:this.findListIndexWithoutSegmentStartTimes(t,r)},e.prototype.getMinStartTime=function(e){void 0===e&&(e=this.currentRepresentationId);var t=this.manifestService.hasSinglePeriod()?this.getSeekableRange().start:this.manifestService.getPeriod(e).start;return this.manifestService.isFirstPeriod(e.periodId)&&(t+=this.distanceToListStartSeconds),t},e.prototype.getLiveEdgeTime=function(){var e,t=null===(e=this.manifestService)||void 0===e?void 0:e.getLastPeriod();return t?t.start+l.DurationConverter.getDurationInSec(t._duration):1/0},e.prototype.getActualTimeShiftBufferDepth=function(){return this.manifestService.getTimeShiftBufferDepthSeconds()},e.prototype.getSeekableRange=function(){var e={start:0,end:0},t=this.manifestService.getTotalDuration();if(!this.manifestService.isLive())return e.start=(0,o.getStartTimeOffset)(this.getSourceStateService().getState()),e.end=t,e;var n=this.manifestService.getTimeShiftBufferDepthSeconds(),i=(0,d.toSeconds)(this.manifestService.getRequestTimestamp()-this.manifestService.getAvailabilityStartTime());if(e.start=Math.max(i+n,0),e.end=Math.max(i,0),f.ModuleManager.has(p.ModuleName.HLS)&&this.manifestService.isHlsManifest()){var r=f.ModuleManager.get(p.ModuleName.HLS).selectors,a=r.getHlsState;"EVENT"===(0,r.getPlaylistType)(a(this.getSourceStateService().getState()))?(e.start=0,e.end=t):e.start+=this.distanceToListStartSeconds}return e.end-=this.manifestService.getDesiredDistanceToLiveEdge(),e},e.prototype.getPendingSegmentInfoRequest=function(){return this.pendingSegmentInfoRequest},e.prototype.setPendingSegmentInfoRequest=function(e){this.pendingSegmentInfoRequest=e},e.prototype.canSwitchRepresentation=function(e){var t,n=this.getSegmentListEntries(this.currentRepresentationId)[this.listIndex];if(!n)return!0;var i=P(e,n,this.settings.LL_HLS_SEGMENT_END_TIME_MATCHING_TOLERANCE);if(!R(n)&&i)return!0;var r=this.isTargetTimeOfOngoingSeek(e);return!((null===(t=null==n?void 0:n.parts)||void 0===t?void 0:t.length)&&M(n,e,r)!==n.parts[0])},e.prototype.getLatestTimeForPossibleSwitch=function(e){var t,n=this.getLLHlsInfoForTime(e),i=n.isLoadingCurrentSegmentsParts,r=n.currentSegment;return r&&i&&null!==(t=r.startTime)&&void 0!==t?t:e},e.prototype.adjustTimeToNextSegmentStart=function(e){var t=this.getLLHlsInfoForTime(e),n=t.isLoadingCurrentSegmentsParts,i=t.currentSegment;return n&&(null==i?void 0:i.startTime)&&(null==i?void 0:i.duration)?i.startTime+i.duration:e},e.prototype.getLLHlsInfoForTime=function(e){var t,n=y(e,this.getSegmentListEntries(this.currentRepresentationId)),i=null===(t=null==n?void 0:n.parts)||void 0===t?void 0:t[0],r=this.isTargetTimeOfOngoingSeek(e),a=!1;if(i&&n){var o=M(n,e,r);a=void 0!==o&&o!==i}return{currentSegment:n,isLoadingCurrentSegmentsParts:a}},e.prototype.dispose=function(){this.settings=null,this.manifestService=null,this.adaptationSetId=null,this.currentRepresentationId=null,this.manifestService=null,this.pendingSegmentInfoRequest=null,this.segmentListMap=null},e}();function y(e,t){var n,i,r;if(t&&!(t.length<1)&&(null===(n=t[0])||void 0===n?void 0:n.duration)){var a=(0,h.findSegmentIndexForTime)(t,e,t[0].duration),o=null===a,s=e<(null!==(r=null===(i=t[0])||void 0===i?void 0:i.startTime)&&void 0!==r?r:1/0),u=t[t.length-1];if(void 0!==u.startTime&&void 0!==u.duration){var l=e>u.startTime+u.duration;if(!(o||s||l))return t[a]}}}function I(e,t){for(var n=e[0].discontinuitySequenceNumber,i=1;i<e.length;i++){var r=e[i].discontinuitySequenceNumber;if(r!==n){var a=T(t,r);if(null!=a)return{index:i,discontinuityStartTime:a}}}return null}function T(e,t){var n=e[String(t)];return n&&n.startTime>0?n.startTime:(n=e[String(t-1)])&&n.endTime>0?n.endTime:null}function _(e,t,n){if(e.length&&n.hasSegmentStartTimeForHlsRepresentation(t)){var i;null!=e[0].startTime?i=(0,u.findIndexFromEnd)(e,(function(e){return null!=e.startTime}))+1:(e[0].startTime=n.getStartTimeForHlsSegment(t,e[0]),i=1);var r=function(e,t){return t.startTime=e.startTime+e.duration};(0,u.forEachFromIndex)(e,i,r)}}function O(e,t){var n={url:e.uri,duration:e.duration,isInitSegment:!1,internalRepresentationId:t.internalRepresentationId,isPreloadHint:Boolean(e.isPreloadHint),isIndependent:Boolean(e.independent)};return e.byteRange&&(n.byteRange=e.byteRange),t.key&&(n.key=t.key),t.init&&(n.init=t.init),(0,c.isDefined)(t.mediaSequence)&&(n.mediaSequence=t.mediaSequence),(0,c.isDefined)(t.discontinuitySequenceNumber)&&(n.discontinuitySequenceNumber=t.discontinuitySequenceNumber),n}function R(e){return e.url.endsWith(t.PHANTOM_SEGMENT_URL)}function N(e,n){var i=n.duration-e.duration;return b(n,e,["url","mediaURL","duration","parts","init","key","discontinuitySequenceNumber","isDiscontinuityStart"]),!e.url.endsWith(t.PHANTOM_SEGMENT_URL)&&(e.url=(0,s.forceReallocation)(e.url)),i}function b(e,t,n){n.forEach((function(n){return t[n]=e[n]}))}function M(e,t,n){var i,r=null===(i=e.parts)||void 0===i?void 0:i.every(h.isSegmentTiming);if(e.parts&&r){var a=e.parts,o=a.reduce((function(e,t){return e+t.duration}),0)/a.length,s=(0,h.findSegmentIndexForTime)(a,t,o);if(null!=s)return n?e.parts[0]:(s=A(t,s,a),e.parts[s])}}function A(e,t,n){var i=n[t],r=i.startTime+i.duration;return Math.abs(e-i.startTime)<=Math.abs(e-r)?t:t+1}function P(e,t,n){return void 0!==t.startTime&&void 0!==t.duration&&Math.abs(e-(t.startTime+t.duration))<n}function D(e){var t,n,i=24,r=e.totalDuration/(null!==(n=null===(t=e.entries)||void 0===t?void 0:t.length)&&void 0!==n?n:1);return Math.ceil(i/r)}t.SegmentListMPDHandler=E},7697:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.WebWorkerContext=void 0;var i=n(5469),r=function(){function e(e){var t=this;this.runsInWebWorker="undefined"==typeof window,this.onmessage=null,this.sendMessage=function(e,n){t.runsInWebWorker?postMessage(e,n):"function"==typeof t.onmessage&&setTimeout((function(){t.onmessage({data:e})}),0)},this.postMessage=function(e){setTimeout((function(){t.onMessageCallback({data:e})}),0)},this.terminate=function(){},this.onMessageCallback=function(e){e.data.action&&e.data&&t.messageHandler[e.data.action]&&t.messageHandler[e.data.action](e.data)},this.runsInWebWorker&&(0,i.installWorkerPolyfills)(),this.messageHandler=new e(this.runsInWebWorker,this.sendMessage)}return e}();t.WebWorkerContext=r},7722:function(e,t,n){var i,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.CapabilityReducer=void 0;var a=n(1829),o=n(1399),s={};function u(e){return e}function l(e,t){var n,i=t.key,a=t.value;return r(r({},e),((n={})[i]=a,n))}t.CapabilityReducer=(0,a.default)(s,((i={})[o.CapabilityActionType.InitCapabilities]=function(e,t){return u(t.payload)},i[o.CapabilityActionType.SetCapability]=function(e,t){return l(e,t.payload)},i))},7967:function(e,t,n){var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.generateRendition=u,t.generateStartAttributes=l,t.generatePartInfoTag=d,t.generateServerControlTag=c,t.generateSkipTag=f,t.generatePartTag=p,t.generatePreloadHintTag=S,t.generateStreamInfTag=v,t.generateVariantPlaylistId=m,t.generateSessionKeyTag=h,t.generateIFrameStreamInfTag=E,t.generateSegmentEntrySkeleton=y,t.generateCueTag=I,t.generateDateRange=T,t.generateKeyTag=_,t.generateByteRangeTag=O,t.generateMapTag=R,t.generateCustomTag=L;var a=n(2510),o=n(1397),s=n(6093);function u(e){var t,n,r;return{name:e.name,attributes:i(i({},e.attributes),{TYPE:null===(t=e.attributes)||void 0===t?void 0:t.TYPE,"GROUP-ID":null===(n=e.attributes)||void 0===n?void 0:n["GROUP-ID"],NAME:null===(r=e.attributes)||void 0===r?void 0:r.NAME})}}function l(e){var t,n;return(null===(t=e.attributes)||void 0===t?void 0:t.hasOwnProperty("TIME-OFFSET"))&&(n={timeOffset:Number(e.attributes["TIME-OFFSET"]),precise:"YES"===e.attributes.PRECISE}),e.attributes&&n&&n.hasOwnProperty("timeOffset")&&(e.attributes["TIME-OFFSET"]=n.timeOffset,e.attributes.PRECISE=n.precise),n}function d(e){return{targetDuration:parseFloat(e["PART-TARGET"])}}function c(e){var t={canBlockReload:!1};return e.attributes?(e.attributes["CAN-SKIP-UNTIL"]&&(t.canSkipUntil=parseFloat(e.attributes["CAN-SKIP-UNTIL"])),void 0!==t.canSkipUntil&&e.attributes["CAN-SKIP-DATERANGES"]&&(t.canSkipDateRanges="YES"===e.attributes["CAN-SKIP-DATERANGES"]),e.attributes["HOLD-BACK"]&&(t.holdBack=parseFloat(e.attributes["HOLD-BACK"])),e.attributes["PART-HOLD-BACK"]&&(t.partHoldBack=parseFloat(e.attributes["PART-HOLD-BACK"])),e.attributes["CAN-BLOCK-RELOAD"]&&(t.canBlockReload="YES"===e.attributes["CAN-BLOCK-RELOAD"]),t):t}function f(e){var t={skippedSegments:0,recentlyRemovedDateranges:[]};return e.attributes?(e.attributes["SKIPPED-SEGMENTS"]&&(t.skippedSegments=parseInt(e.attributes["SKIPPED-SEGMENTS"])),e.attributes["RECENTLY-REMOVED-DATERANGES"]&&(t.recentlyRemovedDateranges=e.attributes["RECENTLY-REMOVED-DATERANGES"].split("\\t")),t):t}function p(e,t){var n={uri:(0,o.forceReallocation)(e.URI),duration:parseFloat(e.DURATION)};if("YES"===e.INDEPENDENT&&(n.independent=!0),e.BYTERANGE){var i=e.BYTERANGE.split("@"),r=parseInt(i[0]),a=parseInt(i[1]);isNaN(a)&&(a=(null==t?void 0:t.byteRange)?t.byteRange.end+1:0),n.byteRange={start:a,end:a+r-1}}return"YES"===e.GAP&&(n.gap=!0),n}function g(e){var t=parseInt(e["BYTERANGE-START"]),n=parseInt(e["BYTERANGE-END"]);if(!isNaN(t)||!isNaN(n))return{start:isNaN(t)?0:t,end:isNaN(n)?Math.pow(2,53)-1:n}}function S(e,t){if("PART"===e.TYPE){var n={uri:(0,o.forceReallocation)(e.URI),duration:t,isPreloadHint:!0};return(i=g(e))&&(n.byteRange=i),{part:n}}if("MAP"===e.TYPE){var i,r={url:(0,o.forceReallocation)(e.URI)};return(i=g(e))&&(r.byteRange=i),{init:r}}return{}}function v(e,t){var n,r,a,o,s,u={name:e.name,uri:t,attributes:i(i({},e.attributes),{BANDWIDTH:null===(n=e.attributes)||void 0===n?void 0:n.BANDWIDTH})};return(null===(r=e.attributes)||void 0===r?void 0:r.RESOLUTION)&&(u.attributes.RESOLUTION=b(e)),(null===(a=e.attributes)||void 0===a?void 0:a.BANDWIDTH)&&(u.attributes.BANDWIDTH=parseInt(e.attributes.BANDWIDTH)),(null===(o=e.attributes)||void 0===o?void 0:o["AVERAGE-BANDWIDTH"])&&(u.attributes["AVERAGE-BANDWIDTH"]=parseInt(e.attributes["AVERAGE-BANDWIDTH"])),(null===(s=e.attributes)||void 0===s?void 0:s["PROGRAM-ID"])&&(u.attributes["PROGRAM-ID"]=parseInt(e.attributes["PROGRAM-ID"])),u.id=m(u.attributes),u}function m(e){var t="";return e.RESOLUTION&&(t+=e.RESOLUTION.height+"_"),t+=e.BANDWIDTH}function h(e){N(e)}function E(e){var t,n,i;(null===(t=e.attributes)||void 0===t?void 0:t.BANDWIDTH)&&(e.attributes.BANDWIDTH=parseInt(e.attributes.BANDWIDTH)),(null===(n=e.attributes)||void 0===n?void 0:n["AVERAGE-BANDWIDTH"])&&(e.attributes["AVERAGE-BANDWIDTH"]=parseInt(e.attributes["AVERAGE-BANDWIDTH"],10)),(null===(i=e.attributes)||void 0===i?void 0:i.RESOLUTION)&&(e.attributes.RESOLUTION=b(e))}function y(){return{uri:"",duration:0,mediaSequence:0}}function I(e,t){var n={type:t};if(e.attributes){var i=[];for(var r in e.attributes)if(e.attributes.hasOwnProperty(r)){var a=e.attributes[r]?"".concat(r,"=").concat(e.attributes[r]):r;i.push(a)}n.attributes=i}return n}function T(e){var t={type:a.MetadataType.DATERANGE};if(e.attributes){t.clientAttributes={};var n=e.attributes;for(var i in n)if(n.hasOwnProperty(i))if(i.startsWith("X-")){var r=2;t.clientAttributes[(0,o.kebabCaseToCamelCase)(i.substring(r))]=(0,o.forceReallocation)(n[i])}else t[(0,o.kebabCaseToCamelCase)(i)]=(0,o.forceReallocation)(n[i])}return t.duration&&(t.duration=Number(t.duration)),t.plannedDuration&&(t.plannedDuration=Number(t.plannedDuration)),t.endOnNext&&(t.endOnNext=!0),t}function _(e){if(N(e),e.attributes&&"NONE"!==e.attributes.METHOD&&e.attributes.URI){var t={method:(0,o.forceReallocation)(e.attributes.METHOD)||"AES-128",uri:(0,o.forceReallocation)(e.attributes.URI)};return void 0!==e.attributes.IV&&(t.iv=e.attributes.IV),e.attributes.KEYFORMAT&&(t.keyformat=(0,o.forceReallocation)(e.attributes.KEYFORMAT)),e.attributes.KEYFORMATVERSIONS&&(t.keyformatversions=(0,o.forceReallocation)(e.attributes.KEYFORMATVERSIONS)),e.attributes.KEYID&&(t.keyid=(0,o.forceReallocation)(e.attributes.KEYID)),t}}function O(e,t){var n;if(e.value){var i=e.value.split("@"),r=i[0],a=i[1],o={length:parseInt(r||"0")};if(a)o.offset=parseInt(a);else{var s=t[t.length-1];(null==s?void 0:s.byterange)&&(o.offset=(null!==(n=s.byterange.offset)&&void 0!==n?n:0)+s.byterange.length)}return o}}function R(e,t){var n,i;if(null===(n=e.attributes)||void 0===n?void 0:n.URI){var a={url:(0,o.forceReallocation)(e.attributes.URI)};if(null===(i=e.attributes)||void 0===i?void 0:i.BYTERANGE){var s=e.attributes.BYTERANGE.split("@"),u=s[0],l=s[1];a.byteRange={start:parseInt(l),end:parseInt(l)+parseInt(u)-1}}return t.keys.length>0&&(a.keys=r([],t.keys,!0),t.keyInUse=!0),a}}function N(e){var t;if(null===(t=e.attributes)||void 0===t?void 0:t.IV){e.attributes.IV=e.attributes.IV.toLowerCase();var n=2;"0x"===e.attributes.IV.substring(0,n)&&(e.attributes.IV=e.attributes.IV.substring(n)),e.attributes.IV=new Uint8Array(s.FormatHelper.hexToBytes(e.attributes.IV)).buffer}}function b(e){var t,n={width:0,height:0},i=null===(t=e.attributes)||void 0===t?void 0:t.RESOLUTION.split("x");return i[0]&&(n.width=parseInt(i[0])),i[1]&&(n.height=parseInt(i[1])),n}var M=/(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))/,A=/([^=]*)=(.*)/,P=/^[\'"](.*)[\'"]$/,D=/^[\'"](.*)[\'"]$/g,C=/^([^:="]*)([:=])?(.*)$/;function L(e){try{e=(0,o.forceReallocation)(e);var t=/^#([^:=]*)([:=])?(.*)$/.exec(e);if(!(null==t?void 0:t[1]))return;var n={name:t[1]},i="";if("="===t[2]&&t[3])i=t[3];else if(":"===t[2]&&t[3]){var r=C.exec(t[3]);r&&"="===r[2]&&r[3]?n.attributes=U(t[3]):i=t[3]}return i&&(n.value=i.replace(D,"$1")),n}catch(e){return}}function U(e){for(var t=e.split(M),n=t.length,i={};n--;)if(""!==t[n]){var r=A.exec(t[n]);if(r){var a=r.slice(1),o=a[0],s=a[1];i[o.trim()]=s.trim().replace(P,"$1")}}return i}},8367:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.ArrayIncludesPolyfill=void 0,t.ArrayIncludesPolyfill=function(){return{polyfill:function(){Object.defineProperty(Array.prototype,"includes",{value:function(e,t){if(null==this)throw new TypeError(\'"this" is null or not defined\');var n=Object(this),i=n.length>>>0;if(0===i)return!1;var r=0|t,a=Math.max(r>=0?r:i-Math.abs(r),0);function o(e,t){return e===t||"number"==typeof e&&"number"==typeof t&&isNaN(e)&&isNaN(t)}for(;a<i;){if(o(n[a],e))return!0;a++}return!1}})}}}()},8665:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.ServiceName=void 0,function(e){e.AdaptationService="AdaptationService",e.DrmDetectorService="DrmDetectorService",e.DrmService="DrmService",e.HeartbeatService="HeartbeatService",e.LiveLatencyService="LiveLatencyService",e.ManifestCachingService="ManifestCachingService",e.ManifestLoadingService="ManifestLoadingService",e.ManifestService="ManifestService",e.ManifestUpdateSchedulingService="ManifestUpdateSchedulingService",e.MetadataParsedService="MetadataParsedService",e.PlayerStateService="PlayerStateService",e.SegmentService="SegmentService",e.SourceStoreService="SourceStoreService",e.StallTimeTrackerService="StallTimeTrackerService",e.StartOffsetService="StartOffsetService",e.StreamTimeService="StreamTimeService",e.SubtitleService="SubtitleService",e.SynchronizedTimeService="SynchronizedTimeService",e.ThumbnailService="ThumbnailService",e.TimedMetadataService="TimedMetadataService"}(n||(t.ServiceName=n={}))},8764:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerError=void 0;var i=n(5550),r=function(){function e(e,t,n,r){this.code=e,this.name=i.ErrorCode[e],this.sourceIdentifier=r,this.message=null!=n?n:"".concat(this.name),this.data=t,"function"==typeof Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error(this.message).stack}return e}();t.PlayerError=r},8794:function(e,t,n){var i=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.HlsParserWebworkerMessageHandler=void 0,t.getContext=l;var r=n(1715),a=n(7697),o=n(9692),s=null;function u(){var e=new a.WebWorkerContext(d);return onmessage=e.onMessageCallback,e}function l(){return s||u()}var d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.parseHlsPlaylist=function(e){var t=e.payload;try{var n=(0,o.parsePlaylist)(t.data,t.options);this.postMessageFunction({action:"parsedPlaylist",data:n})}catch(e){this.postMessageFunction({action:"error",data:e.message})}},t}(r.MessageHandler);t.HlsParserWebworkerMessageHandler=d,"undefined"==typeof window&&(s=u())},8842:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.devToolsExtension=void 0;var i=n(7093);t.devToolsExtension=i.default.__REDUX_DEVTOOLS_EXTENSION__},8915:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.isSegmentTiming=a,t.findSegmentIndexForTime=o,t.createInitSegmentInfoBase=l,t.getSegmentInfoTimeRange=d,t.generateSegmentId=c;var i=n(5711),r=n(16);function a(e){return"startTime"in e&&(0,r.isNumber)(e.startTime)&&"duration"in e&&(0,r.isNumber)(e.duration)}function o(e,t,n){var i=!e||!e.length,r=t<e[0].startTime+e[0].duration;if(i||r)return 0;var a=e.length-1;if(t>=e[a].startTime)return a;var o=Math.max(t-e[0].startTime,0),l=Math.min(Math.floor(o/n),e.length-1);return s(e[0])&&s(e[l])?u(e,t,l):null}function s(e){return e&&(0,r.isNumber)(e.startTime)}function u(e,t,n){for(;e[n]&&e[n].startTime>t;)n--;for(;e[n]&&e[n].startTime+e[n].duration<=t;)n++;return n=Math.max(n,0),n=Math.min(n,e.length-1)}function l(e){var t=e._internalId;return{internalRepresentationId:t,representationId:t.representationId,periodId:t.periodId,mimeType:e._mimeType,isInitSegment:!0}}function d(e){if((null==e?void 0:e.duration)&&void 0!==(null==e?void 0:e.startTime))return{start:e.startTime,end:e.startTime+e.duration}}function c(e){var t,n,r=null!==(n=null===(t=e.discontinuitySequenceNumber)||void 0===t?void 0:t.toString())&&void 0!==n?n:e.periodId,a=e.byteRange?"-".concat(e.byteRange.start,":").concat(e.byteRange.end):"";return(0,i.calcHash)("".concat(r,":").concat(e.url).concat(a))}},9181:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.ManifestMimeType=void 0,function(e){e.Dash="application/dash+xml",e.Hls="application/x-mpegURL",e.Smooth="application/vnd.ms-sstr+xml"}(n||(t.ManifestMimeType=n={}))},9210:function(e,t,n){var i,r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},a=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.ReachableDomainsReducer=void 0;var o=n(1829),s=n(9463),u={domains:[]};function l(e,t){return r(r({},e),{domains:e.domains.includes(t)?e.domains:a(a([],e.domains,!0),[t],!1)})}t.ReachableDomainsReducer=(0,o.default)(u,((i={})[s.ReachableDomainsActionType.AddDomain]=function(e,t){return l(e,t.payload)},i))},9422:function(e,t){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.addStoreServiceDecoration=void 0;var i=function(e){return function(t){return function(){var i=function(){return e.buildReducer()},r=t(i()),a=function(){return r.replaceReducer(i())};return a(),n(n({},r),{addReducer:function(t,n){e.set(t,n),a()},getReducer:function(t){return e.get(t)},removeReducer:function(t){e.delete(t),a()},hasReducer:function(t){return e.has(t)}})}}};t.addStoreServiceDecoration=i},9463:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.ReachableDomainsActionType=void 0,function(e){e.AddDomain="adddomain"}(n||(t.ReachableDomainsActionType=n={}))},9692:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.parsePlaylist=d;var i,r=n(6461),a=n(2600),o=n(5877),s=n(7967),u=/\\r\\n|\\r(?=[^\\n]|$)/gm,l=/^#EXTM3U($|[ \\t\\n])/m;function d(e,t){void 0===t&&(t={});var n=-1===e.indexOf("#EXTINF")?i.Master:i.Media,d=e.replace(u,"\\n").trim().split(/\\n+/m),f=(0,r.removeTrailingWhitespaces)(d[0]);if(!l.test(f))throw new Error("Playlist parsing failed: missing #EXTM3U tag");if(n===i.Media)return(0,o.parseVariant)(d,t);for(var p,g=[],S=[],v=[],m=1,h=!0,E=!1,y=!0,I=0;I<d.length;I++){var T=(0,r.removeTrailingWhitespaces)(d[I]),_=d[I+1];if((0,r.isCustomTag)(T))c(g,T);else if(y)y=!1;else{var O=(0,a.parseTag)(T);switch(O.name){case"EXT-X-STREAM-INF":v.push((0,s.generateStreamInfTag)(O,(0,r.removeTrailingWhitespaces)(_))),y=!0;break;case"EXT-X-MEDIA":var R=(0,s.generateRendition)(O);S.push(R),g.push(R);break;case"EXT-X-VERSION":m=Number(O.value);break;case"EXT-X-START":p=(0,s.generateStartAttributes)(O),g.push(O);break;case"EXT-X-ALLOW-CACHE":h="YES"===O.value||void 0===O.value;break;case"EXT-X-INDEPENDENT-SEGMENTS":E=!0,g.push(O);break;case"EXT-X-I-FRAME-STREAM-INF":(0,s.generateIFrameStreamInfTag)(O),g.push(O);break;case"EXT-X-SESSION-DATA":default:g.push(O);break;case"EXT-X-SESSION-KEY":(0,s.generateSessionKeyTag)(O),g.push(O)}}}var N={version:m,allowCache:h,independentSegments:E,hasM3u:!0,media:S,playlists:v,tags:g=g.concat(v)};return p&&(N.start=p),N}function c(e,t){var n=(0,s.generateCustomTag)(t);n&&e.push(n)}!function(e){e.Master="master",e.Media="media"}(i||(i={}))},9814:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.MimeTypeHelper=t.MimeType=void 0,t.isMediaTypeContained=s;var i,r=n(9181),a=n(3696);!function(e){e.VideoMp4="video/mp4",e.VideoTs="video/mp2t",e.AudioMp4="audio/mp4",e.AudioTs="audio/mp2t"}(i||(t.MimeType=i={}));var o=function(){function e(){}return e.isSubtitle=function(e){return this.isApplication(e)||this.isText(e)},e.isText=function(e){return e.includes("text")},e.isTtmlXml=function(e){return e.includes("ttml+xml")},e.isApplication=function(e){return e.includes("application")},e.isAV=function(e){return this.isVideo(e)||this.isAudio(e)},e.isVideo=function(e){return e.includes("video")},e.isUnknown=function(e){return e.includes("unknown")},e.isAudio=function(e){return e.includes("audio")},e.isImage=function(e){return e.includes("image")},e.isDash=function(e){return e.includes(r.ManifestMimeType.Dash)},e.isHls=function(e){return e.includes(r.ManifestMimeType.Hls)||e.toLowerCase().includes("vnd.apple.mpegurl")},e.isMP4=function(e){return e.includes("mp4")},e.getMediaType=function(e){return this.isVideo(e)?a.MediaType.Video:this.isAudio(e)?a.MediaType.Audio:this.isSubtitle(e)?a.MediaType.Subtitles:this.isImage(e)?a.MediaType.Thumbnails:void 0},e.extractContentType=function(e){return e.split("/")[0]},e.extractContainerType=function(e){var t=e.split("/");return t.length>1?t[1]:null},e}();function s(e,t){return Object.keys(e).map((function(e){return o.getMediaType(e)})).includes(o.getMediaType(t))}t.MimeTypeHelper=o}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var a=t[i]={exports:{}};return e[i].call(a.exports,a,a.exports,n),a.exports}!function(){n.d=function(e,t){for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}();n(8794)}();',n.p+"47228a6cbe027482169e.worker.js")}},83740:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.uniqueRenditions=r;var i=n(86865);function r(e){var t=[];return e.forEach((function(e){var n=t.findIndex((function(t){return(0,i.compareValues)(e,t,["GROUP-ID","NAME","TYPE"])}));if(-1!==n){var r=t[n];"YES"===e.DEFAULT&&"YES"!==r.DEFAULT&&(t[n]=e)}else t.push(e)})),t}},90993:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.HlsActionType=void 0,function(e){e.SetMasterPlaylistString="@instance/hls/setmasterplayliststring",e.SetPlaylistType="@instance/hls/setplaylisttype",e.SetIsLowLatencyPlaylist="@instance/hls/setislowlatencyplaylist",e.SetEndlist="@instance/hls/setendlist",e.SetDefaultLanguages="@instance/hls/setsefaultlanguages",e.SetCustomTags="@instance/hls/setsustomtags",e.SetDiscoTimings="@instance/hls/setdiscotimings",e.SetPresentationTimeOffset="@instance/hls/setpresentationtimeoffset",e.SetTimestampRolloverPositions="@instance/hls/settimestamprolloverpositions",e.SetContentLocationId="@instance/hls/setcontentlocationid"}(n||(t.HlsActionType=n={}))},97662:function(e,t){"use strict";function n(e){return e.reduce((function(e,t,n,i){var r=t._discontinuitySequenceNumber,a=t._playbackTime;if(void 0===e.discoTimings[r]){e.discoTimings[r]={startTime:a,endTime:1/0};var o=e.discoTimings[r-1];o&&(o.endTime=a)}return n===i.length-1&&(e.discoTimings[r].endTime=a+t._duration),e.totalDuration+=t._duration,e}),{totalDuration:0,discoTimings:{}})}Object.defineProperty(t,"__esModule",{value:!0}),t.getTimingInfoFromSegmentList=n}},function(e){return function(t){return e(e.s=t)}(64732)}])}));
})();
