/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports["advertising-ima"]=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player["advertising-ima"]=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[399],{3562:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.ImaModuleErrorCode=void 0,function(e){e[e.FAILED_TO_REQUEST_IMA_SDK=103]="FAILED_TO_REQUEST_IMA_SDK",e[e.VAST_NO_ADS_AFTER_WRAPPER=303]="VAST_NO_ADS_AFTER_WRAPPER",e[e.FAILED_TO_REQUEST_ADS=1005]="FAILED_TO_REQUEST_ADS",e[e.VAST_ASSET_NOT_FOUND=1007]="VAST_ASSET_NOT_FOUND",e[e.VAST_EMPTY_RESPONSE=1009]="VAST_EMPTY_RESPONSE",e[e.UNKNOWN_AD_RESPONSE=1010]="UNKNOWN_AD_RESPONSE",e[e.UNSUPPORTED_LOCALE=1011]="UNSUPPORTED_LOCALE",e[e.ADS_REQUEST_NETWORK_ERROR=1012]="ADS_REQUEST_NETWORK_ERROR",e[e.INVALID_AD_TAG=1013]="INVALID_AD_TAG",e[e.STREAM_INITIALIZATION_FAILED=1020]="STREAM_INITIALIZATION_FAILED",e[e.ASSET_FALLBACK_FAILED=1021]="ASSET_FALLBACK_FAILED",e[e.INVALID_ARGUMENTS=1101]="INVALID_ARGUMENTS",e[e.AUTOPLAY_DISALLOWED=1205]="AUTOPLAY_DISALLOWED"}(i||(t.ImaModuleErrorCode=i={}))},9269:function(e,t){var i;function r(e,t){var r=null,a=e.getCuePoints(),n=a.map((function(e){return{state:i.SCHEDULED,position:e,ads:[]}})),o=function(e){var t=e.getAd();t&&h(t,i.PLAYING)},s=function(e){var t=e.getAd();t&&h(t,i.PLAYED)},d=function(e){var t=e.getAd();t&&h(t,i.SKIPPED)},l=function(e){var t=e.getAd();t&&h(t,i.LOADED)},u=function(e){var t=e.getAd();t&&(r=t)},c=function(e){var t,a,o=e.getAdData();if(o.adError&&r){var s=r.getAdPodInfo().getTimeOffset(),d=n.find((function(e){return e.position===s}));d&&[i.SCHEDULED,i.LOADED].includes(d.state)&&h(r,i.PLAYED)}(null===(a=null===(t=o.adError)||void 0===t?void 0:t.data)||void 0===a?void 0:a.errorCode)===google.ima.AdError.ErrorCode.VAST_EMPTY_RESPONSE&&n.forEach((function(e){return e.state=i.PLAYED}))},A=function(e){var t=e.getError();if(t&&r){var a=r.getAdPodInfo().getTimeOffset(),o=n.find((function(e){return e.position===a}));o&&[i.SCHEDULED,i.LOADED].includes(o.state)&&h(r,i.PLAYED)}(null==t?void 0:t.getErrorCode())===google.ima.AdError.ErrorCode.VAST_EMPTY_RESPONSE&&n.forEach((function(e){return e.state=i.PLAYED}))};function g(e){var t,r=e.getAd();if(r){var a=r.getAdPodInfo().getTimeOffset();void 0!==a&&r.isLinear()&&(null===(t=n.find((function(e){return e.position===a})))||void 0===t||t.ads.push({id:r.getAdId(),state:i.LOADED}))}}function h(e,t){var r=e.getAdPodInfo().getTimeOffset(),a=e.getAdId();if(void 0!==r){var o=n.find((function(e){return e.position===r}));if(!o)return;var s=o.ads.findIndex((function(e){return e.id===a}));-1!==s&&(o.ads[s].state=t,f(o.ads)?(o.state=i.PLAYED,p(r),v(r)):o.state=t)}}function p(e){n.filter((function(t){return t.state===i.SCHEDULED&&t.position<e&&t.position>0})).forEach((function(e){e.state=i.MISSED}))}function v(e){n.filter((function(t){return t.state===i.MISSED&&t.position>e})).forEach((function(e){e.state=i.SCHEDULED}))}function E(e){var r=2;return n.some((function(a){var n=a.position,o=[i.SCHEDULED,i.LOADED].includes(a.state);return!(a.state===i.LOADED&&0===a.ads.length&&n<=e+r)&&(-1===n?o&&e>=t-r:o&&n<=e+r)}))}function f(e){return e.every((function(e){return e.state===i.PLAYED||e.state===i.SKIPPED}))}function m(){r=null,n=[],a=[],e.removeEventListener(google.ima.AdEvent.Type.LOADED,g),e.removeEventListener(google.ima.AdEvent.Type.AD_BREAK_READY,l),e.removeEventListener(google.ima.AdEvent.Type.STARTED,o),e.removeEventListener(google.ima.AdEvent.Type.COMPLETE,s),e.removeEventListener(google.ima.AdEvent.Type.SKIPPED,d),e.removeEventListener(google.ima.AdEvent.Type.LOG,c),e.removeEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,A),e.removeEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,u)}return e.addEventListener(google.ima.AdEvent.Type.LOADED,g),e.addEventListener(google.ima.AdEvent.Type.AD_BREAK_READY,l),e.addEventListener(google.ima.AdEvent.Type.STARTED,o),e.addEventListener(google.ima.AdEvent.Type.COMPLETE,s),e.addEventListener(google.ima.AdEvent.Type.SKIPPED,d),e.addEventListener(google.ima.AdEvent.Type.LOG,c),e.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,A),e.addEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,u),{updateAdBreakState:h,reset:m,hasAdBreaksToPlay:E,getImaInternalAdBreaks:function(){return n}}}Object.defineProperty(t,"__esModule",{value:!0}),t.ImaInternalAdbreakState=void 0,t.ImaInternalAdBreaksTracker=r,function(e){e.SCHEDULED="SCHEDULED",e.LOADED="LOADED",e.PLAYING="PLAYING",e.PLAYED="PLAYED",e.SKIPPED="SKIPPED",e.MISSED="MISSED"}(i||(t.ImaInternalAdbreakState=i={}))},22416:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.ImaPassthroughMode=void 0,function(e){e.None="none",e.Vast="vast",e.VastAndVmap="vastandvmap"}(i||(t.ImaPassthroughMode=i={}))},24295:function(e,t){function i(e,t){var i=e.clientWidth,n=e.clientHeight,o=t.getCompanionAds(i,n,r());return o.length>0?o[0]:t.getCompanionAds(i,n,a())[0]}function r(){var e=new google.ima.CompanionAdSelectionSettings;return e.resourceType=google.ima.CompanionAdSelectionSettings.ResourceType.STATIC,e.creativeType=google.ima.CompanionAdSelectionSettings.CreativeType.IMAGE,e.sizeCriteria=google.ima.CompanionAdSelectionSettings.SizeCriteria.SELECT_NEAR_MATCH,e}function a(){var e=new google.ima.CompanionAdSelectionSettings;return e.resourceType=google.ima.CompanionAdSelectionSettings.ResourceType.STATIC,e.creativeType=google.ima.CompanionAdSelectionSettings.CreativeType.IMAGE,e.sizeCriteria=google.ima.CompanionAdSelectionSettings.SizeCriteria.IGNORE,e}Object.defineProperty(t,"__esModule",{value:!0}),t.selectCompanionAd=i},45641:function(e,t,i){var r=this&&this.__extends||function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},e(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function r(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(r.prototype=i.prototype,new r)}}(),a=this&&this.__assign||function(){return a=Object.assign||function(e){for(var t,i=1,r=arguments.length;i<r;i++)for(var a in t=arguments[i])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},a.apply(this,arguments)},n=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var r,a=0,n=t.length;a<n;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.ImaModule=void 0;var o=i(4006),s=i(62510),d=i(10981),l=i(44920),u=i(82315),c=i(331),A=i(70016),g=i(54838),h=i(59972),p=i(92284),v=i(42512),E=i(66598),f=i(84668),m=i(857),y=i(58431),T=i(3954),k=i(22416),P=i(75515),S=i(9269),I=i(3562),C=i(24295);function D(e){for(var t=[],i=1;i<arguments.length;i++)t[i-1]=arguments[i];e&&"function"==typeof e&&e.apply(void 0,t)}function L(){return[100,404,google.ima.AdError.ErrorCode.COMPANION_REQUIRED_ERROR,google.ima.AdError.ErrorCode.FAILED_TO_REQUEST_ADS,google.ima.AdError.ErrorCode.NONLINEAR_DIMENSIONS_ERROR,google.ima.AdError.ErrorCode.UNKNOWN_AD_RESPONSE,google.ima.AdError.ErrorCode.UNKNOWN_ERROR,google.ima.AdError.ErrorCode.VAST_ASSET_NOT_FOUND,google.ima.AdError.ErrorCode.VAST_EMPTY_RESPONSE,google.ima.AdError.ErrorCode.VAST_LINEAR_ASSET_MISMATCH,google.ima.AdError.ErrorCode.VAST_LOAD_TIMEOUT,google.ima.AdError.ErrorCode.VAST_MALFORMED_RESPONSE,google.ima.AdError.ErrorCode.VAST_NO_ADS_AFTER_WRAPPER,google.ima.AdError.ErrorCode.VAST_SCHEMA_VALIDATION_ERROR,google.ima.AdError.ErrorCode.VAST_TOO_MANY_REDIRECTS,google.ima.AdError.ErrorCode.VAST_TRAFFICKING_ERROR,google.ima.AdError.ErrorCode.VAST_UNSUPPORTED_VERSION,google.ima.AdError.ErrorCode.VAST_WRAPPER_ERROR,1012]}var R="advertising-ima",M=250,_=function(e){function t(t,i){var r=e.call(this,t,i)||this;return r.isAdLeftInCurrentAdPod=!0,r.onAdsManagerLoaded=function(e){r.adsRenderingSettings.restoreCustomPlaybackStateOnAdBreakComplete=!1,r.adsRenderingSettings.loadVideoTimeout=r.adVideoLoadTimeout,r.adsManager=e.getAdsManager(r.player.getVideoElement(),r.adsRenderingSettings);var t=r.player.getVideoElement().duration;r.imaAdBreaksTracker=(0,S.ImaInternalAdBreaksTracker)(r.adsManager,t),r.adsManager.addEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,r.onContentPauseRequested),r.adsManager.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,r.onContentResumeRequested),r.adsManager.addEventListener(google.ima.AdEvent.Type.STARTED,r.onStarted),r.adsManager.addEventListener(google.ima.AdEvent.Type.LOADED,r.onLoaded),r.adsManager.addEventListener(google.ima.AdEvent.Type.SKIPPED,r.onSkipped),r.adsManager.addEventListener(google.ima.AdEvent.Type.COMPLETE,r.onComplete),r.adsManager.addEventListener(google.ima.AdEvent.Type.PAUSED,r.onPaused),r.adsManager.addEventListener(google.ima.AdEvent.Type.RESUMED,r.onResumed),r.adsManager.addEventListener(google.ima.AdEvent.Type.CLICK,r.onClicked),r.adsManager.addEventListener(google.ima.AdEvent.Type.DURATION_CHANGE,r.onDurationChange),r.adsManager.addEventListener(google.ima.AdEvent.Type.FIRST_QUARTILE,r.onFirstQuartile),r.adsManager.addEventListener(google.ima.AdEvent.Type.MIDPOINT,r.onMidpoint),r.adsManager.addEventListener(google.ima.AdEvent.Type.THIRD_QUARTILE,r.onThirdQuartile),r.adsManager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,r.onAdError),r.adsManager.addEventListener(google.ima.AdEvent.Type.INTERACTION,r.onAdInteraction),r.adsManager.addEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED,r.onAllAdsCompleted),D(r.config.onAdsManagerAvailable,r.adsManager),r.adsManager.init(r.playerContainer.clientWidth,r.playerContainer.clientHeight,google.ima.ViewMode.NORMAL),r.adsManager.setVolume(r.player.getVolume()/100),r.getActiveAdBreak()||r.handleMissingAdBreak()},r.onAdError=function(e){var t,i=r.getActiveAdBreak(),a=e.getError(),n=a.getInnerError(),o=a.getErrorCode(),s=null!=n&&Boolean(n.getErrorCode)?n:null;if(i){if(null===(t=r.adPlaybackApiPromise)||void 0===t||t.then((function(){return(0,h.pingVmapTrackingUrls)(i.trackingEvents,E.VmapTrackingEventType.Error)})),B(o)&&!r.adPlaybackApiPromise&&((0,h.pingVmapTrackingUrls)(i.trackingEvents,E.VmapTrackingEventType.BreakStart),(0,h.pingVmapTrackingUrls)(i.trackingEvents,E.VmapTrackingEventType.Error),(0,h.pingVmapTrackingUrls)(i.trackingEvents,E.VmapTrackingEventType.BreakEnd)),s&&s.getErrorCode()===google.ima.AdError.ErrorCode.AUTOPLAY_DISALLOWED)return r.hasContentStarted||(r.schedulableAdBreaks=[i].concat(r.queuedAdBreaks,r.schedulableAdBreaks),r.queuedAdBreaks=[]),void r.emitAdError(s);r.waterfallingErrorCodes.includes(a.getErrorCode())?r.tryNextFallbackAdTag(i).then((function(e){if(e.manifest)r.passAdBreakToIma(i,e.manifest,!0);else{if(i.passthroughMode===k.ImaPassthroughMode.None)return Promise.reject();r.passAdBreakToIma(i,null,!0)}})).catch((function(){return r.emitAdError(a)})):r.emitAdError(a)}else r.emitAdError(a)},r.onContentPauseRequested=function(e){r.contentPauseRequested=!0,(r.adPlaybackApiPromise?r.resetActiveAdPlaybackApi(!1):Promise.resolve()).then((function(){var t=a({},r.getActiveAdBreak());if(r.removeInternalProps(t),!t){var i=r.player.getVideoElement().currentTime;t={tag:null,id:g.Util.generateUniqueId(),position:String(i),scheduleTime:i}}r.adPlaybackApiPromise=r.player.requestAdPlayback(r.enrichAdBreakWithAdInfo(e,t)),r.adPlaybackReporterPromise=r.adPlaybackApiPromise.then((function(e){return e.requestReporter()})),r.adPlaybackApiPromise.then((function(){return(0,h.pingVmapTrackingUrls)(t.trackingEvents,E.VmapTrackingEventType.BreakStart)}));var n=e.getAd();r.adjustAdContainer(n.isLinear(),n.getWidth(),n.getHeight())}))},r.onContentResumeRequested=function(){var e,t,i=null===(e=r.imaAdBreaksTracker)||void 0===e?void 0:e.hasAdBreaksToPlay(r.currentScheduleTime),a=r.getActiveAdBreak();null===(t=r.adPlaybackApiPromise)||void 0===t||t.then((function(){return(0,h.pingVmapTrackingUrls)(null==a?void 0:a.trackingEvents,E.VmapTrackingEventType.BreakEnd)})),i||(r.activeAd&&r.onComplete(),r.contentPauseRequested=!1,r.isFallbackAd&&(r.isFallbackAd=!1,a&&delete a.currentFallbackIndex),r.activeAdBreak=null,r.isAdPlaying=!1,r.adContainer.style.display="none",r.adPlaybackApiPromise?r.adPlaybackApiPromise.then((function(e){r.queuedAdBreaks.length>0?r.playAdBreak(r.queuedAdBreaks.shift()):e.done().then((function(){r.adPlaybackApiPromise=null,r.maybeResolveAdBreaks()}))})):r.maybeResolveAdBreaks())},r.onLoaded=function(e){r.adPlaybackApiPromise&&r.isPreviousAdPlaybackApiActive&&r.resetActiveAdPlaybackApi(!e.getAd().isLinear());var t=r.getActiveAdBreak();t&&r.onAdBreakLoaded(r.enrichAdBreakWithAdInfo(e,t))},r.onStarted=function(e){(r.activeAdBreak||r.activeAd)&&(r.adjustAdContainer(e.getAd().isLinear(),e.getAd().getWidth(),e.getAd().getHeight()),r.activeAd||r.initializeActiveAd(e),r.activeAdBreak&&(r.activeAdBreak.ads=r.activeAdBreak.ads||[],r.activeAdBreak.ads.includes(r.activeAd)||r.activeAdBreak.ads.push(r.activeAd)),r.config&&r.config.companionAdContainers&&r.displayCompanionAds(r.config.companionAdContainers(),e),r.activeAd.isLinear?r.onLinearAdStarted(e):r.onNonLinearAdStarted(e))},r.triggerTimeUpdate=function(){if(r.adPlaybackReporterPromise){var e=r.currentTime();e!==r.lastKnownAdPlaybackTime&&e>0&&r.adPlaybackReporterPromise.then((function(t){return t.timeupdate(e)})),r.lastKnownAdPlaybackTime=e}},r.onSkipped=function(){r.resetActiveAd(),r.adPlaybackReporterPromise&&r.adPlaybackReporterPromise.then((function(e){return e.skipped()}))},r.onComplete=function(){var e;r.triggerRemainingQuartiles(),r.resetActiveAd(),r.adPlaybackReporterPromise&&(r.adPlaybackReporterPromise.then((function(e){return e.done()})),r.adPlaybackReporterPromise=null===(e=r.adPlaybackApiPromise)||void 0===e?void 0:e.then((function(e){return e.requestReporter()})))},r.onAllAdsCompleted=function(){r.isAdLeftInCurrentAdPod||(r.adsManager.destroy(),r.adsLoader.contentComplete())},r.onPaused=function(){r.isAdPlaying=!1,r.adPlaybackReporterPromise.then((function(e){return e.paused(r.currentTime())}))},r.onResumed=function(){r.isAdPlaying=!0,r.adPlaybackReporterPromise.then((function(e){return e.resumed(r.currentTime())}))},r.onClicked=function(){var e=r.activeAd?r.activeAd.clickThroughUrl:null;r.player.adClicked(e)},r.onAdInteraction=function(e){var t;r.player.adInteraction(s.AdInteractionType.Vpaid,null===(t=e.getAdData())||void 0===t?void 0:t.id)},r.onDurationChange=function(e){r.currentDuration=e.getAd().getDuration()},r.onFirstQuartile=function(){r.player.onQuartile(s.AdQuartile.FIRST_QUARTILE),r.triggeredQuartiles.push(s.AdQuartile.FIRST_QUARTILE)},r.onMidpoint=function(){r.player.onQuartile(s.AdQuartile.MIDPOINT),r.triggeredQuartiles.push(s.AdQuartile.MIDPOINT)},r.onThirdQuartile=function(){r.player.onQuartile(s.AdQuartile.THIRD_QUARTILE),r.triggeredQuartiles.push(s.AdQuartile.THIRD_QUARTILE)},r.schedulableAdBreaks=[],r.unschedulableAdBreaks=[],r.queuedAdBreaks=[],r.persistentAdBreaks=[],r.currentScheduleTime=0,r.activeAdBreak=null,r.activeAd=null,r.imaAdBreaksTracker=null,r.adVideoLoadTimeout=i.videoLoadTimeout,r.waterfallingErrorCodes=[],r.contentPauseRequested=!1,r.triggeredQuartiles=[],r.isUsingCustomAdContainer=!1,r.isImaSdkAvailable=!1,r.scheduleAdBreaks=Promise.resolve(null),r.isAdPlaying=!1,r.isFallbackAd=!1,r.isPreviousAdPlaybackApiActive=!1,r.config.placeholders=a(a({},p.adTagPlaceholderDefaults),r.config.placeholders),r.initializeImaSdkDeferred=new d.Deferred,r.loadImaSdkPromise=u.FileLoader.getInstance().loadScript(i.sdkUrl),r.loadImaSdkPromise.then((function(){r.createDomStructure(),r.initialize(),r.isImaSdkAvailable=!0})).catch((function(){r.player.onError(I.ImaModuleErrorCode.FAILED_TO_REQUEST_IMA_SDK,"could not load the IMA SDK"),r.isImaSdkAvailable=!1,r.initializeImaSdkDeferred.resolve()})),r}return r(t,e),t.prototype.createDomStructure=function(){"function"==typeof this.config.adContainer&&(this.adContainer=this.config.adContainer(),this.adContainer instanceof HTMLElement?this.isUsingCustomAdContainer=!0:this.player.onError(y.VastErrorCode.UNDEFINED_ERROR,"AdvertisingConfig.adContainer was defined but did not provide a valid HTMLElement.")),this.isUsingCustomAdContainer||(this.adContainer=l.DOMHelper.createTag("div",{class:o.Environment.idPrefix+"ima-container"},{height:"100%",width:"100%",display:"none",border:"none",overflow:"hidden",position:"absolute",top:"0"}),this.playerContainer=this.player.getVideoElement().parentElement,this.playerContainer.appendChild(this.adContainer)),D(this.config.onAdContainerAvailable,this.adContainer)},t.prototype.initialize=function(){google.ima.settings.setPlayerType("bitmovin-player"),google.ima.settings.setNumRedirects(25),google.ima.settings.setCookiesEnabled(this.config.withCredentials),D(this.config.beforeInitialization,google.ima.settings),this.adsRenderingSettings=new google.ima.AdsRenderingSettings,this.config.allowedUiElements&&(this.adsRenderingSettings.uiElements=this.config.allowedUiElements),this.adDisplayContainer=new google.ima.AdDisplayContainer(this.adContainer,this.player.getVideoElement()),this.adDisplayContainer.initialize(),this.adsLoader=new google.ima.AdsLoader(this.adDisplayContainer),this.adsLoader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,this.onAdError),this.adsLoader.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,this.onAdsManagerLoaded),this.waterfallingErrorCodes=L(),this.initializeImaSdkDeferred.resolve()},t.prototype.handleMissingAdBreak=function(){this.adPlaybackApiPromise&&this.resetActiveAdPlaybackApi(),this.passthroughDownloadTime=null},t.prototype.recoverFromAdError=function(){this.maybeResolveAdBreaks(),this.contentPauseRequested||this.resetActiveAdPlaybackApi().catch((function(){}))},t.prototype.emitAdError=function(e){this.player.onError(e.getErrorCode(),e.getMessage(),this.getActiveAdBreak()),this.resetActiveAd(),this.activeAdBreak=null,this.queuedAdBreaks.length>0?this.playAdBreak(this.queuedAdBreaks.shift()):this.recoverFromAdError()},t.prototype.maybeResolveAdBreaks=function(){this.finishAllAdBreaksForPosition&&(this.finishAllAdBreaksForPosition.resolve(),this.finishAllAdBreaksForPosition=null)},t.prototype.onAdBreakLoaded=function(e){this.adPlaybackApiPromise&&(this.isPreviousAdPlaybackApiActive=!0),e.passthroughMode!==k.ImaPassthroughMode.None&&this.fireMetadataLoadedEventForPassthroughMode(e),this.passthroughDownloadTime=null,e.hasOwnProperty("ads")||(e.ads=[]),this.maybePlayLoadedAdBreak(e)},t.prototype.fireMetadataLoadedEventForPassthroughMode=function(e){var t=(0,A.isNumber)(e.currentFallbackIndex)&&e.currentFallbackIndex>=0?e.fallbackTags[e.currentFallbackIndex]:e.tag,i=this.passthroughDownloadTime&&t.type!==f.AdTagType.VMAP?{downloadTime:(0,c.toSeconds)(Date.now()-this.passthroughDownloadTime)}:null;this.player.metadataLoaded(e,i)},t.prototype.maybePlayLoadedAdBreak=function(e){this.config.strategy.shouldPlayAdBreak(e)?this.adsManager.start():(this.activeAdBreak=null,this.queuedAdBreaks.length>0?this.playAdBreak(this.queuedAdBreaks.shift()):(this.isPreviousAdPlaybackApiActive&&this.resetActiveAdPlaybackApi(),this.maybeResolveAdBreaks()))},t.prototype.enrichAdBreakWithAdInfo=function(e,t){var i,r=a({},t);r.ads=t.ads?n([],t.ads,!0):[];var o=this.activeAd;return o||O(o={id:null===(i=e.getAd())||void 0===i?void 0:i.getAdId()},e),-1===r.ads.findIndex((function(t){var i;return t.id===(null===(i=e.getAd())||void 0===i?void 0:i.getAdId())}))&&r.ads.push(o),r},t.prototype.initializeActiveAd=function(e){var t=this.getActiveAdBreak();if(t&&t.ads){var i=e.getAd().getAdId(),r=t.ads.find((function(e){return e.id===i}));r&&(this.activeAd=r)}this.activeAd=this.activeAd||{id:e.getAd().getAdId()},O(this.activeAd,e)},t.prototype.onLinearAdStarted=function(e){var t=this,i=e.getAd().getSkipTimeOffset();this.currentDuration=e.getAd().getDuration(),-1===this.currentDuration&&(this.currentDuration=null),this.activeAd.width=e.getAd().getVastMediaWidth(),this.activeAd.height=e.getAd().getVastMediaHeight(),this.activeAd.duration=this.currentDuration,this.activeAd.skippable=i!==m.NON_SKIPPABLE_IDENTIFIER,this.activeAd.skippable&&(this.activeAd.skippableAfter=i),this.isAdPlaying=!0,this.adPlaybackReporterPromise.then((function(e){e.started(t.activeAd),t.triggerTimeUpdateInterval=window.setInterval(t.triggerTimeUpdate,M)}))},t.prototype.onNonLinearAdStarted=function(e){this.queuedAdBreaks.length>0?this.playAdBreak(this.queuedAdBreaks.shift()):(this.activeAd.width=e.getAd().getWidth(),this.activeAd.height=e.getAd().getHeight(),this.player.overlayAdStarted(this.activeAd),this.finishAllAdBreaksForPosition&&(this.finishAllAdBreaksForPosition.resolve(),this.finishAllAdBreaksForPosition=null)),this.activeAdBreak=null},t.prototype.adjustAdContainer=function(e,t,i){this.adContainer.style.display="block",this.isUsingCustomAdContainer||(e?(this.adContainer.style.width="100%",this.adContainer.style.height="100%",this.adContainer.style.top="0",this.adContainer.style.bottom="",this.adContainer.style.left="",this.adContainer.style.right="",this.adContainer.style.marginLeft="",this.adContainer.style.marginRight=""):(this.adContainer.style.width=t+"px",this.adContainer.style.height=i+"px",this.adContainer.style.top="",this.adContainer.style.bottom=m.DEFAULT_OVERLAY_AD_BOTTOM_POSITION,this.adContainer.style.left="0",this.adContainer.style.right="0",this.adContainer.style.marginLeft="auto",this.adContainer.style.marginRight="auto")),this.adjustImaIframe(e,t,i)},t.prototype.adjustImaIframe=function(e,t,i){var r=this.adContainer.firstElementChild;r&&(e?(r.style.width="100%",r.style.height="100%"):(r.style.width=t+"px",r.style.height=i+"px"));var a=r.getElementsByTagName("iframe")[0];a&&(e?(a.style.width="100%",a.style.height="100%"):(a.style.width=t+"px",a.style.height=i+"px"))},t.prototype.displayCompanionAds=function(e,t){var i=this,r=t.getAd();r&&e.forEach((function(e){var t=(0,C.selectCompanionAd)(e,r);"innerHTML"in e&&void 0!==t&&(i.activeAd.hasOwnProperty("companionAds")||(i.activeAd.companionAds=[]),i.activeAd.companionAds.push({width:t.getWidth(),height:t.getHeight()}),e.innerHTML=t.getContent())}))},t.prototype.resetActiveAdPlaybackApi=function(e){if(void 0===e&&(e=!0),!this.adPlaybackApiPromise)return Promise.resolve();var t=this.adPlaybackApiPromise.then((function(t){return t.done(e)}));return this.adPlaybackApiPromise=null,this.isPreviousAdPlaybackApiActive=null,t},t.prototype.resetActiveAd=function(){this.activeAd&&this.activeAd.companionAds&&this.config.companionAdContainers().forEach((function(e){return e.innerHTML=""})),clearInterval(this.triggerTimeUpdateInterval),this.triggerTimeUpdateInterval=null,this.activeAd=null,this.isAdPlaying=!1,this.lastKnownAdPlaybackTime=null,this.triggeredQuartiles=[]},t.prototype.triggerRemainingQuartiles=function(){var e=this;[s.AdQuartile.FIRST_QUARTILE,s.AdQuartile.MIDPOINT,s.AdQuartile.THIRD_QUARTILE].filter((function(t){return!e.triggeredQuartiles.includes(t)})).forEach((function(t){e.player.onQuartile(t)}))},t.prototype.onTimeChanged=function(e){var t=this;if(!this.isLinearAdActive()){this.hasContentStarted=!0,this.currentScheduleTime=e;var i=this.schedulableAdBreaks.filter((function(t){return t.preloadOffset&&!t.vastResponse&&t.scheduleTime-t.preloadOffset<=e})),r=this.schedulableAdBreaks.filter((function(t){return t.scheduleTime<=e&&!t.isScheduled}));i.forEach((function(e){return t.loadAdBreak(e)})),r.forEach((function(e){e.isScheduled=!0,t.loadAdBreak(e).then((function(){t.schedulableAdBreaks.includes(e)&&t.playAdBreak(e)}))}))}},t.prototype.resetPlaybackStatus=function(e){this.schedulableAdBreaks.forEach((function(t){return t.scheduleTime>=e&&(t.isScheduled=!1)}))},t.prototype.onSeek=function(e,t){this.resetPlaybackStatus(t);var i=this.schedulableAdBreaks.filter((function(e){return e.scheduleTime<=t&&!e.isScheduled}));if(this.schedulableAdBreaks=this.schedulableAdBreaks.filter((function(e){return!i.includes(e)})),i.length>0){var r=this.config.strategy.shouldPlaySkippedAdBreaks(n([],i,!0),e,t);(i=i.filter((function(e){return!e.discardAfterPlayback||r.includes(e)}))).forEach((function(e){return e.isScheduled=!0})),r.forEach((function(e){var t=i.findIndex((function(t){return t===e}));t>-1&&(i[t].isScheduled=!1)})),this.schedulableAdBreaks=i.concat(this.schedulableAdBreaks)}},t.prototype.onResize=function(e,t,i){if(this.adsManager&&(!this.activeAd||this.activeAd.isLinear)){var r=i?google.ima.ViewMode.FULLSCREEN:google.ima.ViewMode.NORMAL;this.adsManager.resize(e,t,r)}},t.prototype.onVolumeChanged=function(e){this.adsManager&&this.adsManager.setVolume(e/100)},t.prototype.beforeContent=function(){var e=this;return this.storeAdBreaks(this.unschedulableAdBreaks.splice(0,this.unschedulableAdBreaks.length)),this.loadImaSdkPromise.then((function(){return e.isImaSdkAvailable?(e.deInitialize(),e.initialize(),e.scheduleAdBreaks.catch((function(){})).then((function(){return e.playAllAdBreaksForOffset("pre")}))):Promise.resolve()}))},t.prototype.afterContent=function(){var e=this;return this.isImaSdkAvailable?(this.activeAd&&!this.activeAd.isLinear&&(this.resetActiveAd(),this.activeAdBreak=null,this.adsManager.stop()),this.adsLoader.contentComplete(),this.playAllAdBreaksForOffset("post").then((function(){return e.resetPlaybackStatus(0)}))):Promise.resolve()},t.prototype.isValidPassthroughMode=function(e){return Object.keys(k.ImaPassthroughMode).some((function(t){return e===k.ImaPassthroughMode[t]}))},t.prototype.preProcessAdConfig=function(e){e.fallbackTags&&e.fallbackTags.length>0&&(e.fallbackTags=e.fallbackTags.filter((function(e){return!!e.url}))),this.isValidPassthroughMode(e.passthroughMode)||(e.passthroughMode=k.ImaPassthroughMode.Vast);var t=e;return t.position?t.position=String(t.position).toLowerCase():t.position=m.DEFAULT_AD_BREAK_POSITION,"number"!=typeof t.preloadOffset&&(t.preloadOffset=m.DEFAULT_PRELOAD_OFFSET),t},t.prototype.validateAdTag=function(e){return e.tag?e.tag.url?Object.keys(f.AdTagType).some((function(t){return e.tag.type===f.AdTagType[t]}))?void 0:"The provided ad tag type is invalid":"No ad tag url was provided":"No ad tag was provided"},t.prototype.getAdBreaksFromAdBreakConfigs=function(e,t){return e.map((function(e){var i=a(a({},e.publicAdBreakConfig),{isScheduled:!1,parentAdTag:t.tag});return e.trackingEvents&&(i.trackingEvents=e.trackingEvents),void 0===i.id&&(i.id=g.Util.generateUniqueId()),i}))},t.prototype.removeInternalProps=function(e){return delete e.isScheduled,delete e.parentAdTag,e},t.prototype.schedule=function(e){var t=this;return this.scheduleAdBreaks=this.initializeImaSdkDeferred.promise.then((function(){if(!t.isImaSdkAvailable)return Promise.reject("The IMA SDK was not loaded and initialized properly");var i=t.validateAdTag(e);if(i)return Promise.reject(i);var r=t.preProcessAdConfig(e);return t.extractSchedulableAdBreakConfigs(r).then((function(i){if(0===i.length&&r.fallbackTags&&r.fallbackTags.length>0)return r.tag=r.fallbackTags.shift(),t.schedule(r);var o=t.getAdBreaksFromAdBreakConfigs(i,e);o.forEach((function(t){return t.discardAfterPlayback=!1!==e.discardAfterPlayback}));var s=t.storeAdBreaks(o),d=n([],s.map((function(e){var i=a({},e);return t.removeInternalProps(i),i})),!0);return Promise.resolve(d)}))}))},t.prototype.storeAdBreaks=function(e){var t=this,i=[];e.forEach((function(e){var r=t.player.parsePosition(e.position);isNaN(r)?t.unschedulableAdBreaks.push(e):(e.scheduleTime=r,i.push(e))}));var r=i.filter((function(e){return e.persistent}));return this.schedulableAdBreaks=this.schedulableAdBreaks.concat(i),this.persistentAdBreaks=this.persistentAdBreaks.concat(r),this.schedulableAdBreaks.sort(b),this.persistentAdBreaks.sort(b),i},t.prototype.extractSchedulableAdBreakConfigs=function(e){var t=this;return new Promise((function(i){if(e.tag.type===f.AdTagType.VMAP&&e.passthroughMode!==k.ImaPassthroughMode.VastAndVmap){var r=e.tag.url;e.tag.url=t.applyAdTagPlaceholders(e.tag.url),T.VmapExtractor.extractAdBreakConfigs(e).then((function(a){var n=a.adBreakConfigs.filter((function(e){var i=t.player.parsePosition(e.publicAdBreakConfig.position);return isNaN(i)||i>=t.currentScheduleTime}));e.tag.url=r,t.player.metadataLoaded(e,a.downloadTiming),i(n)})).catch((function(a){e.tag.url=r,a?t.player.onError(a.code,null,e):t.player.onError(y.VastErrorCode.UNDEFINED_ERROR,null,e),i([])}))}else i([{publicAdBreakConfig:e}])}))},t.prototype.playAllAdBreaksForOffset=function(e){var t=this;return this.initializeImaSdkDeferred.promise.then((function(){if(!t.isImaSdkAvailable)return Promise.resolve();var i;i="post"===e?t.schedulableAdBreaks.filter((function(t){return t.position.includes(e)||t.scheduleTime===1/0})):t.schedulableAdBreaks.filter((function(t){return t.position.includes(e)||0===t.scheduleTime}));var r=Promise.resolve();return i.length>0&&(t.finishAllAdBreaksForPosition=new d.Deferred,r=t.finishAllAdBreaksForPosition.promise,i.forEach((function(e){e.isScheduled=!0,t.loadAdBreak(e).then((function(){return t.playAdBreak(e)}))}))),r}))},t.prototype.loadAdBreak=function(e,t){var i=this,r=e.tag;if(t&&(e.vastResponse=null,r=t),e.vastResponse&&!t)return e.vastResponse;var a=this.applyAdTagPlaceholders(r.url);return e.passthroughMode!==k.ImaPassthroughMode.None?e.vastResponse=Promise.resolve({}):e.vastResponse=h.AdManifestHelper.downloadVastManifest(a).then((function(t){var r=t.manifest,a={manifest:h.AdManifestHelper.xmlToString(r)};return h.AdManifestHelper.parseAdVerifications(r).then((function(r){return r&&r.length>0&&(e.ads=[],r.forEach((function(t){t.verifications&&t.verifications.length>0&&e.ads.push({id:t.id,verifications:t.verifications})}))),i.player.metadataLoaded(e,t.downloadTiming),Promise.resolve(a)}))})).catch((function(t){return i.tryNextFallbackAdTag(e).catch((function(){return delete e.currentFallbackIndex,t?i.player.onError(t.code,null,e):i.player.onError(y.VastErrorCode.UNDEFINED_ERROR,null,e),Promise.resolve({})}))})),e.vastResponse},t.prototype.tryNextFallbackAdTag=function(e){return"number"!=typeof e.currentFallbackIndex?e.currentFallbackIndex=0:e.currentFallbackIndex++,e.fallbackTags&&e.fallbackTags[e.currentFallbackIndex]?this.loadAdBreak(e,e.fallbackTags[e.currentFallbackIndex]):Promise.reject()},t.prototype.playAdBreak=function(e){var t=this;this.initializeImaSdkDeferred.promise.then((function(){var i;if(t.isImaSdkAvailable)if(t.schedulableAdBreaks.includes(e)&&e.discardAfterPlayback&&t.schedulableAdBreaks.splice(t.schedulableAdBreaks.indexOf(e),1),t.activeAdBreak)t.queuedAdBreaks.push(e);else{t.activeAd&&!t.activeAd.isLinear&&t.adsManager&&(t.onAllAdsCompleted(),t.resetActiveAd()),t.activeAdBreak=e;var r=function(e){var i;return e.parentAdTag&&(null===(i=t.activeAdBreak)||void 0===i?void 0:i.parentAdTag)&&e.parentAdTag===t.activeAdBreak.parentAdTag};t.isAdLeftInCurrentAdPod=t.schedulableAdBreaks.some(r)||t.queuedAdBreaks.some(r),null===(i=e.vastResponse)||void 0===i||i.then((function(i){return t.passAdBreakToIma(e,i.manifest)}))}}))},t.prototype.getUrlFromAdBreak=function(e){var t,i,r;return null!==(r=null===(i=null===(t=null==e?void 0:e.fallbackTags)||void 0===t?void 0:t[e.currentFallbackIndex])||void 0===i?void 0:i.url)&&void 0!==r?r:e.tag.url},t.prototype.passAdBreakToIma=function(e,t,i){var r=t?h.AdManifestHelper.toDataUri(t):void 0,a=null!=r?r:this.getUrlFromAdBreak(e);a&&this.requestAds(a,i)},t.prototype.requestAds=function(e,t){var i,r=new google.ima.AdsRequest,a=e,n="data:text/xml,";0===a.indexOf(n)?r.adsResponse=decodeURIComponent(a.substring(n.length)):r.adTagUrl=this.applyAdTagPlaceholders(e),this.playerContainer&&(r.linearAdSlotWidth=this.playerContainer.clientWidth,r.linearAdSlotHeight=this.playerContainer.clientHeight,r.nonLinearAdSlotWidth=this.playerContainer.clientWidth),(null===(i=this.config)||void 0===i?void 0:i.vastLoadTimeout)&&(r.vastLoadTimeout=this.config.vastLoadTimeout),this.attachOmidAccessModes(r),r.nonLinearAdSlotHeight=150,this.isFallbackAd=t,this.passthroughDownloadTime=Date.now(),this.adsLoader.requestAds(r)},t.prototype.attachOmidAccessModes=function(e){var t,i,r=null===(i=null===(t=this.config.trackers)||void 0===t?void 0:t.omSdk)||void 0===i?void 0:i.onAccessMode;if(void 0!==r&&"function"==typeof r){var a=r(e);if(a){var n={},o=function(e,t){t&&t.forEach((function(t){if("string"==typeof t){var i=google.ima.OmidVerificationVendor[t];void 0!==i&&(n[i]=e)}}))};o(google.ima.OmidAccessMode.FULL,a.full),o(google.ima.OmidAccessMode.DOMAIN,a.domain),o(google.ima.OmidAccessMode.LIMITED,a.limited),Object.keys(n).length>0&&(e.omidAccessModeRules=n)}}},t.prototype.applyAdTagPlaceholders=function(e){var t=this,i={playbackTime:String(this.player.getVideoElement().currentTime),height:String(this.playerContainer.clientHeight),width:String(this.playerContainer.clientWidth),domain:encodeURIComponent(document.location.hostname),page:encodeURIComponent(document.location.href),referrer:encodeURIComponent(document.referrer),random:Math.floor(1e7+9e7*Math.random()).toString(),timestamp:String(Date.now())};return Object.keys(this.config.placeholders).forEach((function(r){t.config.placeholders[r].forEach((function(t){e=e.replace(t,i[r])}))})),e},t.prototype.list=function(){var e=this,t=n(n([],this.queuedAdBreaks.map((function(e){return a({},e)})),!0),this.schedulableAdBreaks.map((function(e){return a({},e)})),!0);return t.forEach((function(t){return e.removeInternalProps(t)})),t},t.prototype.getActiveAdBreak=function(){return this.activeAdBreak},t.prototype.getActiveAd=function(){return this.activeAd},t.prototype.isLinearAdActive=function(e){return!!this.activeAd&&(e?Boolean(this.activeAd.isLinear)&&this.isAdPlaying:Boolean(this.activeAd.isLinear))},t.prototype.discardAdBreak=function(e){e&&(this.activeAdBreak&&this.activeAdBreak.id===e&&(this.onSkipped(),this.activeAdBreak=null,this.adsManager.stop()),this.schedulableAdBreaks=this.schedulableAdBreaks.filter((function(t){return t.id!==e})),this.queuedAdBreaks=this.queuedAdBreaks.filter((function(t){return t.id!==e})))},t.prototype.currentTime=function(){return!isFinite(this.currentDuration)||!this.adsManager||this.adsManager.getRemainingTime()<0?NaN:this.currentDuration-this.adsManager.getRemainingTime()},t.prototype.duration=function(){return isFinite(this.currentDuration)?this.currentDuration:0},t.prototype.pause=function(){this.adsManager&&this.adsManager.pause()},t.prototype.resume=function(){this.adsManager&&this.adsManager.resume()},t.prototype.skip=function(){return this.adsManager&&(this.adsManager.skip(),this.activeAd&&!this.activeAd.isLinear&&(this.adsManager.stop(),this.adContainer.style.display="none",this.resetActiveAd())),Promise.resolve()},t.prototype.getModuleInfo=function(){return{name:R,version:o.Environment.VERSION_NUMBER}},t.prototype.deInitialize=function(){var e=this.schedulableAdBreaks,t=this.unschedulableAdBreaks;this.reset(!1),this.schedulableAdBreaks=e,this.unschedulableAdBreaks=t,this.cleanUpImaSdk(),this.initializeImaSdkDeferred=new d.Deferred},t.prototype.reset=function(e){var t;return void 0===e&&(e=!0),this.adPlaybackApiPromise=null,this.adPlaybackReporterPromise=null,this.getActiveAdBreak()&&(null===(t=this.adsManager)||void 0===t||t.stop()),this.resetActiveAd(),this.schedulableAdBreaks=this.persistentAdBreaks,this.queuedAdBreaks=[],this.currentScheduleTime=0,this.currentDuration=null,this.activeAdBreak=null,this.hasContentStarted=!1,this.isFallbackAd=!1,this.isPreviousAdPlaybackApiActive=!1,this.passthroughDownloadTime=null,e&&(this.scheduleAdBreaks=Promise.resolve(null)),Promise.resolve()},t.prototype.cleanUpImaSdk=function(){var e;this.adsLoader&&(this.adsLoader.removeEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,this.onAdError),this.adsLoader.removeEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,this.onAdsManagerLoaded),this.adsLoader.destroy(),this.adsLoader=null),this.adsManager&&(null===(e=this.imaAdBreaksTracker)||void 0===e||e.reset(),this.imaAdBreaksTracker=null,this.adsManager.removeEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,this.onContentPauseRequested),this.adsManager.removeEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,this.onContentResumeRequested),this.adsManager.removeEventListener(google.ima.AdEvent.Type.STARTED,this.onStarted),this.adsManager.removeEventListener(google.ima.AdEvent.Type.LOADED,this.onLoaded),this.adsManager.removeEventListener(google.ima.AdEvent.Type.SKIPPED,this.onSkipped),this.adsManager.removeEventListener(google.ima.AdEvent.Type.COMPLETE,this.onComplete),this.adsManager.removeEventListener(google.ima.AdEvent.Type.PAUSED,this.onPaused),this.adsManager.removeEventListener(google.ima.AdEvent.Type.RESUMED,this.onResumed),this.adsManager.removeEventListener(google.ima.AdEvent.Type.CLICK,this.onClicked),this.adsManager.removeEventListener(google.ima.AdEvent.Type.DURATION_CHANGE,this.onDurationChange),this.adsManager.removeEventListener(google.ima.AdEvent.Type.FIRST_QUARTILE,this.onFirstQuartile),this.adsManager.removeEventListener(google.ima.AdEvent.Type.MIDPOINT,this.onMidpoint),this.adsManager.removeEventListener(google.ima.AdEvent.Type.THIRD_QUARTILE,this.onThirdQuartile),this.adsManager.removeEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,this.onAdError),this.adsManager.removeEventListener(google.ima.AdEvent.Type.INTERACTION,this.onAdInteraction),this.adsManager.removeEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED,this.onAllAdsCompleted),this.adsManager.destroy(),this.adsManager=null),this.adDisplayContainer&&(this.adDisplayContainer.destroy(),this.adDisplayContainer=null),this.adsRenderingSettings=null},t.prototype.dispose=function(){this.resetActiveAd(),this.cleanUpImaSdk(),this.playerContainer&&this.playerContainer.removeChild(this.adContainer),this.adContainer=null,this.activeAdBreak=null,this.activeAd=null},t.imaMarker=function(){},t}(v.AdvertisingModule);function b(e,t){return e.scheduleTime-e.preloadOffset-(t.scheduleTime-t.preloadOffset)}function B(e){return e===google.ima.AdError.ErrorCode.VAST_EMPTY_RESPONSE||e===google.ima.AdError.ErrorCode.VAST_NO_ADS_AFTER_WRAPPER}function O(e,t){var i=t.getAd();if(i){if(e.isLinear=i.isLinear(),1===Object.keys(i).length){var r=i[Object.keys(i)[0]].clickThroughUrl;r&&(e.clickThroughUrl=r)}var a=i.getMediaUrl();a&&(e.mediaFileUrl=a),e.data=(0,P.collectAdData)(t)}}t.ImaModule=_},48467:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.ImaPassthroughMode=t.ImaModuleErrorCode=t.AdvertisingImaModuleDefinition=void 0;var r=i(16368),a=i(22416);Object.defineProperty(t,"ImaPassthroughMode",{enumerable:!0,get:function(){return a.ImaPassthroughMode}});var n=i(45641),o=i(3562);Object.defineProperty(t,"ImaModuleErrorCode",{enumerable:!0,get:function(){return o.ImaModuleErrorCode}}),t.AdvertisingImaModuleDefinition={name:r.ModuleName.Advertising,module:function(){return n.ImaModule},dependencies:[r.ModuleName.AdvertisingCore],exports:{ImaModuleErrorCode:o.ImaModuleErrorCode,ImaPassthroughMode:a.ImaPassthroughMode}},t.default=t.AdvertisingImaModuleDefinition},75515:function(e,t,i){var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,r=arguments.length;i<r;i++)for(var a in t=arguments[i])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.collectAdData=o;var a=i(70016),n=i(857);function o(e){var t,i=e.getAd();return i?d({adTitle:i.getTitle(),adSystem:{name:i.getAdSystem()},mimeType:i.getContentType(),bitrate:i.getVastMediaBitrate&&i.getVastMediaBitrate()>0?i.getVastMediaBitrate():-1,wrapperAdIds:i.getWrapperAdIds(),adDescription:i.getDescription(),advertiser:{name:i.getAdvertiserName()},apiFramework:null!==(t=i.getApiFramework())&&void 0!==t?t:void 0,creative:{id:i.getCreativeId(),adId:i.getCreativeAdId(),universalAdId:{idRegistry:i.getUniversalAdIdRegistry(),value:i.getUniversalAdIdValue()}},dealId:i.getDealId(),minSuggestedDuration:i.isLinear()?n.NON_SKIPPABLE_IDENTIFIER:i.getMinSuggestedDuration(),survey:s(i),traffickingParameters:i.getTraffickingParameters()}):{}}function s(e){var t=e.getSurveyUrl();return t?{uri:t}:void 0}function d(e){var t=r({},e);return Object.keys(t).forEach((function(e){var i=t[e],r=i&&"object"==typeof i&&!Object.keys(i).some((function(e){return Boolean(i[e])}));((0,a.isNumber)(i)&&i<0||!(0,a.isNumber)(i)&&!i||r)&&delete t[e]})),t}}},function(e){return function(t){return e(e.s=t)}(48467)}])}));
})();
