/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.mserenderer=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player.mserenderer=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[737],{1504:function(e,t){function r(e){return"ManagedMediaSource"in window&&(e||!("MediaSource"in window))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseManagedMediaSource=r},6863:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.MSERenderer=void 0;var n=r(92712),i=r(52442),o=r(25550),s=r(28764),u=r(18665),a=r(62510),d=r(33696),c=r(94304),f=r(17990),h=r(27279),g=r(3464),l=r(27177),p=r(10981),m=r(79705),v=r(53968),y=r(10676),S=r(79814),E=r(70016),b=r(46462),T=r(96873),M=r(33669),R=r(23729),C=r(93159),P=r(58765),B=r(1504),O=r(76316),D=function(){function e(e){var t=this;this.context=e,this.consecutiveErrorCount=0,this.waitingForSeekedPromise=null,this.onSeek=function(e){t.setCurrentTimeDelayedContext&&t.setCurrentTimeDelayedContext.time!==e.seekTarget&&Math.abs(t.setCurrentTimeDelayedContext.time-e.seekTarget)>=t.context.settings.GAP_TOLERANCE&&t.cancelSetCurrentTimeDelayed()},this.reOpenMse=function(){var e=t.segmentQueueMimeTypes;if(e.length<1)return Promise.resolve();var r=e.find((function(e){return t.timestampOffset.hasOwnProperty(e)}));return r?t.setTimestampOffset(r,t.timestampOffset[r]):t.setTimestampOffset(e[0],0)},this.firePictureInPictureEvent=function(){if(t.video){var e=t.video.isPictureInPicture()?d.ViewMode.PictureInPicture:d.ViewMode.Inline;t.context.store.dispatch((0,f.setPlayerViewMode)(e))}},this.resetConsecutiveErrorCountCallback=function(e){e.time&&e.time>0&&(t.consecutiveErrorCount=0)},this.postBufferRemoval=function(e,r,n){t.logger.debug("successfully removed buffer for ".concat(e," (from  ").concat(r," to ").concat(n,")"))},this.memoizedIsTypeSupported=(0,y.memoize)(this.isTypeSupported.bind(this)),this.logger=e.logger,this.video=e.videoElement,this.settings=e.settings,this.readyPromise=Promise.resolve(),this.eventHandler=void 0,this.timestampOffset={},this.sourceBufferMimeCodecMap=new R.BufferMimeCodecMap(e.store,e.settings),this.savedCurrentTime=0,this.playerEventHandler=e.eventHandler,this.shutdownPromise=Promise.resolve(),this.init(!0)}return e.prototype.createMSEWrapper=function(){return new P.MSEWrapper(this.context)},e.prototype.createMse=function(e){return e?this.createMseSynchronously():this.createMseAsynchronously()},e.prototype.createMseAsynchronously=function(){var e=this;return function(){var t;return null===(t=e.mse)||void 0===t?void 0:t.createNewMSE()}},e.prototype.createMseSynchronously=function(){var e,t=null===(e=this.mse)||void 0===e?void 0:e.createNewMSE();return function(){return t}},e.prototype.init=function(e){var t=this;this.segmentQueueProcessingPromise=Promise.resolve(),this.mse=this.createMSEWrapper(),this.readyPromise=this.readyPromise.catch((function(e){return t.context.logger.debug("Caught error during MSE recreation",e)})).then(this.createMse(e)),this.needsInitialSeek=!0,this.segmentQueues={},this.setCurrentTimeContext=void 0,this.setCurrentTimeDelayedContext=void 0,this.quotaExceededMap=new Map,this.quotaExceededDeferredMap=new Map,this.gapHandler=new C.GapHandler(this.context),null==this.eventHandler&&this.video&&(this.eventHandler=new O.VideoEventHandler(this.video),this.eventHandler.on(b.MediaElementEvent.timeupdate,this.resetConsecutiveErrorCountCallback)),this.on(b.MediaElementEvent.webkitpresentationmodechanged,this.firePictureInPictureEvent),this.playerEventHandler.on(a.PlayerEvent.Seek,this.onSeek)},e.prototype.trackRendererError=function(e){this.consecutiveErrorCount++,this.consecutiveErrorCount>=this.settings.MAX_CONSECUTIVE_RENDERER_ERRORS&&this.context.eventHandler.fireError(new s.PlayerError(o.ErrorCode.PLAYBACK_VIDEO_DECODING_ERROR,{codec:e.getCodec(),mimeType:e.getMimeType(),segmentUrl:e.getUrl()},"Failed to append the segment to the buffer. The MSE has thrown an error."))},e.prototype.hasSourceBuffers=function(){return this.segmentQueueMimeTypes.length>0},e.prototype.hasDataInSourceBuffers=function(){var e=this.mse;return!(!e||!this.hasSourceBuffers())&&this.segmentQueueMimeTypes.every((function(t){return e.getBufferedRanges(t).length>0}))},e.prototype.updateRendererRangesInStore=function(e){var t;null===(t=(0,h.getSourceStore)(this.context))||void 0===t||t.dispatch((0,T.setRendererRanges)(e,this.getOverallBufferedRanges(e)))},e.prototype.addBuffer=function(e,t){var r,n=this;if(!this.mse)return!1;this.sourceBufferMimeCodecMap.add(e,t);var i=Boolean(this.mse.addBuffer(e,t,(function(){return n.updateRendererRangesInStore(e)})));return i&&(this.mse.addUpdateEndCallback(e,(function(){var t;return null===(t=n.setCurrentTimeDelayedContext)||void 0===t||t.reevaluateDataInBuffer(),n.quotaExceededMap.set(e,!1)})),this.segmentQueues[e]=[],null===(r=(0,h.getSourceStore)(this.context))||void 0===r||r.dispatch((0,T.setRendererRanges)(e,[]))),i},e.prototype.addDrmInitData=function(e){if(null==e?void 0:e.length){var t=this.drmService;t?e.forEach((function(e){return t.addInitData(e)})):this.context.logger.debug("PSSH data found but DRM is not configured")}},e.prototype.storeDrmInitDataFromManifest=function(e){var t=this;e&&this.context.serviceManager.maybeCall(u.ServiceName.ManifestService,(function(r){var n=r.getContentProtectionForAdaptationSet(e).flatMap((function(e){var r=c.KeySystemKind[e.name.toUpperCase()],n=e.schemeIdUri.replace("urn:uuid:","").replace(/-/g,"");return(e.pssh||[]).map((function(i,o){return t.logger.insane("adding pssh for ".concat(e.name," : ").concat(i)),{initData:e.psshData[o],initDataStr:i,systemID:e.schemeIdUri,systemIDraw:n,systemName:r,kid:[]}}))}));t.addDrmInitData(n)}),void 0,this.context.sourceContext.sourceIdentifier)},e.prototype.appendData=function(e){var t=this;return this.addDrmInitData(e.getDrmInitData()),this.addSegmentToSegmentQueue(e),this.segmentQueueProcessingPromise=this.segmentQueueProcessingPromise.catch((function(){})).then((function(){return t.processSegmentQueues()})),this.segmentQueueProcessingPromise},e.prototype.addSegmentToSegmentQueue=function(e){var t=e.getMimeType();this.segmentQueues[t]?(this.segmentQueues[t].push(e),this.updateRendererRangesInStore(t)):this.logger.debug("Renderer missing segment queue for mimeType(".concat(t,") but trying to push segment.(url=").concat(e.getUrl(),")"))},e.prototype.getBufferedRanges=function(e){var t,r;return null!==(r=null===(t=this.mse)||void 0===t?void 0:t.getBufferedRanges(e))&&void 0!==r?r:[]},e.prototype.getVideoElementBufferedRanges=function(){return(0,n.getBufferedRanges)(this.video)},e.prototype.getOverallBufferedRanges=function(e){if(!this.mse||!this.segmentQueues.hasOwnProperty(e))return[];var t=n.BufferRangeHelper.getRangesFromQueue(this.segmentQueues[e],this.settings.GAP_TOLERANCE);return t=t.concat(this.getBufferedRanges(e)),t=n.BufferRangeHelper.mergeRanges(t,this.settings.GAP_TOLERANCE)},e.prototype.isDataBeingAppended=function(e){return!!this.mse&&(this.segmentQueues&&this.segmentQueues[e]&&this.segmentQueues[e].length>0||this.mse.isAnyBufferUpdating())},e.prototype.getRangesFromStore=function(e){var t=(0,h.getSourceState)(this.context);if(!t)return[];var r=(0,M.getBufferStateMap)(t);return r?(0,M.getRendererBufferedRanges)(r,e):[]},e.prototype.getEndOfBufferTime=function(){var e=this,t=[];if(this.segmentQueueMimeTypes.forEach((function(r){var n=e.getRangesFromStore(r);if(n.length>0){var i=n[n.length-1].end;t.push(i)}})),0!==t.length)return Math.min.apply(Math,t)},e.prototype.removeDataFromSegmentQueues=function(e,t,r){var n=this;void 0===t&&(t=-1/0),void 0===r&&(r=1/0),this.segmentQueues.hasOwnProperty(e)&&(this.segmentQueues[e]=this.segmentQueues[e].filter((function(i){var o=i.getDuration(),s=i.getPlaybackTime(),u=i.getRepresentationId().representationId,a=i.isInit()||s<t||s+o>r;return n.logger.debug("[MSERenderer][".concat(e,"][").concat(u,"] Removing segment from queue [").concat(!a,"]. ")+"Clearing from ".concat(t," to ").concat(r,". Segment: [playbackTime=").concat(i.getPlaybackTime(),", ")+"duration=".concat(i.getDuration(),", url=").concat(i.getUrl(),"]")),a})),this.updateRendererRangesInStore(e))},e.prototype.removeData=function(e,t,r){return this.mse?(void 0===t&&void 0===r&&this.saveCurrentTime(),this.removeDataFromSegmentQueues(e,t,r),this.mse.removeFromBuffer(e,t,r,this.postBufferRemoval)):Promise.reject()},e.prototype.getDroppedVideoFrames=function(){var e,t,r,n,i;return null!==(i=null!==(r=null===(t=null===(e=this.video)||void 0===e?void 0:e.getVideoPlaybackQuality())||void 0===t?void 0:t.droppedVideoFrames)&&void 0!==r?r:null===(n=this.video)||void 0===n?void 0:n.droppedVideoFrames)&&void 0!==i?i:0},e.prototype.setPlaybackSpeed=function(e){this.video&&!isNaN(e)&&e>0&&e!==this.video.playbackRate&&(this.video.playbackRate=e)},e.prototype.getPlaybackSpeed=function(){var e,t;return null!==(t=null===(e=this.video)||void 0===e?void 0:e.playbackRate)&&void 0!==t?t:1},e.prototype.setVolume=function(e){this.video&&!isNaN(e)&&(this.video.volume=Math.min(e/100,1))},e.prototype.getVolume=function(){var e,t;return 100*(null!==(t=null===(e=this.video)||void 0===e?void 0:e.volume)&&void 0!==t?t:1)},e.prototype.mute=function(){this.video&&(this.video.muted=!0)},e.prototype.unmute=function(){this.video&&(this.video.muted=!1)},e.prototype.isMuted=function(){var e,t;return null!==(t=null===(e=this.video)||void 0===e?void 0:e.muted)&&void 0!==t&&t},e.prototype.play=function(){var e=this;return this.video?this.video.play().catch((function(t){if((0,v.isSwitchingBufferBlocks)((0,h.getSourceStore)(e.context)))return e.ready();throw t})):Promise.resolve()},e.prototype.pause=function(){this.video&&this.video.pause()},e.prototype.end=function(){this.pause(),this.cancelSetCurrentTimeDelayed()},e.prototype.isPaused=function(){var e,t;return null===(t=null===(e=this.video)||void 0===e?void 0:e.paused)||void 0===t||t},e.prototype.setDuration=function(e){this.mse&&(e=Math.floor(1e4*e)/1e4,this.duration=e,this.mse.setDuration(e))},e.prototype.getDuration=function(){var e,t;return null!==(t=null===(e=this.video)||void 0===e?void 0:e.duration)&&void 0!==t?t:NaN},e.prototype.setCurrentTime=function(e){var t=this;if(!this.mse)return Promise.reject("MSE is not open");if(e=Math.ceil(100*e)/100,this.setCurrentTimeContext){if(this.setCurrentTimeContext.time===e)return this.setCurrentTimeContext.promise;this.cancelSetCurrentTimeDelayed()}var r=Promise.resolve();this.isMSEOpen()||this.isTimeInBuffer(e)||(this.logger.debug("reopen MSE on setCurrentTime"),r=this.reOpenMse());var n=r.then((function(){return t.setCurrentTimeInternal(e).catch((function(r){return t.logger.debug("could not set time ".concat(e," on video element"),r),t.getCurrentTime()})).finally((function(){return t.needsInitialSeek=!1}))}));return this.setCurrentTimeContext={time:e,isInitialSeek:this.needsInitialSeek,promise:n},n},e.prototype.getCurrentVideoTime=function(){var e,t;return null!==(t=null===(e=this.video)||void 0===e?void 0:e.currentTime)&&void 0!==t?t:0},e.prototype.getCurrentFallbackTime=function(){var e=this.setCurrentTimeContext;return e&&e.isInitialSeek&&-1!==e.time?e.time:0!==this.savedCurrentTime&&this.isVideoCurrentTimeUnreliable()?this.savedCurrentTime:(this.savedCurrentTime=0,this.getCurrentVideoTime())},e.prototype.isVideoCurrentTimeUnreliable=function(){var e;return!this.hasDataInSourceBuffers()&&!(null===(e=this.mse)||void 0===e?void 0:e.isBufferRemovalPending())||!(!(0,v.isSwitchingBufferBlocks)((0,h.getSourceStore)(this.context))&&this.isTimeInBuffer(this.getCurrentVideoTime()))},e.prototype.getCurrentTime=function(e){return void 0===e&&(e=!1),e?this.getCurrentVideoTime():this.getCurrentFallbackTime()},e.prototype.getSnapshotData=function(e,t){void 0===t&&(t=1);try{if(!this.video)return;var r=this.video.videoWidth,n=this.video.videoHeight;this.snapshotCanvas||(this.snapshotCanvas=document.createElement("canvas"),this.snapshotCanvas.id="snapshotHiddenCanvas"),this.snapshotCanvas.width=r,this.snapshotCanvas.height=n;var i=this.snapshotCanvas.getContext("2d");null==i||i.drawImage(this.video.getWrappedElement(),0,0,r,n);var o=void 0;return"image/jpeg"===e||"image/webp"===e?(t=Math.max(t,0),t=Math.min(t,1),o=this.snapshotCanvas.toDataURL(e,t)):o=this.snapshotCanvas.toDataURL(e,t),{height:n,width:r,data:o}}catch(e){return void(e&&e.message?this.logger.debug("Snapshot acquisition failed: "+e.message):this.logger.debug("Snapshot acquisition failed"))}},e.prototype.getDrmShutdownPromise=function(){return this.drmService?this.drmService.shutdownPromise:Promise.resolve()},e.prototype.ready=function(){var e=this;return this.shutdownPromise.then((function(){return e.getDrmShutdownPromise()})).then((function(){return e.mse||e.init(!1),e.readyPromise}))},e.prototype.setTimestampOffset=function(e,t){var r=this;return this.mse?this.mse.setTimestampOffset(e,t).then((function(){r.timestampOffset[e]=t})):Promise.resolve()},e.prototype.shutdown=function(e){var t,r=this;if(!this.mse)return this.shutdownPromise;for(var n in this.waitingForSeekedPromise=null,e&&this.saveCurrentTime(),this.segmentQueues)this.segmentQueues.hasOwnProperty(n)&&(delete this.segmentQueues[n],null===(t=(0,h.getSourceStore)(this.context))||void 0===t||t.dispatch((0,T.setRendererRanges)(n,[])));var i=this.mse;return this.shutdownPromise=this.shutdownPromise.then((function(){return i.tearDownMediaSource()})).catch((function(e){r.logger.debug("Could not tear down media source",{err:e,message:e.message})})),this.mse=void 0,this.cancelSetCurrentTimeDelayed(),this.gapHandler&&(this.gapHandler.shutdown(),this.gapHandler=void 0),this.eventHandler&&!e&&(this.eventHandler.dispose(),this.eventHandler=void 0),this.quotaExceededMap.clear(),this.quotaExceededDeferredMap.clear(),this.shutdownPromise},e.prototype.release=function(){var e;for(var t in this.segmentQueues)this.segmentQueues.hasOwnProperty(t)&&null!==this.mse&&(delete this.segmentQueues[t],null===(e=(0,h.getSourceStore)(this.context))||void 0===e||e.dispatch((0,T.setRendererRanges)(t,[])));return this.sourceBufferMimeCodecMap.dispose(),this.mse&&this.mse.dispose(),this.readyPromise=Promise.resolve(),this.timestampOffset={},this.cancelSetCurrentTimeDelayed(),this.gapHandler&&(this.gapHandler.shutdown(),this.gapHandler=void 0),this.off(b.MediaElementEvent.webkitpresentationmodechanged,this.firePictureInPictureEvent),this.eventHandler&&(this.eventHandler.off(b.MediaElementEvent.timeupdate,this.resetConsecutiveErrorCountCallback),this.eventHandler.dispose(),this.eventHandler=void 0),this.waitingForSeekedPromise=null,this.playerEventHandler.off(a.PlayerEvent.Seek,this.onSeek),this.memoizedIsTypeSupported.invalidate(),this.getDrmShutdownPromise()},e.prototype.on=function(e,t){var r;null===(r=this.eventHandler)||void 0===r||r.on(e,t)},e.prototype.off=function(e,t){this.eventHandler&&this.eventHandler.off(e,t)},Object.defineProperty(e.prototype,"drmService",{get:function(){var e;return null===(e=this.context.serviceManager)||void 0===e?void 0:e.get(u.ServiceName.DrmService)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"segmentQueueMimeTypes",{get:function(){return Object.keys(this.segmentQueues)},enumerable:!1,configurable:!0}),e.prototype.setDrmConfig=function(e,t){return this.drmService?this.drmService.updateDrmConfig(e,t):Promise.reject(i.DrmSetupError.MISSING_CONFIGURATION)},e.prototype.isMediaTypeSupported=function(e,t){return this.memoizedIsTypeSupported("".concat(e,'; codecs="').concat(t,'"'))},e.prototype.isTypeSupported=function(e){return(0,B.shouldUseManagedMediaSource)(this.context.settings.PREFER_MANAGED_MEDIA_SOURCE)?window.ManagedMediaSource.isTypeSupported(e):"MediaSource"in window&&MediaSource.isTypeSupported(e)},e.prototype.cancelPendingEndOfStream=function(){this.setEndOfStreamDeferred&&(this.setEndOfStreamDeferred.reject(new Error("Cancelled pending end of stream.")),this.setEndOfStreamDeferred=void 0)},e.prototype.deferSetEndOfStream=function(){var e=this;return this.setEndOfStreamDeferred=this.setEndOfStreamDeferred||new p.Deferred,this.setEndOfStreamDeferred.promise.then((function(){var t;return null===(t=e.mse)||void 0===t?void 0:t.endOfStream()})).catch((function(t){(0,l.isContextAvailable)(e.context)&&e.context.logger.debug(t)}))},e.prototype.setEndOfStream=function(e){if(void 0===e&&(e=!0),!e)return this.cancelPendingEndOfStream(),this.isMSEOpen()?Promise.resolve():this.reOpenMse();if(!this.hasSourceBuffers()||!this.mse)return this.logger.debug("Cannot signal EOS as no buffers have been added yet"),Promise.resolve();var t=this.getEndOfBufferTime();return this.setCurrentTimeDelayedContext&&this.setCurrentTimeDelayedContext.time>=this.duration&&t&&!isNaN(t)&&this.cancelSetCurrentTimeDelayed(),this.setCurrentTimeDelayedContext?this.deferSetEndOfStream():this.mse.endOfStream()},e.prototype.isTimeInRangePendingRemoval=function(e){var t=this.getRangesPendingRemoval();for(var r in t){var n=t[r];if(n.start<=e&&n.end>=e)return!0}return!1},e.prototype.isTimeInBuffer=function(e,t){if(void 0===t&&(t=0),this.isTimeInRangePendingRemoval(e))return!1;var r=this.getRangesInSourceBuffers();return!!r&&Object.keys(r).every((function(i){return(0,n.isInTimeRanges)(r[i],e,t)}))},e.prototype.ensureSafeDistanceToEndOfBuffer=function(e,t){var r,n=null!==(r=this.getRangesInSourceBuffers())&&void 0!==r?r:{},i=Object.keys(n),o=x(n,e);if(0===i.length||o.length<i.length)return NaN;var s=Math.min.apply(Math,o.map((function(e){var t;return null!==(t=null==e?void 0:e.end)&&void 0!==t?t:1/0}))),u=s-e;return u>=t?e:e-(t-u)},e.prototype.adjustTimeToNextCommonBufferStart=function(e){var t,r,n,i=this,o=null===(t=(0,h.getSourceState)(this.context))||void 0===t?void 0:t.drm,s=(0,m.getBufferEndSafetyMargin)(o);if(this.isTimeInBuffer(e,s))return e;var u=this.isTimeInBuffer(e),a=this.getFutureBufferStarts(e).filter((function(t){return t>=e})),d=0===a.length,c=null===(n=null===(r=(0,h.getSourceState)(this.context))||void 0===r?void 0:r.buffer.isLoadingRangeFinished)||void 0===n||n;if(u&&d){var f=this.ensureSafeDistanceToEndOfBuffer(e,s);return f===e||c?!isNaN(f)&&this.isTimeInBuffer(f)?(this.logger.debug("Adjusting current time away from end of buffer: ".concat(f)),f):e:(this.logger.debug("Not adjusting seek target, because there will be future data"),NaN)}if(!u&&d)return NaN;var g=a.find((function(e){return i.isTimeInBuffer(e,s)})),l=null!=g?g:Math.min.apply(Math,a);return g||c?(this.logger.debug("Adjusting current time to next safe buffer start: ".concat(l)),l):(this.logger.debug("Not adjusting seek target, as it is not safe across media types and there will be future data"),NaN)},e.prototype.getFutureBufferStarts=function(e){var t,r=this.getRangesInSourceBuffers();if(!r)return[];if(this.mse){var i=this.getRangesPendingRemoval();t=Object.keys(i).map((function(e){return i[e]}))}else t=[];return n.BufferRangeHelper.getCommonBufferedRanges(r).filter((function(t){return t.getEnd()>=e&&!isNaN(t.getStart())})).filter((function(e){return t.every((function(t){return(0,n.areDisjoint)(t,{start:e.getStart(),end:e.getEnd()})}))})).map((function(e){return e.getStart()}))},e.prototype.getRangesInSourceBuffers=function(){if(!this.mse)return null;var e={};for(var t in this.segmentQueues)if(this.segmentQueues.hasOwnProperty(t)&&(e[t]=this.mse.getBufferedRanges(t),e[t].length<1))return null;return Object.keys(e).length?e:null},e.prototype.getRangesPendingRemoval=function(){if(!this.mse||!this.mse.isBufferRemovalPending())return{};var e={};for(var t in this.segmentQueues){var r=this.mse.getRangePendingRemoval(t);this.segmentQueues.hasOwnProperty(t)&&r&&(e[t]=r)}return e},e.prototype.processSegmentQueues=function(){var e=this,t=this.segmentQueueMimeTypes.filter((function(t){return e.segmentQueues[t].length>0})).map((function(t){return e.addSegmentFromQueueToMse(t)}));return 0===t.length?Promise.resolve():Promise.all(t).then((function(){return e.processSegmentQueues()})).catch((function(e){if(e!==i.SOURCE_BUFFER_APPEND_STATUS.SUSPENDED)throw e}))},e.prototype.getHeadOfQueue=function(e){return this.segmentQueues[e][0]},e.prototype.isMSEOpen=function(){var e;return(null===(e=this.mse)||void 0===e?void 0:e.readyState)===P.MediaSourceReadyState.open},e.prototype.maybeWaitForDrmLicense=function(e){var t,r=null===(t=(0,h.getSourceState)(this.context))||void 0===t?void 0:t.drm;return this.context.serviceManager.maybeCall(u.ServiceName.DrmService,(function(t){return t.maybeWaitForDrmLicense(e,r)}),Promise.resolve())},e.prototype.addSegmentFromQueueToMse=function(e){var t=this;if(!this.mse)return Promise.resolve();var r=Promise.resolve();this.isMSEOpen()||(r=this.reOpenMse());var n=this.getHeadOfQueue(e);if(!n)return Promise.resolve();var o=n.getRepresentationId().representationId,s=n.getUrl();return this.context.logger.debug("[MSERenderer][".concat(e,"][").concat(o,"] Scheduling appendage to buffer [url=").concat(s,"], DRM key ID (").concat(n.getDrmKid(),")")),r.then((function(){return t.maybeWaitForDrmLicense(n)})).then((function(){return t.mse?t.segmentQueues[e].includes(n)?t.mse.addToBuffer(n).then((function(){var r=t.getHeadOfQueue(e)!==n;if(t.logger.debug("[MSERenderer][".concat(e,"][").concat(o,"] Appended segment to buffer [url=").concat(s,"]")),r){var i=t.segmentQueues[e][0],u=i?i.getUrl():"none";t.logger.debug("[MSERenderer][".concat(e,"][").concat(o,"] Skipping removal of [url=").concat(s,"] ")+"from segment queue as the queue head has unexpectedly changed to [url=".concat(u,"]"))}else t.segmentQueues[e].shift();t.drmService&&t.hasDataInSourceBuffers()&&!(0,v.isSwitchingBufferBlocks)((0,h.getSourceStore)(t.context))&&t.drmService.signalInitDataShouldBeAvailable()})):(t.logger.debug("[MSERenderer][".concat(e,"][").concat(o,"] Segment ").concat(n.getUrl()," no longer part of the queue - skipping appendage")),Promise.resolve()):Promise.reject(i.SOURCE_BUFFER_APPEND_STATUS.FAILURE)})).catch((function(r){if(t.logger.debug("[MSERenderer][".concat(e,"][").concat(o,"] Adding segment from queue to buffer failed"),r),r===i.SOURCE_BUFFER_APPEND_STATUS.FAILURE&&t.trackRendererError(n),t.mse&&r===i.SOURCE_BUFFER_APPEND_STATUS.QUOTA_EXCEEDED){t.quotaExceededMap.set(e,!0);var s=t.quotaExceededDeferredMap.get(e);return s||(s=new p.Deferred,t.quotaExceededDeferredMap.set(e,s)),t.mse.addOneUpdateEndCallback(e,s.resolve).then((function(){t.quotaExceededDeferredMap.delete(e),t.context.logger.debug("Appending segment after quota has been exceeded...")})),s.promise.then((function(){return t.processSegmentQueues()}))}return t.mse?t.mse.queueActionOnBuffer(e,(function(){return t.processSegmentQueues()})):Promise.reject(r)}))},e.prototype.cancelSetCurrentTimeDelayed=function(){this.setCurrentTimeDelayedContext&&(this.context.logger.debug("Cancelling delayed set currentTime to ".concat(this.setCurrentTimeDelayedContext.time)),this.setCurrentTimeDelayedContext.unsubscribeFromSourceStore(),this.setCurrentTimeDelayedContext.deferred.reject("cancelled seek"),this.setCurrentTimeDelayedContext=void 0)},e.prototype.recoverFromExceededQuota=function(e){var t=this;this.segmentQueueMimeTypes.forEach((function(r){t.segmentQueues[r]=t.segmentQueues[r].filter((function(t){return t.getPlaybackTime()+t.getDuration()>=e})),t.updateRendererRangesInStore(r),t.quotaExceededMap.set(r,!1)})),this.quotaExceededDeferredMap.forEach((function(e){return e.reject()})),this.quotaExceededDeferredMap.clear()},e.prototype.resolvePendingEndOfStream=function(){this.setEndOfStreamDeferred&&(this.setEndOfStreamDeferred.resolve(),this.setEndOfStreamDeferred=void 0)},e.prototype.setCurrentTimeInternal=function(e){var t=this;if(!this.mse)return Promise.reject("Couldn't set current time, MSE is not open");w(this.quotaExceededMap,(function(e){return e}))&&this.recoverFromExceededQuota(e);var r=this.mse.waitForBuffers().then((function(){return t.waitingForSeekedPromise})).then((function(){if(t.setCurrentTimeContext){if(t.setCurrentTimeContext.time!==e)return Promise.reject("A newer setting of change time has been scheduled.")}else t.setCurrentTimeContext={time:e,isInitialSeek:!1,promise:r};var n=t.adjustTimeToNextCommonBufferStart(e);return isNaN(n)?t.waitForDataInBuffer(e):n})).then((function(e){var r,n,i,o,s=null!==(n=null===(r=t.video)||void 0===r?void 0:r.currentTime)&&void 0!==n?n:0,u=!t.isPaused();return(null===(i=t.setCurrentTimeContext)||void 0===i?void 0:i.isInitialSeek)&&u&&s>e?(t.logger.debug("Skip initial seek to ".concat(e," as playback has started and is already at ").concat(s)),o=Promise.resolve(s)):(t.logger.debug("set time on video element to ".concat(e)),o=t.seekOnVideoElement(e)),o.then((function(e){return t.resolvePendingEndOfStream(),e})).catch((function(r){return t.handleVideoSetCurrentTimeError(e,r)})).finally((function(){t.waitingForSeekedPromise=null,t.setCurrentTimeContext=void 0,t.savedCurrentTime=0}))}));return r},e.prototype.waitForDataInBuffer=function(e){var t=this;this.context.logger.debug("Delaying seek, waiting for data in buffer for time ".concat(e));var r=(0,h.getSourceStore)(this.context);if(!r)return Promise.reject(new Error("No source store available"));var n=new p.Deferred,i=function(){var r=t.adjustTimeToNextCommonBufferStart(e);isNaN(r)?t.context.logger.debug("Re-evaluated delayed seek to ".concat(e,": Cannot yet resolve, waiting for data in buffer"),{video:t.getBufferedRanges("video/mp4"),audio:t.getBufferedRanges("audio/mp4"),adjustedTime:r}):(o(),t.context.logger.debug("Re-evaluated delayed seek to ".concat(e,": Resolving with seek target ").concat(r)),t.setCurrentTimeDelayedContext=void 0,n.resolve(r))},o=(0,g.subscribe)(r)((function(e){return(0,M.getBufferState)(e)}),(function(){var e;null===(e=t.setCurrentTimeDelayedContext)||void 0===e||e.reevaluateDataInBuffer()}),(function(e,t){return Boolean((null==e?void 0:e.isLoadingRangeFinished)&&!(null==t?void 0:t.isLoadingRangeFinished))}));return this.setCurrentTimeDelayedContext={time:e,reevaluateDataInBuffer:i,deferred:n,unsubscribeFromSourceStore:o},n.promise},e.prototype.handleVideoSetCurrentTimeError=function(e,t){return this.logger.debug("failed to set current time",t),this.waitingForSeekedPromise=null,this.setCurrentTimeInternal(e)},e.prototype.seekOnVideoElement=function(e){var t=this;return this.waitingForSeekedPromise=new Promise((function(r,n){t.video?(t.video.currentTime=e,r(t.video.currentTime)):n("No video reference")})),this.waitingForSeekedPromise},e.prototype.saveCurrentTime=function(){this.setCurrentTimeContext?this.savedCurrentTime=this.setCurrentTimeContext.time:this.savedCurrentTime=this.getCurrentVideoTime()},e.prototype.recreateSourceBuffers=function(e){var t=this;return void 0===e&&(e=this.getCurrentTime()),this.shutdown(!1).then((function(){return t.ready()})).then((function(){t.sourceBufferMimeCodecMap.forEach((function(e,r){t.addBuffer(e,r)||(t.context.logger.debug("Failed to create source buffer ".concat(e)),t.sourceBufferMimeCodecMap.remove(e))}))})).then((function(){t.setCurrentTime(e).then((function(){var e,r;return(null===(r=null===(e=t.context.serviceManager)||void 0===e?void 0:e.get(u.ServiceName.PlayerStateService))||void 0===r?void 0:r.isPlaying())?t.play():Promise.resolve()}))}))},e.prototype.updateSegmentQueueMimeType=function(e){var t,r=this,n=S.MimeTypeHelper.getMediaType(e);this.segmentQueueMimeTypes.forEach((function(e){var t;S.MimeTypeHelper.getMediaType(e)===n&&(delete r.segmentQueues[e],null===(t=(0,h.getSourceStore)(r.context))||void 0===t||t.dispatch((0,T.setRendererRanges)(e,[])))})),this.segmentQueues[e]=[],null===(t=(0,h.getSourceStore)(this.context))||void 0===t||t.dispatch((0,T.setRendererRanges)(e,[]))},e.prototype.changeBufferType=function(e,t){var r=this;return this.sourceBufferMimeCodecMap.update(e,t),this.updateSegmentQueueMimeType(e),this.mse&&this.mse.isChangeTypeSupported()?this.mse.changeBufferType(e,t).then((function(){var r;return(r={})[e]=t,r})).catch((function(n){return r.logger.debug("Failed to change buffer type to ".concat(e," ").concat(t,":"),n),r.recreateSourceBuffers().then((function(){return r.sourceBufferMimeCodecMap.entries()}))})):this.recreateSourceBuffers().then((function(){return r.sourceBufferMimeCodecMap.entries()}))},e.prototype.shouldContinueBuffering=function(){var e,t;return null!==(t=null===(e=this.mse)||void 0===e?void 0:e.shouldContinueBuffering)&&void 0!==t&&t},e}();function w(e,t){var r=!1;return e.forEach((function(e){return r=r||t(e)})),r}function x(e,t){return Object.keys(e).map((function(r){return e[r].find((function(e){return(0,n.isInTimeRanges)([e],t)}))})).filter(E.isDefined)}t.MSERenderer=D},23729:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.BufferMimeCodecMap=void 0;var n=r(27517),i=r(76650),o=r(28819),s=r(8272),u=r(27177),a=r(79814),d=function(){function e(e,t){this.mimeCodeMap={},this.store=e,this.settings=t}return e.prototype.ensureMetricsForMimeType=function(e){var t,r,s=null===(t=this.store)||void 0===t?void 0:t.getState();(0,n.isInstanceState)(s)&&c(this.settings)&&((0,o.getMetricsState)(s)[e]||null===(r=this.store)||void 0===r||r.dispatch((0,i.initializeMetricsForMimeType)(e,this.settings)))},e.prototype.removeMetrics=function(e){var t;null===(t=this.store)||void 0===t||t.dispatch((0,i.removeMetricsForMimeType)(e))},e.prototype.add=function(e,t){var r;this.mimeCodeMap[e]=t,this.ensureMetricsForMimeType(e),null===(r=this.store)||void 0===r||r.dispatch((0,i.addMetricsValue)(e,s.MetricType.StalledSeconds,0))},e.prototype.remove=function(e){var t=this,r=a.MimeTypeHelper.getMediaType(e);Object.keys(this.mimeCodeMap).forEach((function(e){a.MimeTypeHelper.getMediaType(e)===r&&(t.removeMetrics(e),delete t.mimeCodeMap[e])}))},e.prototype.has=function(e,t){var r=this,n=a.MimeTypeHelper.getMediaType(e),i=a.MimeTypeHelper.extractContainerType(e);return Object.keys(this.mimeCodeMap).some((function(o){var s=a.MimeTypeHelper.getMediaType(o),u=a.MimeTypeHelper.extractContainerType(o);return s===n&&u===i&&t===r.mimeCodeMap[e]}))},e.prototype.update=function(e,t){this.remove(e),this.add(e,t)},e.prototype.forEach=function(e){var t=this;Object.keys(this.entries()).forEach((function(r){return e(r,t.entries()[r])}))},e.prototype.entries=function(){return this.mimeCodeMap},e.prototype.dispose=function(){this.mimeCodeMap={},this.store=void 0,this.settings=void 0},e}();t.BufferMimeCodecMap=d;var c=function(e){return(0,u.isObject)(e)}},24869:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.SourceBufferWrapper=void 0;var n=r(10981),i=r(63916),o=function(){function e(e,t){var r=this;this.onBufferedRangesChanged=t,this.onUpdateSuccess=function(){r.updateExposedBufferRanges(),r.onBufferedRangesChanged(r.bufferedRanges),r.cleanUpPendingUpdateEndedPromises((function(e){return e.resolve()}))},this.onUpdateError=function(e){r.cleanUpPendingUpdateEndedPromises((function(t){return t.reject(e)}))},this.ready=function(){return r.buffer.updating?r.onUpdateEnded():Promise.resolve()},this.buffer=e,this.bufferedRanges=[],this.queuePromise=Promise.resolve(),this.pendingUpdateEndedPromises=new i.LinkedListQueue,["error","abort","sourceended","sourceclose"].forEach((function(e){r.buffer.addEventListener(e,r.onUpdateError)})),this.buffer.addEventListener("updateend",this.onUpdateSuccess)}return e.prototype.cleanUpPendingUpdateEndedPromises=function(e){for(var t;null!=(t=this.pendingUpdateEndedPromises.dequeue());)e(t)},e.prototype.updateExposedBufferRanges=function(){try{this.bufferedRanges=s(this.buffer.buffered)}catch(e){}},e.prototype.onUpdateEnded=function(){var e=new n.Deferred;return this.pendingUpdateEndedPromises.enqueue(e),e.promise},e.prototype.queueAction=function(e){var t=this,r=this.queuePromise.then((function(){return t.ready()})).then((function(){return Promise.resolve(e()).then((function(){return t.ready()}))}));return this.queuePromise=r.catch((function(){})),r},e.prototype.changeType=function(e){this.buffer.changeType(e)},e.prototype.dispose=function(){var e=this;["error","abort","sourceended","sourceclose"].forEach((function(t){e.buffer.removeEventListener(t,e.onUpdateError)})),this.cleanUpPendingUpdateEndedPromises((function(e){return e.reject("disposing source buffer")})),this.buffer.removeEventListener("updateend",this.onUpdateSuccess)},e}();function s(e){for(var t=[],r=0;r<e.length;r++)t.push({start:e.start(r),end:e.end(r)});return t}t.SourceBufferWrapper=o},27838:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.MSERendererModuleDefinition=void 0;var n=r(16368),i=r(6863),o=r(58765),s=r(89413),u=r(98758);t.MSERendererModuleDefinition={name:n.ModuleName.RendererMse,module:{MSEWrapper:o.MSEWrapper,MSERenderer:i.MSERenderer,Ranges:s.Ranges,technologyChecker:new u.TechnologyChecker}},t.default=t.MSERendererModuleDefinition},58765:function(e,t,r){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.MSEWrapper=t.MediaSourceReadyState=void 0;var i,o=r(52442),s=r(25550),u=r(28764),a=r(63546),d=r(35148),c=r(62510),f=r(58935),h=r(11399),g=r(96873),l=r(60627),p=r(27279),m=r(13533),v=r(10981),y=r(79814),S=r(56435),E=r(70016),b=r(61878),T=r(73904),M=r(24869),R=r(1504);!function(e){e.closed="closed",e.open="open",e.ended="ended"}(i||(t.MediaSourceReadyState=i={}));var C=function(){function e(e){var t=this;this.postBufferRemoval=function(e,r,n){t.logger.debug("successfully removed buffer for ".concat(e," (from  ").concat(r," to ").concat(n,")"))},this.waitForBuffer=function(e){return t.sourceBuffers.hasOwnProperty(e)?t.mseReady().then((function(){return t.sourceBuffers[e].ready()})):Promise.reject("No SourceBuffer for '".concat(e,"' available"))},this.startStreaming=function(){t.logger.debug("[MSEWrapper][ManagedMediaSource] Start Streaming received, resuming loading"),t.shouldContinueStreaming=!0},this.endStreaming=function(){t.logger.debug("[MSEWrapper][ManagedMediaSource] Stop Streaming received, stopping loading"),t.shouldContinueStreaming=!1},this.bufferedChange=function(e){t.logger.isLevelEnabled(f.LogLevel.DEBUG)&&t.logger.debug("Received bufferedChange event, Added Ranges: ".concat(P(e.addedRanges),", Removed Ranges: ").concat(P(e.removedRanges)))},e.videoElement&&(this.context=e,this.logger=e.logger,this.video=e.videoElement,this.sourceBuffers={},this.isInit=!1,this.teardownInProgressPromise=Promise.resolve(),this.shouldContinueStreaming=!0)}return e.prototype.getBufferedRanges=function(e){return this.sourceBuffers.hasOwnProperty(e)?this.sourceBuffers[e].bufferedRanges:[]},e.prototype.printDetailedBufferRange=function(e,t){var r;try{r=e.buffered}catch(e){return void this.logger.debug("".concat(t," buffer range: Error accessing buffered ranges"))}if(0!==r.length)for(var n=0;n<r.length;n++)this.logger.debug("".concat(t," buffer range [").concat(n,"]: ").concat(r.start(n)," - ").concat(r.end(n)));else this.logger.debug("".concat(t," buffer range: empty"))},e.prototype.getBufferStartTime=function(){var e=this,t=Object.keys(this.sourceBuffers).map((function(t){return 0===e.sourceBuffers[t].bufferedRanges.length?0:e.sourceBuffers[t].bufferedRanges[0].start}));return Math.max.apply(null,t)},e.prototype.addBuffer=function(e,t,r){var n=this;if(this.mediaSource){if(this.isInit&&!this.sourceBuffers.hasOwnProperty(e))try{var i=this.mediaSource.addSourceBuffer(e+"; codecs="+t);this.sourceBuffers[e]=new M.SourceBufferWrapper(i,(function(t){r(t),n.context.logger.isLevelEnabled(f.LogLevel.DEBUG)&&n.printDetailedBufferRange(i,e)}))}catch(r){return this.logger.debug("Could not add SourceBuffer for "+e+" with codecs "+t+":"+r),void this.context.eventHandler.dispatchEvent(c.PlayerEvent.Warning,new a.PlayerWarning(d.WarningCode.SOURCE_CODEC_OR_FILE_FORMAT_NOT_SUPPORTED))}return this.logger.debug("Added SourceBuffer for "+e+" with codecs "+t),isNaN(this.mediaSource.duration)||(this.sourceBuffers[e].appendWindowEnd=this.mediaSource.duration),this.sourceBuffers[e].buffer}},e.prototype.updateTimestampOffset=function(e,t){var r=this.sourceBuffers[e];r&&(this.logger.debug("setting new source buffer [".concat(e,"] offset from ").concat(r.buffer.timestampOffset," to ").concat(t)),r.buffer.timestampOffset=t)},e.prototype.setTimestampOffset=function(e,t){return this.sourceBuffers.hasOwnProperty(e)?((0,E.isDefined)(t)&&!isNaN(t)||(this.logger.debug("source buffer [".concat(e,"] offset is ").concat(t,", setting it to 0")),t=0),this.queueTimestampOffsetUpdate(e,t)):(this.logger.debug("no such source buffer found (mime type=".concat(e,")")),Promise.resolve())},e.prototype.queueTimestampOffsetUpdate=function(e,t){var r=this;return this.queueActionOnBuffer(e,(function(){return r.updateTimestampOffset(e,t)})).catch((function(t){r.logger.debug("Failed to set timestamp offset for",e,":",t)}))},e.prototype.getBufferRangeStart=function(e,t){return t||e.start(0)},e.prototype.getBufferRangeEnd=function(e,t){return t||e.end(e.length-1)},e.prototype.getBufferRemovalRange=function(e,t,r){var n=this.sourceBuffers[e].buffer.buffered;return{start:this.getBufferRangeStart(n,t),end:this.getBufferRangeEnd(n,r)}},e.prototype.preBufferRemoval=function(e,t,r){this.logger.debug("removing buffer for ".concat(e," (from ").concat(t," to ").concat(r,")"))},e.prototype.removeBufferRange=function(e,t,r){this.preBufferRemoval(e,t,r),this.sourceBuffers[e].buffer.remove(t,r)},e.prototype.removeFromBuffer=function(e,t,r,n){var o=this;return void 0===n&&(n=this.postBufferRemoval),this.queueActionOnBuffer(e,(function(){var s,u,f;try{if(o.video&&o.video.currentTime>=0&&(null===(f=null===(u=null===(s=o.sourceBuffers[e])||void 0===s?void 0:s.buffer)||void 0===u?void 0:u.buffered)||void 0===f?void 0:f.length)>0){var h=o.getBufferRemovalRange(e,t,r),g=h.start,l=h.end;if(g>=0&&l>g){if(o.sourceBuffers[e].removalPending=!0,o.sourceBuffers[e].rangePendingRemoval={start:g,end:l},!o.mediaSource||o.mediaSource.readyState===i.open){var p=o.sourceBuffers[e].onUpdateEnded().then((function(){o.sourceBuffers[e]&&(o.sourceBuffers[e].rangePendingRemoval=null,o.sourceBuffers[e].removalPending=!1,n(e,g,l))}));return o.removeBufferRange(e,g,l),p}o.logger.debug("cannot remove buffer for ".concat(e," (from ").concat(g," to ").concat(l,"): MediaSource is not open")),o.addOneSourceOpenCallback((function(){o.removeFromBuffer(e,g,l,n).catch((function(t){o.logger.debug("Couldn't remove buffer from ".concat(g," to ").concat(l," for ").concat(e),t)}))}))}else o.logger.debug("Ignoring buffer clearing request for ".concat(e,", as the passed-in range is not valid: ").concat(g," to ").concat(l))}else o.logger.debug("Ignoring buffer clearing request for ".concat(e,", as there are no buffered ranges."))}catch(t){var m="".concat(d.WarningCode[d.WarningCode.PLAYBACK_WARNING],": Buffer clearing failed for ").concat(e,". Exception: ").concat(JSON.stringify(t));o.context.eventHandler.dispatchEvent(c.PlayerEvent.Warning,new a.PlayerWarning(d.WarningCode.PLAYBACK_WARNING,m))}return Promise.resolve()}))},e.prototype.removeBuffer=function(e){var t=this,r=Promise.resolve(),i=this.sourceBuffers[e];if(!i||!this.mediaSource)return r;if(this.isMediaSourceOpen())try{null==i||i.buffer.abort()}catch(e){return this.logger.debug("Could not abort last action on buffer",n({},e)),Promise.resolve()}if(y.MimeTypeHelper.isSubtitle(e))r=r.then((function(){return t.removeFromBuffer(e).catch((function(e){t.logger.debug("Error removing buffer data",e)}))}));else try{this.mediaSource&&this.mediaSource.removeSourceBuffer(i.buffer)}catch(t){this.logger.debug("Error while trying to remove ".concat(e," source buffer."),t)}return this.deleteSourceBuffer(e),r},e.prototype.deleteSourceBuffer=function(e){this.sourceBuffers[e]?(this.sourceBuffers[e].dispose(),delete this.sourceBuffers[e],this.logger.debug("Removed SourceBuffer for ".concat(e))):this.logger.debug("No source buffer to remove for mimeType=".concat(e))},e.prototype.isMediaSourceOpen=function(){var e;return(null===(e=this.mediaSource)||void 0===e?void 0:e.readyState)===i.open},e.prototype.resetAllBuffers=function(){var e=this;if(!this.mediaSource)return this.logger.debug("Media source is removed. Can not reset buffers."),Promise.resolve();var t=[];for(var r in this.sourceBuffers)this.sourceBuffers.hasOwnProperty(r)&&t.push(this.removeBuffer(r));return Promise.all(t).then((function(){e.sourceBuffers={},e.logger.debug("Removed all SourceBuffers")}))},e.prototype.tearDownMediaSource=function(){var e=this;return this.teardownInProgressPromise=this.teardownInProgressPromise.then((function(){return e.resetAllBuffers()})).then((function(){e.mediaSource?e.detachVideoElementAndMSE():e.logger.debug("Media source has already been removed.")})),this.teardownInProgressPromise},e.prototype.detachVideoElementAndMSE=function(){var e;if(this.video)if(null!==this.video.getWrappedElement()){var t=(0,T.getMseObjectUrl)((0,p.getSourceState)(this.context));if("function"==typeof(null===(e=null===window||void 0===window?void 0:window.URL)||void 0===e?void 0:e.revokeObjectURL)&&""!==t)try{window.URL.revokeObjectURL(t)}catch(e){this.logger.debug("Could not revoke MSE object URL")}this.mediaSource&&(this.mediaSource.removeEventListener("startstreaming",this.startStreaming),this.mediaSource.removeEventListener("endstreaming",this.endStreaming),this.mediaSource.removeEventListener("bufferedchange",this.bufferedChange)),this.mediaSourceReadyDeferred&&this.mediaSourceReadyDeferred.reject("MediaSource is being destroyed."),this.mediaSourceReadyDeferred=void 0,this.mediaSourceReadyPromise=void 0,t===this.video.src&&(b.VideoElementUtil.removeSource(this.video),this.video.load()),this.mediaSource=void 0,this.isInit=!1,this.logger.debug("Detached video element and MSE")}else this.logger.debug("No video element to teardown.");else this.logger.debug("No video wrapper to teardown.")},e.prototype.removeUpdateEndCallback=function(e,t){this.sourceBuffers.hasOwnProperty(e)&&this.sourceBuffers[e].buffer.removeEventListener("updateend",t)},e.prototype.addUpdateEndCallback=function(e,t){this.sourceBuffers.hasOwnProperty(e)&&this.sourceBuffers[e].buffer.addEventListener("updateend",t)},e.prototype.addOneUpdateEndCallback=function(e,t){return this.sourceBuffers[e].onUpdateEnded().catch((function(){})).then(t)},e.prototype.addSourceOpenCallback=function(e){this.mediaSource&&this.mediaSource.addEventListener("sourceopen",e)},e.prototype.removeSourceOpenCallback=function(e){this.mediaSource&&this.mediaSource.removeEventListener("sourceopen",e)},e.prototype.addOneSourceOpenCallback=function(e){var t=this,r=function(n){t.removeSourceOpenCallback(r),e(n)};this.addSourceOpenCallback(r)},e.prototype.isAnyBufferUpdating=function(){var e=this;return 0!==Object.keys(this.sourceBuffers).filter((function(t){return e.sourceBuffers[t].buffer.updating})).length},e.prototype.setDuration=function(e){var t=this;this.queueActionOnBuffers((function(){if(t.mediaSource){var r=(0,E.isNumber)(e)&&e>0?e:1/0;r===1/0&&t.context.settings.MAX_SAFE_MSE_DURATION!==1/0&&(0,E.isNumber)(t.context.settings.MAX_SAFE_MSE_DURATION)&&t.context.settings.MAX_SAFE_MSE_DURATION>0&&(r=t.context.settings.MAX_SAFE_MSE_DURATION),t.mediaSource.duration!==r&&(t.isMediaSourceOpen()?(t.logger.debug("Setting video duration on MSE: ".concat(t.mediaSource.duration," -> ").concat(r)),t.mediaSource.duration=r):t.logger.debug("Skipping setDuration (".concat(t.mediaSource.duration," -> ").concat(r,"), MSE is not open")))}else t.logger.debug("Skipping setDuration (".concat(e,"), MSE is gone"))})).catch((function(e){t.logger.debug("Could not set the duration of the MSE!",e)}))},e.prototype.getDuration=function(){return this.mediaSource&&!isNaN(this.mediaSource.duration)?this.mediaSource.duration:0},e.prototype.isInitialized=function(){return this.isInit},Object.defineProperty(e.prototype,"readyState",{get:function(){return this.mediaSource?this.mediaSource.readyState:void 0},enumerable:!1,configurable:!0}),e.prototype.isBufferRemovalPending=function(){var e=this;return Object.keys(this.sourceBuffers).some((function(t){return e.sourceBuffers[t].removalPending}))},e.prototype.getRangePendingRemoval=function(e){var t,r,n;return null!==(n=null===(r=null===(t=this.sourceBuffers)||void 0===t?void 0:t[e])||void 0===r?void 0:r.rangePendingRemoval)&&void 0!==n?n:void 0},e.prototype.getBufferSizeInSeconds=function(e){return this.getBufferedRanges(e).reduce((function(e,t){return e+(t.end-t.start)}),0)},e.prototype.addToBuffer=function(e){var t=this,r=e.getMimeType();return this.queueActionOnBuffer(r,(function(){if(!t.mediaSource)return t.logger.debug("Could not queue action on SourceBuffer: MediaSource has been destroyed"),Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.FAILURE);if(t.mediaSource.readyState!==i.open)return t.logger.warn("MediaSource is not open (readyState="+t.mediaSource.readyState+")"),Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.FAILURE);if(!t.sourceBuffers.hasOwnProperty(r))return t.logger.log("sourceBuffers has no entry for "+r),Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.FAILURE);var n=e.getRepresentationId().representationId;try{t.logger.debug("[MSEWrapper][".concat(r,"][").concat(n,"] Adding segment to SourceBuffer [").concat(e.getUrl(),"]"));var a=t.sourceBuffers[r];return a.buffer.appendBuffer(e.getData()),a.onUpdateEnded()}catch(i){return t.logger.debug("[MSEWrapper][".concat(r,"][").concat(n,"] appendBuffer() failed"),i),i.name&&"QuotaExceededError"===i.name?(t.logger.debug("QuotaExceededError, trying to remove old data from buffers..."),t.context.store.dispatch((0,g.setBufferMaxSize)(r,t.getBufferSizeInSeconds(r))),Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.QUOTA_EXCEEDED)):(t.video&&t.video.error||t.context.eventHandler.fireError(new u.PlayerError(s.ErrorCode.UNKNOWN,{exception:i,codec:e.getCodec(),mimeType:r,segmentUrl:e.getUrl()},"Unexpected error while attempting to append a segment to the SourceBuffer.")),t.logger.debug("[MSEWrapper][".concat(r,"][").concat(n,"] Failed appending segment to SourceBuffer"),i),Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.FAILURE))}}))},e.prototype.endOfStream=function(){var e=this;return this.queueActionOnBuffers((function(){var t;e.isMediaSourceOpen()?(e.logger.debug("Signaled mse end of stream"),null===(t=e.mediaSource)||void 0===t||t.endOfStream()):e.logger.debug("Skipped signaling EOS as the mse is not open")})).catch((function(t){e.logger.debug("Unable to signal EOS on media source",t)}))},e.prototype.waitForBuffers=function(){var e=Object.keys(this.sourceBuffers).map(this.waitForBuffer);return this.mseReady().then((function(){return Promise.all(e).then((function(){}))}))},e.prototype.queueActionOnBuffers=function(e){var t=this,r=new Promise((function(e,n){var i=Object.keys(t.sourceBuffers).map((function(e){var n=t.queueActionOnBuffer(e,(function(){}));return t.queueActionOnBuffer(e,(function(){return r})).then().catch(),n}));Promise.all(i).then((function(){return e()}),n)})).then((function(){return e()})).then((function(){return Promise.all(Object.keys(t.sourceBuffers).map((function(e){return t.sourceBuffers[e].ready()}))).then((function(){}))}));return r},e.prototype.queueActionOnBuffer=function(e,t){return this.sourceBuffers.hasOwnProperty(e)?this.sourceBuffers[e].queueAction(t):Promise.reject("No SourceBuffer for '".concat(e,"' available"))},e.prototype.rejectMediaSourceReady=function(e){var t,r=this;return null===(t=this.mediaSourceReadyDeferred)||void 0===t||t.reject(e),this.mediaSourceReadyPromise?this.mediaSourceReadyPromise.then((function(){r.mediaSourceReadyDeferred=void 0})):Promise.reject(e)},e.prototype.mseReady=function(){var e,t=this;if(this.mediaSourceReadyPromise)return this.mediaSourceReadyPromise;this.mediaSourceReadyDeferred=new v.Deferred,this.mediaSourceReadyPromise=this.mediaSourceReadyDeferred.promise;var r=function(e){var r;i(),null===(r=t.mediaSourceReadyDeferred)||void 0===r||r.reject(e)},n=function(){var e;i(),t.isInit=!0,t.isUsingManagedMediaSource||(t.shouldContinueStreaming=!0),null===(e=t.mediaSourceReadyDeferred)||void 0===e||e.resolve()},i=function(){t.mediaSource&&(t.mediaSource.removeEventListener("error",r),t.mediaSource.removeEventListener("sourceopen",n),t.mediaSource.removeEventListener("webkitsourceopen",n))};if(!this.video)return this.rejectMediaSourceReady("Could not set source to video element");if(!this.mediaSource)return this.rejectMediaSourceReady("mediaSource must not be null or undefined");if(this.mediaSource.addEventListener("error",r),this.mediaSource.addEventListener("sourceopen",n,!1),this.mediaSource.addEventListener("webkitsourceopen",n,!1),this.isUsingManagedMediaSource&&(this.video.disableRemotePlayback=!0,this.mediaSource.addEventListener("startstreaming",this.startStreaming),this.mediaSource.addEventListener("endstreaming",this.endStreaming),this.mediaSource.addEventListener("bufferedchange",this.bufferedChange)),window&&window.URL&&"function"==typeof window.URL.createObjectURL){var o=window.URL.createObjectURL(this.mediaSource);this.video.src=o,null===(e=(0,p.getSourceStore)(this.context))||void 0===e||e.dispatch((0,l.setMseObjectUrl)(o))}else this.logger.debug("Could not use window.URL.createObjectURL(mediaSource) as the browser claims the function does not exist."),this.mediaSourceReadyDeferred.reject("Could not create Object URL");return this.mediaSourceReadyPromise.then((function(){t.mediaSourceReadyDeferred=void 0}))},Object.defineProperty(e.prototype,"shouldContinueBuffering",{get:function(){return this.shouldContinueStreaming},enumerable:!1,configurable:!0}),e.prototype.createNewMSE=function(){var e=this;return this.mediaSource?this.tearDownMediaSource().then((function(){return e.setNewMediaSource()})):this.teardownInProgressPromise.then((function(){return e.setNewMediaSource()}))},e.prototype.setNewMediaSource=function(){return this.isUsingManagedMediaSource=(0,R.shouldUseManagedMediaSource)(this.context.settings.PREFER_MANAGED_MEDIA_SOURCE),this.isUsingManagedMediaSource?(this.logger.debug("[MSEWrapper] Using ManagedMediaSource"),this.mediaSource=new window.ManagedMediaSource):(this.logger.debug("[MSEWrapper] Using MediaSource"),this.mediaSource=new window.MediaSource),this.mseReady()},e.prototype.isChangeTypeSupported=function(){var e,t,r=(0,m.getCapabilities)(),n=r.isWebOS&&r[h.CapabilityKey.webOSChromiumVersion]>=S.WEBOS_2021_CHROMIUM_VERSION;return!!(null===(t=null===(e=window.SourceBuffer)||void 0===e?void 0:e.prototype)||void 0===t?void 0:t.changeType)&&!(0,m.getCapabilities)().isTizen&&!n},e.prototype.updateSourceBufferKey=function(e){var t=this,r=y.MimeTypeHelper.getMediaType(e),n=Object.keys(this.sourceBuffers).find((function(n){var i=y.MimeTypeHelper.getMediaType(n);return t.sourceBuffers.hasOwnProperty(n)&&i===r&&n!==e}));n&&(this.sourceBuffers[e]=this.sourceBuffers[n],delete this.sourceBuffers[n])},e.prototype.changeBufferType=function(e,t){var r=this;return this.updateSourceBufferKey(e),this.queueActionOnBuffer(e,(function(){r.sourceBuffers[e].changeType("".concat(e,"; codecs=").concat(t)),r.context.logger.debug("Changed ".concat(e," SourceBuffer type with codec=").concat(t))}))},e.prototype.dispose=function(){var e=this;this.tearDownMediaSource().then((function(){e.video=void 0}))},e}();function P(e){for(var t=[],r=0;r<(null==e?void 0:e.length);r++){var n="[".concat(e.start(r)," - ").concat(e.end(r),"]");t.push(n)}return t.toString()}t.MSEWrapper=C},63916:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.LinkedListQueue=void 0;var r=function(){function e(e){this.value=e}return e}(),n=function(){function e(){this.count=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this.count},enumerable:!1,configurable:!0}),e.prototype.dequeue=function(){if(0!==this.count){var e=this.last;return this.last=null==e?void 0:e.previous,e&&(e.previous=void 0,e.next=void 0),this.count--,null==e?void 0:e.value}},e.prototype.enqueue=function(e){if(0===this.count){var t=new r(e);this.first=t,this.last=t}else{var n=this.first;this.first=new r(e),this.first.next=n,n&&(n.previous=this.first)}this.count++},e.prototype.isEmpty=function(){return 0===this.length},e}();t.LinkedListQueue=n},72566:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.findNextRangeForGap=i;var n=r(89413);function i(e,t){for(var r=!1,i=1/0,o=0;o<t.length;o++){var s=t.start(o),u=t.end(o),a=e+2*n.Ranges.TIME_FUDGE_FACTOR;if(r&&!isFinite(i)){i=s;break}s<e&&u>e&&a>u&&(r=!0)}return{isNearEndOfBufferedRange:r,nextRangeStart:i}}},73904:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getMseObjectUrl=void 0;var r=function(e){var t,r;return null!==(r=null===(t=null==e?void 0:e.renderer)||void 0===t?void 0:t.mseObjectUrl)&&void 0!==r?r:""};t.getMseObjectUrl=r},76316:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.VideoEventHandler=void 0;var r=function(){function e(e){var t=this;this.addTimeToEvent=function(e){return e.time=t.video&&t.video.currentTime||0,e},this.video=e}return e.prototype.on=function(e,t){this.video&&this.video.addEventListener(e,t,this.addTimeToEvent)},e.prototype.off=function(e,t){this.video&&this.video.removeEventListener(e,t)},e.prototype.fire=function(e,t){this.video&&this.video.eventHandler.triggerEvent(e,t)},e.prototype.dispose=function(){},e}();t.VideoEventHandler=r},89413:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Ranges=void 0;var r=function(){function e(){}return e.filterRanges=function(t,r){var n=[];if(t&&t.length)for(var i=0;i<t.length;i++)r(t.start(i),t.end(i))&&n.push([t.start(i),t.end(i)]);return e.createTimeRanges(n)},e.createTimeRangesObj=function(e){return void 0===e||0===e.length?{length:0,start:function(){return 0},end:function(){return 0}}:{length:e.length,start:function(t){return e[t][0]},end:function(t){return e[t][1]}}},e.createTimeRanges=function(t,r){return Array.isArray(t)?e.createTimeRangesObj(t):void 0===t||void 0===r?e.createTimeRangesObj():e.createTimeRangesObj([[t,r]])},e.findNextRange=function(t,r){return e.filterRanges(t,(function(t,n){return t-e.TIME_FUDGE_FACTOR>=r&&n-t>e.MIN_RANGE_DURATION}))},e.findGaps=function(t,r){if(t.length<2)return e.createTimeRanges();for(var n=[],i=1;i<t.length;i++){var o=t.end(i-1),s=t.start(i);s-o>r&&n.push([o,s])}return e.createTimeRanges(n)},e.findRangeForTime=function(t,r){return e.filterRanges(t,(function(e,t){return e<=r&&t>=r}))},e.TIME_FUDGE_FACTOR=1/30,e.MIN_RANGE_DURATION=.2,e}();t.Ranges=r},93159:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.GapHandler=void 0;var n=r(62510),i=r(11399),o=r(27517),s=r(58975),u=r(13533),a=r(79814),d=r(26905),c=r(46462),f=r(72566),h=r(89413),g=[c.MediaElementEvent.seeking,c.MediaElementEvent.seeked,c.MediaElementEvent.pause,c.MediaElementEvent.playing,c.MediaElementEvent.error],l=5,p=10,m=250,v=.01,y=function(){function e(e){var t,r,i,u,a,d=this;this.isExecutingGapSkipping=!1,this.play=function(){d.lastPlayEventTimestamp=Date.now()},this.checkIfWaiting=function(){if(!d.context.isDisposed&&void 0!==d.video){var e=d.video.paused,t=d.video.seeking,r=d.isAtEndOfBufferedRange(d.video.currentTime),n=d.hasRecentlyTrackedTimeupdateEvent(),i=d.hasRecentlyStartedPlayback();e||t&&!r||n||(d.consecutiveWaiting++,i||d.waiting())}},this.waiting=function(){var e,t=d.context.store.getState();if((0,o.isInstanceState)(t)&&d.video){var r=(0,s.getPlayerState)(t);((0,s.getIsSeeking)(r)||(null===(e=d.video)||void 0===e?void 0:e.seeking))&&!d.isAtEndOfBufferedRange(d.video.currentTime)||d.setTimer()}},this.stalled=function(){d.setTimer()},this.onSeek=function(){d.gapAhead=void 0},this.timeupdate=function(){if(d.lastTimeupdate=Date.now(),d.video&&!d.video.paused&&!d.video.seeking){d.hasPlayedAnythingYet=!0;var e=d.context.renderer.getCurrentTime(!0);if(d.shouldSkipGap(e))d.skipTheGapProactively();else{var t=d.context.renderer.getVideoElementBufferedRanges();if(t.length>1&&!d.gapAhead){var r=d.context.settings.MIN_SIZE_FOR_GAP_SKIPPING;d.gapAhead=T(t,e,r)}else d.shouldTriggerWaitingEvent(e)?(d.consecutiveUpdates++,d.waiting()):e===d.lastRecordedTime?d.consecutiveUpdates++:(d.consecutiveUpdates=0,d.consecutiveWaiting=0,d.lastRecordedTime=e)}}},this.cancelTimer=function(){d.consecutiveUpdates=0,d.consecutiveWaiting=0,clearTimeout(d.timer),d.timer=-1},this.skipTheGap=function(e){if(d.video&&!d.isExecutingGapSkipping){var t=d.context.renderer.getVideoElementBufferedRanges().sort((function(e,t){return e.start-t.start})),r=d.context.renderer.getCurrentTime(!0),n=M(t,r);if(d.consecutiveUpdates=0,d.consecutiveWaiting=0,d.timer=-1,void 0!==n&&r===e){var i=n.start;d.context.logger.debug("Skipping gap from ".concat(r," to ").concat(i)),d.executeGapSkipping(r,i)}}},this.context=e,this.video=e.videoElement,this.lastTimeupdate=0,this.consecutiveUpdates=0,this.consecutiveWaiting=0,this.lastPlayEventTimestamp=0,this.hasPlayedAnythingYet=!1,this.gapAhead=void 0,null===(t=this.video)||void 0===t||t.addEventListener(c.MediaElementEvent.waiting,this.waiting),null===(r=this.video)||void 0===r||r.addEventListener(c.MediaElementEvent.stalled,this.stalled),null===(i=this.video)||void 0===i||i.addEventListener(c.MediaElementEvent.timeupdate,this.timeupdate),null===(u=this.video)||void 0===u||u.addEventListener(c.MediaElementEvent.play,this.play),this.context.eventHandler.on(n.PlayerEvent.Seek,this.onSeek);for(var f=0,h=g;f<h.length;f++){var l=h[f];null===(a=this.video)||void 0===a||a.addEventListener(l,this.cancelTimer)}this.timeupdateIntervalId=window.setInterval(this.checkIfWaiting,m)}return e.prototype.hasRecentlyStartedPlayback=function(){return Date.now()-this.lastPlayEventTimestamp<2*m},e.prototype.hasRecentlyTrackedTimeupdateEvent=function(){return Date.now()-this.lastTimeupdate<=1e3},e.prototype.shouldSkipGap=function(e){return!(!this.gapAhead||this.isExecutingGapSkipping)&&(S(e,this.gapAhead)||E(this.context.settings.PROACTIVE_GAP_SKIP_DISTANCE_SECONDS,e,this.gapAhead)||b(e,this.gapAhead))},e.prototype.shouldTriggerWaitingEvent=function(e){return this.consecutiveUpdates===l&&e===this.lastRecordedTime},e.prototype.skipTheGapProactively=function(){if(this.video&&this.gapAhead){var e=this.context.renderer.getCurrentTime(!0),t=Math.max(e+v,this.gapAhead.end);this.context.logger.debug("Skipping gap proactively from ".concat(e," to ").concat(t)),this.executeGapSkipping(e,t)}},e.prototype.executeGapSkipping=function(e,t){var r=this;this.isExecutingGapSkipping=!0,this.gapAhead=void 0,this.maybeRemoveVideoBufferData(e,t).then((function(){return r.context.renderer.setCurrentTime(t+h.Ranges.TIME_FUDGE_FACTOR)})).then((function(t){return r.context.logger.debug("Jumped buffer gap from ".concat(e," to ").concat(t))})).catch((function(n){return r.context.logger.debug("Failed to skip buffer gap from ".concat(e," to ").concat(t),n)})).finally((function(){r.isExecutingGapSkipping=!1,r.gapAhead=void 0}))},e.prototype.maybeRemoveVideoBufferData=function(e,t){if(!(0,u.getCapabilities)().isTizen)return Promise.resolve();var r=this.getVideoMimeType();if(void 0===r)return Promise.resolve();var n=M(this.context.renderer.getBufferedRanges(r).sort((function(e,t){return e.start-t.start})),e);return t!==(null==n?void 0:n.start)?(this.context.logger.debug("Clearing video data from ".concat(e," to ").concat(t," to ensure gap can be skipped")),this.context.renderer.removeData(r,e,t)):Promise.resolve()},e.prototype.getVideoMimeType=function(){var e;return null===(e=this.context.segmentInfoService)||void 0===e?void 0:e.getSegmentControllerMimeTypes().find((function(e){return a.MimeTypeHelper.isVideo(e)}))},e.prototype.gapFromVideoUnderflow=function(e,t,r){for(var n=h.Ranges.findGaps(e,r),i=0;i<n.length;i++){var o=n.start(i),s=n.end(i);if(t-o<4&&t-o>2)return{start:o,end:s}}},e.prototype.setTimer=function(){if(!(this.timer>-1)&&this.hasPlayedAnythingYet&&this.video){var e=this.video.buffered,t=this.video.currentTime,r=h.Ranges.findNextRange(e,t),n=this.context.settings.MIN_SIZE_FOR_GAP_SKIPPING;if(0===r.length){var i=this.gapFromVideoUnderflow(e,t,n);if(i)return this.context.logger.debug("Encountered a gap in video from ".concat(i.start," to ").concat(i.end,". Seeking to ").concat(t,"...")),this.consecutiveWaiting=0,void this.setCurrentTime(t);var o=(0,f.findNextRangeForGap)(t,e),s=o.isNearEndOfBufferedRange,u=o.nextRangeStart;return s&&isFinite(u)?(this.context.logger.debug("Playback stalled because of a very small gap. Seeking to ".concat(u,"...")),this.consecutiveWaiting=0,void this.setCurrentTime(u)):void(this.consecutiveWaiting>p&&(this.consecutiveWaiting=0,this.triggerCurrentTimeNotAdvancing(t,this.video)))}if(!R(e,t,n)){var a=r.start(0)-t;this.context.logger.debug("setTimer: stopped at: ".concat(t,", setting timer for: ").concat(a,", seeking to: ").concat(r.start(0))),this.timer=window.setTimeout(this.skipTheGap.bind(this),a,this.context.renderer.getCurrentTime(!0))}}},e.prototype.triggerCurrentTimeNotAdvancing=function(e,t){this.context.logger.debug("currentTime not advancing from ".concat(e," although the video element is neither paused nor seeking")),this.maybePokeVideoElement(e,t),t.eventHandler.triggerEvent(c.MediaElementEvent.currenttimenotadvancing)},e.prototype.maybePokeVideoElement=function(e,t){if(e>t.duration&&!(0,u.getCapabilities)().isSafari)this.setCurrentTime(t.duration);else if(this.context.settings.QJY_BROWSER_WORKAROUND||(0,u.getCapabilities)().isTizen)this.setCurrentTime(e+v);else{var r=(0,u.getCapabilities)()[i.CapabilityKey.isPlayStation5];this.setCurrentTime(e,r)}},e.prototype.isAtEndOfBufferedRange=function(e){if(!this.video)return!1;for(var t=this.video.buffered,r=0;r<t.length;r++)if(t.end(r)===e)return!0;return!1},e.prototype.shutdown=function(){var e,t,r,i,o;null===(e=this.video)||void 0===e||e.removeEventListener(c.MediaElementEvent.waiting,this.waiting),null===(t=this.video)||void 0===t||t.removeEventListener(c.MediaElementEvent.stalled,this.stalled),null===(r=this.video)||void 0===r||r.removeEventListener(c.MediaElementEvent.timeupdate,this.timeupdate),null===(i=this.video)||void 0===i||i.removeEventListener(c.MediaElementEvent.play,this.play),this.context.eventHandler.off(n.PlayerEvent.Seek,this.onSeek);for(var s=0,u=g;s<u.length;s++){var a=u[s];null===(o=this.video)||void 0===o||o.removeEventListener(a,this.cancelTimer)}clearInterval(this.timeupdateIntervalId),this.cancelTimer()},e.prototype.setCurrentTime=function(e,t){if(void 0===t&&(t=!1),this.video&&(this.video.currentTime!==e||t))try{this.video.currentTime=e}catch(t){this.context.logger.debug("GapHandler failed to set currentTime on video element to ".concat(e),t)}},e}();function S(e,t){return e>t.start&&e<t.end}function E(e,t,r){return!(e<=0)&&(r.start>t&&r.start-e<t)}function b(e,t){return e>t.end&&(0,d.shouldResetCurrentTimeAfterBufferUnderrun)()}function T(e,t,r){for(var n=0;n<e.length-1;n++){var i=e[n].end,o=e[n+1].start;if(i>t&&o>i&&o-i>=r)return{start:i,end:o}}}function M(e,t){return e.find((function(e){return e.start-h.Ranges.TIME_FUDGE_FACTOR>=t}))}function R(e,t,r){if(0===r)return!1;var n=h.Ranges.findNextRange(e,t);if(0===n.length)return!1;var i=h.Ranges.findRangeForTime(e,t),o=i.length?i.end(0):t;return n.start(0)-o<r}t.GapHandler=y},98758:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.TechnologyChecker=void 0;var n=r(33696),i=function(){function e(){}return e.prototype.getSupportedTechnologies=function(){return[{player:n.PlayerType.Html5,streaming:n.StreamType.Dash},{player:n.PlayerType.Html5,streaming:n.StreamType.Hls},{player:n.PlayerType.Html5,streaming:n.StreamType.Smooth}]},e}();t.TechnologyChecker=i}},function(e){return function(t){return e(e.s=t)}(27838)}])}));
})();
