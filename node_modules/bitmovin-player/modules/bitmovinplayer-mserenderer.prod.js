/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
"use strict";!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.mserenderer=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player.mserenderer=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[737],{16838:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.LinkedListQueue=void 0;var r=function(){function e(e){this.value=e}return e}(),n=function(){function e(){this.count=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this.count},enumerable:!1,configurable:!0}),e.prototype.dequeue=function(){if(0!==this.count){var e=this.last;return this.last=null==e?void 0:e.previous,e&&(e.previous=void 0,e.next=void 0),this.count--,null==e?void 0:e.value}},e.prototype.enqueue=function(e){if(0===this.count){var t=new r(e);this.first=t,this.last=t}else{var n=this.first;this.first=new r(e),this.first.next=n,n&&(n.previous=this.first)}this.count++},e.prototype.isEmpty=function(){return 0===this.length},e}();t.LinkedListQueue=n},19912:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.findNextRangeForGap=i;var n=r(81963);function i(e,t){for(var r=!1,i=1/0,o=0;o<t.length;o++){var s=t.start(o),u=t.end(o),a=e+2*n.Ranges.TIME_FUDGE_FACTOR;if(r&&!isFinite(i)){i=s;break}s<e&&u>e&&a>u&&(r=!0)}return{isNearEndOfBufferedRange:r,nextRangeStart:i}}},38867:function(e,t,r){var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.MSEWrapper=t.MediaSourceReadyState=void 0;var i,o=r(16072),s=r(81668),u=r(17854),a=r(4792),d=r(65622),c=r(33964),f=(r(90681),r(20561)),h=r(98231),p=r(72137),m=r(78225),v=r(84815),l=r(10395),g=r(40060),y=r(69837),S=r(58974),E=r(54668),T=r(77822),R=r(98563),M=r(79902);!function(e){e.closed="closed",e.open="open",e.ended="ended"}(i||(t.MediaSourceReadyState=i={}));var P=function(){function e(e){var t=this;this.postBufferRemoval=function(e,t,r){},this.waitForBuffer=function(e){return t.sourceBuffers.hasOwnProperty(e)?t.mseReady().then((function(){return t.sourceBuffers[e].ready()})):Promise.reject("No SourceBuffer for '".concat(e,"' available"))},this.startStreaming=function(){t.shouldContinueStreaming=!0},this.endStreaming=function(){t.shouldContinueStreaming=!1},this.bufferedChange=function(e){0},e.videoElement&&(this.context=e,this.logger=e.logger,this.video=e.videoElement,this.sourceBuffers={},this.isInit=!1,this.teardownInProgressPromise=Promise.resolve(),this.shouldContinueStreaming=!0)}return e.prototype.getBufferedRanges=function(e){return this.sourceBuffers.hasOwnProperty(e)?this.sourceBuffers[e].bufferedRanges:[]},e.prototype.printDetailedBufferRange=function(e,t){var r;try{r=e.buffered}catch(e){return}if(0!==r.length)for(var n=0;n<r.length;n++);},e.prototype.getBufferStartTime=function(){var e=this,t=Object.keys(this.sourceBuffers).map((function(t){return 0===e.sourceBuffers[t].bufferedRanges.length?0:e.sourceBuffers[t].bufferedRanges[0].start}));return Math.max.apply(null,t)},e.prototype.addBuffer=function(e,t,r){if(this.mediaSource){if(this.isInit&&!this.sourceBuffers.hasOwnProperty(e))try{var n=this.mediaSource.addSourceBuffer(e+"; codecs="+t);this.sourceBuffers[e]=new R.SourceBufferWrapper(n,(function(e){r(e)}))}catch(e){return void this.context.eventHandler.dispatchEvent(c.PlayerEvent.Warning,new a.PlayerWarning(d.WarningCode.SOURCE_CODEC_OR_FILE_FORMAT_NOT_SUPPORTED))}return isNaN(this.mediaSource.duration)||(this.sourceBuffers[e].appendWindowEnd=this.mediaSource.duration),this.sourceBuffers[e].buffer}},e.prototype.updateTimestampOffset=function(e,t){var r=this.sourceBuffers[e];r&&(r.buffer.timestampOffset=t)},e.prototype.setTimestampOffset=function(e,t){return this.sourceBuffers.hasOwnProperty(e)?((0,S.isDefined)(t)&&!isNaN(t)||(t=0),this.queueTimestampOffsetUpdate(e,t)):Promise.resolve()},e.prototype.queueTimestampOffsetUpdate=function(e,t){var r=this;return this.queueActionOnBuffer(e,(function(){return r.updateTimestampOffset(e,t)})).catch((function(e){}))},e.prototype.getBufferRangeStart=function(e,t){return t||e.start(0)},e.prototype.getBufferRangeEnd=function(e,t){return t||e.end(e.length-1)},e.prototype.getBufferRemovalRange=function(e,t,r){var n=this.sourceBuffers[e].buffer.buffered;return{start:this.getBufferRangeStart(n,t),end:this.getBufferRangeEnd(n,r)}},e.prototype.preBufferRemoval=function(e,t,r){},e.prototype.removeBufferRange=function(e,t,r){this.preBufferRemoval(e,t,r),this.sourceBuffers[e].buffer.remove(t,r)},e.prototype.removeFromBuffer=function(e,t,r,n){var o=this;return void 0===n&&(n=this.postBufferRemoval),this.queueActionOnBuffer(e,(function(){var s,u,f;try{if(o.video&&o.video.currentTime>=0&&(null===(f=null===(u=null===(s=o.sourceBuffers[e])||void 0===s?void 0:s.buffer)||void 0===u?void 0:u.buffered)||void 0===f?void 0:f.length)>0){var h=o.getBufferRemovalRange(e,t,r),p=h.start,m=h.end;if(p>=0&&m>p){if(o.sourceBuffers[e].removalPending=!0,o.sourceBuffers[e].rangePendingRemoval={start:p,end:m},!o.mediaSource||o.mediaSource.readyState===i.open){var v=o.sourceBuffers[e].onUpdateEnded().then((function(){o.sourceBuffers[e]&&(o.sourceBuffers[e].rangePendingRemoval=null,o.sourceBuffers[e].removalPending=!1,n(e,p,m))}));return o.removeBufferRange(e,p,m),v}o.addOneSourceOpenCallback((function(){o.removeFromBuffer(e,p,m,n).catch((function(e){}))}))}}}catch(t){var l="".concat(d.WarningCode[d.WarningCode.PLAYBACK_WARNING],": Buffer clearing failed for ").concat(e,". Exception: ").concat(JSON.stringify(t));o.context.eventHandler.dispatchEvent(c.PlayerEvent.Warning,new a.PlayerWarning(d.WarningCode.PLAYBACK_WARNING,l))}return Promise.resolve()}))},e.prototype.removeBuffer=function(e){var t=this,r=Promise.resolve(),n=this.sourceBuffers[e];if(!n||!this.mediaSource)return r;if(this.isMediaSourceOpen())try{null==n||n.buffer.abort()}catch(e){return Promise.resolve()}if(g.MimeTypeHelper.isSubtitle(e))r=r.then((function(){return t.removeFromBuffer(e).catch((function(e){}))}));else try{this.mediaSource&&this.mediaSource.removeSourceBuffer(n.buffer)}catch(e){}return this.deleteSourceBuffer(e),r},e.prototype.deleteSourceBuffer=function(e){this.sourceBuffers[e]&&(this.sourceBuffers[e].dispose(),delete this.sourceBuffers[e])},e.prototype.isMediaSourceOpen=function(){var e;return(null===(e=this.mediaSource)||void 0===e?void 0:e.readyState)===i.open},e.prototype.resetAllBuffers=function(){var e=this;if(!this.mediaSource)return Promise.resolve();var t=[];for(var r in this.sourceBuffers)this.sourceBuffers.hasOwnProperty(r)&&t.push(this.removeBuffer(r));return Promise.all(t).then((function(){e.sourceBuffers={}}))},e.prototype.tearDownMediaSource=function(){var e=this;return this.teardownInProgressPromise=this.teardownInProgressPromise.then((function(){return e.resetAllBuffers()})).then((function(){e.mediaSource&&e.detachVideoElementAndMSE()})),this.teardownInProgressPromise},e.prototype.detachVideoElementAndMSE=function(){var e;if(this.video&&null!==this.video.getWrappedElement()){var t=(0,T.getMseObjectUrl)((0,m.getSourceState)(this.context));if("function"==typeof(null===(e=null===window||void 0===window?void 0:window.URL)||void 0===e?void 0:e.revokeObjectURL)&&""!==t)try{window.URL.revokeObjectURL(t)}catch(e){}this.mediaSource&&(this.mediaSource.removeEventListener("startstreaming",this.startStreaming),this.mediaSource.removeEventListener("endstreaming",this.endStreaming),this.mediaSource.removeEventListener("bufferedchange",this.bufferedChange)),this.mediaSourceReadyDeferred&&this.mediaSourceReadyDeferred.reject("MediaSource is being destroyed."),this.mediaSourceReadyDeferred=void 0,this.mediaSourceReadyPromise=void 0,t===this.video.src&&(E.VideoElementUtil.removeSource(this.video),this.video.load()),this.mediaSource=void 0,this.isInit=!1}},e.prototype.removeUpdateEndCallback=function(e,t){this.sourceBuffers.hasOwnProperty(e)&&this.sourceBuffers[e].buffer.removeEventListener("updateend",t)},e.prototype.addUpdateEndCallback=function(e,t){this.sourceBuffers.hasOwnProperty(e)&&this.sourceBuffers[e].buffer.addEventListener("updateend",t)},e.prototype.addOneUpdateEndCallback=function(e,t){return this.sourceBuffers[e].onUpdateEnded().catch((function(){})).then(t)},e.prototype.addSourceOpenCallback=function(e){this.mediaSource&&this.mediaSource.addEventListener("sourceopen",e)},e.prototype.removeSourceOpenCallback=function(e){this.mediaSource&&this.mediaSource.removeEventListener("sourceopen",e)},e.prototype.addOneSourceOpenCallback=function(e){var t=this,r=function(n){t.removeSourceOpenCallback(r),e(n)};this.addSourceOpenCallback(r)},e.prototype.isAnyBufferUpdating=function(){var e=this;return 0!==Object.keys(this.sourceBuffers).filter((function(t){return e.sourceBuffers[t].buffer.updating})).length},e.prototype.setDuration=function(e){var t=this;this.queueActionOnBuffers((function(){if(t.mediaSource){var r=(0,S.isNumber)(e)&&e>0?e:1/0;r===1/0&&t.context.settings.MAX_SAFE_MSE_DURATION!==1/0&&(0,S.isNumber)(t.context.settings.MAX_SAFE_MSE_DURATION)&&t.context.settings.MAX_SAFE_MSE_DURATION>0&&(r=t.context.settings.MAX_SAFE_MSE_DURATION),t.mediaSource.duration!==r&&t.isMediaSourceOpen()&&(t.mediaSource.duration=r)}})).catch((function(e){}))},e.prototype.getDuration=function(){return this.mediaSource&&!isNaN(this.mediaSource.duration)?this.mediaSource.duration:0},e.prototype.isInitialized=function(){return this.isInit},Object.defineProperty(e.prototype,"readyState",{get:function(){return this.mediaSource?this.mediaSource.readyState:void 0},enumerable:!1,configurable:!0}),e.prototype.isBufferRemovalPending=function(){var e=this;return Object.keys(this.sourceBuffers).some((function(t){return e.sourceBuffers[t].removalPending}))},e.prototype.getRangePendingRemoval=function(e){var t,r,n;return null!==(n=null===(r=null===(t=this.sourceBuffers)||void 0===t?void 0:t[e])||void 0===r?void 0:r.rangePendingRemoval)&&void 0!==n?n:void 0},e.prototype.getBufferSizeInSeconds=function(e){return this.getBufferedRanges(e).reduce((function(e,t){return e+(t.end-t.start)}),0)},e.prototype.addToBuffer=function(e){var t=this,r=e.getMimeType();return this.queueActionOnBuffer(r,(function(){if(!t.mediaSource)return Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.FAILURE);if(t.mediaSource.readyState!==i.open)return Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.FAILURE);if(!t.sourceBuffers.hasOwnProperty(r))return Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.FAILURE);e.getRepresentationId().representationId;try{var n=t.sourceBuffers[r];return n.buffer.appendBuffer(e.getData()),n.onUpdateEnded()}catch(n){return n.name&&"QuotaExceededError"===n.name?(t.context.store.dispatch((0,h.setBufferMaxSize)(r,t.getBufferSizeInSeconds(r))),Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.QUOTA_EXCEEDED)):(t.video&&t.video.error||t.context.eventHandler.fireError(new u.PlayerError(s.ErrorCode.UNKNOWN,{exception:n,codec:e.getCodec(),mimeType:r,segmentUrl:e.getUrl()},"Unexpected error while attempting to append a segment to the SourceBuffer.")),Promise.reject(o.SOURCE_BUFFER_APPEND_STATUS.FAILURE))}}))},e.prototype.endOfStream=function(){var e=this;return this.queueActionOnBuffers((function(){var t;e.isMediaSourceOpen()&&(null===(t=e.mediaSource)||void 0===t||t.endOfStream())})).catch((function(e){}))},e.prototype.waitForBuffers=function(){var e=Object.keys(this.sourceBuffers).map(this.waitForBuffer);return this.mseReady().then((function(){return Promise.all(e).then((function(){}))}))},e.prototype.queueActionOnBuffers=function(e){var t=this,r=new Promise((function(e,n){var i=Object.keys(t.sourceBuffers).map((function(e){var n=t.queueActionOnBuffer(e,(function(){}));return t.queueActionOnBuffer(e,(function(){return r})).then().catch(),n}));Promise.all(i).then((function(){return e()}),n)})).then((function(){return e()})).then((function(){return Promise.all(Object.keys(t.sourceBuffers).map((function(e){return t.sourceBuffers[e].ready()}))).then((function(){}))}));return r},e.prototype.queueActionOnBuffer=function(e,t){return this.sourceBuffers.hasOwnProperty(e)?this.sourceBuffers[e].queueAction(t):Promise.reject("No SourceBuffer for '".concat(e,"' available"))},e.prototype.rejectMediaSourceReady=function(e){var t,r=this;return null===(t=this.mediaSourceReadyDeferred)||void 0===t||t.reject(e),this.mediaSourceReadyPromise?this.mediaSourceReadyPromise.then((function(){r.mediaSourceReadyDeferred=void 0})):Promise.reject(e)},e.prototype.mseReady=function(){var e,t=this;if(this.mediaSourceReadyPromise)return this.mediaSourceReadyPromise;this.mediaSourceReadyDeferred=new l.Deferred,this.mediaSourceReadyPromise=this.mediaSourceReadyDeferred.promise;var r=function(e){var r;i(),null===(r=t.mediaSourceReadyDeferred)||void 0===r||r.reject(e)},n=function(){var e;i(),t.isInit=!0,t.isUsingManagedMediaSource||(t.shouldContinueStreaming=!0),null===(e=t.mediaSourceReadyDeferred)||void 0===e||e.resolve()},i=function(){t.mediaSource&&(t.mediaSource.removeEventListener("error",r),t.mediaSource.removeEventListener("sourceopen",n),t.mediaSource.removeEventListener("webkitsourceopen",n))};if(!this.video)return this.rejectMediaSourceReady("Could not set source to video element");if(!this.mediaSource)return this.rejectMediaSourceReady("mediaSource must not be null or undefined");if(this.mediaSource.addEventListener("error",r),this.mediaSource.addEventListener("sourceopen",n,!1),this.mediaSource.addEventListener("webkitsourceopen",n,!1),this.isUsingManagedMediaSource&&(this.video.disableRemotePlayback=!0,this.mediaSource.addEventListener("startstreaming",this.startStreaming),this.mediaSource.addEventListener("endstreaming",this.endStreaming),this.mediaSource.addEventListener("bufferedchange",this.bufferedChange)),window&&window.URL&&"function"==typeof window.URL.createObjectURL){var o=window.URL.createObjectURL(this.mediaSource);this.video.src=o,null===(e=(0,m.getSourceStore)(this.context))||void 0===e||e.dispatch((0,p.setMseObjectUrl)(o))}else this.mediaSourceReadyDeferred.reject("Could not create Object URL");return this.mediaSourceReadyPromise.then((function(){t.mediaSourceReadyDeferred=void 0}))},Object.defineProperty(e.prototype,"shouldContinueBuffering",{get:function(){return this.shouldContinueStreaming},enumerable:!1,configurable:!0}),e.prototype.createNewMSE=function(){var e=this;return this.mediaSource?this.tearDownMediaSource().then((function(){return e.setNewMediaSource()})):this.teardownInProgressPromise.then((function(){return e.setNewMediaSource()}))},e.prototype.setNewMediaSource=function(){return this.isUsingManagedMediaSource=(0,M.shouldUseManagedMediaSource)(this.context.settings.PREFER_MANAGED_MEDIA_SOURCE),this.isUsingManagedMediaSource?this.mediaSource=new window.ManagedMediaSource:this.mediaSource=new window.MediaSource,this.mseReady()},e.prototype.isChangeTypeSupported=function(){var e,t,r=(0,v.getCapabilities)(),n=r.isWebOS&&r[f.CapabilityKey.webOSChromiumVersion]>=y.WEBOS_2021_CHROMIUM_VERSION;return!!(null===(t=null===(e=window.SourceBuffer)||void 0===e?void 0:e.prototype)||void 0===t?void 0:t.changeType)&&!(0,v.getCapabilities)().isTizen&&!n},e.prototype.updateSourceBufferKey=function(e){var t=this,r=g.MimeTypeHelper.getMediaType(e),n=Object.keys(this.sourceBuffers).find((function(n){var i=g.MimeTypeHelper.getMediaType(n);return t.sourceBuffers.hasOwnProperty(n)&&i===r&&n!==e}));n&&(this.sourceBuffers[e]=this.sourceBuffers[n],delete this.sourceBuffers[n])},e.prototype.changeBufferType=function(e,t){var r=this;return this.updateSourceBufferKey(e),this.queueActionOnBuffer(e,(function(){r.sourceBuffers[e].changeType("".concat(e,"; codecs=").concat(t))}))},e.prototype.dispose=function(){var e=this;this.tearDownMediaSource().then((function(){e.video=void 0}))},e}();t.MSEWrapper=P},60124:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.TechnologyChecker=void 0;var n=r(50930),i=function(){function e(){}return e.prototype.getSupportedTechnologies=function(){return[{player:n.PlayerType.Html5,streaming:n.StreamType.Dash},{player:n.PlayerType.Html5,streaming:n.StreamType.Hls},{player:n.PlayerType.Html5,streaming:n.StreamType.Smooth}]},e}();t.TechnologyChecker=i},67461:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.MSERenderer=void 0;var n=r(50238),i=r(16072),o=r(81668),s=r(17854),u=r(9851),a=r(33964),d=r(50930),c=r(53670),f=r(844),h=r(78225),p=r(87402),m=r(69303),v=r(10395),l=r(7239),g=r(21890),y=r(81210),S=r(40060),E=r(58974),T=r(98892),R=r(98231),M=r(12071),P=r(92347),C=r(96089),B=r(38867),O=r(79902),b=r(75294),D=function(){function e(e){var t=this;this.context=e,this.consecutiveErrorCount=0,this.waitingForSeekedPromise=null,this.onSeek=function(e){t.setCurrentTimeDelayedContext&&t.setCurrentTimeDelayedContext.time!==e.seekTarget&&Math.abs(t.setCurrentTimeDelayedContext.time-e.seekTarget)>=t.context.settings.GAP_TOLERANCE&&t.cancelSetCurrentTimeDelayed()},this.reOpenMse=function(){var e=t.segmentQueueMimeTypes;if(e.length<1)return Promise.resolve();var r=e.find((function(e){return t.timestampOffset.hasOwnProperty(e)}));return r?t.setTimestampOffset(r,t.timestampOffset[r]):t.setTimestampOffset(e[0],0)},this.firePictureInPictureEvent=function(){if(t.video){var e=t.video.isPictureInPicture()?d.ViewMode.PictureInPicture:d.ViewMode.Inline;t.context.store.dispatch((0,f.setPlayerViewMode)(e))}},this.resetConsecutiveErrorCountCallback=function(e){e.time&&e.time>0&&(t.consecutiveErrorCount=0)},this.postBufferRemoval=function(e,t,r){},this.memoizedIsTypeSupported=(0,y.memoize)(this.isTypeSupported.bind(this)),this.logger=e.logger,this.video=e.videoElement,this.settings=e.settings,this.readyPromise=Promise.resolve(),this.eventHandler=void 0,this.timestampOffset={},this.sourceBufferMimeCodecMap=new P.BufferMimeCodecMap(e.store,e.settings),this.savedCurrentTime=0,this.playerEventHandler=e.eventHandler,this.shutdownPromise=Promise.resolve(),this.init(!0)}return e.prototype.createMSEWrapper=function(){return new B.MSEWrapper(this.context)},e.prototype.createMse=function(e){return e?this.createMseSynchronously():this.createMseAsynchronously()},e.prototype.createMseAsynchronously=function(){var e=this;return function(){var t;return null===(t=e.mse)||void 0===t?void 0:t.createNewMSE()}},e.prototype.createMseSynchronously=function(){var e,t=null===(e=this.mse)||void 0===e?void 0:e.createNewMSE();return function(){return t}},e.prototype.init=function(e){this.segmentQueueProcessingPromise=Promise.resolve(),this.mse=this.createMSEWrapper(),this.readyPromise=this.readyPromise.catch((function(e){})).then(this.createMse(e)),this.needsInitialSeek=!0,this.segmentQueues={},this.setCurrentTimeContext=void 0,this.setCurrentTimeDelayedContext=void 0,this.quotaExceededMap=new Map,this.quotaExceededDeferredMap=new Map,this.gapHandler=new C.GapHandler(this.context),null==this.eventHandler&&this.video&&(this.eventHandler=new b.VideoEventHandler(this.video),this.eventHandler.on(T.MediaElementEvent.timeupdate,this.resetConsecutiveErrorCountCallback)),this.on(T.MediaElementEvent.webkitpresentationmodechanged,this.firePictureInPictureEvent),this.playerEventHandler.on(a.PlayerEvent.Seek,this.onSeek)},e.prototype.trackRendererError=function(e){this.consecutiveErrorCount++,this.consecutiveErrorCount>=this.settings.MAX_CONSECUTIVE_RENDERER_ERRORS&&this.context.eventHandler.fireError(new s.PlayerError(o.ErrorCode.PLAYBACK_VIDEO_DECODING_ERROR,{codec:e.getCodec(),mimeType:e.getMimeType(),segmentUrl:e.getUrl()},"Failed to append the segment to the buffer. The MSE has thrown an error."))},e.prototype.hasSourceBuffers=function(){return this.segmentQueueMimeTypes.length>0},e.prototype.hasDataInSourceBuffers=function(){var e=this.mse;return!(!e||!this.hasSourceBuffers())&&this.segmentQueueMimeTypes.every((function(t){return e.getBufferedRanges(t).length>0}))},e.prototype.updateRendererRangesInStore=function(e){var t;null===(t=(0,h.getSourceStore)(this.context))||void 0===t||t.dispatch((0,R.setRendererRanges)(e,this.getOverallBufferedRanges(e)))},e.prototype.addBuffer=function(e,t){var r,n=this;if(!this.mse)return!1;this.sourceBufferMimeCodecMap.add(e,t);var i=Boolean(this.mse.addBuffer(e,t,(function(){return n.updateRendererRangesInStore(e)})));return i&&(this.mse.addUpdateEndCallback(e,(function(){var t;return null===(t=n.setCurrentTimeDelayedContext)||void 0===t||t.reevaluateDataInBuffer(),n.quotaExceededMap.set(e,!1)})),this.segmentQueues[e]=[],null===(r=(0,h.getSourceStore)(this.context))||void 0===r||r.dispatch((0,R.setRendererRanges)(e,[]))),i},e.prototype.addDrmInitData=function(e){if(null==e?void 0:e.length){var t=this.drmService;t&&e.forEach((function(e){return t.addInitData(e)}))}},e.prototype.storeDrmInitDataFromManifest=function(e){var t=this;e&&this.context.serviceManager.maybeCall(u.ServiceName.ManifestService,(function(r){var n=r.getContentProtectionForAdaptationSet(e).flatMap((function(e){var t=c.KeySystemKind[e.name.toUpperCase()],r=e.schemeIdUri.replace("urn:uuid:","").replace(/-/g,"");return(e.pssh||[]).map((function(n,i){return{initData:e.psshData[i],initDataStr:n,systemID:e.schemeIdUri,systemIDraw:r,systemName:t,kid:[]}}))}));t.addDrmInitData(n)}),void 0,this.context.sourceContext.sourceIdentifier)},e.prototype.appendData=function(e){var t=this;return this.addDrmInitData(e.getDrmInitData()),this.addSegmentToSegmentQueue(e),this.segmentQueueProcessingPromise=this.segmentQueueProcessingPromise.catch((function(){})).then((function(){return t.processSegmentQueues()})),this.segmentQueueProcessingPromise},e.prototype.addSegmentToSegmentQueue=function(e){var t=e.getMimeType();this.segmentQueues[t]&&(this.segmentQueues[t].push(e),this.updateRendererRangesInStore(t))},e.prototype.getBufferedRanges=function(e){var t,r;return null!==(r=null===(t=this.mse)||void 0===t?void 0:t.getBufferedRanges(e))&&void 0!==r?r:[]},e.prototype.getVideoElementBufferedRanges=function(){return(0,n.getBufferedRanges)(this.video)},e.prototype.getOverallBufferedRanges=function(e){if(!this.mse||!this.segmentQueues.hasOwnProperty(e))return[];var t=n.BufferRangeHelper.getRangesFromQueue(this.segmentQueues[e],this.settings.GAP_TOLERANCE);return t=t.concat(this.getBufferedRanges(e)),t=n.BufferRangeHelper.mergeRanges(t,this.settings.GAP_TOLERANCE)},e.prototype.isDataBeingAppended=function(e){return!!this.mse&&(this.segmentQueues&&this.segmentQueues[e]&&this.segmentQueues[e].length>0||this.mse.isAnyBufferUpdating())},e.prototype.getRangesFromStore=function(e){var t=(0,h.getSourceState)(this.context);if(!t)return[];var r=(0,M.getBufferStateMap)(t);return r?(0,M.getRendererBufferedRanges)(r,e):[]},e.prototype.getEndOfBufferTime=function(){var e=this,t=[];if(this.segmentQueueMimeTypes.forEach((function(r){var n=e.getRangesFromStore(r);if(n.length>0){var i=n[n.length-1].end;t.push(i)}})),0!==t.length)return Math.min.apply(Math,t)},e.prototype.removeDataFromSegmentQueues=function(e,t,r){void 0===t&&(t=-1/0),void 0===r&&(r=1/0),this.segmentQueues.hasOwnProperty(e)&&(this.segmentQueues[e]=this.segmentQueues[e].filter((function(e){var n=e.getDuration(),i=e.getPlaybackTime();e.getRepresentationId().representationId;return e.isInit()||i<t||i+n>r})),this.updateRendererRangesInStore(e))},e.prototype.removeData=function(e,t,r){return this.mse?(void 0===t&&void 0===r&&this.saveCurrentTime(),this.removeDataFromSegmentQueues(e,t,r),this.mse.removeFromBuffer(e,t,r,this.postBufferRemoval)):Promise.reject()},e.prototype.getDroppedVideoFrames=function(){var e,t,r,n,i;return null!==(i=null!==(r=null===(t=null===(e=this.video)||void 0===e?void 0:e.getVideoPlaybackQuality())||void 0===t?void 0:t.droppedVideoFrames)&&void 0!==r?r:null===(n=this.video)||void 0===n?void 0:n.droppedVideoFrames)&&void 0!==i?i:0},e.prototype.setPlaybackSpeed=function(e){this.video&&!isNaN(e)&&e>0&&e!==this.video.playbackRate&&(this.video.playbackRate=e)},e.prototype.getPlaybackSpeed=function(){var e,t;return null!==(t=null===(e=this.video)||void 0===e?void 0:e.playbackRate)&&void 0!==t?t:1},e.prototype.setVolume=function(e){this.video&&!isNaN(e)&&(this.video.volume=Math.min(e/100,1))},e.prototype.getVolume=function(){var e,t;return 100*(null!==(t=null===(e=this.video)||void 0===e?void 0:e.volume)&&void 0!==t?t:1)},e.prototype.mute=function(){this.video&&(this.video.muted=!0)},e.prototype.unmute=function(){this.video&&(this.video.muted=!1)},e.prototype.isMuted=function(){var e,t;return null!==(t=null===(e=this.video)||void 0===e?void 0:e.muted)&&void 0!==t&&t},e.prototype.play=function(){var e=this;return this.video?this.video.play().catch((function(t){if((0,g.isSwitchingBufferBlocks)((0,h.getSourceStore)(e.context)))return e.ready();throw t})):Promise.resolve()},e.prototype.pause=function(){this.video&&this.video.pause()},e.prototype.end=function(){this.pause(),this.cancelSetCurrentTimeDelayed()},e.prototype.isPaused=function(){var e,t;return null===(t=null===(e=this.video)||void 0===e?void 0:e.paused)||void 0===t||t},e.prototype.setDuration=function(e){this.mse&&(e=Math.floor(1e4*e)/1e4,this.duration=e,this.mse.setDuration(e))},e.prototype.getDuration=function(){var e,t;return null!==(t=null===(e=this.video)||void 0===e?void 0:e.duration)&&void 0!==t?t:NaN},e.prototype.setCurrentTime=function(e){var t=this;if(!this.mse)return Promise.reject("MSE is not open");if(e=Math.ceil(100*e)/100,this.setCurrentTimeContext){if(this.setCurrentTimeContext.time===e)return this.setCurrentTimeContext.promise;this.cancelSetCurrentTimeDelayed()}var r=Promise.resolve();this.isMSEOpen()||this.isTimeInBuffer(e)||(r=this.reOpenMse());var n=r.then((function(){return t.setCurrentTimeInternal(e).catch((function(e){return t.getCurrentTime()})).finally((function(){return t.needsInitialSeek=!1}))}));return this.setCurrentTimeContext={time:e,isInitialSeek:this.needsInitialSeek,promise:n},n},e.prototype.getCurrentVideoTime=function(){var e,t;return null!==(t=null===(e=this.video)||void 0===e?void 0:e.currentTime)&&void 0!==t?t:0},e.prototype.getCurrentFallbackTime=function(){var e=this.setCurrentTimeContext;return e&&e.isInitialSeek&&-1!==e.time?e.time:0!==this.savedCurrentTime&&this.isVideoCurrentTimeUnreliable()?this.savedCurrentTime:(this.savedCurrentTime=0,this.getCurrentVideoTime())},e.prototype.isVideoCurrentTimeUnreliable=function(){var e;return!this.hasDataInSourceBuffers()&&!(null===(e=this.mse)||void 0===e?void 0:e.isBufferRemovalPending())||!(!(0,g.isSwitchingBufferBlocks)((0,h.getSourceStore)(this.context))&&this.isTimeInBuffer(this.getCurrentVideoTime()))},e.prototype.getCurrentTime=function(e){return void 0===e&&(e=!1),e?this.getCurrentVideoTime():this.getCurrentFallbackTime()},e.prototype.getSnapshotData=function(e,t){void 0===t&&(t=1);try{if(!this.video)return;var r=this.video.videoWidth,n=this.video.videoHeight;this.snapshotCanvas||(this.snapshotCanvas=document.createElement("canvas"),this.snapshotCanvas.id="snapshotHiddenCanvas"),this.snapshotCanvas.width=r,this.snapshotCanvas.height=n;var i=this.snapshotCanvas.getContext("2d");null==i||i.drawImage(this.video.getWrappedElement(),0,0,r,n);var o=void 0;return"image/jpeg"===e||"image/webp"===e?(t=Math.max(t,0),t=Math.min(t,1),o=this.snapshotCanvas.toDataURL(e,t)):o=this.snapshotCanvas.toDataURL(e,t),{height:n,width:r,data:o}}catch(e){return void(e&&e.message)}},e.prototype.getDrmShutdownPromise=function(){return this.drmService?this.drmService.shutdownPromise:Promise.resolve()},e.prototype.ready=function(){var e=this;return this.shutdownPromise.then((function(){return e.getDrmShutdownPromise()})).then((function(){return e.mse||e.init(!1),e.readyPromise}))},e.prototype.setTimestampOffset=function(e,t){var r=this;return this.mse?this.mse.setTimestampOffset(e,t).then((function(){r.timestampOffset[e]=t})):Promise.resolve()},e.prototype.shutdown=function(e){var t;if(!this.mse)return this.shutdownPromise;for(var r in this.waitingForSeekedPromise=null,e&&this.saveCurrentTime(),this.segmentQueues)this.segmentQueues.hasOwnProperty(r)&&(delete this.segmentQueues[r],null===(t=(0,h.getSourceStore)(this.context))||void 0===t||t.dispatch((0,R.setRendererRanges)(r,[])));var n=this.mse;return this.shutdownPromise=this.shutdownPromise.then((function(){return n.tearDownMediaSource()})).catch((function(e){})),this.mse=void 0,this.cancelSetCurrentTimeDelayed(),this.gapHandler&&(this.gapHandler.shutdown(),this.gapHandler=void 0),this.eventHandler&&!e&&(this.eventHandler.dispose(),this.eventHandler=void 0),this.quotaExceededMap.clear(),this.quotaExceededDeferredMap.clear(),this.shutdownPromise},e.prototype.release=function(){var e;for(var t in this.segmentQueues)this.segmentQueues.hasOwnProperty(t)&&null!==this.mse&&(delete this.segmentQueues[t],null===(e=(0,h.getSourceStore)(this.context))||void 0===e||e.dispatch((0,R.setRendererRanges)(t,[])));return this.sourceBufferMimeCodecMap.dispose(),this.mse&&this.mse.dispose(),this.readyPromise=Promise.resolve(),this.timestampOffset={},this.cancelSetCurrentTimeDelayed(),this.gapHandler&&(this.gapHandler.shutdown(),this.gapHandler=void 0),this.off(T.MediaElementEvent.webkitpresentationmodechanged,this.firePictureInPictureEvent),this.eventHandler&&(this.eventHandler.off(T.MediaElementEvent.timeupdate,this.resetConsecutiveErrorCountCallback),this.eventHandler.dispose(),this.eventHandler=void 0),this.waitingForSeekedPromise=null,this.playerEventHandler.off(a.PlayerEvent.Seek,this.onSeek),this.memoizedIsTypeSupported.invalidate(),this.getDrmShutdownPromise()},e.prototype.on=function(e,t){var r;null===(r=this.eventHandler)||void 0===r||r.on(e,t)},e.prototype.off=function(e,t){this.eventHandler&&this.eventHandler.off(e,t)},Object.defineProperty(e.prototype,"drmService",{get:function(){var e;return null===(e=this.context.serviceManager)||void 0===e?void 0:e.get(u.ServiceName.DrmService)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"segmentQueueMimeTypes",{get:function(){return Object.keys(this.segmentQueues)},enumerable:!1,configurable:!0}),e.prototype.setDrmConfig=function(e,t){return this.drmService?this.drmService.updateDrmConfig(e,t):Promise.reject(i.DrmSetupError.MISSING_CONFIGURATION)},e.prototype.isMediaTypeSupported=function(e,t){return this.memoizedIsTypeSupported("".concat(e,'; codecs="').concat(t,'"'))},e.prototype.isTypeSupported=function(e){return(0,O.shouldUseManagedMediaSource)(this.context.settings.PREFER_MANAGED_MEDIA_SOURCE)?window.ManagedMediaSource.isTypeSupported(e):"MediaSource"in window&&MediaSource.isTypeSupported(e)},e.prototype.cancelPendingEndOfStream=function(){this.setEndOfStreamDeferred&&(this.setEndOfStreamDeferred.reject(new Error("Cancelled pending end of stream.")),this.setEndOfStreamDeferred=void 0)},e.prototype.deferSetEndOfStream=function(){var e=this;return this.setEndOfStreamDeferred=this.setEndOfStreamDeferred||new v.Deferred,this.setEndOfStreamDeferred.promise.then((function(){var t;return null===(t=e.mse)||void 0===t?void 0:t.endOfStream()})).catch((function(t){(0,m.isContextAvailable)(e.context)}))},e.prototype.setEndOfStream=function(e){if(void 0===e&&(e=!0),!e)return this.cancelPendingEndOfStream(),this.isMSEOpen()?Promise.resolve():this.reOpenMse();if(!this.hasSourceBuffers()||!this.mse)return Promise.resolve();var t=this.getEndOfBufferTime();return this.setCurrentTimeDelayedContext&&this.setCurrentTimeDelayedContext.time>=this.duration&&t&&!isNaN(t)&&this.cancelSetCurrentTimeDelayed(),this.setCurrentTimeDelayedContext?this.deferSetEndOfStream():this.mse.endOfStream()},e.prototype.isTimeInRangePendingRemoval=function(e){var t=this.getRangesPendingRemoval();for(var r in t){var n=t[r];if(n.start<=e&&n.end>=e)return!0}return!1},e.prototype.isTimeInBuffer=function(e,t){if(void 0===t&&(t=0),this.isTimeInRangePendingRemoval(e))return!1;var r=this.getRangesInSourceBuffers();return!!r&&Object.keys(r).every((function(i){return(0,n.isInTimeRanges)(r[i],e,t)}))},e.prototype.ensureSafeDistanceToEndOfBuffer=function(e,t){var r,n=null!==(r=this.getRangesInSourceBuffers())&&void 0!==r?r:{},i=Object.keys(n),o=x(n,e);if(0===i.length||o.length<i.length)return NaN;var s=Math.min.apply(Math,o.map((function(e){var t;return null!==(t=null==e?void 0:e.end)&&void 0!==t?t:1/0}))),u=s-e;return u>=t?e:e-(t-u)},e.prototype.adjustTimeToNextCommonBufferStart=function(e){var t,r,n,i=this,o=null===(t=(0,h.getSourceState)(this.context))||void 0===t?void 0:t.drm,s=(0,l.getBufferEndSafetyMargin)(o);if(this.isTimeInBuffer(e,s))return e;var u=this.isTimeInBuffer(e),a=this.getFutureBufferStarts(e).filter((function(t){return t>=e})),d=0===a.length,c=null===(n=null===(r=(0,h.getSourceState)(this.context))||void 0===r?void 0:r.buffer.isLoadingRangeFinished)||void 0===n||n;if(u&&d){var f=this.ensureSafeDistanceToEndOfBuffer(e,s);return f===e||c?!isNaN(f)&&this.isTimeInBuffer(f)?f:e:NaN}if(!u&&d)return NaN;var p=a.find((function(e){return i.isTimeInBuffer(e,s)})),m=null!=p?p:Math.min.apply(Math,a);return p||c?m:NaN},e.prototype.getFutureBufferStarts=function(e){var t,r=this.getRangesInSourceBuffers();if(!r)return[];if(this.mse){var i=this.getRangesPendingRemoval();t=Object.keys(i).map((function(e){return i[e]}))}else t=[];return n.BufferRangeHelper.getCommonBufferedRanges(r).filter((function(t){return t.getEnd()>=e&&!isNaN(t.getStart())})).filter((function(e){return t.every((function(t){return(0,n.areDisjoint)(t,{start:e.getStart(),end:e.getEnd()})}))})).map((function(e){return e.getStart()}))},e.prototype.getRangesInSourceBuffers=function(){if(!this.mse)return null;var e={};for(var t in this.segmentQueues)if(this.segmentQueues.hasOwnProperty(t)&&(e[t]=this.mse.getBufferedRanges(t),e[t].length<1))return null;return Object.keys(e).length?e:null},e.prototype.getRangesPendingRemoval=function(){if(!this.mse||!this.mse.isBufferRemovalPending())return{};var e={};for(var t in this.segmentQueues){var r=this.mse.getRangePendingRemoval(t);this.segmentQueues.hasOwnProperty(t)&&r&&(e[t]=r)}return e},e.prototype.processSegmentQueues=function(){var e=this,t=this.segmentQueueMimeTypes.filter((function(t){return e.segmentQueues[t].length>0})).map((function(t){return e.addSegmentFromQueueToMse(t)}));return 0===t.length?Promise.resolve():Promise.all(t).then((function(){return e.processSegmentQueues()})).catch((function(e){if(e!==i.SOURCE_BUFFER_APPEND_STATUS.SUSPENDED)throw e}))},e.prototype.getHeadOfQueue=function(e){return this.segmentQueues[e][0]},e.prototype.isMSEOpen=function(){var e;return(null===(e=this.mse)||void 0===e?void 0:e.readyState)===B.MediaSourceReadyState.open},e.prototype.maybeWaitForDrmLicense=function(e){var t,r=null===(t=(0,h.getSourceState)(this.context))||void 0===t?void 0:t.drm;return this.context.serviceManager.maybeCall(u.ServiceName.DrmService,(function(t){return t.maybeWaitForDrmLicense(e,r)}),Promise.resolve())},e.prototype.addSegmentFromQueueToMse=function(e){var t=this;if(!this.mse)return Promise.resolve();var r=Promise.resolve();this.isMSEOpen()||(r=this.reOpenMse());var n=this.getHeadOfQueue(e);if(!n)return Promise.resolve();n.getRepresentationId().representationId,n.getUrl();return r.then((function(){return t.maybeWaitForDrmLicense(n)})).then((function(){return t.mse?t.segmentQueues[e].includes(n)?t.mse.addToBuffer(n).then((function(){if(t.getHeadOfQueue(e)!==n){var r=t.segmentQueues[e][0];r&&r.getUrl()}else t.segmentQueues[e].shift();t.drmService&&t.hasDataInSourceBuffers()&&!(0,g.isSwitchingBufferBlocks)((0,h.getSourceStore)(t.context))&&t.drmService.signalInitDataShouldBeAvailable()})):Promise.resolve():Promise.reject(i.SOURCE_BUFFER_APPEND_STATUS.FAILURE)})).catch((function(r){if(r===i.SOURCE_BUFFER_APPEND_STATUS.FAILURE&&t.trackRendererError(n),t.mse&&r===i.SOURCE_BUFFER_APPEND_STATUS.QUOTA_EXCEEDED){t.quotaExceededMap.set(e,!0);var o=t.quotaExceededDeferredMap.get(e);return o||(o=new v.Deferred,t.quotaExceededDeferredMap.set(e,o)),t.mse.addOneUpdateEndCallback(e,o.resolve).then((function(){t.quotaExceededDeferredMap.delete(e)})),o.promise.then((function(){return t.processSegmentQueues()}))}return t.mse?t.mse.queueActionOnBuffer(e,(function(){return t.processSegmentQueues()})):Promise.reject(r)}))},e.prototype.cancelSetCurrentTimeDelayed=function(){this.setCurrentTimeDelayedContext&&(this.setCurrentTimeDelayedContext.unsubscribeFromSourceStore(),this.setCurrentTimeDelayedContext.deferred.reject("cancelled seek"),this.setCurrentTimeDelayedContext=void 0)},e.prototype.recoverFromExceededQuota=function(e){var t=this;this.segmentQueueMimeTypes.forEach((function(r){t.segmentQueues[r]=t.segmentQueues[r].filter((function(t){return t.getPlaybackTime()+t.getDuration()>=e})),t.updateRendererRangesInStore(r),t.quotaExceededMap.set(r,!1)})),this.quotaExceededDeferredMap.forEach((function(e){return e.reject()})),this.quotaExceededDeferredMap.clear()},e.prototype.resolvePendingEndOfStream=function(){this.setEndOfStreamDeferred&&(this.setEndOfStreamDeferred.resolve(),this.setEndOfStreamDeferred=void 0)},e.prototype.setCurrentTimeInternal=function(e){var t=this;if(!this.mse)return Promise.reject("Couldn't set current time, MSE is not open");w(this.quotaExceededMap,(function(e){return e}))&&this.recoverFromExceededQuota(e);var r=this.mse.waitForBuffers().then((function(){return t.waitingForSeekedPromise})).then((function(){if(t.setCurrentTimeContext){if(t.setCurrentTimeContext.time!==e)return Promise.reject("A newer setting of change time has been scheduled.")}else t.setCurrentTimeContext={time:e,isInitialSeek:!1,promise:r};var n=t.adjustTimeToNextCommonBufferStart(e);return isNaN(n)?t.waitForDataInBuffer(e):n})).then((function(e){var r,n,i,o=null!==(n=null===(r=t.video)||void 0===r?void 0:r.currentTime)&&void 0!==n?n:0,s=!t.isPaused();return((null===(i=t.setCurrentTimeContext)||void 0===i?void 0:i.isInitialSeek)&&s&&o>e?Promise.resolve(o):t.seekOnVideoElement(e)).then((function(e){return t.resolvePendingEndOfStream(),e})).catch((function(r){return t.handleVideoSetCurrentTimeError(e,r)})).finally((function(){t.waitingForSeekedPromise=null,t.setCurrentTimeContext=void 0,t.savedCurrentTime=0}))}));return r},e.prototype.waitForDataInBuffer=function(e){var t=this,r=(0,h.getSourceStore)(this.context);if(!r)return Promise.reject(new Error("No source store available"));var n=new v.Deferred,i=function(){var r=t.adjustTimeToNextCommonBufferStart(e);isNaN(r)||(o(),t.setCurrentTimeDelayedContext=void 0,n.resolve(r))},o=(0,p.subscribe)(r)((function(e){return(0,M.getBufferState)(e)}),(function(){var e;null===(e=t.setCurrentTimeDelayedContext)||void 0===e||e.reevaluateDataInBuffer()}),(function(e,t){return Boolean((null==e?void 0:e.isLoadingRangeFinished)&&!(null==t?void 0:t.isLoadingRangeFinished))}));return this.setCurrentTimeDelayedContext={time:e,reevaluateDataInBuffer:i,deferred:n,unsubscribeFromSourceStore:o},n.promise},e.prototype.handleVideoSetCurrentTimeError=function(e,t){return this.waitingForSeekedPromise=null,this.setCurrentTimeInternal(e)},e.prototype.seekOnVideoElement=function(e){var t=this;return this.waitingForSeekedPromise=new Promise((function(r,n){t.video?(t.video.currentTime=e,r(t.video.currentTime)):n("No video reference")})),this.waitingForSeekedPromise},e.prototype.saveCurrentTime=function(){this.setCurrentTimeContext?this.savedCurrentTime=this.setCurrentTimeContext.time:this.savedCurrentTime=this.getCurrentVideoTime()},e.prototype.recreateSourceBuffers=function(e){var t=this;return void 0===e&&(e=this.getCurrentTime()),this.shutdown(!1).then((function(){return t.ready()})).then((function(){t.sourceBufferMimeCodecMap.forEach((function(e,r){t.addBuffer(e,r)||t.sourceBufferMimeCodecMap.remove(e)}))})).then((function(){t.setCurrentTime(e).then((function(){var e,r;return(null===(r=null===(e=t.context.serviceManager)||void 0===e?void 0:e.get(u.ServiceName.PlayerStateService))||void 0===r?void 0:r.isPlaying())?t.play():Promise.resolve()}))}))},e.prototype.updateSegmentQueueMimeType=function(e){var t,r=this,n=S.MimeTypeHelper.getMediaType(e);this.segmentQueueMimeTypes.forEach((function(e){var t;S.MimeTypeHelper.getMediaType(e)===n&&(delete r.segmentQueues[e],null===(t=(0,h.getSourceStore)(r.context))||void 0===t||t.dispatch((0,R.setRendererRanges)(e,[])))})),this.segmentQueues[e]=[],null===(t=(0,h.getSourceStore)(this.context))||void 0===t||t.dispatch((0,R.setRendererRanges)(e,[]))},e.prototype.changeBufferType=function(e,t){var r=this;return this.sourceBufferMimeCodecMap.update(e,t),this.updateSegmentQueueMimeType(e),this.mse&&this.mse.isChangeTypeSupported()?this.mse.changeBufferType(e,t).then((function(){var r;return(r={})[e]=t,r})).catch((function(e){return r.recreateSourceBuffers().then((function(){return r.sourceBufferMimeCodecMap.entries()}))})):this.recreateSourceBuffers().then((function(){return r.sourceBufferMimeCodecMap.entries()}))},e.prototype.shouldContinueBuffering=function(){var e,t;return null!==(t=null===(e=this.mse)||void 0===e?void 0:e.shouldContinueBuffering)&&void 0!==t&&t},e}();function w(e,t){var r=!1;return e.forEach((function(e){return r=r||t(e)})),r}function x(e,t){return Object.keys(e).map((function(r){return e[r].find((function(e){return(0,n.isInTimeRanges)([e],t)}))})).filter(E.isDefined)}t.MSERenderer=D},75294:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.VideoEventHandler=void 0;var r=function(){function e(e){var t=this;this.addTimeToEvent=function(e){return e.time=t.video&&t.video.currentTime||0,e},this.video=e}return e.prototype.on=function(e,t){this.video&&this.video.addEventListener(e,t,this.addTimeToEvent)},e.prototype.off=function(e,t){this.video&&this.video.removeEventListener(e,t)},e.prototype.fire=function(e,t){this.video&&this.video.eventHandler.triggerEvent(e,t)},e.prototype.dispose=function(){},e}();t.VideoEventHandler=r},77822:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getMseObjectUrl=void 0;var r=function(e){var t,r;return null!==(r=null===(t=null==e?void 0:e.renderer)||void 0===t?void 0:t.mseObjectUrl)&&void 0!==r?r:""};t.getMseObjectUrl=r},79902:function(e,t){function r(e){return"ManagedMediaSource"in window&&(e||!("MediaSource"in window))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseManagedMediaSource=r},81200:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.MSERendererModuleDefinition=void 0;var n=r(86246),i=r(67461),o=r(38867),s=r(81963),u=r(60124);t.MSERendererModuleDefinition={name:n.ModuleName.RendererMse,module:{MSEWrapper:o.MSEWrapper,MSERenderer:i.MSERenderer,Ranges:s.Ranges,technologyChecker:new u.TechnologyChecker}},t.default=t.MSERendererModuleDefinition},81963:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Ranges=void 0;var r=function(){function e(){}return e.filterRanges=function(t,r){var n=[];if(t&&t.length)for(var i=0;i<t.length;i++)r(t.start(i),t.end(i))&&n.push([t.start(i),t.end(i)]);return e.createTimeRanges(n)},e.createTimeRangesObj=function(e){return void 0===e||0===e.length?{length:0,start:function(){return 0},end:function(){return 0}}:{length:e.length,start:function(t){return e[t][0]},end:function(t){return e[t][1]}}},e.createTimeRanges=function(t,r){return Array.isArray(t)?e.createTimeRangesObj(t):void 0===t||void 0===r?e.createTimeRangesObj():e.createTimeRangesObj([[t,r]])},e.findNextRange=function(t,r){return e.filterRanges(t,(function(t,n){return t-e.TIME_FUDGE_FACTOR>=r&&n-t>e.MIN_RANGE_DURATION}))},e.findGaps=function(t,r){if(t.length<2)return e.createTimeRanges();for(var n=[],i=1;i<t.length;i++){var o=t.end(i-1),s=t.start(i);s-o>r&&n.push([o,s])}return e.createTimeRanges(n)},e.findRangeForTime=function(t,r){return e.filterRanges(t,(function(e,t){return e<=r&&t>=r}))},e.TIME_FUDGE_FACTOR=1/30,e.MIN_RANGE_DURATION=.2,e}();t.Ranges=r},92347:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.BufferMimeCodecMap=void 0;var n=r(38395),i=r(16248),o=r(74821),s=r(23682),u=r(69303),a=r(40060),d=function(){function e(e,t){this.mimeCodeMap={},this.store=e,this.settings=t}return e.prototype.ensureMetricsForMimeType=function(e){var t,r,s=null===(t=this.store)||void 0===t?void 0:t.getState();(0,n.isInstanceState)(s)&&c(this.settings)&&((0,o.getMetricsState)(s)[e]||null===(r=this.store)||void 0===r||r.dispatch((0,i.initializeMetricsForMimeType)(e,this.settings)))},e.prototype.removeMetrics=function(e){var t;null===(t=this.store)||void 0===t||t.dispatch((0,i.removeMetricsForMimeType)(e))},e.prototype.add=function(e,t){var r;this.mimeCodeMap[e]=t,this.ensureMetricsForMimeType(e),null===(r=this.store)||void 0===r||r.dispatch((0,i.addMetricsValue)(e,s.MetricType.StalledSeconds,0))},e.prototype.remove=function(e){var t=this,r=a.MimeTypeHelper.getMediaType(e);Object.keys(this.mimeCodeMap).forEach((function(e){a.MimeTypeHelper.getMediaType(e)===r&&(t.removeMetrics(e),delete t.mimeCodeMap[e])}))},e.prototype.has=function(e,t){var r=this,n=a.MimeTypeHelper.getMediaType(e),i=a.MimeTypeHelper.extractContainerType(e);return Object.keys(this.mimeCodeMap).some((function(o){var s=a.MimeTypeHelper.getMediaType(o),u=a.MimeTypeHelper.extractContainerType(o);return s===n&&u===i&&t===r.mimeCodeMap[e]}))},e.prototype.update=function(e,t){this.remove(e),this.add(e,t)},e.prototype.forEach=function(e){var t=this;Object.keys(this.entries()).forEach((function(r){return e(r,t.entries()[r])}))},e.prototype.entries=function(){return this.mimeCodeMap},e.prototype.dispose=function(){this.mimeCodeMap={},this.store=void 0,this.settings=void 0},e}();t.BufferMimeCodecMap=d;var c=function(e){return(0,u.isObject)(e)}},96089:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.GapHandler=void 0;var n=r(33964),i=r(20561),o=r(38395),s=r(41497),u=r(84815),a=r(40060),d=r(17279),c=r(98892),f=r(19912),h=r(81963),p=[c.MediaElementEvent.seeking,c.MediaElementEvent.seeked,c.MediaElementEvent.pause,c.MediaElementEvent.playing,c.MediaElementEvent.error],m=5,v=10,l=250,g=.01,y=function(){function e(e){var t,r,i,u,a,d=this;this.isExecutingGapSkipping=!1,this.play=function(){d.lastPlayEventTimestamp=Date.now()},this.checkIfWaiting=function(){if(!d.context.isDisposed&&void 0!==d.video){var e=d.video.paused,t=d.video.seeking,r=d.isAtEndOfBufferedRange(d.video.currentTime),n=d.hasRecentlyTrackedTimeupdateEvent(),i=d.hasRecentlyStartedPlayback();e||t&&!r||n||(d.consecutiveWaiting++,i||d.waiting())}},this.waiting=function(){var e,t=d.context.store.getState();if((0,o.isInstanceState)(t)&&d.video){var r=(0,s.getPlayerState)(t);((0,s.getIsSeeking)(r)||(null===(e=d.video)||void 0===e?void 0:e.seeking))&&!d.isAtEndOfBufferedRange(d.video.currentTime)||d.setTimer()}},this.stalled=function(){d.setTimer()},this.onSeek=function(){d.gapAhead=void 0},this.timeupdate=function(){if(d.lastTimeupdate=Date.now(),d.video&&!d.video.paused&&!d.video.seeking){d.hasPlayedAnythingYet=!0;var e=d.context.renderer.getCurrentTime(!0);if(d.shouldSkipGap(e))d.skipTheGapProactively();else{var t=d.context.renderer.getVideoElementBufferedRanges();if(t.length>1&&!d.gapAhead){var r=d.context.settings.MIN_SIZE_FOR_GAP_SKIPPING;d.gapAhead=R(t,e,r)}else d.shouldTriggerWaitingEvent(e)?(d.consecutiveUpdates++,d.waiting()):e===d.lastRecordedTime?d.consecutiveUpdates++:(d.consecutiveUpdates=0,d.consecutiveWaiting=0,d.lastRecordedTime=e)}}},this.cancelTimer=function(){d.consecutiveUpdates=0,d.consecutiveWaiting=0,clearTimeout(d.timer),d.timer=-1},this.skipTheGap=function(e){if(d.video&&!d.isExecutingGapSkipping){var t=d.context.renderer.getVideoElementBufferedRanges().sort((function(e,t){return e.start-t.start})),r=d.context.renderer.getCurrentTime(!0),n=M(t,r);if(d.consecutiveUpdates=0,d.consecutiveWaiting=0,d.timer=-1,void 0!==n&&r===e){var i=n.start;d.executeGapSkipping(r,i)}}},this.context=e,this.video=e.videoElement,this.lastTimeupdate=0,this.consecutiveUpdates=0,this.consecutiveWaiting=0,this.lastPlayEventTimestamp=0,this.hasPlayedAnythingYet=!1,this.gapAhead=void 0,null===(t=this.video)||void 0===t||t.addEventListener(c.MediaElementEvent.waiting,this.waiting),null===(r=this.video)||void 0===r||r.addEventListener(c.MediaElementEvent.stalled,this.stalled),null===(i=this.video)||void 0===i||i.addEventListener(c.MediaElementEvent.timeupdate,this.timeupdate),null===(u=this.video)||void 0===u||u.addEventListener(c.MediaElementEvent.play,this.play),this.context.eventHandler.on(n.PlayerEvent.Seek,this.onSeek);for(var f=0,h=p;f<h.length;f++){var m=h[f];null===(a=this.video)||void 0===a||a.addEventListener(m,this.cancelTimer)}this.timeupdateIntervalId=window.setInterval(this.checkIfWaiting,l)}return e.prototype.hasRecentlyStartedPlayback=function(){return Date.now()-this.lastPlayEventTimestamp<2*l},e.prototype.hasRecentlyTrackedTimeupdateEvent=function(){return Date.now()-this.lastTimeupdate<=1e3},e.prototype.shouldSkipGap=function(e){return!(!this.gapAhead||this.isExecutingGapSkipping)&&(S(e,this.gapAhead)||E(this.context.settings.PROACTIVE_GAP_SKIP_DISTANCE_SECONDS,e,this.gapAhead)||T(e,this.gapAhead))},e.prototype.shouldTriggerWaitingEvent=function(e){return this.consecutiveUpdates===m&&e===this.lastRecordedTime},e.prototype.skipTheGapProactively=function(){if(this.video&&this.gapAhead){var e=this.context.renderer.getCurrentTime(!0),t=Math.max(e+g,this.gapAhead.end);this.executeGapSkipping(e,t)}},e.prototype.executeGapSkipping=function(e,t){var r=this;this.isExecutingGapSkipping=!0,this.gapAhead=void 0,this.maybeRemoveVideoBufferData(e,t).then((function(){return r.context.renderer.setCurrentTime(t+h.Ranges.TIME_FUDGE_FACTOR)})).then((function(e){})).catch((function(e){})).finally((function(){r.isExecutingGapSkipping=!1,r.gapAhead=void 0}))},e.prototype.maybeRemoveVideoBufferData=function(e,t){if(!(0,u.getCapabilities)().isTizen)return Promise.resolve();var r=this.getVideoMimeType();if(void 0===r)return Promise.resolve();var n=M(this.context.renderer.getBufferedRanges(r).sort((function(e,t){return e.start-t.start})),e);return t!==(null==n?void 0:n.start)?this.context.renderer.removeData(r,e,t):Promise.resolve()},e.prototype.getVideoMimeType=function(){var e;return null===(e=this.context.segmentInfoService)||void 0===e?void 0:e.getSegmentControllerMimeTypes().find((function(e){return a.MimeTypeHelper.isVideo(e)}))},e.prototype.gapFromVideoUnderflow=function(e,t,r){for(var n=h.Ranges.findGaps(e,r),i=0;i<n.length;i++){var o=n.start(i),s=n.end(i);if(t-o<4&&t-o>2)return{start:o,end:s}}},e.prototype.setTimer=function(){if(!(this.timer>-1)&&this.hasPlayedAnythingYet&&this.video){var e=this.video.buffered,t=this.video.currentTime,r=h.Ranges.findNextRange(e,t),n=this.context.settings.MIN_SIZE_FOR_GAP_SKIPPING;if(0===r.length){if(this.gapFromVideoUnderflow(e,t,n))return this.consecutiveWaiting=0,void this.setCurrentTime(t);var i=(0,f.findNextRangeForGap)(t,e),o=i.isNearEndOfBufferedRange,s=i.nextRangeStart;return o&&isFinite(s)?(this.consecutiveWaiting=0,void this.setCurrentTime(s)):void(this.consecutiveWaiting>v&&(this.consecutiveWaiting=0,this.triggerCurrentTimeNotAdvancing(t,this.video)))}if(!P(e,t,n)){var u=r.start(0)-t;this.timer=window.setTimeout(this.skipTheGap.bind(this),u,this.context.renderer.getCurrentTime(!0))}}},e.prototype.triggerCurrentTimeNotAdvancing=function(e,t){this.maybePokeVideoElement(e,t),t.eventHandler.triggerEvent(c.MediaElementEvent.currenttimenotadvancing)},e.prototype.maybePokeVideoElement=function(e,t){if(e>t.duration&&!(0,u.getCapabilities)().isSafari)this.setCurrentTime(t.duration);else if(this.context.settings.QJY_BROWSER_WORKAROUND||(0,u.getCapabilities)().isTizen)this.setCurrentTime(e+g);else{var r=(0,u.getCapabilities)()[i.CapabilityKey.isPlayStation5];this.setCurrentTime(e,r)}},e.prototype.isAtEndOfBufferedRange=function(e){if(!this.video)return!1;for(var t=this.video.buffered,r=0;r<t.length;r++)if(t.end(r)===e)return!0;return!1},e.prototype.shutdown=function(){var e,t,r,i,o;null===(e=this.video)||void 0===e||e.removeEventListener(c.MediaElementEvent.waiting,this.waiting),null===(t=this.video)||void 0===t||t.removeEventListener(c.MediaElementEvent.stalled,this.stalled),null===(r=this.video)||void 0===r||r.removeEventListener(c.MediaElementEvent.timeupdate,this.timeupdate),null===(i=this.video)||void 0===i||i.removeEventListener(c.MediaElementEvent.play,this.play),this.context.eventHandler.off(n.PlayerEvent.Seek,this.onSeek);for(var s=0,u=p;s<u.length;s++){var a=u[s];null===(o=this.video)||void 0===o||o.removeEventListener(a,this.cancelTimer)}clearInterval(this.timeupdateIntervalId),this.cancelTimer()},e.prototype.setCurrentTime=function(e,t){if(void 0===t&&(t=!1),this.video&&(this.video.currentTime!==e||t))try{this.video.currentTime=e}catch(e){}},e}();function S(e,t){return e>t.start&&e<t.end}function E(e,t,r){return!(e<=0)&&(r.start>t&&r.start-e<t)}function T(e,t){return e>t.end&&(0,d.shouldResetCurrentTimeAfterBufferUnderrun)()}function R(e,t,r){for(var n=0;n<e.length-1;n++){var i=e[n].end,o=e[n+1].start;if(i>t&&o>i&&o-i>=r)return{start:i,end:o}}}function M(e,t){return e.find((function(e){return e.start-h.Ranges.TIME_FUDGE_FACTOR>=t}))}function P(e,t,r){if(0===r)return!1;var n=h.Ranges.findNextRange(e,t);if(0===n.length)return!1;var i=h.Ranges.findRangeForTime(e,t),o=i.length?i.end(0):t;return n.start(0)-o<r}t.GapHandler=y},98563:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.SourceBufferWrapper=void 0;var n=r(10395),i=r(16838),o=function(){function e(e,t){var r=this;this.onBufferedRangesChanged=t,this.onUpdateSuccess=function(){r.updateExposedBufferRanges(),r.onBufferedRangesChanged(r.bufferedRanges),r.cleanUpPendingUpdateEndedPromises((function(e){return e.resolve()}))},this.onUpdateError=function(e){r.cleanUpPendingUpdateEndedPromises((function(t){return t.reject(e)}))},this.ready=function(){return r.buffer.updating?r.onUpdateEnded():Promise.resolve()},this.buffer=e,this.bufferedRanges=[],this.queuePromise=Promise.resolve(),this.pendingUpdateEndedPromises=new i.LinkedListQueue,["error","abort","sourceended","sourceclose"].forEach((function(e){r.buffer.addEventListener(e,r.onUpdateError)})),this.buffer.addEventListener("updateend",this.onUpdateSuccess)}return e.prototype.cleanUpPendingUpdateEndedPromises=function(e){for(var t;null!=(t=this.pendingUpdateEndedPromises.dequeue());)e(t)},e.prototype.updateExposedBufferRanges=function(){try{this.bufferedRanges=s(this.buffer.buffered)}catch(e){}},e.prototype.onUpdateEnded=function(){var e=new n.Deferred;return this.pendingUpdateEndedPromises.enqueue(e),e.promise},e.prototype.queueAction=function(e){var t=this,r=this.queuePromise.then((function(){return t.ready()})).then((function(){return Promise.resolve(e()).then((function(){return t.ready()}))}));return this.queuePromise=r.catch((function(){})),r},e.prototype.changeType=function(e){this.buffer.changeType(e)},e.prototype.dispose=function(){var e=this;["error","abort","sourceended","sourceclose"].forEach((function(t){e.buffer.removeEventListener(t,e.onUpdateError)})),this.cleanUpPendingUpdateEndedPromises((function(e){return e.reject("disposing source buffer")})),this.buffer.removeEventListener("updateend",this.onUpdateSuccess)},e}();function s(e){for(var t=[],r=0;r<e.length;r++)t.push({start:e.start(r),end:e.end(r)});return t}t.SourceBufferWrapper=o}},function(e){return function(t){return e(e.s=t)}(81200)}])}));
})();
