/**************************************************************************** 
 * Copyright (C) 2025, Bitmovin, Inc., All Rights Reserved 
 * 
 * This source code and its use and distribution, is subject to the terms 
 * and conditions of the applicable license agreement. 
 * 
 * Bitmovin Player Version 8.219.0 
 * 
 ****************************************************************************/
(function() {
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.analytics=t():(e.bitmovin=e.bitmovin||{},e.bitmovin.player=e.bitmovin.player||{},e.bitmovin.player.analytics=t())}(self,(function(){return(self.webpackChunkbitmovin_player=self.webpackChunkbitmovin_player||[]).push([[153],{30100:function(e){!function(t,n){e.exports=n()}(self,(function(){return function(){var e={92:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.post=function(e,t,n,a){void 0===a&&(a=!0);var r,o=!1;window.XDomainRequest?(r=new window.XDomainRequest,o=!0):r=new XMLHttpRequest;var s=function(){if(r.readyState==XMLHttpRequest.DONE){if(r.responseText<=0)return;var e=JSON.parse(r.responseText);n(e)}};o?r.onload=s:r.onreadystatechange=s;try{r.open("POST",e,a),o||r.setRequestHeader("Content-Type","text/plain"),r.send(JSON.stringify(t))}catch(e){i.logger.error("Failed to send POST request.",e)}};var i=n(1916)},348:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Feature=void 0;var i=n(5063),a=function(){function e(){this._config=void 0,this._isEnabled=!0}return Object.defineProperty(e.prototype,"config",{get:function(){return this._config},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isEnabled",{get:function(){return this._isEnabled},enumerable:!1,configurable:!0}),e.prototype.disable=function(){this._isEnabled=!1,this.disabled()},e.prototype.configure=function(e,t){return(0,i.isNotNullish)(t)&&(this._config=this.extractConfig(t)),this.configured(e,this._config),this._config},e}();t.Feature=a},399:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LongPlaytimeFilteringBackend=void 0;var i=n(5063),a=function(){function e(e){this.nextBackend=e}return e.prototype.sendRequest=function(e){this.isPlayedAboveThreshold(e)||this.nextBackend.sendRequest(e)},e.prototype.sendUnloadRequest=function(e){this.isPlayedAboveThreshold(e)||this.nextBackend.sendUnloadRequest(e)},e.prototype.sendRequestSynchronous=function(e){this.isPlayedAboveThreshold(e)||this.nextBackend.sendRequestSynchronous(e)},e.prototype.sendAdRequest=function(e){this.nextBackend.sendAdRequest(e)},e.prototype.isPlayedAboveThreshold=function(e){return((0,i.isNullish)(e.played)?0:e.played)>=9e4},e}();t.LongPlaytimeFilteringBackend=a},404:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodecHelper=void 0;var n={avc:['video/mp4; codecs="avc1.42000a"'],vp9:['video/mp4; codecs="vp09.00.50.08"'],av1:['video/mp4; codecs="av01.0.08M.08"'],hevc:['video/mp4; codecs="hev1.1.6.L93.B0"']},i=function(){function e(){}return Object.defineProperty(e,"supportedVideoFormats",{get:function(){var e=this;if("MediaSource"in window)return Object.getOwnPropertyNames(n).filter((function(t){return n[t].some((function(t){return e.isCodecSupported(t)}))}))},enumerable:!1,configurable:!0}),e.isCodecSupported=function(e){return"MediaSource"in window&&MediaSource.isTypeSupported(e)},e}();t.CodecHelper=i},556:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Player=void 0,function(e){e.AMAZON_IVS="amazonivs",e.BITMOVIN="bitmovin",e.BITMOVIN_PWX="bitmovin-pwx",e.CHROMECAST_SHAKA="chromecast-shaka",e.DASHJS="dashjs",e.HLSJS="hlsjs",e.HTML5="html5",e.JW="jw",e.RADIANT="radiant",e.SHAKA="shaka",e.VIDEOJS="videojs"}(n||(t.Player=n={}))},665:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SpeedMeterAdapter=void 0;var n=function(){function e(e,t){var n=this;this.meter=t,e.on(e.exports.PlayerEvent.DownloadFinished,(function(e){return n.handleOnDownloadFinishEvent(e)}))}return e.prototype.getDownloadSpeedMeter=function(){return this.meter},e.prototype.handleOnDownloadFinishEvent=function(e){e.success&&0===e.downloadType.indexOf("media/video")&&this.meter.addMeasurement({duration:e.downloadTime,size:e.size,timeToFirstByte:e.timeToFirstByte,timestamp:new Date,httpStatus:e.httpStatus})},e}();t.SpeedMeterAdapter=n},675:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventDispatcher=void 0;var n=function(){function e(){this.callbacks=[]}return e.prototype.subscribe=function(e){var t=this;return this.callbacks.push(e),function(){return t.unsubscribe(e)}},e.prototype.dispatch=function(e){this.callbacks.slice(0).forEach((function(t){t(e)}))},e.prototype.unsubscribe=function(e){var t=this.callbacks.indexOf(e);t>-1&&this.callbacks.splice(t,1)},Object.defineProperty(e.prototype,"subscriberCount",{get:function(){return this.callbacks.length},enumerable:!1,configurable:!0}),e}();t.EventDispatcher=n},681:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerModule=void 0;var i=n(4679);t.PlayerModule={name:"analytics",module:{Analytics:i.Bitmovin8Adapter},hooks:{setup:function(e,t){var n=t.getConfig(),i=!n,a=!1===(null==n?void 0:n.analytics);if(i||a)return Promise.resolve();var r=new(0,e.Analytics)(t);return Promise.resolve(r)}}}},847:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateCookieMaxAgeValue=t.validateCookieDomainValue=t.SessionPersistenceHandler=t.COOKIE_DEFAULT_MAX_AGE=t.COOKIE_NAME_BITMOVIN_ANALYTICS_UUID=void 0;var i=n(4934),a=n(1820),r=n(5063),o=n(2982);t.COOKIE_NAME_BITMOVIN_ANALYTICS_UUID="bitmovin_analytics_uuid",t.COOKIE_DEFAULT_MAX_AGE=31536e3;var s=function(e){var n=e.config;if(!1===(null==n?void 0:n.cookiesEnabled))return(0,i.deleteCookie)(t.COOKIE_NAME_BITMOVIN_ANALYTICS_UUID),void(this.userId=(0,o.generateUUID)());var r=(0,i.getCookie)(t.COOKIE_NAME_BITMOVIN_ANALYTICS_UUID);this.userId=(0,a.isBlank)(r)?(0,o.generateUUID)():r;var s=(0,t.validateCookieDomainValue)(null==n?void 0:n.cookiesDomain),u=(0,t.validateCookieMaxAgeValue)(null==n?void 0:n.cookiesMaxAge),l=Date.now(),c=new Date(l+1e3*u).toUTCString();(0,i.setCookie)(t.COOKIE_NAME_BITMOVIN_ANALYTICS_UUID,this.userId,{domain:s,expires:c,maxAge:u})};t.SessionPersistenceHandler=s,t.validateCookieDomainValue=function(e){if(!(0,r.isNullish)(e)&&(0,a.isValidString)(e)&&!(0,a.isBlank)(e))return encodeURIComponent(e)},t.validateCookieMaxAgeValue=function(e){return(0,r.isNullish)(e)?t.COOKIE_DEFAULT_MAX_AGE:(0,r.isNumber)(e)?e:t.COOKIE_DEFAULT_MAX_AGE}},887:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Bitmovin8AdModule=void 0;var i=n(5517),a=function(){function e(e,t){var n=this;this.player=e,this.windowEventTracker=t,this.adCallbacks=new i.AdCallbacks,this.isLinearAdActive=function(){return n.player.ads&&n.player.ads.isLinearAdActive()||!1},this.getContainer=function(){return n.player.getContainer()},this.getAdModuleInfo=function(){return n.player.ads?n.player.ads.getModuleInfo():{name:void 0,version:void 0}},this.currentTime=function(){return n.player.getCurrentTime()},this.register()}return e.prototype.register=function(){var e=this;this.player.on(this.player.exports.PlayerEvent.Play,(function(t){"advertising-api"===t.issuer&&e.adCallbacks.onPlay(t.issuer)})),this.player.on(this.player.exports.PlayerEvent.Paused,(function(t){"advertising-api"===t.issuer&&e.adCallbacks.onPause(t.issuer)})),this.player.on(this.player.exports.PlayerEvent.AdStarted,(function(t){e.adCallbacks.onAdStarted(t)})),this.player.on(this.player.exports.PlayerEvent.AdFinished,(function(t){e.adCallbacks.onAdFinished(t)})),this.player.on(this.player.exports.PlayerEvent.AdBreakStarted,(function(t){e.adCallbacks.onAdBreakStarted(t)})),this.player.on(this.player.exports.PlayerEvent.AdBreakFinished,(function(t){e.adCallbacks.onAdBreakFinished(t)})),this.player.on(this.player.exports.PlayerEvent.AdClicked,(function(t){e.adCallbacks.onAdClicked(t)})),this.player.on(this.player.exports.PlayerEvent.AdQuartile,(function(t){e.adCallbacks.onAdQuartile(t)})),this.player.on(this.player.exports.PlayerEvent.AdSkipped,(function(t){e.adCallbacks.onAdSkipped(t)})),this.player.on(this.player.exports.PlayerEvent.AdError,(function(t){e.adCallbacks.onAdError(t)})),this.player.on(this.player.exports.PlayerEvent.AdManifestLoaded,(function(t){e.adCallbacks.onAdManifestLoaded(t)}));var t=function(){e.adCallbacks.onBeforeUnload()};this.windowEventTracker.addEventListener("beforeunload",t),this.windowEventTracker.addEventListener("unload",t)},e}();t.Bitmovin8AdModule=a},1125:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.QueueBackend=void 0;var n=function(){function e(){this.queue=[],this.unloadQueue=[],this.syncQueue=[],this.adQueue=[]}return e.prototype.sendRequest=function(e){this.queue.push(e)},e.prototype.sendUnloadRequest=function(e){this.unloadQueue.push(e)},e.prototype.sendRequestSynchronous=function(e){this.syncQueue.push(e)},e.prototype.sendAdRequest=function(e){this.adQueue.push(e)},e.prototype.flushTo=function(e){this.queue.forEach((function(t){e.sendRequest(t)})),this.unloadQueue.forEach((function(t){e.sendUnloadRequest(t)})),this.syncQueue.forEach((function(t){e.sendRequestSynchronous(t)})),this.adQueue.forEach((function(t){e.sendAdRequest(t)}))},e}();t.QueueBackend=n},1652:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventDebugging=void 0;var n=function(e,t,n,i,a){this.event=e,this.from=t,this.to=n,this.timestamp=i,this.eventObject=a};t.EventDebugging=n},1820:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.startsWith=t.endsWith=t.isBlank=t.isEmpty=t.isValidString=void 0;var i=n(5063);t.isValidString=function(e){return(0,i.isNotNullish)(e)&&"string"==typeof e},t.isEmpty=function(e){return(0,i.isNullish)(e)||0===e.length},t.isBlank=function(e){return(0,t.isEmpty)(e)||(0,t.isEmpty)(null==e?void 0:e.trim())},t.endsWith=function(e,n){return!(!(0,t.isValidString)(e)||!(0,t.isValidString)(n))&&((0,i.isNotNullish)(String.prototype.endsWith)?e.endsWith(n):e.substring(e.length-n.length)===n)},t.startsWith=function(e,n){return!(!(0,t.isValidString)(e)||!(0,t.isValidString)(n))&&((0,i.isNotNullish)(String.prototype.startsWith)?e.startsWith(n):e.substring(0,n.length)===n)}},1916:function(e,t){"use strict";var n=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,a=0,r=t.length;a<r;a++)!i&&a in t||(i||(i=Array.prototype.slice.call(t,0,a)),i[a]=t[a]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.logger=t.padRight=void 0;var i=function(){function e(){}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.prototype.initialize=function(e){this.analyticsDebugConfig=e},e.prototype.log=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.enabled&&console.log(e,t)},e.prototype.table=function(e,t){this.enabled&&console.table(e,t)},e.prototype.warn=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.enabled&&console.warn(e,t)},e.prototype.error=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.enabled&&console.error(e,t)},e.prototype.errorMessageToUser=function(e){for(var t=[],i=1;i<arguments.length;i++)t[i-1]=arguments[i];console.error.apply(console,n([e],t,!1))},Object.defineProperty(e.prototype,"enabled",{get:function(){return this.analyticsDebugConfig||!1},enumerable:!1,configurable:!0}),e}();t.padRight=function(e,t){return(e+new Array(t).join(" ")).slice(0,t)},t.logger=i.getInstance()},2108:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BITMOVIN_ANALYTICS_AUGMENTED_MARKER=void 0,t.hasPlayerAlreadyBeenAugmented=function(e){return!0===e[t.BITMOVIN_ANALYTICS_AUGMENTED_MARKER]},t.markPlayerInstanceAsAugmented=function(e){e[t.BITMOVIN_ANALYTICS_AUGMENTED_MARKER]=!0},t.isDefined=function(e){return!(0,a.isNullish)(e)||(i.logger.log("Bitmovin Analytics: Adapter hasn't been initialized yet"),!1)};var i=n(1916),a=n(5063);t.BITMOVIN_ANALYTICS_AUGMENTED_MARKER="__bitmovinAnalyticsHasBeenSetup"},2226:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NoOpBackend=void 0;var n=function(){function e(){}return e.prototype.sendRequest=function(e){},e.prototype.sendUnloadRequest=function(e){},e.prototype.sendRequestSynchronous=function(e){},e.prototype.sendAdRequest=function(e){},e}();t.NoOpBackend=n},2361:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Event=t.JSM_INITIAL_EVENT=void 0,t.JSM_INITIAL_EVENT="jsm-initial",function(e){e.AUDIO_CHANGE="audioChange",e.AUDIOTRACK_CHANGED="audioTrackChanged",e.CUSTOM_DATA_CHANGE="customDataChange",e.END="end",e.END_AD="adEnd",e.END_BUFFERING="endBuffering",e.END_CAST="endCasting",e.END_FULLSCREEN="endFullscreen",e.ERROR="playerError",e.MANUAL_SOURCE_CHANGE="manualSourceChangeInitiated",e.MUTE="mute",e.PAUSE="pause",e.PLAY="play",e.PLAYING="playing",e.PLAYLIST_TRANSITION="playlistTransition",e.READY="ready",e.SCREEN_RESIZE="resize",e.SEEK="seek",e.SEEKED="seeked",e.SOURCE_LOADED="sourceLoaded",e.SOURCE_UNLOADED="sourceUnloaded",e.START_AD="adStart",e.START_BUFFERING="startBuffering",e.START_CAST="startCasting",e.START_FULLSCREEN="startFullscreen",e.SUBTITLE_CHANGE="subtitleChange",e.TIMECHANGED="timechanged",e.UN_MUTE="unMute",e.UNLOAD="unload",e.VIDEO_CHANGE="videoChange",e.VIDEOSTART_TIMEOUT="VIDEOSTART_TIMEOUT"}(n||(t.Event=n={}))},2364:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RemoteBackend=void 0;var i=n(5426),a=n(5063),r=function(){function e(e,t,n){this.licenseKey=n,this.hasAdModule=e,this.analyticsCall=new i.AnalyticsCall(t)}return e.prototype.sendRequest=function(e){e.key=this.licenseKey,this.analyticsCall.sendRequest(e,a.noOp)},e.prototype.sendUnloadRequest=function(e){e.key=this.licenseKey,this.analyticsCall.sendUnloadRequest(e)},e.prototype.sendRequestSynchronous=function(e){e.key=this.licenseKey,this.analyticsCall.sendRequestSynchronous(e,a.noOp)},e.prototype.sendAdRequest=function(e){e.key=this.licenseKey,this.hasAdModule&&this.analyticsCall.sendAdRequest(e)},e}();t.RemoteBackend=r},2376:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WindowEventTracker=void 0;var n=function(){function e(){this.trackedObjects=[]}return e.prototype.addEventListener=function(e,t){this.trackedObjects.push({eventName:e,func:t}),window.addEventListener(e,t)},e.prototype.release=function(){for(;0!==this.trackedObjects.length;){var e=this.trackedObjects.pop();void 0!==e&&window.removeEventListener(e.eventName,e.func)}},e}();t.WindowEventTracker=n},2403:function(e,t,n){"use strict";var i,a=this&&this.__extends||(i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},i(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Bitmovin8AnalyticsStateMachine=void 0;var r,o=n(2595),s=n(2361),u=n(3565),l=n(1652),c=n(1916),d=n(5063),p=n(7596),h=n(8253);!function(e){e.AD_PAUSE="AD_PAUSE",e.AD_PLAYING="AD_PLAYING",e.AD_READY="AD_READY",e.AD_STARTUP="AD_STARTUP",e.AUDIOTRACK_CHANGING="AUDIOTRACK_CHANGING",e.CASTING="CASTING",e.CUSTOMDATACHANGE="CUSTOMDATACHANGE",e.END="END",e.END_PLAY_SEEKING="END_PLAY_SEEKING",e.ERROR="ERROR",e.INITIAL_SOURCE_CHANGE="INITIAL_SOURCE_CHANGE",e.MUTING_PAUSE="MUTING_PAUSE",e.MUTING_PLAY="MUTING_PLAY",e.MUTING_READY="MUTING_READY",e.PAUSE="PAUSE",e.PLAY_SEEKING="PLAY_SEEKING",e.PLAYING="PLAYING",e.QUALITYCHANGE="QUALITYCHANGE",e.QUALITYCHANGE_PAUSE="QUALITYCHANGE_PAUSE",e.QUALITYCHANGE_REBUFFERING="QUALITYCHANGE_REBUFFERING",e.READY="READY",e.REBUFFERING="REBUFFERING",e.SETUP="SETUP",e.SOURCE_CHANGING="SOURCE_CHANGING",e.STARTUP="STARTUP",e.SUBTITLE_CHANGING="SUBTITLE_CHANGING",e.VIDEOSTART_FAILED="VIDEOSTART_FAILED"}(r||(r={}));var f=function(e){function t(t,n,i){var a=e.call(this,t,n,i)||this;return a.debuggingStates=[],a.enabledDebugging=!1,a}return a(t,e),t.prototype.getAllStatesBut=function(e){return this.getAllStates().filter((function(t){return e.indexOf(t)<0}))},t.prototype.getAllStates=function(){return Object.keys(r).map((function(e){return r[e]}))},t.prototype.createStateMachine=function(e){var t,n=this;return o.create({initial:{state:r.SETUP,event:s.JSM_INITIAL_EVENT},error:h.customStateMachineErrorCallback,events:[{name:s.Event.SOURCE_LOADED,from:[r.SETUP,r.ERROR,r.SOURCE_CHANGING,r.INITIAL_SOURCE_CHANGE],to:r.READY},{name:s.Event.PLAY,from:r.READY,to:r.STARTUP},{name:s.Event.PLAYING,from:r.READY,to:r.PLAYING},(0,h.on)(s.Event.READY).stayIn(r.READY),(0,h.on)(s.Event.VIDEO_CHANGE).stayIn(r.READY),(0,h.on)(s.Event.AUDIO_CHANGE).stayIn(r.READY),{name:s.Event.ERROR,from:[r.STARTUP,r.AD_STARTUP],to:r.VIDEOSTART_FAILED},{name:s.Event.UNLOAD,from:[r.STARTUP,r.AD_STARTUP],to:r.VIDEOSTART_FAILED},{name:s.Event.VIDEOSTART_TIMEOUT,from:r.STARTUP,to:r.VIDEOSTART_FAILED},(0,h.on)(s.Event.PLAY).stayIn(r.STARTUP),(0,h.on)(s.Event.PLAYING).stayIn(r.STARTUP),{name:s.Event.TIMECHANGED,from:r.STARTUP,to:r.PLAYING},(0,h.on)(s.Event.START_BUFFERING).stayIn(r.STARTUP),(0,h.on)(s.Event.END_BUFFERING).stayIn(r.STARTUP),(0,h.on)(s.Event.VIDEO_CHANGE).stayIn(r.STARTUP),(0,h.on)(s.Event.AUDIO_CHANGE).stayIn(r.STARTUP),(0,h.on)(s.Event.READY).stayIn(r.STARTUP),{name:s.Event.PAUSE,from:r.STARTUP,to:r.READY},(0,h.on)(s.Event.PLAYING).stayIn(r.PLAYING),(0,h.on)(s.Event.TIMECHANGED).stayIn(r.PLAYING),(0,h.on)(s.Event.END_BUFFERING).stayIn(r.PLAYING),{name:s.Event.START_BUFFERING,from:r.PLAYING,to:r.REBUFFERING},{name:s.Event.END_BUFFERING,from:r.REBUFFERING,to:r.PLAYING},(0,h.on)(s.Event.TIMECHANGED).stayIn(r.REBUFFERING),{name:s.Event.PAUSE,from:r.PLAYING,to:r.PAUSE},{name:s.Event.PAUSE,from:r.REBUFFERING,to:r.PAUSE},{name:s.Event.PLAY,from:r.PAUSE,to:r.PLAYING},{name:s.Event.VIDEO_CHANGE,from:r.PLAYING,to:r.QUALITYCHANGE},{name:s.Event.AUDIO_CHANGE,from:r.PLAYING,to:r.QUALITYCHANGE},(0,h.on)(s.Event.VIDEO_CHANGE).stayIn(r.QUALITYCHANGE),(0,h.on)(s.Event.AUDIO_CHANGE).stayIn(r.QUALITYCHANGE),{name:"FINISH_QUALITYCHANGE",from:r.QUALITYCHANGE,to:r.PLAYING},{name:s.Event.VIDEO_CHANGE,from:r.PAUSE,to:r.QUALITYCHANGE_PAUSE},{name:s.Event.AUDIO_CHANGE,from:r.PAUSE,to:r.QUALITYCHANGE_PAUSE},(0,h.on)(s.Event.VIDEO_CHANGE).stayIn(r.QUALITYCHANGE_PAUSE),(0,h.on)(s.Event.AUDIO_CHANGE).stayIn(r.QUALITYCHANGE_PAUSE),{name:"FINISH_QUALITYCHANGE_PAUSE",from:r.QUALITYCHANGE_PAUSE,to:r.PAUSE},{name:s.Event.SEEK,from:r.PLAYING,to:r.PLAY_SEEKING},{name:s.Event.PLAY,from:r.PLAY_SEEKING,to:r.END_PLAY_SEEKING},{name:s.Event.PLAYING,from:r.PLAY_SEEKING,to:r.END_PLAY_SEEKING},{name:s.Event.SEEKED,from:r.PLAY_SEEKING,to:r.END_PLAY_SEEKING},{name:s.Event.SEEK,from:r.END_PLAY_SEEKING,to:r.PLAY_SEEKING},{name:s.Event.PLAYING,from:r.END_PLAY_SEEKING,to:r.PLAYING},{name:s.Event.TIMECHANGED,from:r.END_PLAY_SEEKING,to:r.PLAYING},{name:s.Event.PAUSE,from:[r.PLAY_SEEKING,r.END_PLAY_SEEKING],to:r.PAUSE},(0,h.on)(s.Event.SEEK).stayIn(r.PLAY_SEEKING),(0,h.on)(s.Event.AUDIO_CHANGE).stayIn(r.PLAY_SEEKING),(0,h.on)(s.Event.VIDEO_CHANGE).stayIn(r.PLAY_SEEKING),(0,h.on)(s.Event.START_BUFFERING).stayIn(r.PLAY_SEEKING),(0,h.on)(s.Event.END_BUFFERING).stayIn(r.PLAY_SEEKING),(0,h.on)(s.Event.START_BUFFERING).stayIn(r.END_PLAY_SEEKING),(0,h.on)(s.Event.END_BUFFERING).stayIn(r.END_PLAY_SEEKING),(0,h.on)(s.Event.SEEKED).stayIn(r.END_PLAY_SEEKING),(0,h.on)(s.Event.PLAY).stayIn(r.END_PLAY_SEEKING),(0,h.on)(s.Event.SEEK).stayIn(r.PAUSE),(0,h.on)(s.Event.SEEKED).stayIn(r.PAUSE),(0,h.on)(s.Event.SEEKED).stayIn(r.PLAYING),{name:s.Event.END,from:r.PLAY_SEEKING,to:r.END},{name:s.Event.END,from:r.PLAYING,to:r.END},{name:s.Event.END,from:r.PAUSE,to:r.END},(0,h.on)(s.Event.SEEK).stayIn(r.END),(0,h.on)(s.Event.SEEKED).stayIn(r.END),(0,h.on)(s.Event.TIMECHANGED).stayIn(r.END),(0,h.on)(s.Event.END_BUFFERING).stayIn(r.END),(0,h.on)(s.Event.START_BUFFERING).stayIn(r.END),(0,h.on)(s.Event.END).stayIn(r.END),{name:s.Event.PLAY,from:r.END,to:r.STARTUP},{name:s.Event.ERROR,from:this.getAllStatesBut([r.STARTUP]),to:r.ERROR},{name:s.Event.UNLOAD,from:this.getAllStatesBut([r.STARTUP,r.AD_STARTUP]),to:r.END},{name:s.Event.CUSTOM_DATA_CHANGE,from:[r.PLAYING,r.PAUSE],to:r.CUSTOMDATACHANGE},{name:s.Event.PLAYING,from:r.CUSTOMDATACHANGE,to:r.PLAYING},{name:s.Event.PAUSE,from:r.CUSTOMDATACHANGE,to:r.PAUSE},{name:s.Event.SUBTITLE_CHANGE,from:r.PLAYING,to:r.SUBTITLE_CHANGING},(0,h.on)(s.Event.SUBTITLE_CHANGE).stayIn(r.PAUSE),(0,h.on)(s.Event.SUBTITLE_CHANGE).stayIn(r.REBUFFERING),(0,h.on)(s.Event.SUBTITLE_CHANGE).stayIn(r.END_PLAY_SEEKING),(0,h.on)(s.Event.SUBTITLE_CHANGE).stayIn(r.SUBTITLE_CHANGING),{name:s.Event.TIMECHANGED,from:r.SUBTITLE_CHANGING,to:r.PLAYING},{name:s.Event.AUDIOTRACK_CHANGED,from:r.PLAYING,to:r.AUDIOTRACK_CHANGING},(0,h.on)(s.Event.AUDIOTRACK_CHANGED).stayIn(r.PAUSE),(0,h.on)(s.Event.AUDIOTRACK_CHANGED).stayIn(r.REBUFFERING),(0,h.on)(s.Event.AUDIOTRACK_CHANGED).stayIn(r.END_PLAY_SEEKING),(0,h.on)(s.Event.AUDIOTRACK_CHANGED).stayIn(r.AUDIOTRACK_CHANGING),{name:s.Event.TIMECHANGED,from:r.AUDIOTRACK_CHANGING,to:r.PLAYING},{name:s.Event.START_AD,from:r.STARTUP,to:r.AD_STARTUP},{name:s.Event.END_AD,from:r.AD_STARTUP,to:r.STARTUP},(0,h.on)(s.Event.PLAY).stayIn(r.AD_STARTUP),(0,h.on)(s.Event.PAUSE).stayIn(r.AD_STARTUP),{name:s.Event.START_AD,from:r.READY,to:r.AD_READY},{name:s.Event.END_AD,from:r.AD_READY,to:r.STARTUP},(0,h.on)(s.Event.PLAY).stayIn(r.AD_READY),(0,h.on)(s.Event.PAUSE).stayIn(r.AD_READY),{name:s.Event.START_AD,from:r.PAUSE,to:r.AD_PAUSE},{name:s.Event.END_AD,from:r.AD_PAUSE,to:r.PAUSE},(0,h.on)(s.Event.PLAY).stayIn(r.AD_PAUSE),(0,h.on)(s.Event.PAUSE).stayIn(r.AD_PAUSE),{name:s.Event.START_AD,from:r.PLAYING,to:r.AD_PLAYING},{name:s.Event.END_AD,from:r.AD_PLAYING,to:r.PLAYING},(0,h.on)(s.Event.PLAY).stayIn(r.AD_PLAYING),(0,h.on)(s.Event.PAUSE).stayIn(r.AD_PLAYING),{name:s.Event.END,from:r.AD_PLAYING,to:r.END},(0,h.on)(s.Event.TIMECHANGED).stayIn(r.AD_STARTUP),(0,h.on)(s.Event.TIMECHANGED).stayIn(r.AD_READY),(0,h.on)(s.Event.TIMECHANGED).stayIn(r.AD_PAUSE),(0,h.on)(s.Event.TIMECHANGED).stayIn(r.AD_PLAYING),(0,h.on)(s.Event.END_AD).stayIn(r.PLAYING),(0,h.on)(s.Event.END_AD).stayIn(r.PAUSE),(0,h.on)(s.Event.END_AD).stayIn(r.READY),(0,h.on)(s.Event.END_AD).stayIn(r.STARTUP),{name:s.Event.MUTE,from:r.READY,to:r.MUTING_READY},{name:s.Event.UN_MUTE,from:r.READY,to:r.MUTING_READY},{name:"FINISH_MUTING",from:r.MUTING_READY,to:r.READY},{name:s.Event.MUTE,from:r.PLAYING,to:r.MUTING_PLAY},{name:s.Event.UN_MUTE,from:r.PLAYING,to:r.MUTING_PLAY},{name:"FINISH_MUTING",from:r.MUTING_PLAY,to:r.PLAYING},{name:s.Event.MUTE,from:r.PAUSE,to:r.MUTING_PAUSE},{name:s.Event.UN_MUTE,from:r.PAUSE,to:r.MUTING_PAUSE},{name:"FINISH_MUTING",from:r.MUTING_PAUSE,to:r.PAUSE},{name:s.Event.START_CAST,from:[r.READY,r.PAUSE,r.PLAYING],to:r.CASTING},(0,h.on)(s.Event.PAUSE).stayIn(r.CASTING),(0,h.on)(s.Event.PLAY).stayIn(r.CASTING),(0,h.on)(s.Event.PLAYING).stayIn(r.CASTING),(0,h.on)(s.Event.TIMECHANGED).stayIn(r.CASTING),(0,h.on)(s.Event.MUTE).stayIn(r.CASTING),(0,h.on)(s.Event.UN_MUTE).stayIn(r.CASTING),(0,h.on)(s.Event.SEEK).stayIn(r.CASTING),(0,h.on)(s.Event.SEEKED).stayIn(r.CASTING),{name:s.Event.END_CAST,from:r.CASTING,to:r.READY},(0,h.on)(s.Event.SEEK).stayIn(r.READY),(0,h.on)(s.Event.SEEKED).stayIn(r.READY),(0,h.on)(s.Event.SEEKED).stayIn(r.STARTUP),{name:s.Event.MANUAL_SOURCE_CHANGE,from:this.getAllStatesBut([r.SETUP]),to:r.SOURCE_CHANGING},{name:s.Event.MANUAL_SOURCE_CHANGE,from:r.SETUP,to:r.INITIAL_SOURCE_CHANGE},{name:s.Event.SOURCE_UNLOADED,from:this.getAllStates(),to:r.SOURCE_CHANGING},{name:s.Event.VIDEO_CHANGE,from:r.REBUFFERING,to:r.QUALITYCHANGE_REBUFFERING},{name:s.Event.AUDIO_CHANGE,from:r.REBUFFERING,to:r.QUALITYCHANGE_REBUFFERING},(0,h.on)(s.Event.VIDEO_CHANGE).stayIn(r.QUALITYCHANGE_REBUFFERING),(0,h.on)(s.Event.AUDIO_CHANGE).stayIn(r.QUALITYCHANGE_REBUFFERING),{name:"FINISH_QUALITYCHANGE_REBUFFERING",from:r.QUALITYCHANGE_REBUFFERING,to:r.REBUFFERING}],callbacks:(t={onbeforeevent:function(e,t,n,i,a){if(t===r.REBUFFERING&&n===r.QUALITYCHANGE_REBUFFERING)return!1}},t["onleave".concat(r.END_PLAY_SEEKING)]=function(e,t,i,a,o){var s;if(i!==r.PLAY_SEEKING){var u=a-(null!==(s=n.seekStartTimestamp)&&void 0!==s?s:a);n.seekStartTimestamp=void 0,n.stateMachineCallbacks.setVideoTimeEndFromEvent(o),n.stateMachineCallbacks.end_play_seeking(u,"end_play_seeking")}},t["onleave".concat(r.PLAYING)]=function(){n.playingHeartbeatService.stopHeartbeat()},t["onleave".concat(r.PLAY_SEEKING)]=function(e,t,i,a,o){var s;if(i!==r.END_PLAY_SEEKING){var u=a-(null!==(s=n.seekStartTimestamp)&&void 0!==s?s:a);n.seekStartTimestamp=void 0,n.stateMachineCallbacks.end_play_seeking(u,"end_play_seeking")}},t.onleavestate=function(e,t,i,a,o){if((0,h.logStateMachineTransition)("LEAVE",e,t,i),e!==s.JSM_INITIAL_EVENT&&(t===r.REBUFFERING&&n.resetRebufferingHelpers(),a)){n.addStatesToLog(e,t,i,a,o);var l=a-n.onEnterStateTimestamp;o&&i!==r.END_PLAY_SEEKING&&n.stateMachineCallbacks.setVideoTimeEndFromEvent(o),o&&e!==s.Event.END_AD&&(n.lastTrackedVideoEndObject=o),t!==r.READY&&t!==r.AD_STARTUP||i!==r.STARTUP?t!==r.STARTUP||i!==r.PLAYING&&i!==r.AD_STARTUP||n.clearVideoStartTimeout():n.setVideoStartTimeout();var p=String(t).toLowerCase();if(i===r.VIDEOSTART_FAILED)n.clearVideoStartTimeout(),n.stateMachineCallbacks.videoStartFailed(l,(0,u.createVideoStartFailedReason)(e));else if(t===r.END_PLAY_SEEKING)(0,d.noOp)();else if(e===s.Event.UNLOAD)n.stateMachineCallbacks.unload(l,p);else if(t===r.AD_PAUSE||t===r.AD_READY||t===r.AD_PLAYING||t===r.AD_STARTUP)n.stateMachineCallbacks.setVideoTimeStartFromEvent(n.lastTrackedVideoEndObject),n.stateMachineCallbacks.setVideoTimeEndFromEvent(n.lastTrackedVideoEndObject),n.stateMachineCallbacks.ad(l,"ad");else if(e===s.Event.START_AD&&t===r.STARTUP)(0,d.noOp)();else if(e===s.Event.SOURCE_LOADED&&t===r.INITIAL_SOURCE_CHANGE)(0,d.noOp)();else if(t===r.SETUP)n.stateMachineCallbacks.setup(1,r.SETUP.toLowerCase());else if(e===s.Event.PAUSE&&t===r.STARTUP&&i===r.READY)n.clearVideoStartTimeout();else if(t!==r.ERROR){var f=n.stateMachineCallbacks[p];if("function"==typeof f)try{f(l,p,o)}catch(e){c.logger.error("Exception occurred in State Machine callback "+p,o,e)}else(0,h.logMissingCallbackWarning)(t,[r.READY,r.SOURCE_CHANGING])}o&&i!==r.END_PLAY_SEEKING&&t!==r.PLAY_SEEKING&&i!==r.PLAY_SEEKING&&t!==r.AD_STARTUP&&t!==r.AD_READY&&t!==r.AD_PLAYING&&t!==r.AD_PAUSE&&n.stateMachineCallbacks.setVideoTimeStartFromEvent(o),e===s.Event.VIDEO_CHANGE?n.stateMachineCallbacks.videoChange(o):e===s.Event.AUDIO_CHANGE?n.stateMachineCallbacks.audioChange(o):e===s.Event.MUTE?n.stateMachineCallbacks.mute():e===s.Event.UN_MUTE&&n.stateMachineCallbacks.unMute()}},t["onenter".concat(r.PLAYING)]=function(){n.playingHeartbeatService.startHeartbeat()},t["onenter".concat(r.PLAY_SEEKING)]=function(e,t,i,a,o){t!==r.END_PLAY_SEEKING&&(n.seekStartTimestamp=a)},t.onenterstate=function(t,i,a,o,u){(0,h.logStateMachineTransition)("ENTER",t,i,a),n.onEnterStateTimestamp=null!=o?o:Date.now(),(0,d.isNumber)(e.starttime)&&t===s.JSM_INITIAL_EVENT&&(n.onEnterStateTimestamp=e.starttime),!u||a===r.END_PLAY_SEEKING||i===r.PLAY_SEEKING||i===r.AD_STARTUP||i===r.AD_READY||i===r.AD_PLAYING||i===r.AD_PAUSE||t===s.Event.SEEK&&i===r.END_PLAY_SEEKING||n.stateMachineCallbacks.setVideoTimeStartFromEvent(u),t===s.Event.START_CAST&&a===r.CASTING&&n.stateMachineCallbacks.startCasting(o,u),a===r.REBUFFERING&&n.startRebufferingHeartbeatInterval()},t["onafter".concat(s.Event.ERROR)]=function(e,t,i,a,r){n.stateMachineCallbacks.error(r)},t.onafterevent=function(e,t,i,a,o){e===s.Event.MANUAL_SOURCE_CHANGE&&t===r.SETUP?n.stateMachineCallbacks.initialSourceChange(o):e===s.Event.MANUAL_SOURCE_CHANGE&&n.stateMachineCallbacks.manualSourceChange(o),i===r.QUALITYCHANGE_PAUSE&&n.stateMachine.FINISH_QUALITYCHANGE_PAUSE(a),i===r.QUALITYCHANGE&&n.stateMachine.FINISH_QUALITYCHANGE(a),i===r.QUALITYCHANGE_REBUFFERING&&n.stateMachine.FINISH_QUALITYCHANGE_REBUFFERING(a),i!==r.MUTING_READY&&i!==r.MUTING_PLAY&&i!==r.MUTING_PAUSE||n.stateMachine.FINISH_MUTING(a)},t)})},t.prototype.onSsaiPlaybackInteraction=function(e,t){this.currentState==r.PLAYING&&this.sendPlayingSample(e,t,!0)},t.prototype.getStates=function(){return this.debuggingStates},t.prototype.setEnabledDebugging=function(e){this.enabledDebugging=e},t.prototype.addStatesToLog=function(e,t,n,i,a){this.enabledDebugging&&this.debuggingStates.push(new l.EventDebugging(e,t,n,i,a))},t}(p.AnalyticsStateMachine);t.Bitmovin8AnalyticsStateMachine=f},2595:function(e,t){var n;n={VERSION:"2.4.0",Result:{SUCCEEDED:1,NOTRANSITION:2,CANCELLED:3,PENDING:4},Error:{INVALID_TRANSITION:100,PENDING_TRANSITION:200,INVALID_CALLBACK:300},WILDCARD:"*",ASYNC:"async",create:function(e,t){var i="string"==typeof e.initial?{state:e.initial}:e.initial,a=e.terminal||e.final,r=t||e.target||{},o=e.events||[],s=e.callbacks||{},u={},l={},c=function(e){var t=Array.isArray(e.from)?e.from:e.from?[e.from]:[n.WILDCARD];u[e.name]=u[e.name]||{};for(var i=0;i<t.length;i++)l[t[i]]=l[t[i]]||[],l[t[i]].push(e.name),u[e.name][t[i]]=e.to||t[i];e.to&&(l[e.to]=l[e.to]||[])};i&&(i.event=i.event||"startup",c({name:i.event,from:"none",to:i.state}));for(var d=0;d<o.length;d++)c(o[d]);for(var p in u)u.hasOwnProperty(p)&&(r[p]=n.buildEvent(p,u[p]));for(var p in s)s.hasOwnProperty(p)&&(r[p]=s[p]);return r.current="none",r.is=function(e){return Array.isArray(e)?e.indexOf(this.current)>=0:this.current===e},r.can=function(e){return!this.transition&&void 0!==u[e]&&(u[e].hasOwnProperty(this.current)||u[e].hasOwnProperty(n.WILDCARD))},r.cannot=function(e){return!this.can(e)},r.transitions=function(){return(l[this.current]||[]).concat(l[n.WILDCARD]||[])},r.isFinished=function(){return this.is(a)},r.error=e.error||function(e,t,n,i,a,r,o){throw o||r},r.states=function(){return Object.keys(l).sort()},i&&!i.defer&&r[i.event](),r},doCallback:function(e,t,i,a,r,o){if(t)try{return t.apply(e,[i,a,r].concat(o))}catch(t){return e.error(i,a,r,o,n.Error.INVALID_CALLBACK,"an exception occurred in a caller-provided callback function",t)}},beforeAnyEvent:function(e,t,i,a,r){return n.doCallback(e,e.onbeforeevent,t,i,a,r)},afterAnyEvent:function(e,t,i,a,r){return n.doCallback(e,e.onafterevent||e.onevent,t,i,a,r)},leaveAnyState:function(e,t,i,a,r){return n.doCallback(e,e.onleavestate,t,i,a,r)},enterAnyState:function(e,t,i,a,r){return n.doCallback(e,e.onenterstate||e.onstate,t,i,a,r)},changeState:function(e,t,i,a,r){return n.doCallback(e,e.onchangestate,t,i,a,r)},beforeThisEvent:function(e,t,i,a,r){return n.doCallback(e,e["onbefore"+t],t,i,a,r)},afterThisEvent:function(e,t,i,a,r){return n.doCallback(e,e["onafter"+t]||e["on"+t],t,i,a,r)},leaveThisState:function(e,t,i,a,r){return n.doCallback(e,e["onleave"+i],t,i,a,r)},enterThisState:function(e,t,i,a,r){return n.doCallback(e,e["onenter"+a]||e["on"+a],t,i,a,r)},beforeEvent:function(e,t,i,a,r){if(!1===n.beforeThisEvent(e,t,i,a,r)||!1===n.beforeAnyEvent(e,t,i,a,r))return!1},afterEvent:function(e,t,i,a,r){n.afterThisEvent(e,t,i,a,r),n.afterAnyEvent(e,t,i,a,r)},leaveState:function(e,t,i,a,r){var o=n.leaveThisState(e,t,i,a,r),s=n.leaveAnyState(e,t,i,a,r);return!1!==o&&!1!==s&&(n.ASYNC===o||n.ASYNC===s?n.ASYNC:void 0)},enterState:function(e,t,i,a,r){n.enterThisState(e,t,i,a,r),n.enterAnyState(e,t,i,a,r)},buildEvent:function(e,t){return function(){var i=this.current,a=t[i]||(t[n.WILDCARD]!=n.WILDCARD?t[n.WILDCARD]:i)||i,r=Array.prototype.slice.call(arguments);if(this.transition)return this.error(e,i,a,r,n.Error.PENDING_TRANSITION,"event "+e+" inappropriate because previous transition did not complete");if(this.cannot(e))return this.error(e,i,a,r,n.Error.INVALID_TRANSITION,"event "+e+" inappropriate in current state "+this.current);if(!1===n.beforeEvent(this,e,i,a,r))return n.Result.CANCELLED;if(i===a)return n.afterEvent(this,e,i,a,r),n.Result.NOTRANSITION;var o=this;this.transition=function(){return o.transition=null,o.current=a,n.enterState(o,e,i,a,r),n.changeState(o,e,i,a,r),n.afterEvent(o,e,i,a,r),n.Result.SUCCEEDED},this.transition.cancel=function(){o.transition=null,n.afterEvent(o,e,i,a,r)};var s=n.leaveState(this,e,i,a,r);return!1===s?(this.transition=null,n.Result.CANCELLED):n.ASYNC===s?n.Result.PENDING:this.transition?this.transition():void 0}}},e.exports&&(t=e.exports=n),t.StateMachine=n},2647:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.InternalAdapter=void 0;var a=n(675),r=n(9796),o=n(4916),s=n(4300),u=n(2361),l=n(4820),c=n(1916),d=n(5063),p=n(2376),h=function(){function e(e){var t=this;this.stateMachineCallbacks=i({},l.defaultStateMachineCallbacks),this.qualityChangeService=new r.default,this.ssaiService=new s.SsaiService,this._onLicenseKeyReceived=new a.EventDispatcher,this._onLicenseCallFailed=new a.EventDispatcher,this.sourceInfoFallbackService=new o.SourceInfoFallbackService,this.drmPerformanceInfo=void 0,this._windowEventTracker=new p.WindowEventTracker,this.eventCallback=function(e,n){n=n||{},t.stateMachine?t.stateMachine.callEvent(e,n,Date.now()):c.logger.log("Bitmovin Analytics: StateMachine isn't ready yet")},e||(e={starttime:void 0}),e.starttime||(e.starttime=(0,d.getCurrentTimestamp)()),this.opts=e}return Object.defineProperty(e.prototype,"windowEventTracker",{get:function(){return this._windowEventTracker},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onLicenseKeyReceived",{get:function(){return this._onLicenseKeyReceived},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onLicenseCallFailed",{get:function(){return this._onLicenseCallFailed},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"downloadSpeedInfo",{get:function(){return{segmentsDownloadCount:0,segmentsDownloadSize:0,segmentsDownloadTime:0,avgDownloadSpeed:0,minDownloadSpeed:0,maxDownloadSpeed:0,avgTimeToFirstByte:0}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"segments",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.release=function(){this.stateMachine.resetIntervals(),this.stateMachineCallbacks.release(),this.resetSourceRelatedState(),this._windowEventTracker.release()},e.prototype.getCommonPlaybackInfo=function(){var e=1;return window.devicePixelRatio>0&&(e=window.devicePixelRatio),{screenHeight:window.screen.height*e,screenWidth:window.screen.width*e}},e.prototype.clearValues=function(){},e.prototype.clearSegments=function(){},e.prototype.resetSourceRelatedState=function(){this.sourceInfoFallbackService.reset(),this.drmPerformanceInfo=void 0},e.prototype.setCustomData=function(e){var t=this.stateMachine.currentState;!this.stateMachine.currentState||"PAUSE"!==this.stateMachine.currentState&&"PLAYING"!==this.stateMachine.currentState?this.stateMachineCallbacks.customdatachange(void 0,void 0,{values:e}):(this.eventCallback(u.Event.CUSTOM_DATA_CHANGE,{currentTime:this.currentTime}),this.eventCallback(u.Event[t],{values:e,currentTime:this.currentTime}))},e.prototype.isAudioMuted=function(e,t){return e||t<.01},e}();t.InternalAdapter=h},2663:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HeartbeatService=t.DEFAULT_HEARTBEAT_INTERVAL_MS=void 0,t.DEFAULT_HEARTBEAT_INTERVAL_MS=59700;var n=function(){function e(e){this.getPlayerCurrentTime=e}return e.prototype.setListener=function(e){this.listener=e},e.prototype.startHeartbeat=function(){var e=this;void 0!==this.heartbeatInterval&&this.clearHeartbeat(),this.heartbeatInterval=window.setInterval((function(){var t,n={currentTime:e.getPlayerCurrentTime()};null===(t=e.listener)||void 0===t||t.onHeartbeat(n)}),t.DEFAULT_HEARTBEAT_INTERVAL_MS)},e.prototype.stopHeartbeat=function(){this.clearHeartbeat()},e.prototype.clearHeartbeat=function(){window.clearTimeout(this.heartbeatInterval),this.heartbeatInterval=void 0},e}();t.HeartbeatService=n},2982:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generateUUID=void 0,t.generateUUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}},3054:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BackendFactory=void 0;var i=n(5253),a=n(4649),r=n(7540),o=n(3489),s=n(399),u=n(2226),l=n(7390),c=function(){function e(){}return e.prototype.createBackend=function(e,t,n,i){if(!this.isEnabled(e))return new u.NoOpBackend;var a=this.createInnerBackend(e,t,n,i);return this.decorateWithDebuggingBackend(e,a)},e.prototype.decorateWithDebuggingBackend=function(e,t){return e.debug?new r.DebuggingBackend(e,t):t},e.prototype.decorateWithBackendFromConfigIfAvailable=function(e,t){return void 0!==e&&void 0!==e.config&&void 0!==e.config.wrapBackend?e.config.wrapBackend(t):t},e.prototype.isEnabled=function(e){return void 0===e.config||!1!==e.config.enabled},e.prototype.createInnerBackend=function(e,t,n,r){var u=e.config&&e.config.backendUrl||a.ANALYTICS_BACKEND_BASE_URL,c=new o.LicenseCheckingBackend(t,i.LicenseCall,u,n,r);c=this.decorateWithBackendFromConfigIfAvailable(e,c);var d=new l.SequenceNumberBackend(c);return new s.LongPlaytimeFilteringBackend(d)},e}();t.BackendFactory=c},3129:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Queue=void 0;var n=function(){function e(){this._items=[]}return Object.defineProperty(e.prototype,"items",{get:function(){return this._items},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"length",{get:function(){return this._items.length},enumerable:!1,configurable:!0}),e.prototype.offer=function(e){this._items.push(e)},e.prototype.poll=function(){return this._items.shift()},e.prototype.clear=function(){this._items.splice(0)},e.prototype.limit=function(e){for(;this._items.length>e;)this.poll()},e}();t.Queue=n},3413:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.normalizeVideoDuration=function(e){return(0,i.isNullish)(e)||Number.isNaN(e)||e===1/0?0:e};var i=n(5063)},3458:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ErrorCode=void 0;var n=function(){function e(e,t){this.code=e,this.message=t}return e.BITMOVIN_PLAYER_LICENSING_ERROR=new e(1103,""),e.SETUP_MISSING_LICENSE_WHITELIST=new e(1105,""),e.QUALITY_CHANGE_THRESHOLD_EXCEEDED=new e(1e4,"ANALYTICS_QUALITY_CHANGE_THRESHOLD_EXCEEDED"),e.BUFFERING_TIMEOUT_REACHED=new e(10001,"ANALYTICS_BUFFERING_TIMEOUT_REACHED"),e.VIDEO_STARTUP_TIMEOUT_REACHED=new e(10002,"ANALYTICS_VIDEOSTART_TIMEOUT_REACHED"),e}();t.ErrorCode=n},3489:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.LicenseCheckingBackend=void 0;var a=n(4501),r=n(1916),o=n(4649),s=n(2226),u=n(1125),l=n(2364),c=function(){function e(e,t,n,a,r){var l=this;this.licenseCall=t,this.backendBaseUrl=n,this.adapter=a,this.authenticationCallback=r,this.licenseKeyReceived=function(e){clearTimeout(l.licenseLazyLoadingTimeoutHandle),l.unsubscribeFromAdapter(),l.info.key=e.licenseKey,l.promise=l.wrapLicenseCheckPromiseWithCallback(l.performLicenseCheck(),l.authenticationCallback)},this.licenseCallFailed=function(){clearTimeout(l.licenseLazyLoadingTimeoutHandle),l.unsubscribeFromAdapter(),l.backend=new s.NoOpBackend,l.authenticationCallback.authenticationCompleted(!1,void 0)},this.licenseLazyLoadingTimeout=function(){l.unsubscribeFromAdapter(),l.backend=new s.NoOpBackend,l.authenticationCallback.authenticationCompleted(!1,void 0)},this.info=i({},e),this.backend=new u.QueueBackend,this.licenseCall=t,void 0!==e.key&&""!==e.key?this.promise=this.wrapLicenseCheckPromiseWithCallback(this.performLicenseCheck(),this.authenticationCallback):!0===a.supportsDeferredLicenseLoading?(a.onLicenseKeyReceived.subscribe(this.licenseKeyReceived),a.onLicenseCallFailed.subscribe(this.licenseCallFailed),this.licenseLazyLoadingTimeoutHandle=window.setTimeout(this.licenseLazyLoadingTimeout,o.ANALYTICS_LICENSECALL_TIMEOUT)):(r.authenticationCompleted(!1,void 0),this.backend=new s.NoOpBackend)}return e.prototype.performLicenseCheck=function(){var e=this,t=this.info,n=t.key,i=t.domain,o=t.version;if(!n||""===n){var u={status:a.LicensingResult.Denied,message:"No license key provided"};return this.backend=new s.NoOpBackend,Promise.resolve(u)}return this.licenseCall(n,i,o,this.backendBaseUrl).then((function(t){if(t.status!==a.LicensingResult.Granted)throw new Error(t.message);var i=new l.RemoteBackend(!0,e.backendBaseUrl,n);return e.backend.flushTo(i),e.backend=i,t})).catch((function(t){return"Ignoring Impression due to DNT Header being set"===t.message?r.logger.error("License Check for Bitmovin Analytics failed because of",t):r.logger.errorMessageToUser("License Check for Bitmovin Analytics failed because of",t),e.backend=new s.NoOpBackend,{status:a.LicensingResult.Denied,message:t.message}}))},e.prototype.sendRequest=function(e){this.backend.sendRequest(e)},e.prototype.sendUnloadRequest=function(e){this.backend.sendUnloadRequest(e)},e.prototype.sendRequestSynchronous=function(e){this.backend.sendRequestSynchronous(e)},e.prototype.sendAdRequest=function(e){this.backend.sendAdRequest(e)},e.prototype.unsubscribeFromAdapter=function(){this.adapter.onLicenseKeyReceived.unsubscribe(this.licenseKeyReceived),this.adapter.onLicenseCallFailed.unsubscribe(this.licenseCallFailed)},e.prototype.wrapLicenseCheckPromiseWithCallback=function(e,t){return e.then((function(e){return t.authenticationCompleted(e.status===a.LicensingResult.Granted,e.features),e}))},e}();t.LicenseCheckingBackend=c},3565:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.VideoStartFailedReason=void 0,t.createVideoStartFailedReason=function(e){switch(e){case a.Event.ERROR:return i.PLAYER_ERROR;case a.Event.UNLOAD:return i.PAGE_CLOSED;case a.Event.VIDEOSTART_TIMEOUT:return i.TIMEOUT;default:return i.UNKNOWN}};var i,a=n(2361);!function(e){e.PAGE_CLOSED="PAGE_CLOSED",e.PLAYER_ERROR="PLAYER_ERROR",e.TIMEOUT="TIMEOUT",e.UNKNOWN="UNKNOWN"}(i||(t.VideoStartFailedReason=i={}))},3588:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.AD_TYPE=void 0,function(e){e[e.NO_AD=0]="NO_AD",e[e.CSAI=1]="CSAI",e[e.SSAI=2]="SSAI"}(n||(t.AD_TYPE=n={}))},3612:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ViewportTracker=void 0;var i=n(1916),a=n(5063),r=n(5063),o=function(){function e(e,t,n){void 0===n&&(n=1);var r=this;this.inViewport=void 0,this.threshold=n,this.onIntersectionChanged=t,this.element=e;try{var o={root:null,rootMargin:"0px",threshold:n};this.observer=new IntersectionObserver((function(e,t){return r.handleIntersect(e,t)}),o),this.observer.observe(e)}catch(e){i.logger.log("Couldn't create instance of IntersectionObserver. timeInViewport will always be reported as 100%.")}this.hidden=a.getHiddenProp(),this.hidden&&(this.visibilityChange=this.hidden.replace("hidden","")+"visibilitychange",document.addEventListener(this.visibilityChange,this.handleVisibilityChange=this.handleVisibilityChange.bind(this),!1))}return e.prototype.isHidden=function(){return this.hidden&&document[this.hidden]},e.prototype.isInViewport=function(){return!this.isHidden()&&(!!(0,r.isNullish)(this.inViewport)||this.inViewport)},e.prototype.dispose=function(){this.observer&&(this.observer.unobserve(this.element),this.observer.disconnect()),this.visibilityChange&&document.removeEventListener(this.visibilityChange,this.handleVisibilityChange,!1)},e.prototype.handleVisibilityChange=function(){this.onIntersectionChanged()},e.prototype.handleIntersect=function(e,t){var n=this;e.forEach((function(e){e.target===n.element&&(n.inViewport=!(e.intersectionRatio<n.threshold))})),this.onIntersectionChanged()},e}();t.ViewportTracker=o},3786:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Measure=void 0;var n=function(){function e(e){this.download=e}return Object.defineProperty(e.prototype,"speed",{get:function(){return this.download.size/this.download.duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"duration",{get:function(){return this.download.duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"size",{get:function(){return this.download.size},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"timeToFirstByte",{get:function(){return this.download.timeToFirstByte},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"timestamp",{get:function(){return this.download.timestamp},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"httpStatus",{get:function(){return this.download.httpStatus},enumerable:!1,configurable:!0}),e}();t.Measure=n},4067:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sanitizeBitmovin8AdapterSsaiAdBreakMetadataAdPosition=void 0;var i=n(5063);t.sanitizeBitmovin8AdapterSsaiAdBreakMetadataAdPosition=function(e){var t=e;return(0,i.isNotNullish)(null==t?void 0:t.adPosition)&&("pre-roll"===t.adPosition?t.adPosition="preroll":"mid-roll"===t.adPosition?t.adPosition="midroll":"post-roll"===(null==t?void 0:t.adPosition)&&(t.adPosition="postroll")),t}},4300:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SsaiService=void 0;var a,r=n(3588),o=n(3458),s=n(5529),u=n(5063),l=n(2982),c=n(7906);!function(e){e[e.AD_BREAK_STARTED=0]="AD_BREAK_STARTED",e[e.ACTIVE=1]="ACTIVE",e[e.IDLE=2]="IDLE"}(a||(a={}));var d=function(){function e(){this.eventHandler=void 0,this.adIndex=-1,this.state=a.IDLE,this.isFirstSampleOfAd=!1,this.sentAdQuartiles=[],this.errorSent=!1}return e.prototype.activate=function(e){this.eventHandler=e},e.prototype.adBreakStart=function(e){p(this.eventHandler)&&this.state==a.IDLE&&(this.state=a.AD_BREAK_STARTED,this.currentAdMetadata=i({},e))},e.prototype.adStart=function(e){var t;if(p(this.eventHandler)&&this.state!=a.IDLE){var n=(new Date).getTime(),i=this.eventHandler.getPlayerCurrentTime();this.eventHandler.reportPlaybackInteraction(n,{currentTime:i}),this.state=a.ACTIVE,this.isFirstSampleOfAd=!0,this.adIndex++,this.resetAdData(),this.currentAdMetadata={adPosition:null===(t=this.currentAdMetadata)||void 0===t?void 0:t.adPosition,adId:null==e?void 0:e.adId,adSystem:null==e?void 0:e.adSystem,customData:null==e?void 0:e.customData},this.currentAdImpressionId=(0,l.generateUUID)(),this.timestampOfAdStart=(0,c.getCurrentMonotonicTimestampInMs)(),this.eventHandler.reportEngagementInteraction({type:"started",adIndex:this.adIndex,currentAdImpressionId:this.currentAdImpressionId,currentAdMetadata:(0,u.isNotNullish)(this.currentAdMetadata)?this.currentAdMetadata:{}})}},e.prototype.adQuartileFinished=function(e,t){p(this.eventHandler)&&this.state==a.ACTIVE&&(this.sentAdQuartiles.includes(e)||(this.eventHandler.reportEngagementInteraction({type:"quartile",adIndex:this.adIndex,currentAdImpressionId:this.currentAdImpressionId,currentAdMetadata:(0,u.isNotNullish)(this.currentAdMetadata)?this.currentAdMetadata:{},quartile:e,quartileMetadata:t,timeSinceAdStartedInMs:(0,c.getMonotonicTimestampInMsSince)(this.timestampOfAdStart)}),this.sentAdQuartiles.push(e)))},e.prototype.adBreakEnd=function(){if(p(this.eventHandler)&&this.state!=a.IDLE){if(this.state==a.ACTIVE){var e=(new Date).getTime(),t=this.eventHandler.getPlayerCurrentTime();this.eventHandler.reportPlaybackInteraction(e,{currentTime:t})}this.resetAdBreakData()}},e.prototype.manipulate=function(e){var t,n,i,o;if(p(this.eventHandler)&&this.state==a.ACTIVE){e.ad=r.AD_TYPE.SSAI,e.adId=null===(t=this.currentAdMetadata)||void 0===t?void 0:t.adId,e.adSystem=null===(n=this.currentAdMetadata)||void 0===n?void 0:n.adSystem,e.adPosition=null===(i=this.currentAdMetadata)||void 0===i?void 0:i.adPosition,this.isFirstSampleOfAd&&(e.adIndex=this.adIndex,this.isFirstSampleOfAd=!1);var l=null===(o=this.currentAdMetadata)||void 0===o?void 0:o.customData;(0,u.isNotNullish)(l)&&s.customDataValuesKeys.forEach((function(t){l[t]&&(e[t]=l[t])})),e[c.SSAI_RELATED_SAMPLE_MARKER]=!0}},e.prototype.sendAdEngagementErrorSample=function(e,t){p(this.eventHandler)&&this.state===a.ACTIVE&&(this.errorSent||e!==o.ErrorCode.QUALITY_CHANGE_THRESHOLD_EXCEEDED.code&&(this.eventHandler.reportEngagementInteraction({type:"error",adIndex:this.adIndex,currentAdImpressionId:this.currentAdImpressionId,currentAdMetadata:(0,u.isNotNullish)(this.currentAdMetadata)?this.currentAdMetadata:{},errorCode:e,errorMessage:t,timeSinceAdStartedInMs:(0,c.getMonotonicTimestampInMsSince)(this.timestampOfAdStart)}),this.errorSent=!0))},e.prototype.resetSourceRelatedState=function(){this.resetAdBreakData(),this.adIndex=-1},e.prototype.resetAdBreakData=function(){this.state=a.IDLE,this.isFirstSampleOfAd=!1,delete this.currentAdMetadata,this.resetAdData()},e.prototype.resetAdData=function(){var e,t,n;null===(e=this.currentAdMetadata)||void 0===e||delete e.customData,null===(t=this.currentAdMetadata)||void 0===t||delete t.adId,null===(n=this.currentAdMetadata)||void 0===n||delete n.adSystem,this.errorSent=!1,this.currentAdImpressionId=void 0,this.sentAdQuartiles=[],this.timestampOfAdStart=void 0},e}();function p(e){return(0,u.isNotNullish)(e)}t.SsaiService=d},4350:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.Analytics=void 0,t.setQualityInfoOnSample=_;var a=n(3588),r=n(3458),o=n(8525),s=n(3565),u=n(6845),l=n(5529),c=n(8331),d=n(404),p=n(1916),h=n(1820),f=n(5063),m=n(5063),v=n(2982),E=n(4926),A=n(8917),y=n(3054),g=n(675),S=n(2226),T=n(847),I=n(8918),b=n(7906),C=function(){function e(e,t,n,a,r,o){var s=this;this.adapter=t,this.backendFactory=n,this.sessionHandler=a,this.featureManager=r,this.onErrorDetailEventDispatcher=o,this.pageLoadTime=0,this.playerStartupTime=0,this.videoStartupTime=0,this.autoplay=void 0,this.droppedSampleFrames=0,this.startupTime=0,this.authenticationCallback={authenticationCompleted:function(e,t){s.featureManager.configureFeatures(e,t)}},this.sourceChange=function(e){if(p.logger.log("Processing Source Change for Analytics",e),(0,m.isNullish)(e))s.adapter.sourceChange(s.config);else{(0,c.guardConfigChangeAgainstMissingTitle)(s.config,e),(0,c.guardConfigChangeAgainstMissingIsLive)(s.config,e),(0,c.guardAgainstDuplicatedUserId)(e);var t=(0,c.mergeAnalyticsConfig)(s.config,e);s.adapter.sourceChange(t)}},this.setCustomDataOnce=function(e){var t=i({},s.config);s.setState("customdatachange"),s.sendAnalyticsRequestAndClearValues(e),s.config=i({},t),s.setConfigParameters()},this.setCustomData=function(e){s.adapter.setCustomData(e)},this.getCurrentImpressionId=function(){return s.sample.impressionId},this.getUserId=function(){return s.sessionHandler.userId},this.changeCustomData=function(e){s.config=i(i({},s.config),(0,l.extractCustomDataFieldsOnly)(e)),s.setConfigParameters()},this.config=e,this.sessionHandler=new T.SessionPersistenceHandler(this.config);var u=this;this.errorDetailTrackingSettingsProvider={get domain(){return(0,c.getDomainFromAnalyticsConfig)(u.config)},get licenseKey(){var e;return null!==(e=u.config.key)&&void 0!==e?e:""},get impressionId(){var e;return null!==(e=u.getCurrentImpressionId())&&void 0!==e?e:""},get collectorConfig(){return u.config.config}};var d=this.adapter.onLicenseKeyReceived.subscribe((function(e){s.config.key||(s.config.key=e.licenseKey),d()}));this.sample=this.setupSample(),this.init(),this.setupStateMachineCallbacks();var h=this.adapter.initialize(this);this.featureManager.registerFeatures(h),this.adapter.adModule&&(this.adAnalytics=new A.AdAnalytics(this,this.adapter.adModule))}return Object.defineProperty(e.prototype,"version",{get:function(){return E.VERSION},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"errorDetailSubscribable",{get:function(){return this.onErrorDetailEventDispatcher},enumerable:!1,configurable:!0}),e.prototype.getPlayerInformationFromAdapter=function(){var e=this.config.player||this.adapter.getPlayerName();return{player:e,version:e+"-"+this.adapter.getPlayerVersion(),playerTech:this.adapter.getPlayerTech()}},e.prototype.init=function(){!0===this.adapter.supportsDeferredLicenseLoading||(0,h.isValidString)(this.config.key)&&!(0,h.isBlank)(this.config.key)?(p.logger.initialize(this.config.debug),this.featureManager.resetFeatures(),this.backend=this.createBackend(this.config),this.videoStartupTime=0,this.setConfigParameters(),this.generateNewImpressionId(),this.setUserId(),this.adapter.videoCompletionTracker&&this.adapter.videoCompletionTracker.reset(),this.adapter.qualityChangeService.resetValues(),this.adapter.ssaiService.resetSourceRelatedState()):p.logger.errorMessageToUser("Invalid analytics license key provided")},e.prototype.release=function(){var e;this.backend=new S.NoOpBackend,null===(e=this.adAnalytics)||void 0===e||e.release(),this.adapter.qualityChangeService.stopResetInterval()},e.prototype.setConfigParameters=function(){var e;this.sample.key=this.config.key,this.sample.playerKey=this.config.playerKey,this.config.player&&(this.sample.player=this.config.player),this.sample.domain=(0,c.getDomainFromAnalyticsConfig)(this.config),this.sample.deviceInformation=(0,c.getDeviceInformationFromAnalyticsConfig)(this.config),this.sample.cdnProvider=this.config.cdnProvider,this.sample.videoId=this.config.videoId,this.sample.videoTitle=this.config.title,this.sample.customUserId=null!==(e=this.config.userId)&&void 0!==e?e:this.config.customUserId,f.transferCustomDataFields(this.config,this.sample),this.sample.experimentName=this.config.experimentName},e.prototype.generateNewImpressionId=function(){this.sample.impressionId=(0,v.generateUUID)()},e.prototype.setUserId=function(){this.sample.userId=this.sessionHandler.userId},e.prototype.setupStateMachineCallbacks=function(){var e=this;this.adapter.stateMachineCallbacks.setup=function(t,n){if(p.logger.log("Setup bitmovin analytics "+e.sample.analyticsVersion+" with impressionId: "+e.sample.impressionId),e.setDuration(t),e.setState(n),e.playerStartupTime=e.sample.playerStartupTime=t,window.performance&&window.performance.timing){var i=f.getCurrentTimestamp()-window.performance.timing.navigationStart;e.pageLoadTime=e.sample.pageLoadTime=i}e.startupTime=t,e.sendAnalyticsRequestAndClearValues(),e.sample.pageLoadTime=0},this.adapter.stateMachineCallbacks.startup=function(t,n){e.sample.supportedVideoCodecs=d.CodecHelper.supportedVideoFormats,e.setState(n);var i=Math.max(t,1),a=(e.startupTime||0)+i;e.sample.startupTime=a,e.startupTime=a,e.setDuration(i),e.videoStartupTime=i,e.sample.videoStartupTime=i,e.autoplay=e.sample.autoplay=e.adapter.getAutoPlay(),e.adapter.qualityChangeService.setStartupHasFinished();var r=e.adapter.getDrmPerformanceInfo();(0,m.isNotNullish)(r)&&(e.sample.drmType=r.drmType,e.sample.drmLoadTime=r.drmLoadTime),e.sendAnalyticsRequestAndClearValues(),e.sample.autoplay=void 0},this.adapter.stateMachineCallbacks.playing=function(t,n){e.setDuration(t),e.setState(n),e.sample.played=t,e.setCompletionValues(),e.sendAnalyticsRequestAndClearValues()},this.adapter.stateMachineCallbacks.unload=function(t,n){var i=e.sample.videoTimeStart;"playing"===n&&(e.setDuration(t),e.setState(n),e.sample.played=t,e.setCompletionValues(),e.sendUnloadRequest(),f.isNumber(e.sample.videoTimeEnd)&&(i=e.sample.videoTimeEnd)),e.videoStartupTime>0&&(e.setVideoTimeStart(i),e.setVideoTimeEnd(i),e.clearValues(),e.setState("closed"),e.sendUnloadRequest())},this.adapter.stateMachineCallbacks.heartbeat=function(t,n,a){e.setState(n),e.setDuration(t),e.sample=i(i({},e.sample),a),"playing"===n&&e.setCompletionValues(),e.sendAnalyticsRequestAndClearValues()},this.adapter.stateMachineCallbacks.qualitychange=function(t,n){e.sendQualityChange(n,t)},this.adapter.stateMachineCallbacks.qualitychange_pause=function(t,n){e.sendQualityChange(n,t)},this.adapter.stateMachineCallbacks.qualitychange_rebuffering=function(t,n){e.sendQualityChange(n,t)},this.adapter.stateMachineCallbacks.videoChange=function(t){e.adapter.stateMachineCallbacks.setVideoTimeEndFromEvent(t),e.adapter.stateMachineCallbacks.setVideoTimeStartFromEvent(t),e.setPlaybackVideoPropertiesFromEvent(t)},this.adapter.stateMachineCallbacks.audioChange=function(t){e.adapter.stateMachineCallbacks.setVideoTimeEndFromEvent(t),e.adapter.stateMachineCallbacks.setVideoTimeStartFromEvent(t),e.setPlaybackAudioPropertiesFromEvent(t)},this.adapter.stateMachineCallbacks.audiotrack_changing=function(){e.sendAnalyticsRequestAndClearValues()},this.adapter.stateMachineCallbacks.pause=function(t,n){e.setDuration(t),e.setState(n),e.sample.paused=t,e.sendAnalyticsRequestAndClearValues()},this.adapter.stateMachineCallbacks.paused_seeking=function(t,n){e.setDuration(t),e.setState(n),e.sample.seeked=t,e.sendAnalyticsRequestAndClearValues()},this.adapter.stateMachineCallbacks.end_play_seeking=function(t,n){e.setState(n),e.setDuration(t),e.sample.seeked=t,e.sendAnalyticsRequestAndClearValues()},this.adapter.stateMachineCallbacks.rebuffering=function(t,n){e.setDuration(t),e.setState(n),e.sample.buffered=t,e.sendAnalyticsRequestAndClearValues()},this.adapter.stateMachineCallbacks.videoStartFailed=function(t,n){e.setState("startup"),e.sample.videoStartFailed=!0,e.sample.videoStartFailedReason=n,e.sample.duration=t,n!==s.VideoStartFailedReason.PLAYER_ERROR&&(n===s.VideoStartFailedReason.TIMEOUT&&(e.sample.errorCode=r.ErrorCode.VIDEO_STARTUP_TIMEOUT_REACHED.code,e.sample.errorMessage=r.ErrorCode.VIDEO_STARTUP_TIMEOUT_REACHED.message,e.onErrorDetailEventDispatcher.dispatch(i({},r.ErrorCode.VIDEO_STARTUP_TIMEOUT_REACHED))),e.sendAnalyticsRequestAndClearValues())},this.adapter.stateMachineCallbacks.error=function(t){e.adapter.stateMachineCallbacks.setVideoTimeEndFromEvent(t),e.adapter.stateMachineCallbacks.setVideoTimeStartFromEvent(t),e.setState("error"),e.sample.errorCode=t.code,e.sample.errorMessage=t.message,e.sample.errorData=JSON.stringify(t.legacyData),e.sample.errorSegments=e.adapter.segments.map((function(e){return e.name})),e.adapter.onError&&e.adapter.onError(),e.onErrorDetailEventDispatcher.dispatch({code:t.code,message:t.message,errorData:t.data}),e.sendAnalyticsRequestAndClearValues(),delete e.sample.errorCode,delete e.sample.errorMessage,delete e.sample.errorData},this.adapter.stateMachineCallbacks.ad=function(t,n){e.setDuration(t),e.setState(n),e.sample.ad=a.AD_TYPE.CSAI,e.sendAnalyticsRequestAndClearValues()},this.adapter.stateMachineCallbacks.mute=function(){e.sample.isMuted=!0},this.adapter.stateMachineCallbacks.unMute=function(){e.sample.isMuted=!1},this.adapter.stateMachineCallbacks.subtitle_changing=function(){e.sendAnalyticsRequestAndClearValues()},this.adapter.stateMachineCallbacks.setVideoTimeEndFromEvent=function(t){f.isNumber(t.currentTime)&&e.setVideoTimeEnd(f.calculateTime(t.currentTime))},this.adapter.stateMachineCallbacks.setVideoTimeStartFromEvent=function(t){f.isNumber(t.currentTime)&&e.setVideoTimeStart(f.calculateTime(t.currentTime))},this.adapter.stateMachineCallbacks.manualSourceChange=function(t){e.adapter.resetSourceRelatedState(),e.sample=e.setupSample(),e.startupTime=0,e.config=Object.keys(t.config).length>0?t.config:e.config,e.init()},this.adapter.stateMachineCallbacks.playlistTransition=function(t){e.sample=e.setupSample(),e.startupTime=0,e.init()},this.adapter.stateMachineCallbacks.initialSourceChange=function(t){e.config=t.config,e.setConfigParameters()},this.adapter.stateMachineCallbacks.end=function(){e.sample=e.setupSample(),e.startupTime=0,e.init()},this.adapter.stateMachineCallbacks.release=function(){e.release()},this.adapter.stateMachineCallbacks.customdatachange=function(t,n,i){i&&i.values&&e.changeCustomData(i.values)}},e.prototype.setDuration=function(e){this.sample.duration=e},e.prototype.setState=function(e){this.sample.state=e},e.prototype.setPlaybackVideoPropertiesFromEvent=function(e){f.isNumber(e.width)&&(this.sample.videoPlaybackWidth=e.width),f.isNumber(e.height)&&(this.sample.videoPlaybackHeight=e.height),f.isNumber(e.bitrate)&&(this.sample.videoBitrate=e.bitrate),(0,h.isValidString)(e.codec)&&(this.sample.videoCodec=e.codec)},e.prototype.setPlaybackAudioPropertiesFromEvent=function(e){f.isNumber(e.bitrate)&&(this.sample.audioBitrate=e.bitrate),(0,h.isValidString)(e.codec)&&(this.sample.audioCodec=e.codec)},e.prototype.setPlaybackInfoFromAdapter=function(){var e=this.adapter.getCurrentPlaybackInfo();e&&(this.sample.isLive=(0,m.isNotNullish)(this.config.isLive)?this.config.isLive:e.isLive,(0,m.isNullish)(this.sample.isLive)&&(this.sample.isLive=!1),(0,h.isValidString)(e.size)&&(this.sample.size=e.size),(0,h.isValidString)(e.playerTech)&&(this.sample.playerTech=e.playerTech),f.isNumber(e.videoDuration)&&(this.sample.videoDuration=f.calculateTime(e.videoDuration||0)),(0,h.isValidString)(e.streamFormat)&&(this.sample.streamFormat=e.streamFormat),(0,h.isValidString)(e.mpdUrl)&&(this.sample.mpdUrl=e.mpdUrl),(0,h.isValidString)(e.m3u8Url)&&(this.sample.m3u8Url=e.m3u8Url),(0,h.isValidString)(e.progUrl)&&(this.sample.progUrl=e.progUrl),f.isNumber(e.videoWindowWidth)&&(this.sample.videoWindowWidth=e.videoWindowWidth),f.isNumber(e.videoWindowHeight)&&(this.sample.videoWindowHeight=e.videoWindowHeight),f.isNumber(e.screenHeight)&&(this.sample.screenHeight=e.screenHeight),f.isNumber(e.screenWidth)&&(this.sample.screenWidth=e.screenWidth),f.isBoolean(e.isMuted)&&(this.sample.isMuted=e.isMuted),f.isBoolean(e.isCasting)&&(this.sample.isCasting=e.isCasting),(0,h.isValidString)(e.castTech)&&(this.sample.castTech=e.castTech),(0,h.isValidString)(e.videoTitle)&&!this.config.title&&(this.sample.videoTitle=e.videoTitle),(0,h.isValidString)(e.audioCodec)&&(this.sample.audioCodec=e.audioCodec),(0,h.isValidString)(e.videoCodec)&&(this.sample.videoCodec=e.videoCodec),(0,h.isValidString)(e.audioLanguage)&&(this.sample.audioLanguage=e.audioLanguage),f.isBoolean(e.subtitleEnabled)&&(this.sample.subtitleEnabled=e.subtitleEnabled),(0,h.isValidString)(e.subtitleLanguage)?this.sample.subtitleLanguage=e.subtitleLanguage:this.sample.subtitleLanguage=void 0,f.isNumber(e.droppedFrames)&&(this.sample.droppedFrames=Math.max(e.droppedFrames-this.droppedSampleFrames,0),this.droppedSampleFrames=e.droppedFrames))},e.prototype.setupSample=function(){var e;return this.droppedSampleFrames=0,i({platform:"web",playerStartupTime:0,pageLoadType:f.getPageLoadType(),path:f.sanitizePath(window.location.pathname),language:navigator.language||navigator.userLanguage,userAgent:navigator.userAgent,screenWidth:screen.width,screenHeight:screen.height,isLive:null!==(e=this.config.isLive)&&void 0!==e&&e,videoDuration:0,size:o.PlayerSize.Window,time:0,videoWindowWidth:0,videoWindowHeight:0,droppedFrames:0,played:0,buffered:0,paused:0,ad:0,seeked:0,videoPlaybackWidth:0,videoPlaybackHeight:0,videoBitrate:0,audioBitrate:0,videoTimeStart:0,videoTimeEnd:0,videoStartupTime:0,duration:0,startupTime:0,analyticsVersion:E.VERSION,pageLoadTime:0,completedTotal:0},this.getPlayerInformationFromAdapter())},e.prototype.sendAnalyticsRequest=function(e){if(this.setPlaybackInfoFromAdapter(),this.sample.time=f.getCurrentTimestamp(),this.sample.downloadSpeedInfo=this.adapter.downloadSpeedInfo,this.adapter.ssaiService.manipulate(this.sample),null!=e&&this.changeCustomData(e),(0,m.isNullish)(this.sample.videoBitrate)||this.sample.videoBitrate<=0){_(this.sample,this.adapter.getCurrentPlaybackInfo());var t=i({},this.sample);this.backend.sendRequest(t)}else t=i({},this.sample),this.backend.sendRequest(t),_(this.sample,this.adapter.getCurrentPlaybackInfo())},e.prototype.sendAnalyticsRequestAndClearValues=function(e){this.sendAnalyticsRequest(e),this.clearValues()},e.prototype.sendSsaiEngagementAdSample=function(e){var t;if(!0===(null===(t=this.config.config)||void 0===t?void 0:t.ssaiEngagementTrackingEnabled))if((0,I.isSsaiEngagementStartedData)(e)){var n=(0,b.createStartedAdSample)(this.sample,e);this.backend.sendAdRequest(n)}else if((0,I.isSsaiEngagementQuartileData)(e))n=(0,b.createQuartileAdSample)(this.sample,e),this.backend.sendAdRequest(n);else if((0,I.isSsaiEngagementErrorData)(e)){var i=(0,b.createErrorAdSample)(this.sample,e);this.backend.sendAdRequest(i)}},e.prototype.sendUnloadRequest=function(){this.backend.sendUnloadRequest(this.sample)},e.prototype.clearValues=function(){this.sample.ad=0,this.sample.paused=0,this.sample.played=0,this.sample.seeked=0,this.sample.buffered=0,this.sample.playerStartupTime=0,this.sample.videoStartupTime=0,this.sample.startupTime=0,this.sample.castTech=void 0,this.sample.duration=0,this.sample.droppedFrames=0,this.sample.drmLoadTime=void 0,this.sample.videoStartFailed=void 0,this.sample.videoStartFailedReason=void 0,this.sample.completed=void 0,this.sample.adId=void 0,this.sample.adSystem=void 0,this.sample.adPosition=void 0,this.sample.adIndex=void 0,this.sample[b.SSAI_RELATED_SAMPLE_MARKER]=void 0,this.adapter.clearValues()},e.prototype.createBackend=function(e){var t=(0,c.getDomainFromAnalyticsConfig)(e);return this.backendFactory.createBackend(e,{key:e.key,domain:t,version:E.VERSION},this.adapter,this.authenticationCallback)},e.prototype.setCompletionValues=function(){if(this.adapter.videoCompletionTracker){var e=this.adapter.videoCompletionTracker.addWatched({start:this.sample.videoTimeStart,end:this.sample.videoTimeEnd}),t=this.adapter.videoCompletionTracker.getCompletionPercentage();Number.isNaN(e)||Number.isNaN(t)||(this.sample.completed=e,this.sample.completedTotal=t)}},e.prototype.setVideoTimeStart=function(e){this.sample.videoTimeStart=e},e.prototype.setVideoTimeEnd=function(e){this.sample.videoTimeEnd=e},e.prototype.sendQualityChange=function(e,t){this.adapter.qualityChangeService.startResetInterval(),this.adapter.qualityChangeService.increaseCounter(),this.adapter.qualityChangeService.isQualityChangeEventEnabled()?(_(this.sample,this.adapter.getCurrentPlaybackInfo()),this.setDuration(t),this.setState(e),this.sendAnalyticsRequestAndClearValues()):(this.setDuration(t),this.adapter.stateMachineCallbacks.error(i(i({},r.ErrorCode.QUALITY_CHANGE_THRESHOLD_EXCEEDED),{legacyData:void 0,currentTime:void 0,data:{}})))},e.create=function(t,n){return new e(t,n,new y.BackendFactory,new T.SessionPersistenceHandler(t),new u.FeatureManager,new g.EventDispatcher)},e.version=E.VERSION,e}();function _(e,t){f.isNumber(t.videoPlaybackHeight)&&(e.videoPlaybackHeight=t.videoPlaybackHeight),f.isNumber(t.videoPlaybackWidth)&&(e.videoPlaybackWidth=t.videoPlaybackWidth),f.isNumber(t.videoBitrate)&&(e.videoBitrate=t.videoBitrate),f.isNumber(t.audioBitrate)&&(e.audioBitrate=t.audioBitrate)}t.Analytics=C},4355:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.CdnProvider=void 0,function(e){e.AKAMAI="akamai",e.BITGRAVITY="bitgravity",e.CHINACACHE="chinacache",e.CLOUDFLARE="cloudflare",e.CLOUDFRONT="cloudfront",e.FASTLY="fastly",e.MAXCDN="maxcdn"}(n||(t.CdnProvider=n={}))},4438:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.HttpRequestType=void 0,function(e){e.MANIFEST_DASH="manifest/dash",e.MANIFEST_HLS_MASTER="manifest/hls/master",e.MANIFEST_HLS_VARIANT="manifest/hls/variant",e.MANIFEST_SMOOTH="manifest/smooth",e.MANIFEST_ADS="manifest/ads",e.MEDIA_AUDIO="media/audio",e.MEDIA_VIDEO="media/video",e.MEDIA_SUBTITLES="media/subtitles",e.MEDIA_THUMBNAILS="media/thumbnails",e.MEDIA_SEGMENTINDEX="media/segmentindex",e.DRM_LICENSE_WIDEVINE="drm/license/widevine",e.DRM_LICENSE_PLAYREADY="drm/license/playready",e.DRM_LICENSE_FAIRPLAY="drm/license/fairplay",e.DRM_LICENSE_PRIMETIME="drm/license/primetime",e.DRM_LICENSE_CLEARKEY="drm/license/clearkey",e.DRM_LICENSE_TVKEY="drm/license/tvkey",e.DRM_CERTIFICATE_FAIRPLAY="drm/certificate/fairplay",e.KEY_HLS_AES="key/hls/aes",e.TIME_SYNC="time/sync",e.WEBRTC_SDP_REQUEST="webrtc/sdp/request",e.WEBRTC_SDP_ANSWER="webrtc/sdp/answer",e.WEBRTC_SDP_OFFER="webrtc/sdp/offer",e.UNKNOWN="unknown",e.INTERNAL="internal"}(n||(t.HttpRequestType=n={}))},4439:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AdSample=void 0;var i=n(5063),a=function(){function e(t){if(this.clicked=0,this.closed=0,this.completed=0,this.midpoint=0,this.quartile1=0,this.quartile3=0,this.skipped=0,this.started=0,t){this.adClickthroughUrl=t.clickThroughUrl,this.adId=t.id,this.mediaUrl=t.mediaFileUrl;var n=i.getHostnameAndPathFromUrl(this.mediaUrl||"");this.mediaPath=n.path,this.mediaServer=n.hostname,this.isLinear=t.isLinear,this.adPlaybackHeight=t.height,this.adPlaybackWidth=t.width,t.data&&(this.videoBitrate=void 0===t.data.bitrate?void 0:1e3*t.data.bitrate,this.streamFormat=t.data.mimeType);var a=t.data;a&&(a.adSystem&&(this.adSystem=a.adSystem.name),a.advertiser&&(this.advertiserName=a.advertiser.name),this.apiFramework=a.apiFramework,a.creative&&(this.creativeAdId=a.creative.adId,this.creativeId=a.creative.id,a.creative.universalAdId&&(this.universalAdIdRegistry=a.creative.universalAdId.idRegistry,this.universalAdIdValue=a.creative.universalAdId.value)),this.adDescription=a.adDescription,this.minSuggestedDuration=i.calculateTimeWithUndefined(a.minSuggestedDuration),a.survey&&(this.surveyUrl=a.survey.uri),this.adTitle=a.adTitle,this.wrapperAdsCount=a.wrapperAdIds?a.wrapperAdIds.length:0)}var r=t;r&&(this.adSkippableAfter=e.parseSkipAfter(r.skippableAfter),this.adSkippable=r.skippable||(void 0===this.adSkippableAfter?void 0:this.adSkippableAfter>=0),this.adDuration=i.calculateTime(r.duration))}return e.parseSkipAfter=function(e){if(void 0!==e)return-1===e?e:i.calculateTime(e)},e}();t.AdSample=a},4501:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.LicensingResult=void 0,function(e){e.Denied="denied",e.Granted="granted",e.Skip="skip"}(n||(t.LicensingResult=n={}))},4649:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SEQUENCE_NUMBER_LIMIT=t.ANALYTICS_MIN_SEEK_DIFFERENCE_THRESHOLD=t.ANALYTICS_REBUFFER_TIMEOUT=t.ANALYTICS_QUALITY_CHANGE_COUNT_RESET_INTERVAL=t.ANALYTICS_QUALITY_CHANGE_COUNT_THRESHOLD=t.ANALYTICS_VIDEOSTART_TIMEOUT=t.ANALYTICS_LICENSECALL_TIMEOUT=t.ANALYTICS_BACKEND_BASE_URL=t.MESSAGE_NAMESPACE=void 0,t.MESSAGE_NAMESPACE="urn:x-cast:com.bitmovin.analytics.cast",t.ANALYTICS_BACKEND_BASE_URL="https://analytics-ingress-global.bitmovin.com",t.ANALYTICS_LICENSECALL_TIMEOUT=6e4,t.ANALYTICS_VIDEOSTART_TIMEOUT=6e4,t.ANALYTICS_QUALITY_CHANGE_COUNT_THRESHOLD=50,t.ANALYTICS_QUALITY_CHANGE_COUNT_RESET_INTERVAL=36e5,t.ANALYTICS_REBUFFER_TIMEOUT=12e4,t.ANALYTICS_MIN_SEEK_DIFFERENCE_THRESHOLD=3,t.SEQUENCE_NUMBER_LIMIT=1e3},4679:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Bitmovin8Adapter=void 0;var i=n(4350),a=n(6398),r=n(8331),o=n(1916),s=n(2108),u=n(4926),l=n(4067),c=n(6596),d=function(){function e(e,t){var n,u=this;if(this.ssai={adBreakStart:function(e){if((0,s.isDefined)(u.internalAdapter)){var t=(0,l.sanitizeBitmovin8AdapterSsaiAdBreakMetadataAdPosition)(e);if((0,a.hasValidSsaiAdBreakMetadataStructure)(t)){var n=(0,a.simplySanitizeSsaiAdBreakMetadata)(t);u.internalAdapter.ssaiService.adBreakStart(n)}}},adStart:function(e){if((0,s.isDefined)(u.analytics)&&(0,s.isDefined)(u.internalAdapter)&&(0,a.hasValidSsaiAdMetadataStructure)(e)){var t=(0,a.simplySanitizeSsaiAdMetadata)(e);u.internalAdapter.ssaiService.adStart(t),u.analytics.setConfigParameters()}},adQuartileFinished:function(e,t){if((0,s.isDefined)(u.analytics)&&(0,s.isDefined)(u.internalAdapter)&&(0,a.hasValidSsaiAdQuartileStructure)(e)){var n=(0,a.sanitizeSsaiAdQuartileMetadata)(t);u.internalAdapter.ssaiService.adQuartileFinished(e,n)}},adBreakEnd:function(){(0,s.isDefined)(u.analytics)&&(0,s.isDefined)(u.internalAdapter)&&(u.internalAdapter.ssaiService.adBreakEnd(),u.analytics.setConfigParameters())}},(0,s.hasPlayerAlreadyBeenAugmented)(e))o.logger.errorMessageToUser("Bitmovin Analytics is already hooked up to this player instance");else{(0,s.markPlayerInstanceAsAugmented)(e);var d=e.getConfig(),p=(0,r.sanitizeAnalyticsConfig)(d.analytics);(0,r.guardAgainstDuplicatedUserId)(p),p.playerKey=null!==(n=p.playerKey)&&void 0!==n?n:d.key,this.internalAdapter=c.Bitmovin8InternalAdapter.create(e,t),this.analytics=i.Analytics.create(p,this.internalAdapter),e.analytics=this,this.wrapPlayerLoad(e,this.analytics)}}return e.prototype.wrapPlayerLoad=function(e,t){var n=e.load;e.load=function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];if(i.length>0){var r=i[0].analytics;t.sourceChange(r)}return n.apply(e,i)}},Object.defineProperty(e.prototype,"version",{get:function(){return u.VERSION},enumerable:!1,configurable:!0}),e.prototype.getCurrentImpressionId=function(){if((0,s.isDefined)(this.analytics))return this.analytics.getCurrentImpressionId()},e.prototype.getUserId=function(){if((0,s.isDefined)(this.analytics))return this.analytics.getUserId()},e.prototype.setCustomData=function(e){(0,s.isDefined)(this.internalAdapter)&&this.internalAdapter.setCustomData(e)},e.prototype.setCustomDataOnce=function(e){(0,s.isDefined)(this.analytics)&&this.analytics.setCustomDataOnce(e)},e.prototype.sourceChange=function(e){(0,s.isDefined)(this.analytics)&&this.analytics.sourceChange(e)},e.version=u.VERSION,e}();t.Bitmovin8Adapter=d},4820:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultStateMachineCallbacks=void 0,t.defaultStateMachineCallbacks={setup:function(){},startup:function(){},playing:function(){},heartbeat:function(){},qualitychange:function(){},qualitychange_pause:function(){},qualitychange_rebuffering:function(){},videoChange:function(){},audioChange:function(){},audiotrack_changing:function(){},pause:function(){},paused_seeking:function(){},end_play_seeking:function(){},rebuffering:function(){},error:function(){},end:function(){},unload:function(){},ad:function(){},mute:function(){},unMute:function(){},subtitle_changing:function(){},setVideoTimeEndFromEvent:function(){},setVideoTimeStartFromEvent:function(){},videoStartFailed:function(){},startCasting:function(){},casting:function(){},manualSourceChange:function(){},initialSourceChange:function(){},muting_ready:function(){},customdatachange:function(){},release:function(){},playlistTransition:function(){}}},4916:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SourceInfoFallbackService=void 0;var i=n(1820),a=n(5063),r=function(){function e(){}return e.prototype.reset=function(){this.streamFormat=void 0,this.audioCodec=void 0,this.videoCodec=void 0},e.prototype.setStreamFormat=function(e){this.streamFormat=e},e.prototype.applyStreamFormat=function(e){(0,i.isBlank)(this.streamFormat)||(e.streamFormat=this.streamFormat)},e.prototype.applyAndStoreCodecInfo=function(e,t){(0,a.isNotNullish)(null==t?void 0:t.audioCodec)&&(this.audioCodec=t.audioCodec),(0,a.isNotNullish)(null==t?void 0:t.videoCodec)&&(this.videoCodec=t.videoCodec),(0,i.isBlank)(this.audioCodec)||(e.audioCodec=this.audioCodec),(0,i.isBlank)(this.videoCodec)||(e.videoCodec=this.videoCodec)},e}();t.SourceInfoFallbackService=r},4926:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="v2.44.1"},4934:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.deleteCookie=t.getCookie=t.setCookie=void 0,t.setCookie=function(e,t,n){var i=e+"="+t+"; path=/";(null==n?void 0:n.domain)&&(i+="; domain=".concat(n.domain)),(null==n?void 0:n.expires)&&(i+="; expires=".concat(n.expires)),(null==n?void 0:n.maxAge)&&(i+="; max-age=".concat(n.maxAge)),document.cookie=i},t.getCookie=function(e){for(var t=e+"=",n=0,i=document.cookie.split(";");n<i.length;n++){for(var a=i[n],r=a;" "===r.charAt(0);)r=r.substring(1);if(0===r.indexOf(t))return r.substring(t.length,a.length)}return""},t.deleteCookie=function(e){document.cookie="".concat(e,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;")}},5063:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.joinUrls=t.getURLResourceName=t.calculatePercentage=t.getHostnameAndPathFromUrl=t.getHiddenProp=t.isVideoInFullscreen=t.getDocumentPropWithPrefix=t.transferCustomDataFields=t.getCustomDataString=t.noOp=t.getCurrentTimestamp=t.calculateTime=t.calculateTimeWithUndefined=t.sanitizePath=t.isPlainObject=t.isObject=t.isNumber=t.toBoolean=t.isBoolean=t.isNotNullish=t.isNullish=void 0,t.getPageLoadType=function(){var e=(0,t.getHiddenProp)();return(0,t.isNotNullish)(e)&&!0===document[e]?i.PAGE_LOAD_TYPE.BACKGROUND:i.PAGE_LOAD_TYPE.FOREGROUND};var i=n(9946),a=n(5529),r=n(1820);t.isNullish=function(e){return null==e},t.isNotNullish=function(e){return!(0,t.isNullish)(e)},t.isBoolean=function(e){return!(0,t.isNullish)(e)&&"boolean"==typeof e},t.toBoolean=function(e){return Boolean(e).valueOf()},t.isNumber=function(e){return!(0,t.isNullish)(e)&&"number"==typeof e},t.isObject=function(e){return(0,t.isNotNullish)(e)&&"object"==typeof e},t.isPlainObject=function(e){return"[object Object]"===Object.prototype.toString.call(e)},t.sanitizePath=function(e){return e.replace(/\/$/g,"")},t.calculateTimeWithUndefined=function(e){return void 0===e?e:(e*=1e3,Math.round(e))},t.calculateTime=function(e){return e*=1e3,Math.round(e)},t.getCurrentTimestamp=function(){return Date.now()},t.noOp=function(){},t.getCustomDataString=function(e){return"object"==typeof e?JSON.stringify(e):"function"==typeof e?(0,t.getCustomDataString)(e()):void 0===e?e:"string"!=typeof e?String(e):e},t.transferCustomDataFields=function(e,n){for(var i=1;i<=a.customDataFieldCount;i++){var r="customData".concat(i);n[r]=(0,t.getCustomDataString)(e[r])}},t.getDocumentPropWithPrefix=function(e){if(e in document)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),n=0,i=["webkit","moz","ms","o"];n<i.length;n++){var a=i[n];if(a+t in document)return a+t}},t.isVideoInFullscreen=function(){var e=(0,t.getDocumentPropWithPrefix)("fullscreenElement");return void 0!==e&&document[e]&&"VIDEO"===document[e].nodeName},t.getHiddenProp=function(){return(0,t.getDocumentPropWithPrefix)("hidden")},t.getHostnameAndPathFromUrl=function(e){var t=document.createElement("a");return t.href=e,{hostname:t.hostname,path:t.pathname}},t.calculatePercentage=function(e,t){if(void 0!==t&&0!==t)return Math.round((e||0)/t*100)},t.getURLResourceName=function(e){return 0===e.length?"":e.split("/").pop()||""},t.joinUrls=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.reduce((function(e,t){return((0,r.endsWith)(e,"/")&&void 0!==t?e.substr(0,e.length-1):e)+"/"+((0,r.startsWith)(t,"/")?t.substr(1):t)}))}},5248:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSourceInfoFromBitmovinSourceConfig=void 0;var i=n(5063);t.getSourceInfoFromBitmovinSourceConfig=function(e,t){if(!e)return{progBitrate:void 0,progUrl:void 0};if("string"==typeof e)return{progBitrate:0,progUrl:e};if(Array.isArray(e)&&e.length>0){var n=t.getPlaybackVideoData(),a=0;return(0,i.isNotNullish)(n)&&!isNaN(+n.id)&&(a=+n.id),{progBitrate:e[a].bitrate||0,progUrl:e[a].url}}return"object"!=typeof e||Array.isArray(e)?{progBitrate:void 0,progUrl:void 0}:{progBitrate:e.bitrate||0,progUrl:e.url}}},5253:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LicenseCall=function(e,t,n,r){var o=(0,a.joinUrls)(r,"/licensing"),s={analyticsVersion:n,domain:t,key:e};return new Promise((function(e){(0,i.post)(o,s,(function(t){e(t)}))}))};var i=n(92),a=n(5063)},5426:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AnalyticsCall=void 0;var i=n(7906),a=n(92),r=n(5063),o=function(){function e(e){this.analyticsServerUrl=(0,r.joinUrls)(e,"analytics"),this.adAnalyticsServerUrl=(0,r.joinUrls)(e,"analytics/a")}return e.prototype.sendRequest=function(e,t){var n=this.analyticsServerUrl;(0,i.isSsaiRelatedSample)(e)&&(n=(0,i.appendSsaiRoutingParam)(n)),(0,a.post)(n,e,t)},e.prototype.sendUnloadRequest=function(e){var t=this,n=this.analyticsServerUrl;(0,i.isSsaiRelatedSample)(e)&&(n=(0,i.appendSsaiRoutingParam)(n)),this.trySendBeacon((function(e){return t.sendRequestSynchronous(e,r.noOp)}),n,e)},e.prototype.trySendBeacon=function(e,t,n,i){if(void 0===i&&(i={}),void 0===navigator.sendBeacon)e(n);else{var a=new Blob([JSON.stringify(n)],i);navigator.sendBeacon(t,a)||e(n)}},e.prototype.sendRequestSynchronous=function(e,t){var n=this.analyticsServerUrl;(0,i.isSsaiRelatedSample)(e)&&(n=(0,i.appendSsaiRoutingParam)(n)),(0,a.post)(n,e,t,!1)},e.prototype.sendAdRequest=function(e){var t=this.adAnalyticsServerUrl;(0,i.isSsaiRelatedSample)(e)&&(t=(0,i.appendSsaiRoutingParam)(t)),this.trySendBeacon((function(e){return(0,a.post)(t,e,r.noOp)}),t,e)},e}();t.AnalyticsCall=o},5517:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AdCallbacks=void 0;var n=function(){this.onAdStarted=function(e){},this.onAdFinished=function(e){},this.onAdBreakStarted=function(e){},this.onAdBreakFinished=function(e){},this.onAdClicked=function(e){},this.onAdError=function(e){},this.onAdManifestLoaded=function(e){},this.onAdQuartile=function(e){},this.onAdSkipped=function(e){},this.onPlay=function(e){},this.onPause=function(e){},this.onBeforeUnload=function(){}};t.AdCallbacks=n},5529:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extractCustomDataFieldsOnly=t.customDataFieldCount=t.customDataValuesKeys=void 0,t.customDataValuesKeys=["customData1","customData2","customData3","customData4","customData5","customData6","customData7","customData8","customData9","customData10","customData11","customData12","customData13","customData14","customData15","customData16","customData17","customData18","customData19","customData20","customData21","customData22","customData23","customData24","customData25","customData26","customData27","customData28","customData29","customData30","customData31","customData32","customData33","customData34","customData35","customData36","customData37","customData38","customData39","customData40","customData41","customData42","customData43","customData44","customData45","customData46","customData47","customData48","customData49","customData50","experimentName"],t.customDataFieldCount=t.customDataValuesKeys.filter((function(e){return e.match("customData\\d+")})).length,t.extractCustomDataFieldsOnly=function(e){var n={};return t.customDataValuesKeys.forEach((function(t){e[t]&&(n[t]=e[t])})),n}},5766:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Converter=void 0;var n=function(){function e(){}return e.bytesToBits=function(e){return 8*e},e.bitsToBytes=function(e){return Math.round(e/8)},e.kiloBitsToBits=function(e){return 1e3*e},e}();t.Converter=n},5791:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AdAnalyticsSample=void 0;var i=n(5063),a=function(e){this.platform="web",e&&(this.videoImpressionId=e.impressionId,this.userAgent=e.userAgent,this.language=e.language,this.cdnProvider=e.cdnProvider,i.transferCustomDataFields(e,this),this.customUserId=e.customUserId,this.domain=e.domain,this.experimentName=e.experimentName,this.key=e.key,this.path=e.path,this.player=e.player,this.playerKey=e.playerKey,this.playerTech=e.playerTech,this.screenHeight=e.screenHeight,this.screenWidth=e.screenWidth,this.version=e.version,this.size=e.size,this.userId=e.userId,this.videoId=e.videoId,this.videoTitle=e.videoTitle,this.videoWindowHeight=e.videoWindowHeight,this.videoWindowWidth=e.videoWindowWidth,this.audioCodec=e.audioCodec,this.videoCodec=e.videoCodec)};t.AdAnalyticsSample=a},5878:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.CastTech=void 0,function(e){e.AirPlay="AirPlay",e.GoogleCast="GoogleCast",e.WebSocket="WebSocket"}(n||(t.CastTech=n={}))},6235:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentTracker=void 0;var n=function(){function e(){this.segments=[]}return e.prototype.reset=function(){this.segments=[]},e.prototype.addSegment=function(e){this.segments.push(e)},e.prototype.removeSegment=function(e){this.segments=this.segments.filter((function(t){return t.url!==e}))},e.prototype.getSegments=function(){return this.segments},e}();t.SegmentTracker=n},6398:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isAdPosition=s,t.hasValidSsaiAdBreakMetadataStructure=function(e){if((0,o.isNullish)(e))return!0;if(!(0,o.isPlainObject)(e))return a.logger.errorMessageToUser("Invalid SsaiAdBreakMetadata: not a plain object"),!1;var t=e;return!((0,o.isNotNullish)(t.adPosition)&&!s(t.adPosition))||(a.logger.errorMessageToUser("Invalid SsaiAdBreakMetadata: 'adPosition' property is not valid"),!1)},t.simplySanitizeSsaiAdBreakMetadata=function(e){if(!(0,o.isNullish)(e))return{adPosition:e.adPosition}},t.hasValidSsaiAdMetadataStructure=function(e){if((0,o.isNullish)(e))return!0;if(!(0,o.isPlainObject)(e))return a.logger.errorMessageToUser("Invalid SsaiAdMetadata: not a plain object"),!1;var t=e;return(0,o.isNotNullish)(t.adId)&&!(0,r.isValidString)(t.adId)?(a.logger.errorMessageToUser("Invalid SsaiAdMetadata: 'adId' property is not string"),!1):(0,o.isNotNullish)(t.adSystem)&&!(0,r.isValidString)(t.adSystem)?(a.logger.errorMessageToUser("Invalid SsaiAdMetadata: 'adSystem' property is not string"),!1):!((0,o.isNotNullish)(t.customData)&&!(0,o.isPlainObject)(t.customData))||(a.logger.errorMessageToUser("Invalid SsaiAdMetadata: 'customData' property is not a plain object"),!1)},t.simplySanitizeSsaiAdMetadata=function(e){if(!(0,o.isNullish)(e)){var t=void 0;if((0,o.isNotNullish)(e.customData)){var n=(0,i.extractCustomDataFieldsOnly)(e.customData);t={},(0,o.transferCustomDataFields)(n,t)}return{adId:e.adId,adSystem:e.adSystem,customData:t}}},t.isSsaiAdQuartile=u,t.hasValidSsaiAdQuartileStructure=function(e){return!!u(e)||(a.logger.errorMessageToUser("Invalid SsaiAdQuartile"),!1)},t.sanitizeSsaiAdQuartileMetadata=function(e){if((0,o.isNullish)(e))return{};if(!(0,o.isPlainObject)(e))return a.logger.errorMessageToUser("Invalid SsaiAdQuartileMetadata: not a plain object"),{};var t={failedBeaconUrl:e.failedBeaconUrl};return(0,r.isValidString)(t.failedBeaconUrl)?t.failedBeaconUrl=t.failedBeaconUrl.substring(0,500):(a.logger.errorMessageToUser("Invalid SsaiAdQuartileMetadata: 'failedBeaconUrl' property is not string"),t.failedBeaconUrl=void 0),t};var i=n(5529),a=n(1916),r=n(1820),o=n(5063);function s(e){return"preroll"===e||"midroll"===e||"postroll"===e}function u(e){return"first"===e||"midpoint"===e||"third"===e||"completed"===e}},6596:function(e,t,n){"use strict";var i,a=this&&this.__extends||(i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},i(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.Bitmovin8InternalAdapter=void 0;var o,s=n(2403),u=n(9664),l=n(2663),c=n(6235),d=n(4916),p=n(6743),h=n(5878),f=n(3458),m=n(2361),v=n(556),E=n(8525),A=n(7614),y=n(9557),g=n(7881),S=n(3413),T=n(5248),I=n(1916),b=n(4649),C=n(5063),_=n(2647),N=n(887),D=n(7543),P=n(8676),R=n(665);!function(e){e.Fullscreen="fullscreen",e.Inline="inline",e.PictureInPicture="pictureinpicture"}(o||(o={}));var k=function(e){function t(t,n,i,a,r,o,u){var c=e.call(this,u)||this;c.player=t,c.speedMeter=n,c.segmentTracker=i,c.videoCompletionTracker=a,c.httpRequestTrackingAdapter=r,c.onBeforeUnLoadEvent=!1,c.getPlayerVersion=function(){return c.player.version},c.getPlayerName=function(){return v.Player.BITMOVIN},c.getPlayerTech=function(){return c.player.getPlayerType()},c.getDrmPerformanceInfo=function(){return c.drmPerformanceInfo};var d=new l.HeartbeatService((function(){return t.getCurrentTime()})),p=new s.Bitmovin8AnalyticsStateMachine(c.stateMachineCallbacks,d,c.opts);return d.setListener(p),c.sourceInfoFallbackService=o,c.stateMachine=p,c}return a(t,e),Object.defineProperty(t.prototype,"downloadSpeedInfo",{get:function(){return this.speedMeter.getInfo()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"segments",{get:function(){return this.segmentTracker.getSegments()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"adModule",{get:function(){return this._adModule},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"supportsDeferredLicenseLoading",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"currentTime",{get:function(){var e;try{if((0,C.isNotNullish)(null===(e=this.player)||void 0===e?void 0:e.getCurrentTime)&&!this.isAdPlaying)return this.player.getCurrentTime()}catch(e){I.logger.warn("Analytics Collector attempted to access player, but player is not available anymore")}return this.lastTrackedCurrentTime?this.lastTrackedCurrentTime:0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isAdPlaying",{get:function(){var e=this.player.ads&&this.player.ads.getActiveAd();return(0,C.isNotNullish)(e)},enumerable:!1,configurable:!0}),t.prototype.initialize=function(e){var t=this;this.registerPlayerEventListeners(),this.registerUnloadEventListeners(),this._adModule=new N.Bitmovin8AdModule(this.player,this.windowEventTracker),this.ssaiService.activate({getPlayerCurrentTime:function(){return t.currentTime},reportEngagementInteraction:function(t){e.sendSsaiEngagementAdSample(t)},reportPlaybackInteraction:function(e,n){return t.stateMachine.onSsaiPlaybackInteraction(e,n)}});var n=new g.HttpRequestTracking([this.httpRequestTrackingAdapter]);return[new y.ErrorDetailTracking(e.errorDetailTrackingSettingsProvider,new A.ErrorDetailBackend(e.errorDetailTrackingSettingsProvider.collectorConfig),[e.errorDetailSubscribable],n)]},t.prototype.clearValues=function(){this.speedMeter.reset()},t.prototype.clearSegments=function(){this.segmentTracker.reset()},t.prototype.resetSourceRelatedState=function(){this.ssaiService.resetSourceRelatedState(),e.prototype.resetSourceRelatedState.call(this)},t.prototype.getAutoPlay=function(){return this.player.getConfig().playback&&this.player.getConfig().playback.autoplay||!1},t.prototype.getCurrentPlaybackInfo=function(){var t,n=r(r({},e.prototype.getCommonPlaybackInfo.call(this)),{size:this.player.getViewMode()===o.Fullscreen?E.PlayerSize.Fullscreen:E.PlayerSize.Window,playerTech:this.getPlayerTech(),isLive:this.player.isLive(),videoDuration:(0,S.normalizeVideoDuration)(this.videoDuration),streamFormat:this.player.getStreamType(),videoWindowWidth:this.player.getContainer().offsetWidth,videoWindowHeight:this.player.getContainer().offsetHeight,isMuted:this.player.isMuted(),isCasting:this.player.isCasting(),audioLanguage:(0,C.isNotNullish)(this.player.getAudio())?null===(t=this.player.getAudio())||void 0===t?void 0:t.lang:void 0,subtitleEnabled:!1,droppedFrames:(0,C.isNotNullish)(this.player.getSource())?this.player.getDroppedVideoFrames():0});return this.applySubtitleProperties(n),this.applyPlaybackQualityProperties(n),this.applyCastingProperties(n),this.applySourceProperties(n),((0,C.isNullish)(n.streamFormat)||"unknown"===n.streamFormat)&&this.sourceInfoFallbackService.applyStreamFormat(n),n},t.prototype.sourceChange=function(e){this.stateMachine.callManualSourceChangeEvent(e,this.currentTime)},t.prototype.onError=function(){this.clearSegments()},t.prototype.registerPlayerEventListeners=function(){var e=this;this.player.on(this.player.exports.PlayerEvent.SourceUnloaded,(function(t){e.segmentTracker.reset(),e.eventCallback(m.Event.SOURCE_UNLOADED,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.SourceLoaded,(function(t){e.videoCompletionTracker.reset(),e.videoDuration=e.player.getDuration(),e.videoCompletionTracker.setVideoDuration(e.player.getDuration()),e.sourceInfoFallbackService.setStreamFormat(e.player.getStreamType()),e.eventCallback(m.Event.SOURCE_LOADED,{})})),this.player.on(this.player.exports.PlayerEvent.CastStarted,(function(t){e.eventCallback(m.Event.START_CAST,t)})),this.player.on(this.player.exports.PlayerEvent.CastStopped,(function(){e.eventCallback(m.Event.END_CAST,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.Play,(function(t){"ui-seek"!==t.issuer&&e.eventCallback(m.Event.PLAY,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.Playing,(function(t){"advertising-api"!==t.issuer&&e.eventCallback(m.Event.PLAYING,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.Paused,(function(t){"ui-seek"!==t.issuer&&e.eventCallback(m.Event.PAUSE,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.TimeChanged,(function(t){e.isAdPlaying||(e.lastTrackedCurrentTime=e.player.getCurrentTime()),e.eventCallback(m.Event.TIMECHANGED,{currentTime:e.lastTrackedCurrentTime})})),this.player.on(this.player.exports.PlayerEvent.Seek,(function(t){e.allowSeeking(t)&&e.eventCallback(m.Event.SEEK,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.Seeked,(function(){e.eventCallback(m.Event.SEEKED,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.StallStarted,(function(){e.eventCallback(m.Event.START_BUFFERING,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.StallEnded,(function(){e.eventCallback(m.Event.END_BUFFERING,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.AudioPlaybackQualityChanged,(function(t){var n=t,i=n.sourceQuality,a=n.targetQuality;(0,C.isNullish)(a)||((0,C.isNullish)(i)||e.qualityChangeService.shouldAllowAudioQualityChange(a.bitrate)&&e.eventCallback(m.Event.AUDIO_CHANGE,{bitrate:a.bitrate,currentTime:e.currentTime,codec:a.codec}),e.qualityChangeService.setAudioBitrate(a.bitrate))})),this.player.on(this.player.exports.PlayerEvent.VideoPlaybackQualityChanged,(function(t){var n=t,i=n.sourceQuality,a=n.targetQuality;(0,C.isNullish)(a)||((0,C.isNullish)(i)||e.qualityChangeService.shouldAllowVideoQualityChange(a.bitrate)&&e.eventCallback(m.Event.VIDEO_CHANGE,{width:a.width,height:a.height,bitrate:a.bitrate,currentTime:e.currentTime,codec:a.codec}),e.qualityChangeService.setVideoBitrate(a.bitrate))})),this.player.on(this.player.exports.PlayerEvent.ViewModeChanged,(function(t){"fullscreen"===t.to?e.eventCallback(m.Event.START_FULLSCREEN,{currentTime:e.currentTime}):"fullscreen"===t.from&&e.eventCallback(m.Event.END_FULLSCREEN,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.AdStarted,(function(t){e.eventCallback(m.Event.START_AD,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.AdFinished,(function(t){e.eventCallback(m.Event.END_AD,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.AdSkipped,(function(t){e.eventCallback(m.Event.END_AD,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.AdError,(function(t){e.eventCallback(m.Event.END_AD,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.Muted,(function(){e.eventCallback(m.Event.MUTE,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.Unmuted,(function(){e.eventCallback(m.Event.UN_MUTE,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.Error,(function(t){e.eventCallback(m.Event.ERROR,{code:t.code,message:t.name,legacyData:t.data,currentTime:e.currentTime,data:{additionalData:JSON.stringify(t.data)}}),t.code!==f.ErrorCode.BITMOVIN_PLAYER_LICENSING_ERROR.code&&t.code!==f.ErrorCode.SETUP_MISSING_LICENSE_WHITELIST.code||e._onLicenseCallFailed.dispatch({}),e.ssaiService.sendAdEngagementErrorSample(t.code,t.name)})),this.player.on(this.player.exports.PlayerEvent.PlaybackFinished,(function(){e.eventCallback(m.Event.END,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.DownloadFinished,(function(t){0===t.downloadType.indexOf("drm/license/")&&(e.drmPerformanceInfo={drmType:t.downloadType.replace("drm/license/",""),drmLoadTime:1e3*t.downloadTime})})),this.player.on(this.player.exports.PlayerEvent.AudioChanged,(function(t){e.eventCallback(m.Event.AUDIOTRACK_CHANGED,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.SubtitleEnabled,(function(t){e.eventCallback(m.Event.SUBTITLE_CHANGE,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.SubtitleDisabled,(function(t){e.eventCallback(m.Event.SUBTITLE_CHANGE,{currentTime:e.currentTime})})),this.player.on(this.player.exports.PlayerEvent.LicenseValidated,(function(t){t.data.analytics&&void 0!==t.data.analytics.key?e._onLicenseKeyReceived.dispatch({licenseKey:t.data.analytics.key}):e._onLicenseCallFailed.dispatch({})}))},t.prototype.registerUnloadEventListeners=function(){var e=this,t=function(){if(!e.onBeforeUnLoadEvent){e.onBeforeUnLoadEvent=!0;var t=e.lastTrackedCurrentTime;e.eventCallback(m.Event.UNLOAD,{currentTime:t})}e.release()};t=t.bind(!0),this.player.on(this.player.exports.PlayerEvent.Destroy,t),this.windowEventTracker.addEventListener("beforeunload",t),this.windowEventTracker.addEventListener("unload",t)},t.prototype.applySourceProperties=function(e){var t=this.player.getSource();if(!(0,C.isNullish)(t)){e.videoTitle=t.title,e.mpdUrl=t.dash,e.m3u8Url=t.hls;var n=(0,T.getSourceInfoFromBitmovinSourceConfig)(t.progressive,this.player);e.progUrl=n.progUrl,"progressive"===this.player.getStreamType()&&(e.videoBitrate=n.progBitrate)}},t.prototype.applySubtitleProperties=function(e){var t;this.player.subtitles&&(t=this.player.subtitles.list().find((function(e){return!0===e.enabled}))),e.subtitleEnabled=(0,C.isNotNullish)(t),e.subtitleLanguage=(0,C.isNotNullish)(t)?t.lang:null},t.prototype.applyPlaybackQualityProperties=function(e){var t=this.player.getPlaybackVideoData();(0,C.isNotNullish)(t)&&(e.videoBitrate=t.bitrate,e.videoPlaybackHeight=t.height,e.videoPlaybackWidth=t.width,e.videoCodec=t.codec);var n=this.player.getPlaybackAudioData();(0,C.isNotNullish)(n)&&(e.audioBitrate=n.bitrate,e.audioCodec=n.codec)},t.prototype.applyCastingProperties=function(e){var t=this.player.isCasting()||this.player.isAirplayActive()||!1;if(e.isCasting=t,t)if(this.player.isAirplayActive())e.castTech=h.CastTech.AirPlay;else{var n=this.player.getConfig().remotecontrol;if(!(0,C.isNullish)(n))switch(n.type){case"googlecast":e.castTech=h.CastTech.GoogleCast;break;case"websocket":e.castTech=h.CastTech.WebSocket}}},t.prototype.allowSeeking=function(e){var t=e;return Math.abs(t.position-t.seekTarget)>b.ANALYTICS_MIN_SEEK_DIFFERENCE_THRESHOLD},t.create=function(e,n){var i=new R.SpeedMeterAdapter(e,new u.DownloadSpeedMeter).getDownloadSpeedMeter(),a=new P.SegmentTrackerAdapter(e,new c.SegmentTracker).getSegmentTracker(),r=new D.HttpRequestTrackingAdapter(e);return new t(e,i,a,new p.default,r,new d.SourceInfoFallbackService,n)},t}(_.InternalAdapter);t.Bitmovin8InternalAdapter=k},6743:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i,a=n(5063);!function(e){e[e.None=0]="None",e[e.Overlapping=1]="Overlapping",e[e.FirstContainedInSecond=2]="FirstContainedInSecond",e[e.SecondContainedInFirst=3]="SecondContainedInFirst"}(i||(i={}));var r=function(){function e(){this.watched=[],this.videoDuration=null}return e.prototype.reset=function(){this.watched=[]},e.prototype.setVideoDuration=function(e){this.videoDuration=1e3*e},e.prototype.addWatched=function(e){if((0,a.isNullish)(this.videoDuration))throw new Error("no video duration set for completion tracker");this.watched.push(e);var t=this.mergeWatched(this.watched);return(this.getDuration(e)-t)/this.videoDuration},e.prototype.getCompletionPercentage=function(){if((0,a.isNullish)(this.videoDuration))throw new Error("no video duration set for completion tracker");for(var e=0,t=0,n=this.watched;t<n.length;t++){var i=n[t];e+=this.getDuration(i)}return e/this.videoDuration},e.prototype.mergeWatched=function(e){for(var t=0;t<e.length;t++)for(var n=e[t],a=t+1;a<e.length;a++){var r=e[a],o=0,s=this.getOverlappingType(n,r);if(s===i.Overlapping?(this.mergeAndReplace(n,t,r,a),o=this.getOverlappingTimespan(n,r)):s===i.FirstContainedInSecond?(this.watched.splice(t,1),o=this.getDuration(n)):s===i.SecondContainedInFirst&&(this.watched.splice(a,1),o=this.getDuration(r)),s!==i.None)return o+this.mergeWatched(this.watched)}return 0},e.prototype.getOverlappingType=function(e,t){return this.isMomentInTimespan(e.end,t)&&e.start<t.start||this.isMomentInTimespan(e.start,t)&&e.end>t.end?i.Overlapping:this.isMomentInTimespan(e.start,t)&&this.isMomentInTimespan(e.end,t)?i.FirstContainedInSecond:this.isMomentInTimespan(t.start,e)&&this.isMomentInTimespan(t.end,e)?i.SecondContainedInFirst:i.None},e.prototype.isMomentInTimespan=function(e,t){return e>=t.start&&e<t.end},e.prototype.mergeAndReplace=function(e,t,n,i){var a=e.end>n.end?e.end:n.end,r={start:e.start<n.start?e.start:n.start,end:a};this.watched.splice(t,1,r),this.watched.splice(i,1)},e.prototype.getOverlappingTimespan=function(e,t){return(e.end<t.end?e.end:t.end)-(e.start>=t.start?e.start:t.start)},e.prototype.getDuration=function(e){return Math.abs(e.end-e.start)},e}();t.default=r},6845:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FeatureManager=void 0;var i=n(1916),a=function(){function e(){this.features=[]}return e.prototype.registerFeatures=function(e){this.features=e},e.prototype.unregisterFeatures=function(){this.features.forEach((function(e){return e.disable()})),this.features=[]},e.prototype.resetFeatures=function(){this.features.forEach((function(e){return e.reset()}))},e.prototype.configureFeatures=function(e,t){this.features=this.features.filter((function(n){var a=n.configure(e,t);return!(!e||!0!==(null==a?void 0:a.enabled))||(i.logger.log("Disabling feature ".concat(n.constructor.name," as it isn't enabled according to license callback.")),n.disable(),!1)})),this.features.forEach((function(e){return e.enabled()}))},e}();t.FeatureManager=a},7232:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AdBreakSample=void 0;var i=n(5063),a=function(e,t){this.adFallbackIndex=0,t&&(this.manifestDownloadTime=i.calculateTime(t.downloadTiming.downloadTime)),e&&(this.adReplaceContentDuration=i.calculateTimeWithUndefined(e.replaceContentDuration),this.adFallbackIndex=(e.currentFallbackIndex||-1)+1);var n=e;n&&("pre"===n.position||"post"===n.position?this.adPosition=n.position:(this.adPosition="mid",this.adOffset=n.position),this.adPreloadOffset=i.calculateTimeWithUndefined(n.preloadOffset));var a=e;if(a){this.adIsPersistent=a.persistent;var r=a.tag;if(this.adFallbackIndex>0&&a.fallbackTags&&a.fallbackTags.length>=this.adFallbackIndex&&(r=a.fallbackTags[this.adFallbackIndex-1]),r&&(this.adTagType=r.type,this.adTagUrl=r.url),this.adTagUrl){var o=i.getHostnameAndPathFromUrl(this.adTagUrl);this.adTagServer=o.hostname,this.adTagPath=o.path}}var s=e;s&&(this.adScheduleTime=i.calculateTimeWithUndefined(s.scheduleTime),this.adIdPlayer=s.id)};t.AdBreakSample=a},7390:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SequenceNumberBackend=void 0;var i=n(4649),a=function(){function e(e){this.sequenceNumber=0,this.nextBackend=e}return e.prototype.sendRequest=function(e){this.updateSampleBeforeSending(e),this.isSequenceNumberAboveThreshold(e)||this.nextBackend.sendRequest(e)},e.prototype.sendUnloadRequest=function(e){this.updateSampleBeforeSending(e),this.isSequenceNumberAboveThreshold(e)||this.nextBackend.sendUnloadRequest(e)},e.prototype.sendRequestSynchronous=function(e){this.updateSampleBeforeSending(e),this.isSequenceNumberAboveThreshold(e)||this.nextBackend.sendRequestSynchronous(e)},e.prototype.sendAdRequest=function(e){this.isSequenceNumberAboveThreshold(e)||this.nextBackend.sendAdRequest(e)},e.prototype.updateSampleBeforeSending=function(e){e.sequenceNumber=this.sequenceNumber++},e.prototype.isSequenceNumberAboveThreshold=function(e){var t;return(null!==(t=e.sequenceNumber)&&void 0!==t?t:0)>i.SEQUENCE_NUMBER_LIMIT},e}();t.SequenceNumberBackend=a},7540:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DebuggingBackend=void 0;var i=n(1916),a=function(){function e(e,t){this.underlying=t,e.debug&&e.debug.fields&&(this.debugFields=e.debug.fields)}return e.prototype.sendRequest=function(e){this.printFields(e),this.underlying.sendRequest(e)},e.prototype.sendUnloadRequest=function(e){this.printFields(e),this.underlying.sendUnloadRequest(e)},e.prototype.sendRequestSynchronous=function(e){this.printFields(e),this.underlying.sendRequestSynchronous(e)},e.prototype.sendAdRequest=function(e){this.underlying.sendAdRequest(e)},e.prototype.printFields=function(e){if(this.debugFields&&this.debugFields.length){var t={};this.debugFields.forEach((function(n){return t[n]=e[n]})),i.logger.table([t])}},e}();t.DebuggingBackend=a},7543:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HttpRequestTrackingAdapter=void 0;var i=n(675),a=n(5063),r=n(4438),o=function(){function e(e){var t=this;this.eventDispatcher=new i.EventDispatcher,this.playerExports=e.exports,e.on(this.playerExports.PlayerEvent.DownloadFinished,(function(e){return t.onDownloadFinished(e)}))}return e.prototype.subscribe=function(e){return this.eventDispatcher.subscribe(e)},e.prototype.unsubscribe=function(e){this.eventDispatcher.unsubscribe(e)},e.prototype.onDownloadFinished=function(e){var t,n,i;this.eventDispatcher.dispatch({httpRequest:{downloadTime:null!==(t=(0,a.calculateTimeWithUndefined)(null==e?void 0:e.downloadTime))&&void 0!==t?t:0,httpStatus:null!==(n=null==e?void 0:e.httpStatus)&&void 0!==n?n:0,success:null!==(i=null==e?void 0:e.success)&&void 0!==i&&i,timestamp:(0,a.getCurrentTimestamp)(),url:null==e?void 0:e.url,size:null==e?void 0:e.size,timeToFirstByte:(0,a.calculateTimeWithUndefined)(null==e?void 0:e.timeToFirstByte),type:this.mapHttpRequestType(null==e?void 0:e.downloadType)}})},e.prototype.mapHttpRequestType=function(e){return Object.values(r.HttpRequestType).includes(e)?e:r.HttpRequestType.UNKNOWN},e}();t.HttpRequestTrackingAdapter=o},7596:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.AnalyticsStateMachine=void 0;var a=n(7906),r=n(3458),o=n(2361),s=n(1916),u=n(4649),l=n(5063),c=n(5063),d=[3e3,5e3,1e4,3e4,59700],p=function(){function e(e,t,n){this.stateMachineCallbacks=e,this.onEnterStateTimestamp=0,this.videoStartupTimeoutMs=u.ANALYTICS_VIDEOSTART_TIMEOUT,this.rebufferTimeoutMs=u.ANALYTICS_REBUFFER_TIMEOUT,this.currentRebufferingIntervalIndex=0,this.stateMachine=this.createStateMachine(n),this.playingHeartbeatService=t}return e.prototype.callEvent=function(e,t,n){var i=this.stateMachine[e];if(null!=i)try{i.call(this.stateMachine,n,t)}catch(i){s.logger.error('StateMachine ignored event "'.concat(e,'" call, because it has thrown exception in the current state "').concat(this.currentState,'"'),t,n,i)}else s.logger.warn('StateMachine ignored event "'.concat(e,'" call, because it was not defined'),t,n)},e.prototype.callManualSourceChangeEvent=function(e,t){this.callEvent(o.Event.MANUAL_SOURCE_CHANGE,{config:e,currentTime:t},Date.now())},Object.defineProperty(e.prototype,"currentState",{get:function(){return this.stateMachine.current},enumerable:!1,configurable:!0}),e.prototype.resetIntervals=function(){this.clearVideoStartTimeout(),this.clearRebufferingTimeoutHandle(!0),this.clearRebufferingHeartbeatHandle()},e.prototype.startRebufferingHeartbeatInterval=function(e){var t=this;void 0===e&&(e=!0),this.resetRebufferingHelpers(e),this.startRebufferingTimeoutHandle(),this.rebufferingHeartbeatIntervalHandle=window.setInterval((function(){var e=t.stateMachine.current.toLowerCase();if("rebuffering"===e){var n=(new Date).getTime(),i=n-t.onEnterStateTimestamp;t.stateMachineCallbacks.heartbeat(i,e,{buffered:i}),t.onEnterStateTimestamp=n,t.currentRebufferingIntervalIndex=Math.min(t.currentRebufferingIntervalIndex+1,d.length-1),t.startRebufferingHeartbeatInterval(!1)}else t.resetRebufferingHelpers()}),d[this.currentRebufferingIntervalIndex])},e.prototype.resetRebufferingHelpers=function(e){void 0===e&&(e=!0),e&&(this.currentRebufferingIntervalIndex=0),this.clearRebufferingHeartbeatHandle(),this.clearRebufferingTimeoutHandle(e)},e.prototype.clearRebufferingHeartbeatHandle=function(){(0,c.isNotNullish)(this.rebufferingHeartbeatIntervalHandle)&&(window.clearInterval(this.rebufferingHeartbeatIntervalHandle),this.rebufferingHeartbeatIntervalHandle=void 0)},e.prototype.startRebufferingTimeoutHandle=function(){var e=this;this.currentRebufferingIntervalIndex>0||(this.rebufferingTimeoutHandle=window.setTimeout((function(){e.callEvent(o.Event.ERROR,i(i({},r.ErrorCode.BUFFERING_TIMEOUT_REACHED),{data:{additionalData:"StateMachine timed out after ".concat(e.rebufferTimeoutMs," ms of consecutive buffering.")}}),l.getCurrentTimestamp()),e.stateMachineCallbacks.release()}),this.rebufferTimeoutMs))},e.prototype.clearRebufferingTimeoutHandle=function(e){e&&(0,c.isNotNullish)(this.rebufferingTimeoutHandle)&&(window.clearTimeout(this.rebufferingTimeoutHandle),this.rebufferingTimeoutHandle=void 0)},e.prototype.setVideoStartTimeout=function(){var e=this;(0,c.isNotNullish)(this.videoStartTimeout)&&this.clearVideoStartTimeout(),this.videoStartTimeout=window.setTimeout((function(){e.callEvent(o.Event.VIDEOSTART_TIMEOUT,{},Date.now()),e.stateMachineCallbacks.release()}),this.videoStartupTimeoutMs)},e.prototype.clearVideoStartTimeout=function(){window.clearTimeout(this.videoStartTimeout),this.videoStartTimeout=void 0},e.prototype.onHeartbeat=function(e){this.sendPlayingSample(Date.now(),e)},e.prototype.sendPlayingSample=function(e,t,n){void 0===n&&(n=!1);var i=this.stateMachine.current.toLowerCase();if("playing"===i){this.stateMachineCallbacks.setVideoTimeEndFromEvent(t);var r=e-this.onEnterStateTimestamp,o={played:r};n&&(o[a.SSAI_RELATED_SAMPLE_MARKER]=!0),this.stateMachineCallbacks.heartbeat(r,i,o),this.onEnterStateTimestamp=e,this.stateMachineCallbacks.setVideoTimeStartFromEvent(t)}else s.logger.warn('Unexpected call of "sendPlayingSample" in AnalyticsStateMachine from "'.concat(i,'" state'))},e}();t.AnalyticsStateMachine=p},7614:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)},a=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,a=0,r=t.length;a<r;a++)!i&&a in t||(i||(i=Array.prototype.slice.call(t,0,a)),i[a]=t[a]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.ErrorDetailBackend=void 0;var r=n(92),o=n(4649),s=n(5063),u=function(){function e(e){var t;this._queue=[],this._enabled=!1,this.backendUrl=(0,s.joinUrls)(null!==(t=null==e?void 0:e.backendUrl)&&void 0!==t?t:o.ANALYTICS_BACKEND_BASE_URL,"/analytics/error")}return Object.defineProperty(e.prototype,"queue",{get:function(){return this._queue},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"enabled",{get:function(){return this._enabled},set:function(e){this._enabled=e},enumerable:!1,configurable:!0}),e.copyErrorDetailTruncateStringsAndUrls=function(e,t,n){var a,r,o=this;return i(i({},e),{message:null===(a=e.message)||void 0===a?void 0:a.substr(0,t),data:this.copyErrorDataTruncateStrings(e.data,t),httpRequests:null===(r=e.httpRequests)||void 0===r?void 0:r.map((function(e){return o.copyHttpRequestTruncateUrls(e,n)}))})},e.copyHttpRequestTruncateUrls=function(e,t){var n,a;return i(i({},e),{url:null===(n=e.url)||void 0===n?void 0:n.substr(0,t),lastRedirectLocation:null===(a=e.lastRedirectLocation)||void 0===a?void 0:a.substr(0,t)})},e.copyErrorDataTruncateStrings=function(e,t){var n,a;return i(i({},e),{exceptionMessage:null===(n=e.exceptionMessage)||void 0===n?void 0:n.substr(0,t),additionalData:null===(a=e.additionalData)||void 0===a?void 0:a.substr(0,t)})},e.copyErrorDetailTruncateHttpRequests=function(e,t){var n;return i(i({},e),{httpRequests:null===(n=e.httpRequests)||void 0===n?void 0:n.slice(-t)})},e.prototype.limitHttpRequestsOfQueuedErrorDetails=function(t){var n=this;this._queue.forEach((function(i,a){n._queue[a]=e.copyErrorDetailTruncateHttpRequests(i,t)}))},e.prototype.send=function(t){var n=e.copyErrorDetailTruncateStringsAndUrls(t,e.MAX_STRING_LENGTH,e.MAX_URL_LENGTH);this.enabled?(0,r.post)(this.backendUrl,n,(function(){})):this._queue.push(n)},e.prototype.flush=function(){for(var e=a([],this._queue,!0),t=e.shift();(0,s.isNotNullish)(t);)this.removeFromQueue(t),this.send(t),t=e.shift()},e.prototype.clear=function(){this._queue.splice(0,this._queue.length)},e.prototype.removeFromQueue=function(e){var t=this._queue.findIndex((function(t){return t===e}));t>=0&&this._queue.splice(t,1)},e.MAX_URL_LENGTH=450,e.MAX_STRING_LENGTH=400,e}();t.ErrorDetailBackend=u},7881:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HttpRequestTracking=void 0;var i=n(1916),a=n(3129),r=function(){function e(t){var n=this;this.subscribables=t,this._maxRequests=e.DEFAULT_MAX_REQUESTS,this._httpRequestQueue=new a.Queue,this.onDownloadFinishedHandler=function(e){try{n.addRequest(e.httpRequest)}catch(e){i.logger.error("HttpRequestTracking.onDownloadFinished: Error in handler",e)}},t.forEach((function(e){return e.subscribe(n.onDownloadFinishedHandler)}))}return Object.defineProperty(e.prototype,"maxRequests",{get:function(){return this._maxRequests},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"httpRequests",{get:function(){return this._httpRequestQueue.items},enumerable:!1,configurable:!0}),e.prototype.configure=function(e){this._maxRequests=e,this._httpRequestQueue.limit(e)},e.prototype.disable=function(){var e=this;this.subscribables.forEach((function(t){return t.unsubscribe(e.onDownloadFinishedHandler)})),this._httpRequestQueue.clear()},e.prototype.reset=function(){this._httpRequestQueue.clear()},e.prototype.addRequest=function(e){this._httpRequestQueue.offer(e),this._httpRequestQueue.limit(this._maxRequests)},e.DEFAULT_MAX_REQUESTS=10,e}();t.HttpRequestTracking=r},7906:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.ROUTING_URL_PARAM_VALUE_SSAI=t.ROUTING_URL_PARAM=t.SSAI_RELATED_SAMPLE_MARKER=void 0,t.isSsaiRelatedSample=function(e){return e[t.SSAI_RELATED_SAMPLE_MARKER]?(delete e[t.SSAI_RELATED_SAMPLE_MARKER],!0):2===e.adType},t.appendSsaiRoutingParam=function(e){return"".concat(e,"?").concat(t.ROUTING_URL_PARAM,"=").concat(t.ROUTING_URL_PARAM_VALUE_SSAI)},t.createErrorAdSample=function(e,t){return o({playbackSample:e,currentAdImpressionId:t.currentAdImpressionId,adIndex:t.adIndex,currentAdMetadata:t.currentAdMetadata,quartile:void 0,quartileMetadata:void 0,errorCode:t.errorCode,errorMessage:t.errorMessage,timeSinceAdStartedInMs:t.timeSinceAdStartedInMs})},t.createStartedAdSample=function(e,t){return o({playbackSample:e,currentAdImpressionId:t.currentAdImpressionId,adIndex:t.adIndex,currentAdMetadata:t.currentAdMetadata,quartile:"started",quartileMetadata:void 0,errorCode:void 0,errorMessage:void 0,timeSinceAdStartedInMs:void 0})},t.createQuartileAdSample=function(e,t){return o({playbackSample:e,currentAdImpressionId:t.currentAdImpressionId,adIndex:t.adIndex,currentAdMetadata:t.currentAdMetadata,quartile:t.quartile,quartileMetadata:t.quartileMetadata,timeSinceAdStartedInMs:t.timeSinceAdStartedInMs,errorCode:void 0,errorMessage:void 0})},t.getCurrentMonotonicTimestampInMs=s,t.getMonotonicTimestampInMsSince=function(e){if(!(0,r.isNullish)(e))return s()-e};var a=n(5529),r=n(5063);function o(e){var t,n=e.playbackSample,o=e.currentAdImpressionId,s=e.adIndex,u=e.currentAdMetadata,l=e.quartile,c=e.quartileMetadata,d=e.errorCode,p=e.errorMessage,h=e.timeSinceAdStartedInMs,f=i(i({},{audioBitrate:null==(t=n)?void 0:t.audioBitrate,audioCodec:null==t?void 0:t.audioCodec,analyticsVersion:null==t?void 0:t.analyticsVersion,adPlaybackHeight:null==t?void 0:t.videoPlaybackHeight,adPlaybackWidth:null==t?void 0:t.videoPlaybackWidth,cdnProvider:null==t?void 0:t.cdnProvider,customData1:null==t?void 0:t.customData1,customData2:null==t?void 0:t.customData2,customData3:null==t?void 0:t.customData3,customData4:null==t?void 0:t.customData4,customData5:null==t?void 0:t.customData5,customData6:null==t?void 0:t.customData6,customData7:null==t?void 0:t.customData7,customData8:null==t?void 0:t.customData8,customData9:null==t?void 0:t.customData9,customData10:null==t?void 0:t.customData10,customData11:null==t?void 0:t.customData11,customData12:null==t?void 0:t.customData12,customData13:null==t?void 0:t.customData13,customData14:null==t?void 0:t.customData14,customData15:null==t?void 0:t.customData15,customData16:null==t?void 0:t.customData16,customData17:null==t?void 0:t.customData17,customData18:null==t?void 0:t.customData18,customData19:null==t?void 0:t.customData19,customData20:null==t?void 0:t.customData20,customData21:null==t?void 0:t.customData21,customData22:null==t?void 0:t.customData22,customData23:null==t?void 0:t.customData23,customData24:null==t?void 0:t.customData24,customData25:null==t?void 0:t.customData25,customData26:null==t?void 0:t.customData26,customData27:null==t?void 0:t.customData27,customData28:null==t?void 0:t.customData28,customData29:null==t?void 0:t.customData29,customData30:null==t?void 0:t.customData30,customData31:null==t?void 0:t.customData31,customData32:null==t?void 0:t.customData32,customData33:null==t?void 0:t.customData33,customData34:null==t?void 0:t.customData34,customData35:null==t?void 0:t.customData35,customData36:null==t?void 0:t.customData36,customData37:null==t?void 0:t.customData37,customData38:null==t?void 0:t.customData38,customData39:null==t?void 0:t.customData39,customData40:null==t?void 0:t.customData40,customData41:null==t?void 0:t.customData41,customData42:null==t?void 0:t.customData42,customData43:null==t?void 0:t.customData43,customData44:null==t?void 0:t.customData44,customData45:null==t?void 0:t.customData45,customData46:null==t?void 0:t.customData46,customData47:null==t?void 0:t.customData47,customData48:null==t?void 0:t.customData48,customData49:null==t?void 0:t.customData49,customData50:null==t?void 0:t.customData50,customUserId:null==t?void 0:t.customUserId,domain:null==t?void 0:t.domain,experimentName:null==t?void 0:t.experimentName,key:null==t?void 0:t.key,language:null==t?void 0:t.language,path:null==t?void 0:t.path,platform:null==t?void 0:t.platform,player:null==t?void 0:t.player,playerKey:null==t?void 0:t.playerKey,playerTech:null==t?void 0:t.playerTech,size:null==t?void 0:t.size,screenHeight:null==t?void 0:t.screenHeight,screenWidth:null==t?void 0:t.screenWidth,streamFormat:null==t?void 0:t.streamFormat,userAgent:null==t?void 0:t.userAgent,userId:null==t?void 0:t.userId,version:null==t?void 0:t.version,videoBitrate:null==t?void 0:t.videoBitrate,videoCodec:null==t?void 0:t.videoCodec,videoId:null==t?void 0:t.videoId,videoImpressionId:null==t?void 0:t.impressionId,videoTitle:null==t?void 0:t.videoTitle,videoWindowHeight:null==t?void 0:t.videoWindowHeight,videoWindowWidth:null==t?void 0:t.videoWindowWidth}),{adId:null==u?void 0:u.adId,adImpressionId:o,adIndex:s,adPosition:null==u?void 0:u.adPosition,adSystem:null==u?void 0:u.adSystem,adType:2,errorCode:d,errorMessage:p,time:Date.now(),timeSinceAdStartedInMs:h});switch(l){case"started":f.started=1;break;case"first":f.quartile1=1,f.quartile1FailedBeaconUrl=null==c?void 0:c.failedBeaconUrl;break;case"midpoint":f.midpoint=1,f.midpointFailedBeaconUrl=null==c?void 0:c.failedBeaconUrl;break;case"third":f.quartile3=1,f.quartile3FailedBeaconUrl=null==c?void 0:c.failedBeaconUrl;break;case"completed":f.completed=1,f.completedFailedBeaconUrl=null==c?void 0:c.failedBeaconUrl}var m=null==u?void 0:u.customData;return(0,r.isNotNullish)(m)&&a.customDataValuesKeys.forEach((function(e){m[e]&&(f[e]=m[e])})),f}function s(){var e;return(0,r.isNullish)(null===(e=window.performance)||void 0===e?void 0:e.now)||"function"!=typeof window.performance.now?Date.now():Math.floor(performance.now())}t.SSAI_RELATED_SAMPLE_MARKER="triggeredBySsai",t.ROUTING_URL_PARAM="routingParam",t.ROUTING_URL_PARAM_VALUE_SSAI="ssai"},8253:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.logStateMachineTransition=t.logMissingCallbackWarning=t.customStateMachineErrorCallback=t.whileIn=t.on=void 0;var i=n(2595),a=n(1916),r=n(5063);t.on=function(e){return{stayIn:function(t){return{name:e,from:t,to:t}}}},t.whileIn=function(e){var t=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return t.map((function(t){return{name:t,from:e,to:e}}))};return{runEventCallbacksFor:t,passOver:t}},t.customStateMachineErrorCallback=function(e,t,n,o,s,u,l){var c='StateMachine ignored event "'.concat(e,'", ').concat(u),d={eventName:e,from:t,to:n,args:(0,r.isNotNullish)(o)?JSON.stringify(o):void 0,errorCode:s,errorMessage:u};s!==i.Error.INVALID_TRANSITION?a.logger.error(c,d):a.logger.log(c,d)},t.logMissingCallbackWarning=function(e,t){void 0===t&&(t=[]),(0,r.isNullish)(e)||(0,r.isNullish)(t.find((function(t){return e===t})))&&a.logger.warn("Could not find StateMachine callback function for "+e)},t.logStateMachineTransition=function(e,t,n,i){"ENTER"===e?a.logger.log("[ENTER] ".concat((0,a.padRight)(i,20)," EVENT: ").concat((0,a.padRight)(t,20)," from ").concat((0,a.padRight)(n,20))):a.logger.log("[LEAVE] ".concat((0,a.padRight)(n,20)," EVENT: ").concat((0,a.padRight)(t,20)," to ").concat((0,a.padRight)(i,20)))}},8331:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.sanitizeAnalyticsConfig=function(e){return(0,s.isNullish)(e)?{}:(o.isBoolean(e.isLive)||(e.isLive=void 0),e)},t.guardConfigChangeAgainstMissingTitle=function(e,t){(0,s.isNullish)(e)||(0,s.isNullish)(t)||e.title&&(0,r.isBlank)(t.title)&&a.logger.error("The new analytics configuration does not contain the field `title`")},t.guardConfigChangeAgainstMissingIsLive=function(e,t){(0,s.isNullish)(e)||(0,s.isNullish)(t)||e.isLive&&(0,s.isNullish)(t.isLive)&&a.logger.error("The new analytics configuration does not contain the field `isLive`. It will default to `false` which might be unintended? Once stream playback information is available the type will be populated.")},t.guardAgainstDuplicatedUserId=function(e){(0,s.isNullish)(e)||(0,s.isNotNullish)(e.customUserId)&&(0,s.isNotNullish)(e.userId)&&a.logger.warn("Configuration Warning: \nCustomUserId and UserId are set in the config \nValue of UserId will be used in sample \nPlease only use one configuration field to set your userId")},t.getDeviceInformationFromAnalyticsConfig=function(e){if(!(0,s.isNullish)(e)){var t={};if((0,r.isValidString)(e.deviceType)&&!(0,r.isBlank)(e.deviceType)&&(t.model=e.deviceType),(0,r.isValidString)(e.deviceClass)&&!(0,r.isBlank)(e.deviceClass)&&(t.deviceClass=e.deviceClass),0!==Object.keys(t).length)return t}},t.getDomainFromAnalyticsConfig=function(e){var t,n;return null!==(n=null===(t=null==e?void 0:e.config)||void 0===t?void 0:t.origin)&&void 0!==n?n:o.sanitizePath(window.location.hostname)},t.mergeAnalyticsConfig=function(e,t){var n=function(e){var t=["cdnProvider","videoId","title","isLive","experimentName"],n=/customData\d{1,2}$/g;for(var i in e)((0,s.isNotNullish)(i.match(n))||t.includes(i))&&delete e[i];return e}(e),a=function(e,t){return(0,s.isNotNullish)(t)?i(i(i({},e),{enabled:!0}),t):null!=e?e:{}}(e.config,t.config);return i(i(i({},n),t),{config:a})};var a=n(1916),r=n(1820),o=n(5063),s=n(5063)},8525:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerSize=void 0,function(e){e.Fullscreen="FULLSCREEN",e.Window="WINDOW"}(n||(t.PlayerSize=n={}))},8676:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentTrackerAdapter=void 0;var i=n(5063),a=function(){function e(e,t){var n=this;this.handleSegmentPlaybackEvent=function(e){n.segmentTracker.removeSegment(e.url)},this.segmentTracker=t,e.on(e.exports.PlayerEvent.DownloadFinished,(function(e){return n.handleOnDownloadFinishEvent(e)})),e.on(e.exports.PlayerEvent.SegmentPlayback,(function(e){return n.handleSegmentPlaybackEvent(e)}))}return e.prototype.getSegmentTracker=function(){return this.segmentTracker},e.prototype.handleOnDownloadFinishEvent=function(e){if(0===e.downloadType.indexOf("media/video")){var t=e.url,n={name:i.getURLResourceName(t),url:t,timestamp:e.timestamp};this.segmentTracker.addSegment(n)}},e}();t.SegmentTrackerAdapter=a},8917:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.AdAnalytics=void 0;var a,r=n(5791),o=n(7232),s=n(4439),u=n(1916),l=n(5063),c=n(2982),d=n(4926),p=n(3612);!function(e){e.FIRST_QUARTILE="firstQuartile",e.MIDPOINT="midpoint",e.THIRD_QUARTILE="thirdQuartile"}(a||(a={}));var h=function(){function e(e,t){var n=this;this.onBeforeUnLoadEvent=!1,this.adManifestLoadedEvents=[],this.isPlaying=!1,this.adPodPosition=0,this.analytics=e,this.adapter=t,this.adapter.adCallbacks.onAdStarted=function(e){return n.onAdStarted(e)},this.adapter.adCallbacks.onAdFinished=function(e){return n.onAdFinished(e)},this.adapter.adCallbacks.onAdBreakStarted=function(e){return n.onAdBreakStarted(e)},this.adapter.adCallbacks.onAdBreakFinished=function(e){return n.onAdBreakFinished(e)},this.adapter.adCallbacks.onAdClicked=function(e){return n.onAdClicked(e)},this.adapter.adCallbacks.onAdError=function(e){return n.onAdError(e)},this.adapter.adCallbacks.onAdManifestLoaded=function(e){return n.onAdManifestLoaded(e)},this.adapter.adCallbacks.onPlay=function(){return n.onPlay()},this.adapter.adCallbacks.onPause=function(){return n.onPause()},this.adapter.adCallbacks.onBeforeUnload=function(){return n.onBeforeUnload()},this.adapter.adCallbacks.onAdSkipped=function(e){return n.onAdSkipped(e)},this.adapter.adCallbacks.onAdQuartile=function(e){return n.onAdQuartile(e)},this.viewportTracker=new p.ViewportTracker(this.adapter.getContainer(),(function(){return n.onIntersectionChanged()}),.5)}return e.prototype.release=function(){this.clearCurrentTimeInterval(),this.viewportTracker.dispose()},e.prototype.onIntersectionChanged=function(){this.activeAdSample&&(this.isContainerInViewport()?this.enterViewportTimestamp=l.getCurrentTimestamp():this.enterViewportTimestamp&&(this.activeAdSample.timeInViewport=(this.activeAdSample.timeInViewport||0)+l.getCurrentTimestamp()-this.enterViewportTimestamp))},e.prototype.isContainerInViewport=function(){return!this.viewportTracker||this.viewportTracker.isInViewport()},e.prototype.onPlay=function(){if(this.adapter&&this.adapter.isLinearAdActive()&&this.activeAdSample){var e=l.getCurrentTimestamp();this.beginPlayingTimestamp=e,this.enterViewportTimestamp=e,this.isPlaying=!0}},e.prototype.onPause=function(){this.adapter&&this.adapter.isLinearAdActive()&&this.activeAdSample&&(this.updatePlayingTime(this.activeAdSample),this.isPlaying=!1)},e.prototype.onAdManifestLoaded=function(e){var t=e.adConfig;t&&t.tag&&"vmap"===t.tag.type?this.sendAnalyticsRequest(new o.AdBreakSample(e.adConfig,e)):e.adBreak&&this.adManifestLoadedEvents.push(e)},e.prototype.onAdBreakStarted=function(e){this.adPodPosition=0,this.activeAdBreakSample=new o.AdBreakSample(e.adBreak,this.getAdManifestLoadedEvent(e.adBreak)),this.adStartupTimestamp=l.getCurrentTimestamp()},e.prototype.onAdBreakFinished=function(e){this.resetActiveAd(),this.activeAdBreakSample=void 0},e.prototype.onAdStarted=function(e){e.ad.isLinear&&(this.resetActiveAd(),this.activeAdSample=new s.AdSample(e.ad),this.currentTime=void 0,this.activeAdSample.adStartupTime=this.adStartupTimestamp?l.getCurrentTimestamp()-this.adStartupTimestamp:void 0,this.startAd(this.activeAdSample))},e.prototype.onAdFinished=function(e){if(this.activeAdBreakSample&&this.activeAdSample){var t=i({},this.activeAdSample);t.completed=1,this.resetActiveAd(),this.completeAd(this.activeAdBreakSample,t,t.adDuration)}},e.prototype.onAdSkipped=function(e){if(this.activeAdBreakSample&&this.activeAdSample){var t=i({},this.activeAdSample);t.skipped=1,t.skipPosition=this.currentTime,t.skipPercentage=l.calculatePercentage(this.activeAdSample.skipPosition,this.activeAdSample.adDuration),this.resetActiveAd(),this.completeAd(this.activeAdBreakSample,t,t.skipPosition)}},e.prototype.onAdError=function(e){var t,n,i=this,a=e.data||{adBreak:void 0,adConfig:void 0,code:void 0,message:void 0},r=a.adConfig,s=a.adBreak,u=a.code,c=a.message,d=new o.AdBreakSample(s||r,s?this.getAdManifestLoadedEvent(s):void 0);d.errorCode=u||e.code,d.errorData=JSON.stringify(e.data),d.errorMessage=c||e.name,this.activeAdSample&&s&&s.ads&&s.ads.includes((function(e){return e.id===i.activeAdSample.adId}))&&(n=(t=this.activeAdSample).errorPosition=this.currentTime,t.errorPercentage=l.calculatePercentage(t.errorPosition,t.adDuration)),this.completeAd(d,t,n)},e.prototype.onAdClicked=function(e){this.activeAdSample&&(this.activeAdSample.adClickthroughUrl=e.clickThroughUrl,this.activeAdSample.clicked=1,this.activeAdSample.clickPosition=this.currentTime,this.activeAdSample.clickPercentage=l.calculatePercentage(this.activeAdSample.clickPosition,this.activeAdSample.adDuration))},e.prototype.onAdQuartile=function(e){this.activeAdSample&&(e.quartile===a.FIRST_QUARTILE?this.activeAdSample.quartile1=1:e.quartile===a.MIDPOINT?this.activeAdSample.midpoint=1:e.quartile===a.THIRD_QUARTILE&&(this.activeAdSample.quartile3=1))},e.prototype.onBeforeUnload=function(){if(!this.onBeforeUnLoadEvent&&(this.onBeforeUnLoadEvent=!0,this.activeAdSample&&this.activeAdBreakSample)){var e=i({},this.activeAdSample);e.closed=1,e.closePosition=this.currentTime,e.closePercentage=l.calculatePercentage(e.closePosition,e.adDuration),this.resetActiveAd(),this.completeAd(this.activeAdBreakSample,e,e.closePosition)}},e.prototype.createNewAdAnalyticsSample=function(e){var t=this.adapter.getAdModuleInfo();return i(i({},new r.AdAnalyticsSample(e)),{analyticsVersion:d.VERSION,adModule:t.name,adModuleVersion:t.version,playerStartupTime:this.analytics.playerStartupTime,pageLoadTime:this.analytics.pageLoadTime,autoplay:this.analytics.autoplay,pageLoadType:l.getPageLoadType()})},e.prototype.getAdManifestLoadedEvent=function(e){if(e)return this.adManifestLoadedEvents.find((function(t){return t.adBreak&&t.adBreak.id===e.id}))},e.prototype.sendAnalyticsRequest=function(e,t){var n=i(i(i({},this.createNewAdAnalyticsSample(this.analytics.sample)),e),t||new s.AdSample);n.time=l.getCurrentTimestamp(),n.adImpressionId=(0,c.generateUUID)(),n.percentageInViewport=l.calculatePercentage(n.timeInViewport,n.timePlayed),this.analytics.backend.sendAdRequest(n)},e.prototype.updatePlayingTime=function(e){var t=l.getCurrentTimestamp();this.beginPlayingTimestamp&&this.isPlaying&&(void 0!==e.timePlayed&&(e.timePlayed+=t-this.beginPlayingTimestamp),this.isContainerInViewport()&&this.enterViewportTimestamp&&void 0!==e.timeInViewport&&(e.timeInViewport+=t-this.enterViewportTimestamp))},e.prototype.startAd=function(t){var n=this;t.started=1,t.timePlayed=0,t.timeInViewport=0,t.adPodPosition=this.adPodPosition;var i=l.getCurrentTimestamp();this.beginPlayingTimestamp=i,this.enterViewportTimestamp=this.isContainerInViewport()?i:void 0,this.isPlaying=!0,this.currentTime=0,this.adPodPosition++,this.currentTimeInterval=window.setInterval((function(){try{t&&void 0!==t.adDuration&&t.adDuration>0&&n.adapter.isLinearAdActive()&&(n.currentTime=l.calculateTime(Math.max(n.adapter.currentTime(),0)))}catch(e){u.logger.log("AdStarted monitoring interval failed and got cleared",e),n.resetActiveAd()}}),e.TIMEOUT_CURRENT_TIME_INTERVAL)},e.prototype.completeAd=function(e,t,n){void 0===t&&(t=new s.AdSample),t.exitPosition=n,t.playPercentage=l.calculatePercentage(t.exitPosition,t.adDuration),this.adStartupTimestamp=l.getCurrentTimestamp(),this.updatePlayingTime(t),this.isPlaying=!1,this.sendAnalyticsRequest(e,t)},e.prototype.resetActiveAd=function(){this.clearCurrentTimeInterval(),this.activeAdSample=void 0},e.prototype.clearCurrentTimeInterval=function(){window.clearInterval(this.currentTimeInterval),delete this.currentTime},e.MODULE_NAME="ads",e.TIMEOUT_CURRENT_TIME_INTERVAL=100,e}();t.AdAnalytics=h},8918:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isSsaiEngagementErrorData=function(e){return"error"===e.type},t.isSsaiEngagementQuartileData=function(e){return"quartile"===e.type},t.isSsaiEngagementStartedData=function(e){return"started"===e.type}},9557:function(e,t,n){"use strict";var i,a=this&&this.__extends||(i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},i(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,a=0,r=t.length;a<r;a++)!i&&a in t||(i||(i=Array.prototype.slice.call(t,0,a)),i[a]=t[a]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.ErrorDetailTracking=void 0;var o=n(1916),s=n(5063),u=n(4926),l=function(e){function t(t,n,i,a){var l=e.call(this)||this;return l.settingsProvider=t,l.backend=n,l.subscribables=i,l.httpRequestTracking=a,l.errorIndex=0,l.onErrorHandler=function(e){var t,n;try{if(!l.isEnabled)return;var i=null===(t=l.httpRequestTracking)||void 0===t?void 0:t.httpRequests,a=(0,s.isNullish)(i)?void 0:r([],i,!0),c=l.errorIndex;l.errorIndex++;var d={platform:"web",licenseKey:l.settingsProvider.licenseKey,domain:l.settingsProvider.domain,impressionId:l.settingsProvider.impressionId,analyticsVersion:u.VERSION,errorId:c,timestamp:(0,s.getCurrentTimestamp)(),code:e.code,message:e.message,data:null!==(n=e.errorData)&&void 0!==n?n:{additionalData:void 0,exceptionMessage:void 0,exceptionStacktrace:void 0},httpRequests:a};o.logger.log("ErrorDetailTracking.onError: ".concat(JSON.stringify(d))),l.backend.send(d)}catch(e){o.logger.error("ErrorDetailTracking.onError: Error in handler",e)}},i.forEach((function(e){return e.subscribe(l.onErrorHandler)})),l}return a(t,e),t.prototype.reset=function(){var e;null===(e=this.httpRequestTracking)||void 0===e||e.reset(),this.errorIndex=0},t.prototype.enabled=function(){this.backend.enabled=!0,this.backend.flush()},t.prototype.disabled=function(){var e,t=this;this.errorIndex=0,null===(e=this.httpRequestTracking)||void 0===e||e.disable(),this.backend.clear(),this.subscribables.forEach((function(e){return e.unsubscribe(t.onErrorHandler)}))},t.prototype.configured=function(e,t){var n,i,a=Math.max(null!==(n=null==t?void 0:t.numberOfHttpRequests)&&void 0!==n?n:0,0);null===(i=this.httpRequestTracking)||void 0===i||i.configure(a),this.backend.limitHttpRequestsOfQueuedErrorDetails(a)},t.prototype.extractConfig=function(e){return e.errorDetails},t}(n(348).Feature);t.ErrorDetailTracking=l},9664:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DownloadSpeedMeter=void 0;var i=n(5766),a=n(3786),r=function(){function e(){this.measures=[]}return e.prototype.reset=function(){this.measures=[]},e.prototype.addMeasurement=function(e){if(!(e.httpStatus>=400)){var t=new a.Measure(e),n=i.Converter.bitsToBytes(3e8);t.speed>=n||this.measures.push(t)}},e.prototype.getInfo=function(){return{segmentsDownloadCount:this.measures.length,segmentsDownloadSize:this.measures.map((function(e){return e.size})).reduce(this.add,0),segmentsDownloadTime:Math.ceil(1e3*this.totalTime()),avgDownloadSpeed:this.avgSpeed(),minDownloadSpeed:this.minSpeed(),maxDownloadSpeed:this.maxSpeed(),avgTimeToFirstByte:this.avgTimeToFirstByte()}},e.prototype.add=function(e,t){return e+t},e.prototype.avgSpeed=function(){if(0===this.measures.length)return 0;var e=this.speeds().reduce((function(e,t){return e+t}),0),t=this.measures.length;return this.bytePerSecondToBitPerSecond(e/t)},e.prototype.bytePerSecondToBitPerSecond=function(e){return i.Converter.bytesToBits(e)},e.prototype.minSpeed=function(){return 0===this.measures.length?0:this.bytePerSecondToBitPerSecond(Math.min.apply(Math,this.speeds()))},e.prototype.speeds=function(){return this.measures.map((function(e){return e.speed}))},e.prototype.maxSpeed=function(){return 0===this.measures.length?0:this.bytePerSecondToBitPerSecond(Math.max.apply(Math,this.speeds()))},e.prototype.totalTime=function(){return 0===this.measures.length?0:this.measures.reduce((function(e,t){return e+t.duration}),0)},e.prototype.avgTimeToFirstByte=function(){if(0===this.measures.length)return 0;var e=this.measures.reduce((function(e,t){return e+1e3*t.timeToFirstByte}),0)/this.measures.length;return Math.ceil(e)},e}();t.DownloadSpeedMeter=r},9796:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=n(4649),a=n(4649),r=n(5063),o=function(){function e(e){void 0===e&&(e=i.ANALYTICS_QUALITY_CHANGE_COUNT_THRESHOLD),this.qualityChangeThreshold=e,this.qualityChangeCount=0,this.previousVideoBitrate=0,this.previousAudioBitrate=0,this.startupHasFinished=!1}return e.prototype.resetValues=function(){this.qualityChangeCount=0,this.previousAudioBitrate=0,this.previousVideoBitrate=0,this.startupHasFinished=!1,this.stopResetInterval()},e.prototype.isQualityChangeEventEnabled=function(){return this.qualityChangeCount<=this.qualityChangeThreshold},e.prototype.increaseCounter=function(){this.qualityChangeCount++},e.prototype.shouldAllowVideoQualityChange=function(e){return this.startupHasFinished&&(0,r.isNotNullish)(e)&&!isNaN(e)&&0!==this.previousVideoBitrate&&this.previousVideoBitrate!==e&&this.isQualityChangeEventEnabled()},e.prototype.setVideoBitrate=function(e){this.startupHasFinished&&(this.previousVideoBitrate=e)},e.prototype.setStartupHasFinished=function(){this.startupHasFinished=!0},e.prototype.shouldAllowAudioQualityChange=function(e){return this.startupHasFinished&&(0,r.isNotNullish)(e)&&!isNaN(e)&&0!==this.previousAudioBitrate&&this.previousAudioBitrate!==e&&this.isQualityChangeEventEnabled()},e.prototype.setAudioBitrate=function(e){this.startupHasFinished&&(this.previousAudioBitrate=e)},e.prototype.startResetInterval=function(){var e=this;void 0===this.resetIntervalId&&(this.resetIntervalId=window.setInterval((function(){e.resetCounter()}),a.ANALYTICS_QUALITY_CHANGE_COUNT_RESET_INTERVAL))},e.prototype.stopResetInterval=function(){void 0!==this.resetIntervalId&&(window.clearInterval(this.resetIntervalId),delete this.resetIntervalId)},e.prototype.resetCounter=function(){this.qualityChangeCount=0},e}();t.default=o},9946:function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.PAGE_LOAD_TYPE=void 0,function(e){e[e.FOREGROUND=1]="FOREGROUND",e[e.BACKGROUND=2]="BACKGROUND"}(n||(t.PAGE_LOAD_TYPE=n={}))}},t={};function n(i){var a=t[i];if(void 0!==a)return a.exports;var r=t[i]={exports:{}};return e[i].call(r.exports,r,r.exports,n),r.exports}var i={};return function(){"use strict";var e=i;Object.defineProperty(e,"__esModule",{value:!0}),e.PlayerModule=e.adapters=e.Bitmovin8Adapter=e.CdnProviders=e.Players=e.version=void 0;var t=n(4679);Object.defineProperty(e,"Bitmovin8Adapter",{enumerable:!0,get:function(){return t.Bitmovin8Adapter}});var a=n(681);Object.defineProperty(e,"PlayerModule",{enumerable:!0,get:function(){return a.PlayerModule}});var r=n(4355);Object.defineProperty(e,"CdnProviders",{enumerable:!0,get:function(){return r.CdnProvider}});var o=n(556);Object.defineProperty(e,"Players",{enumerable:!0,get:function(){return o.Player}});var s=n(4926);Object.defineProperty(e,"version",{enumerable:!0,get:function(){return s.VERSION}}),e.adapters={Bitmovin8Adapter:t.Bitmovin8Adapter},e.default={PlayerModule:a.PlayerModule}}(),i}()}))},98221:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=n(30100);t.default=i.default.PlayerModule}},function(e){return function(t){return e(e.s=t)}(98221)}])}));
})();
