/**
 * @module Util
 */
import type { PlayerConfig } from './PlayerConfigAPI';
/**
 * Builder to simplify the configuration process of the player.
 */
export declare class PlayerConfigBuilder {
    /**
     * Creates a new `PlayerConfigBuilder` instance.
     *
     * Either a player key or an existing player configuration can be provided.
     * Any build steps that are executed later on may override the provided configuration.
     *
     * @param playerKeyOrConfig - The player key or an existing player configuration to start with.
     */
    constructor(playerKeyOrConfig: string | PlayerConfig);
    /**
     * Optimizes for each platform based on best practices.
     *
     * Supports config optimization for WebOs and Tizen.
     *
     * @param input - Required for platform specifics like the `input.appId`
     */
    optimizeForPlatform(input?: OptimizeForPlatformInput): this;
    /**
     * Enables low latency live playback.
     *
     * Low latency live playback is supported for DASH streams with chunked CMAF segments and LL-HLS streams.
     *
     * Applies the following to the player configuration:
     * - A {@link LiveConfig.lowLatency | low latency configuration} with sensible default values
     * - A {@link LiveConfig.synchronization | client-clock synchronization configuration} using `https://time.akamai.com`
     * - Additional parameters in {@link PlayerConfig.tweaks | player tweaks} to facilitate low latency playback
     *
     * Default `LowLatencyConfig`:
     * ```javascript
     * {
     *   targetLatency: 5,
     *   catchup: {
     *     playbackRateThreshold: 0.075,
     *     seekThreshold: 5,
     *     playbackRate: 1.2,
     *   },
     *   fallback: {
     *     playbackRateThreshold: 0.075,
     *     seekThreshold: 5,
     *     playbackRate: 0.95,
     *   },
     * }
     * ```
     *
     * @param options - Optional parameters to customize the low latency configuration.
     */
    enableLowLatency(options?: ConfigBuilderLowLatencyOptions): this;
    /**
     * Merges the given configuration into the current configuration.
     *
     * The given configuration will overwrite any existing values in the current configuration.
     * This can be called at any point during the configuration building process, allowing you to control which part
     * of the configuration you want to override.
     *
     * @param config - The configuration to merge into the current configuration.
     */
    mergeWith(config: Partial<PlayerConfig>): this;
    /**
     * Builds the finished configuration.
     */
    build(): PlayerConfig;
}
/**
 * Input type for supporting various different platform specifics
 */
export type OptimizeForPlatformInput = {
    /**
     * Required if your application is loaded through the file protocol.
     */
    appId?: string;
};
/**
 * Options for the low latency configuration builder.
 */
export type ConfigBuilderLowLatencyOptions = {
    /**
     * The desired target latency in seconds.
     *
     * Default: 5
     */
    targetLatency?: number;
};
/**
 * The util API is exposed on the global namespace under `bitmovin.player.util`
 */
export interface UtilApi {
    PlayerConfigBuilder: typeof PlayerConfigBuilder;
}
