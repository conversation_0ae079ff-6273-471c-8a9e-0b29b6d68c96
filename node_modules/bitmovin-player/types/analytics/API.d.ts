import type { AnalyticsConfig } from './ConfigAPI';
export interface CustomDataValues {
    customData1?: string;
    customData2?: string;
    customData3?: string;
    customData4?: string;
    customData5?: string;
    customData6?: string;
    customData7?: string;
    customData8?: string;
    customData9?: string;
    customData10?: string;
    customData11?: string;
    customData12?: string;
    customData13?: string;
    customData14?: string;
    customData15?: string;
    customData16?: string;
    customData17?: string;
    customData18?: string;
    customData19?: string;
    customData20?: string;
    customData21?: string;
    customData22?: string;
    customData23?: string;
    customData24?: string;
    customData25?: string;
    customData26?: string;
    customData27?: string;
    customData28?: string;
    customData29?: string;
    customData30?: string;
    customData31?: string;
    customData32?: string;
    customData33?: string;
    customData34?: string;
    customData35?: string;
    customData36?: string;
    customData37?: string;
    customData38?: string;
    customData39?: string;
    customData40?: string;
    customData41?: string;
    customData42?: string;
    customData43?: string;
    customData44?: string;
    customData45?: string;
    customData46?: string;
    customData47?: string;
    customData48?: string;
    customData49?: string;
    customData50?: string;
    experimentName?: string;
}
export type SsaiAdMetadata = {
    adId?: string;
    adSystem?: string;
    customData?: CustomDataValues;
};
export type AdPosition = 'preroll' | 'midroll' | 'postroll';
export type SsaiAdBreakMetadata = {
    adPosition?: AdPosition;
};
export type SsaiAdQuartile = 'first' | 'midpoint' | 'third' | 'completed';
export type SsaiAdQuartileMetadata = {
    failedBeaconUrl?: string;
};
export interface SsaiApi {
    adBreakStart(adBreakMetadata?: SsaiAdBreakMetadata): void;
    adStart(adMetadata?: SsaiAdMetadata): void;
    adQuartileFinished(adQuartile: SsaiAdQuartile, adQuartileMetadata?: SsaiAdQuartileMetadata): void;
    adBreakEnd(): void;
}
export interface Bitmovin8Adapter {
    get version(): string;
    getCurrentImpressionId(): string | undefined;
    getUserId(): string | undefined;
    setCustomData(values: CustomDataValues): void;
    setCustomDataOnce(values: CustomDataValues): void;
    sourceChange(config: AnalyticsConfig): void;
    readonly ssai: SsaiApi;
}
