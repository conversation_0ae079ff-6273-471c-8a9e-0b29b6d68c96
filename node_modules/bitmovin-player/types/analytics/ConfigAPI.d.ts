export interface CollectorConfig {
    backendUrl?: string;
    enabled?: boolean;
    cookiesEnabled?: boolean;
    cookiesDomain?: string;
    cookiesMaxAge?: number;
    origin?: string;
    ssaiEngagementTrackingEnabled?: boolean;
}
export interface AnalyticsDebugConfig {
    fields?: string[];
}
export interface AnalyticsConfig {
    config?: CollectorConfig;
    debug?: AnalyticsDebugConfig | boolean;
    key?: string;
    playerKey?: string;
    deviceType?: string;
    deviceClass?: 'Console' | 'Desktop' | 'Other' | 'Phone' | 'STB' | 'Tablet' | 'TV' | 'Wearable';
    player?: string;
    userId?: string;
    customUserId?: string;
    cdnProvider?: string;
    videoId?: string;
    title?: string;
    isLive?: boolean;
    customData1?: any;
    customData2?: any;
    customData3?: any;
    customData4?: any;
    customData5?: any;
    customData6?: any;
    customData7?: any;
    customData8?: any;
    customData9?: any;
    customData10?: any;
    customData11?: any;
    customData12?: any;
    customData13?: any;
    customData14?: any;
    customData15?: any;
    customData16?: any;
    customData17?: any;
    customData18?: any;
    customData19?: any;
    customData20?: any;
    customData21?: any;
    customData22?: any;
    customData23?: any;
    customData24?: any;
    customData25?: any;
    customData26?: any;
    customData27?: any;
    customData28?: any;
    customData29?: any;
    customData30?: any;
    customData31?: any;
    customData32?: any;
    customData33?: any;
    customData34?: any;
    customData35?: any;
    customData36?: any;
    customData37?: any;
    customData38?: any;
    customData39?: any;
    customData40?: any;
    customData41?: any;
    customData42?: any;
    customData43?: any;
    customData44?: any;
    customData45?: any;
    customData46?: any;
    customData47?: any;
    customData48?: any;
    customData49?: any;
    customData50?: any;
    experimentName?: string;
}
