import {
  __commonJS,
  __require
} from "./chunk-VUNV25KB.js";

// node_modules/bitmovin-player/bitmovinplayer-ui.js
var require_bitmovinplayer_ui = __commonJS({
  "node_modules/bitmovin-player/bitmovinplayer-ui.js"(exports, module) {
    (function() {
      !function(e) {
        var t;
        "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define([], e) : ((t = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : this).bitmovin || (t.bitmovin = {})).playerui = e();
      }(function() {
        return function o(i, r, s) {
          function a(n, e2) {
            if (!r[n]) {
              if (!i[n]) {
                var t = "function" == typeof __require && __require;
                if (!e2 && t) return t(n, true);
                if (l) return l(n, true);
                e2 = new Error("Cannot find module '" + n + "'");
                throw e2.code = "MODULE_NOT_FOUND", e2;
              }
              t = r[n] = { exports: {} };
              i[n][0].call(t.exports, function(e3) {
                var t2 = i[n][1][e3];
                return a(t2 || e3);
              }, t, t.exports, o, i, r, s);
            }
            return r[n].exports;
          }
          for (var l = "function" == typeof __require && __require, e = 0; e < s.length; e++) a(s[e]);
          return a;
        }({ 1: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.ArrayUtils = void 0, (n.ArrayUtils || (n.ArrayUtils = {})).remove = function(e2, t2) {
            return -1 < (t2 = e2.indexOf(t2)) ? e2.splice(t2, 1)[0] : null;
          };
        }, {}], 2: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.AudioTrackSwitchHandler = void 0;
          var i = e("./localization/i18n");
          function o(e2, t2, n2) {
            var o2 = this;
            this.addAudioTrack = function(e3) {
              e3 = e3.track;
              o2.listElement.hasItem(e3.id) || o2.listElement.addItem(e3.id, i.i18n.getLocalizer(e3.label), true);
            }, this.removeAudioTrack = function(e3) {
              e3 = e3.track;
              o2.listElement.hasItem(e3.id) && o2.listElement.removeItem(e3.id);
            }, this.selectCurrentAudioTrack = function() {
              var e3 = o2.player.getAudio();
              e3 && o2.listElement.selectItem(e3.id);
            }, this.refreshAudioTracks = function() {
              var e3 = o2.player.getAvailableAudio();
              o2.listElement.synchronizeItems(e3.map(function(e4) {
                return { key: e4.id, label: e4.label };
              })), o2.selectCurrentAudioTrack();
            }, this.player = e2, this.listElement = t2, this.uimanager = n2, this.bindSelectionEvent(), this.bindPlayerEvents(), this.refreshAudioTracks();
          }
          o.prototype.bindSelectionEvent = function() {
            var n2 = this;
            this.listElement.onItemSelected.subscribe(function(e2, t2) {
              n2.player.setAudio(t2);
            });
          }, o.prototype.bindPlayerEvents = function() {
            this.player.on(this.player.exports.PlayerEvent.AudioChanged, this.selectCurrentAudioTrack), this.player.on(this.player.exports.PlayerEvent.SourceUnloaded, this.refreshAudioTracks), this.player.on(this.player.exports.PlayerEvent.PeriodSwitched, this.refreshAudioTracks), this.player.on(this.player.exports.PlayerEvent.AudioAdded, this.addAudioTrack), this.player.on(this.player.exports.PlayerEvent.AudioRemoved, this.removeAudioTrack), this.uimanager.getConfig().events.onUpdated.subscribe(this.refreshAudioTracks);
          }, n.AudioTrackSwitchHandler = o;
        }, { "./localization/i18n": 91 }], 3: [function(e, t, n) {
          "use strict";
          function o() {
          }
          Object.defineProperty(n, "__esModule", { value: true }), n.BrowserUtils = void 0, Object.defineProperty(o, "isMobile", { get: function() {
            return !!this.windowExists() && navigator && navigator.userAgent && /Mobi/.test(navigator.userAgent);
          }, enumerable: false, configurable: true }), Object.defineProperty(o, "isChrome", { get: function() {
            return !!this.windowExists() && navigator && navigator.userAgent && /Chrome/.test(navigator.userAgent);
          }, enumerable: false, configurable: true }), Object.defineProperty(o, "isAndroid", { get: function() {
            return !!this.windowExists() && navigator && navigator.userAgent && /Android/.test(navigator.userAgent) && !this.isHisense;
          }, enumerable: false, configurable: true }), Object.defineProperty(o, "isIOS", { get: function() {
            return !!this.windowExists() && navigator && navigator.userAgent && /iPad|iPhone|iPod/.test(navigator.userAgent);
          }, enumerable: false, configurable: true }), Object.defineProperty(o, "isMacIntel", { get: function() {
            return !!this.windowExists() && navigator && navigator.userAgent && "MacIntel" === navigator.platform;
          }, enumerable: false, configurable: true }), Object.defineProperty(o, "isHisense", { get: function() {
            return !!this.windowExists() && navigator && navigator.userAgent && /Hisense/.test(navigator.userAgent);
          }, enumerable: false, configurable: true }), Object.defineProperty(o, "isPlayStation", { get: function() {
            return !!this.windowExists() && navigator && navigator.userAgent && /PlayStation/i.test(navigator.userAgent);
          }, enumerable: false, configurable: true }), Object.defineProperty(o, "isWebOs", { get: function() {
            return !!this.windowExists() && navigator && navigator.userAgent && (navigator.userAgent.includes("Web0S") || navigator.userAgent.includes("NetCast"));
          }, enumerable: false, configurable: true }), Object.defineProperty(o, "isTizen", { get: function() {
            return !!this.windowExists() && navigator && navigator.userAgent && /Tizen/.test(navigator.userAgent);
          }, enumerable: false, configurable: true }), Object.defineProperty(o, "isTouchSupported", { get: function() {
            return !!this.windowExists() && ("ontouchstart" in window || navigator && navigator.userAgent && (0 < navigator.maxTouchPoints || 0 < navigator.msMaxTouchPoints));
          }, enumerable: false, configurable: true }), o.windowExists = function() {
            return "undefined" != typeof window;
          }, n.BrowserUtils = o;
        }, {}], 4: [function(e, t, n) {
          "use strict";
          var o, r, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), e = (Object.defineProperty(n, "__esModule", { value: true }), n.AdClickOverlay = void 0, e("./clickoverlay")), e = (r = e.ClickOverlay, i(s, r), s.prototype.configure = function(e2, t2) {
            function n2() {
              o2.setUrl(null);
            }
            var o2 = this, i2 = (r.prototype.configure.call(this, e2, t2), null);
            e2.on(e2.exports.PlayerEvent.AdStarted, function(e3) {
              e3 = e3.ad;
              o2.setUrl(e3.clickThroughUrl), i2 = e3.clickThroughUrlOpened;
            });
            e2.on(e2.exports.PlayerEvent.AdFinished, n2), e2.on(e2.exports.PlayerEvent.AdSkipped, n2), e2.on(e2.exports.PlayerEvent.AdError, n2), this.onClick.subscribe(function() {
              e2.pause("ui-ad-click-overlay"), i2 && i2();
            });
          }, s);
          function s(e2) {
            var t2 = r.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { acceptsTouchWithUiHidden: true }, t2.config), t2;
          }
          n.AdClickOverlay = e;
        }, { "./clickoverlay": 16 }], 5: [function(e, t, n) {
          "use strict";
          var o, a, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.AdMessageLabel = void 0, e("./label")), l = e("../stringutils"), c = e("../localization/i18n"), e = (a = r.Label, i(s, a), s.prototype.configure = function(t2, e2) {
            function n2() {
              t2.off(t2.exports.PlayerEvent.TimeChanged, s2);
            }
            var o2 = this, i2 = (a.prototype.configure.call(this, t2, e2), this.getConfig()), r2 = i2.text, s2 = function() {
              o2.setText(l.StringUtils.replaceAdMessagePlaceholders(c.i18n.performLocalization(r2), null, t2));
            };
            t2.on(t2.exports.PlayerEvent.AdStarted, function(e3) {
              e3 = e3.ad.uiConfig;
              r2 = e3 && e3.message || i2.text, s2(), t2.on(t2.exports.PlayerEvent.TimeChanged, s2);
            }), t2.on(t2.exports.PlayerEvent.AdSkipped, n2), t2.on(t2.exports.PlayerEvent.AdError, n2), t2.on(t2.exports.PlayerEvent.AdFinished, n2);
          }, s);
          function s(e2) {
            var t2 = a.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-label-ad-message", text: c.i18n.getLocalizer("ads.remainingTime") }, t2.config), t2;
          }
          n.AdMessageLabel = e;
        }, { "../localization/i18n": 91, "../stringutils": 111, "./label": 28 }], 6: [function(e, t, n) {
          "use strict";
          var o, c, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.AdSkipButton = void 0, e("./button")), u = e("../stringutils"), e = (c = r.Button, i(s, c), s.prototype.configure = function(t2, e2) {
            function n2() {
              t2.off(t2.exports.PlayerEvent.TimeChanged, l);
            }
            var o2 = this, i2 = (c.prototype.configure.call(this, t2, e2), this.getConfig()), r2 = i2.untilSkippableMessage, s2 = i2.skippableMessage, a = -1, l = function() {
              o2.show(), t2.getCurrentTime() < a ? (o2.setText(u.StringUtils.replaceAdMessagePlaceholders(r2, a, t2)), o2.disable()) : (o2.setText(s2), o2.enable());
            };
            t2.on(t2.exports.PlayerEvent.AdStarted, function(e3) {
              e3 = e3.ad;
              a = e3.skippableAfter, r2 = e3.uiConfig && e3.uiConfig.untilSkippableMessage || i2.untilSkippableMessage, s2 = e3.uiConfig && e3.uiConfig.skippableMessage || i2.skippableMessage, "number" == typeof a && 0 <= a ? (l(), t2.on(t2.exports.PlayerEvent.TimeChanged, l)) : o2.hide();
            }), t2.on(t2.exports.PlayerEvent.AdSkipped, n2), t2.on(t2.exports.PlayerEvent.AdError, n2), t2.on(t2.exports.PlayerEvent.AdFinished, n2), this.onClick.subscribe(function() {
              t2.ads.skip();
            });
          }, s);
          function s(e2) {
            var t2 = c.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-button-ad-skip", untilSkippableMessage: "Skip ad in {remainingTime}", skippableMessage: "Skip ad", acceptsTouchWithUiHidden: true }, t2.config), t2;
          }
          n.AdSkipButton = e;
        }, { "../stringutils": 111, "./button": 12 }], 7: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.AirPlayToggleButton = void 0, e("./togglebutton")), a = e("../localization/i18n"), e = (i = s.ToggleButton, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2, o2 = this;
            i.prototype.configure.call(this, e2, t2), e2.isAirplayAvailable ? (this.onClick.subscribe(function() {
              e2.isAirplayAvailable() ? e2.showAirplayTargetPicker() : console && console.log("AirPlay unavailable");
            }), t2 = function() {
              e2.isAirplayActive() ? o2.on() : o2.off();
            }, e2.on(e2.exports.PlayerEvent.AirplayAvailable, n2 = function() {
              e2.isAirplayAvailable() ? o2.show() : o2.hide();
            }), e2.on(e2.exports.PlayerEvent.AirplayChanged, t2), n2(), t2()) : this.hide();
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-airplaytogglebutton", text: a.i18n.getLocalizer("appleAirplay") }, t2.config), t2;
          }
          n.AirPlayToggleButton = e;
        }, { "../localization/i18n": 91, "./togglebutton": 74 }], 8: [function(e, t, n) {
          "use strict";
          var o, a, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.AudioQualitySelectBox = void 0, e("./selectbox")), l = e("../localization/i18n"), e = (a = r.SelectBox, i(s, a), s.prototype.configure = function(i2, e2) {
            function t2() {
              var e3 = i2.getAvailableAudioQualities();
              r2.clearItems(), r2.addItem("auto", l.i18n.getLocalizer("auto"));
              for (var t3 = 0, n2 = e3; t3 < n2.length; t3++) {
                var o2 = n2[t3];
                r2.addItem(o2.id, o2.label);
              }
              s2();
            }
            var r2 = this, s2 = (a.prototype.configure.call(this, i2, e2), function() {
              r2.selectItem(i2.getAudioQuality().id);
            });
            this.onItemSelected.subscribe(function(e3, t3) {
              i2.setAudioQuality(t3);
            }), i2.on(i2.exports.PlayerEvent.AudioChanged, t2), i2.on(i2.exports.PlayerEvent.SourceUnloaded, t2), i2.on(i2.exports.PlayerEvent.PeriodSwitched, t2), i2.on(i2.exports.PlayerEvent.AudioQualityChanged, s2), i2.exports.PlayerEvent.AudioQualityAdded && (i2.on(i2.exports.PlayerEvent.AudioQualityAdded, t2), i2.on(i2.exports.PlayerEvent.AudioQualityRemoved, t2)), e2.getConfig().events.onUpdated.subscribe(t2);
          }, s);
          function s(e2) {
            var t2 = a.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-audioqualityselectbox"] }, t2.config), t2;
          }
          n.AudioQualitySelectBox = e;
        }, { "../localization/i18n": 91, "./selectbox": 44 }], 9: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.AudioTrackListBox = void 0, e("./listbox")), a = e("../audiotrackutils"), e = (i = s.ListBox, r(l, i), l.prototype.configure = function(e2, t2) {
            i.prototype.configure.call(this, e2, t2), new a.AudioTrackSwitchHandler(e2, this, t2);
          }, l);
          function l() {
            return null !== i && i.apply(this, arguments) || this;
          }
          n.AudioTrackListBox = e;
        }, { "../audiotrackutils": 2, "./listbox": 29 }], 10: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.AudioTrackSelectBox = void 0, e("./selectbox")), a = e("../audiotrackutils"), e = (i = s.SelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            i.prototype.configure.call(this, e2, t2), new a.AudioTrackSwitchHandler(e2, this, t2);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-audiotrackselectbox"] }, t2.config), t2;
          }
          n.AudioTrackSelectBox = e;
        }, { "../audiotrackutils": 2, "./selectbox": 44 }], 11: [function(e, t, n) {
          "use strict";
          var o, s, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.BufferingOverlay = void 0, e("./container")), a = e("./component"), l = e("../timeout"), e = (s = r.Container, i(c, s), c.prototype.configure = function(e2, t2) {
            function n2() {
              r2.start();
            }
            function o2() {
              r2.clear(), i2.hide();
            }
            var i2 = this, t2 = (s.prototype.configure.call(this, e2, t2), this.getConfig()), r2 = new l.Timeout(t2.showDelayMs, function() {
              i2.show();
            });
            e2.on(e2.exports.PlayerEvent.StallStarted, n2), e2.on(e2.exports.PlayerEvent.StallEnded, o2), e2.on(e2.exports.PlayerEvent.Play, n2), e2.on(e2.exports.PlayerEvent.Playing, o2), e2.on(e2.exports.PlayerEvent.Paused, o2), e2.on(e2.exports.PlayerEvent.Seek, n2), e2.on(e2.exports.PlayerEvent.Seeked, o2), e2.on(e2.exports.PlayerEvent.TimeShift, n2), e2.on(e2.exports.PlayerEvent.TimeShifted, o2), e2.on(e2.exports.PlayerEvent.SourceUnloaded, o2), e2.isStalled() && this.show();
          }, c);
          function c(e2) {
            var t2 = s.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.indicators = [new a.Component({ tag: "div", cssClass: "ui-buffering-overlay-indicator", role: "img" }), new a.Component({ tag: "div", cssClass: "ui-buffering-overlay-indicator", role: "img" }), new a.Component({ tag: "div", cssClass: "ui-buffering-overlay-indicator", role: "img" })], t2.config = t2.mergeConfig(e2, { cssClass: "ui-buffering-overlay", hidden: true, components: t2.indicators, showDelayMs: 1e3 }, t2.config), t2;
          }
          n.BufferingOverlay = e;
        }, { "../timeout": 113, "./component": 18, "./container": 19 }], 12: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.Button = void 0, e("./component")), a = e("../dom"), l = e("../eventdispatcher"), c = e("../localization/i18n"), e = (i = s.Component, r(u, i), u.prototype.toDomElement = function() {
            var e2 = this, t2 = { id: this.config.id, "aria-label": c.i18n.performLocalization(this.config.ariaLabel || this.config.text), class: this.getCssClasses(), type: "button", tabindex: this.config.tabIndex.toString() }, t2 = (null != this.config.role && (t2.role = this.config.role), new a.DOM("button", t2, this).append(new a.DOM("span", { class: this.prefixCss("label") }).html(c.i18n.performLocalization(this.config.text))));
            return t2.on("click", function() {
              e2.onClickEvent();
            }), t2;
          }, u.prototype.setText = function(e2) {
            this.getDomElement().find("." + this.prefixCss("label")).html(c.i18n.performLocalization(e2));
          }, u.prototype.onClickEvent = function() {
            this.buttonEvents.onClick.dispatch(this);
          }, Object.defineProperty(u.prototype, "onClick", { get: function() {
            return this.buttonEvents.onClick.getEvent();
          }, enumerable: false, configurable: true }), u);
          function u(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.buttonEvents = { onClick: new l.EventDispatcher() }, t2.config = t2.mergeConfig(e2, { cssClass: "ui-button", role: "button", tabIndex: 0, acceptsTouchWithUiHidden: false }, t2.config), t2;
          }
          n.Button = e;
        }, { "../dom": 84, "../eventdispatcher": 86, "../localization/i18n": 91, "./component": 18 }], 13: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.CastStatusOverlay = void 0, e("./container")), a = e("./label"), l = e("../localization/i18n"), e = (i = s.Container, r(c, i), c.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), e2.on(e2.exports.PlayerEvent.CastWaitingForDevice, function(e3) {
              n2.show();
              e3 = e3.castPayload.deviceName;
              n2.statusLabel.setText(l.i18n.getLocalizer("connectingTo", { castDeviceName: e3 }));
            }), e2.on(e2.exports.PlayerEvent.CastStarted, function(e3) {
              n2.show();
              e3 = e3.deviceName;
              n2.statusLabel.setText(l.i18n.getLocalizer("playingOn", { castDeviceName: e3 }));
            }), e2.on(e2.exports.PlayerEvent.CastStopped, function(e3) {
              n2.hide();
            });
          }, c);
          function c(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.statusLabel = new a.Label({ cssClass: "ui-cast-status-label" }), t2.config = t2.mergeConfig(e2, { cssClass: "ui-cast-status-overlay", components: [t2.statusLabel], hidden: true }, t2.config), t2;
          }
          n.CastStatusOverlay = e;
        }, { "../localization/i18n": 91, "./container": 19, "./label": 28 }], 14: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.CastToggleButton = void 0, e("./togglebutton")), a = e("../localization/i18n"), e = (i = s.ToggleButton, r(l, i), l.prototype.configure = function(e2, t2) {
            function n2() {
              e2.isCastAvailable() ? o2.show() : o2.hide();
            }
            var o2 = this;
            i.prototype.configure.call(this, e2, t2), this.onClick.subscribe(function() {
              e2.isCastAvailable() ? e2.isCasting() ? e2.castStop() : e2.castVideo() : console && console.log("Cast unavailable");
            });
            e2.on(e2.exports.PlayerEvent.CastAvailable, n2), e2.on(e2.exports.PlayerEvent.CastWaitingForDevice, function() {
              o2.on();
            }), e2.on(e2.exports.PlayerEvent.CastStarted, function() {
              o2.on();
            }), e2.on(e2.exports.PlayerEvent.CastStopped, function() {
              o2.off();
            }), n2(), e2.isCasting() && this.on();
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-casttogglebutton", text: a.i18n.getLocalizer("googleCast") }, t2.config), t2;
          }
          n.CastToggleButton = e;
        }, { "../localization/i18n": 91, "./togglebutton": 74 }], 15: [function(e, t, n) {
          "use strict";
          var o, l, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.CastUIContainer = void 0, e("./uicontainer")), c = e("../timeout"), e = (l = r.UIContainer, i(s, l), s.prototype.configure = function(e2, t2) {
            var n2 = this, o2 = (l.prototype.configure.call(this, e2, t2), this.getConfig()), i2 = false, r2 = (this.castUiHideTimeout = new c.Timeout(o2.hideDelay, function() {
              t2.onControlsHide.dispatch(n2), i2 = false;
            }), function() {
              i2 || (t2.onControlsShow.dispatch(n2), i2 = true);
            }), s2 = function() {
              r2(), n2.castUiHideTimeout.clear();
            }, a = function() {
              r2(), n2.castUiHideTimeout.start();
            };
            e2.on(e2.exports.PlayerEvent.Play, a), e2.on(e2.exports.PlayerEvent.Paused, s2), e2.on(e2.exports.PlayerEvent.Seek, s2), e2.on(e2.exports.PlayerEvent.Seeked, function() {
              (e2.isPlaying() ? a : s2)();
            }), t2.getConfig().events.onUpdated.subscribe(a);
          }, s.prototype.release = function() {
            l.prototype.release.call(this), this.castUiHideTimeout.clear();
          }, s);
          function s(e2) {
            return l.call(this, e2) || this;
          }
          n.CastUIContainer = e;
        }, { "../timeout": 113, "./uicontainer": 76 }], 16: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), e = (Object.defineProperty(n, "__esModule", { value: true }), n.ClickOverlay = void 0, e("./button")), e = (i = e.Button, r(s, i), s.prototype.initialize = function() {
            i.prototype.initialize.call(this), this.setUrl(this.config.url);
            var e2 = this.getDomElement();
            e2.on("click", function() {
              e2.data("url") && window.open(e2.data("url"), "_blank");
            });
          }, s.prototype.getUrl = function() {
            return this.getDomElement().data("url");
          }, s.prototype.setUrl = function(e2) {
            void 0 !== e2 && null != e2 || (e2 = ""), this.getDomElement().data("url", e2);
          }, s);
          function s(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-clickoverlay", role: t2.config.role }, t2.config), t2;
          }
          n.ClickOverlay = e;
        }, { "./button": 12 }], 17: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.CloseButton = void 0, e("./button")), a = e("../localization/i18n"), e = (i = s.Button, r(l, i), l.prototype.configure = function(e2, t2) {
            i.prototype.configure.call(this, e2, t2);
            var n2 = this.getConfig();
            this.onClick.subscribe(function() {
              n2.target.hide();
            });
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-closebutton", text: a.i18n.getLocalizer("close") }, t2.config), t2;
          }
          n.CloseButton = e;
        }, { "../localization/i18n": 91, "./button": 12 }], 18: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.Component = n.ViewMode = void 0;
          var o, i = e("../guid"), r = e("../dom"), s = e("../eventdispatcher"), a = e("../localization/i18n");
          (e = o = n.ViewMode || (n.ViewMode = {})).Persistent = "persistent", e.Temporary = "temporary", n.Component = (l.prototype.initialize = function() {
            this.hidden = this.config.hidden, this.disabled = this.config.disabled, this.isHidden() && (this.hidden = false, this.hide()), this.isDisabled() && (this.disabled = false, this.disable());
          }, l.prototype.configure = function(e2, n2) {
            var o2 = this;
            this.onShow.subscribe(function() {
              return n2.onComponentShow.dispatch(o2);
            }), this.onHide.subscribe(function() {
              return n2.onComponentHide.dispatch(o2);
            }), this.onViewModeChanged.subscribe(function(e3, t2) {
              return n2.onComponentViewModeChanged.dispatch(o2, t2);
            }), this.getDomElement().on("mouseenter", function() {
              return o2.onHoverChangedEvent(true);
            }), this.getDomElement().on("mouseleave", function() {
              return o2.onHoverChangedEvent(false);
            });
          }, l.prototype.release = function() {
          }, l.prototype.toDomElement = function() {
            return new r.DOM(this.config.tag, { id: this.config.id, class: this.getCssClasses(), role: this.config.role }, this);
          }, l.prototype.getDomElement = function() {
            return this.element || (this.element = this.toDomElement()), this.element;
          }, l.prototype.hasDomElement = function() {
            return Boolean(this.element);
          }, l.prototype.setAriaLabel = function(e2) {
            this.setAriaAttr("label", a.i18n.performLocalization(e2));
          }, l.prototype.setAriaAttr = function(e2, t2) {
            this.getDomElement().attr("aria-".concat(e2), t2);
          }, l.prototype.mergeConfig = function(e2, t2, n2) {
            return Object.assign({}, n2, t2, e2);
          }, l.prototype.getCssClasses = function() {
            var t2 = this;
            return [this.config.cssClass].concat(this.config.cssClasses).map(function(e2) {
              return t2.prefixCss(e2);
            }).join(" ").trim();
          }, l.prototype.prefixCss = function(e2) {
            return this.config.cssPrefix + "-" + e2;
          }, l.prototype.getConfig = function() {
            return this.config;
          }, l.prototype.hide = function() {
            this.hidden || (this.hidden = true, this.getDomElement().addClass(this.prefixCss(l.CLASS_HIDDEN)), this.onHideEvent());
          }, l.prototype.show = function() {
            this.hidden && (this.getDomElement().removeClass(this.prefixCss(l.CLASS_HIDDEN)), this.hidden = false, this.onShowEvent());
          }, l.prototype.isHidden = function() {
            return this.hidden;
          }, l.prototype.isShown = function() {
            return !this.isHidden();
          }, l.prototype.toggleHidden = function() {
            this.isHidden() ? this.show() : this.hide();
          }, l.prototype.disable = function() {
            this.disabled || (this.disabled = true, this.getDomElement().addClass(this.prefixCss(l.CLASS_DISABLED)), this.onDisabledEvent());
          }, l.prototype.enable = function() {
            this.disabled && (this.getDomElement().removeClass(this.prefixCss(l.CLASS_DISABLED)), this.disabled = false, this.onEnabledEvent());
          }, l.prototype.isDisabled = function() {
            return this.disabled;
          }, l.prototype.isEnabled = function() {
            return !this.isDisabled();
          }, l.prototype.isHovered = function() {
            return this.hovered;
          }, l.prototype.onShowEvent = function() {
            this.componentEvents.onShow.dispatch(this);
          }, l.prototype.onHideEvent = function() {
            this.componentEvents.onHide.dispatch(this);
          }, l.prototype.onEnabledEvent = function() {
            this.componentEvents.onEnabled.dispatch(this);
          }, l.prototype.onDisabledEvent = function() {
            this.componentEvents.onDisabled.dispatch(this);
          }, l.prototype.onViewModeChangedEvent = function(e2) {
            this.viewMode !== e2 && (this.viewMode = e2, this.componentEvents.onViewModeChanged.dispatch(this, { mode: e2 }));
          }, l.prototype.onHoverChangedEvent = function(e2) {
            this.hovered = e2, this.componentEvents.onHoverChanged.dispatch(this, { hovered: e2 });
          }, Object.defineProperty(l.prototype, "onShow", { get: function() {
            return this.componentEvents.onShow.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "onHide", { get: function() {
            return this.componentEvents.onHide.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "onEnabled", { get: function() {
            return this.componentEvents.onEnabled.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "onDisabled", { get: function() {
            return this.componentEvents.onDisabled.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "onHoverChanged", { get: function() {
            return this.componentEvents.onHoverChanged.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "onViewModeChanged", { get: function() {
            return this.componentEvents.onViewModeChanged.getEvent();
          }, enumerable: false, configurable: true }), l.CLASS_HIDDEN = "hidden", l.CLASS_DISABLED = "disabled", l);
          function l(e2) {
            void 0 === e2 && (e2 = {}), this.componentEvents = { onShow: new s.EventDispatcher(), onHide: new s.EventDispatcher(), onViewModeChanged: new s.EventDispatcher(), onHoverChanged: new s.EventDispatcher(), onEnabled: new s.EventDispatcher(), onDisabled: new s.EventDispatcher() }, this.config = this.mergeConfig(e2, { tag: "div", id: "bmpui-id-" + i.Guid.next(), cssPrefix: "bmpui", cssClass: "ui-component", cssClasses: [], hidden: false, disabled: false }, {}), this.viewMode = o.Temporary;
          }
        }, { "../dom": 84, "../eventdispatcher": 86, "../guid": 89, "../localization/i18n": 91 }], 19: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.Container = void 0, e("./component")), a = e("../dom"), l = e("../arrayutils"), c = e("../localization/i18n"), e = (i = s.Component, r(u, i), u.prototype.addComponent = function(e2) {
            this.config.components.push(e2), this.componentsToAdd.push(e2);
          }, u.prototype.removeComponent = function(e2) {
            return null != l.ArrayUtils.remove(this.config.components, e2) && (this.componentsToRemove.push(e2), true);
          }, u.prototype.getComponents = function() {
            return this.config.components;
          }, u.prototype.removeComponents = function() {
            for (var e2 = 0, t2 = this.getComponents().slice(); e2 < t2.length; e2++) {
              var n2 = t2[e2];
              this.removeComponent(n2);
            }
          }, u.prototype.updateComponents = function() {
            for (var e2; void 0 !== (e2 = this.componentsToRemove.shift()); ) e2.getDomElement().remove();
            for (; void 0 !== (e2 = this.componentsToAdd.shift()); ) this.innerContainerElement.append(e2.getDomElement());
          }, u.prototype.toDomElement = function() {
            var e2 = new a.DOM(this.config.tag, { id: this.config.id, class: this.getCssClasses(), role: this.config.role, "aria-label": c.i18n.performLocalization(this.config.ariaLabel) }, this), t2 = new a.DOM(this.config.tag, { class: this.prefixCss("container-wrapper") });
            this.innerContainerElement = t2;
            for (var n2 = 0, o2 = this.config.components; n2 < o2.length; n2++) {
              var i2 = o2[n2];
              this.componentsToAdd.push(i2);
            }
            return this.updateComponents(), e2.append(t2), e2;
          }, u.prototype.suspendHideTimeout = function() {
          }, u.prototype.resumeHideTimeout = function() {
          }, u.prototype.trackComponentViewMode = function(e2) {
            e2 === s.ViewMode.Persistent ? this.componentsInPersistentViewMode++ : e2 === s.ViewMode.Temporary && (this.componentsInPersistentViewMode = Math.max(this.componentsInPersistentViewMode - 1, 0)), 0 < this.componentsInPersistentViewMode ? this.suspendHideTimeout() : this.resumeHideTimeout();
          }, u);
          function u(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-container", components: [] }, t2.config), t2.componentsToAdd = [], t2.componentsToRemove = [], t2.componentsInPersistentViewMode = 0, t2;
          }
          n.Container = e;
        }, { "../arrayutils": 1, "../dom": 84, "../localization/i18n": 91, "./component": 18 }], 20: [function(e, t, n) {
          "use strict";
          var o, r, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.ControlBar = void 0, e("./container")), a = e("../uiutils"), l = e("./spacer"), c = e("../localization/i18n"), u = e("../browserutils"), p = e("./settingspanel"), e = (r = s.Container, i(g, r), g.prototype.configure = function(e2, t2) {
            var n2 = this, o2 = (r.prototype.configure.call(this, e2, t2), 0), i2 = false;
            t2.getConfig().disableAutoHideWhenHovered && !u.BrowserUtils.isTouchSupported && a.UIUtils.traverseTree(this, function(e3) {
              e3 instanceof s.Container || e3 instanceof l.Spacer || e3.onHoverChanged.subscribe(function(e4, t3) {
                t3.hovered ? o2++ : o2--;
              });
            }), u.BrowserUtils.isMobile && (t2.onComponentShow.subscribe(function(e3) {
              e3 instanceof p.SettingsPanel && (i2 = true);
            }), t2.onComponentHide.subscribe(function(e3) {
              e3 instanceof p.SettingsPanel && (i2 = false);
            })), t2.onControlsShow.subscribe(function() {
              n2.show();
            }), t2.onPreviewControlsHide.subscribe(function(e3, t3) {
              t3.cancel = t3.cancel || 0 < o2 || i2;
            }), t2.onControlsHide.subscribe(function() {
              n2.hide();
            });
          }, g);
          function g(e2) {
            var t2 = r.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-controlbar", hidden: true, role: "region", ariaLabel: c.i18n.getLocalizer("controlBar") }, t2.config), t2;
          }
          n.ControlBar = e;
        }, { "../browserutils": 3, "../localization/i18n": 91, "../uiutils": 117, "./container": 19, "./settingspanel": 45, "./spacer": 52 }], 21: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.EcoModeContainer = void 0, e("../localization/i18n")), a = e("./container"), l = e("./ecomodetogglebutton"), c = e("./label"), u = e("./settingspanelitem"), e = (i = a.Container, r(p, i), p.prototype.setOnToggleCallback = function(e2) {
            this.onToggleCallback = e2;
          }, p.prototype.configure = function(l2) {
            var c2 = this;
            l2.on(l2.exports.PlayerEvent.SegmentPlayback, function(e2) {
              var t2, n2, o2, i2, r2, s2, a2;
              e2.mimeType.includes("video") && (s2 = (n2 = e2.mediaInfo).height, a2 = n2.width, t2 = n2.bitrate, n2 = n2.frameRate, o2 = (r2 = c2.getMaxQualityAvailable(l2.getAvailableVideoQualities())).height, i2 = r2.bitrate, r2 = r2.width, s2 = c2.calculateEnergyConsumption(n2, s2, a2, t2, e2.duration), a2 = c2.calculateEnergyConsumption(n2, o2, r2, i2, e2.duration), c2.ecoModeSavedEmissionsItem.isShown()) && c2.updateSavedEmissions(s2, a2, c2.emissionsSavedLabel);
            });
          }, p.prototype.updateSavedEmissions = function(e2, t2, n2) {
            this.currentEnergyEmission = 475 * e2;
            e2 = 475 * t2;
            this.savedEmissons += e2 - this.currentEnergyEmission, n2.setText(this.savedEmissons.toFixed(4) + " gCO2");
          }, p.prototype.calculateEnergyConsumption = function(e2, t2, n2, o2, i2) {
            return (0.035 * e2 + 576e-11 * t2 * n2 + o2 / 1e3 * (697e-8 + 324e-7) + 4.16 + 8.52 + 1.15) * i2 / 36e5;
          }, p.prototype.getMaxQualityAvailable = function(e2) {
            e2 = e2.sort(function(e3, t2) {
              return e3.bitrate - t2.bitrate;
            });
            return e2[e2.length - 1];
          }, p);
          function p(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this, e2 = (t2.savedEmissons = 0, new l.EcoModeToggleButton()), n2 = new c.Label({ text: s.i18n.getLocalizer("ecoMode.title"), for: e2.getConfig().id, id: "ecomodelabel" });
            return t2.emissionsSavedLabel = new c.Label({ text: "".concat(t2.savedEmissons.toFixed(4), " gCO2"), cssClass: "ui-label-savedEnergy" }), t2.ecoModeToggleButtonItem = new u.SettingsPanelItem(n2, e2), t2.ecoModeSavedEmissionsItem = new u.SettingsPanelItem("Saved Emissions", t2.emissionsSavedLabel, { hidden: true }), t2.addComponent(t2.ecoModeToggleButtonItem), t2.addComponent(t2.ecoModeSavedEmissionsItem), e2.onToggleOn.subscribe(function() {
              t2.ecoModeSavedEmissionsItem.show(), t2.onToggleCallback();
            }), e2.onToggleOff.subscribe(function() {
              t2.ecoModeSavedEmissionsItem.hide(), t2.onToggleCallback();
            }), t2;
          }
          n.EcoModeContainer = e;
        }, { "../localization/i18n": 91, "./container": 19, "./ecomodetogglebutton": 22, "./label": 28, "./settingspanelitem": 46 }], 22: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.EcoModeToggleButton = void 0, e("./togglebutton")), a = e("../localization/i18n"), e = (i = s.ToggleButton, r(l, i), l.prototype.configure = function(t2, e2) {
            var n2 = this;
            i.prototype.configure.call(this, t2, e2), this.areAdaptationApisAvailable(t2) ? (this.onClick.subscribe(function() {
              n2.toggle();
            }), this.onToggleOn.subscribe(function() {
              n2.enableEcoMode(t2), t2.setVideoQuality("auto");
            }), this.onToggleOff.subscribe(function() {
              n2.disableEcoMode(t2);
            }), t2.on(t2.exports.PlayerEvent.VideoQualityChanged, function(e3) {
              "auto" !== e3.targetQuality.id && (n2.off(), n2.disableEcoMode(t2));
            })) : i.prototype.disable.call(this);
          }, l.prototype.areAdaptationApisAvailable = function(e2) {
            var t2 = Boolean(e2.adaptation.getConfig && "function" == typeof e2.adaptation.getConfig), n2 = Boolean(e2.adaptation.setConfig && "function" == typeof e2.adaptation.setConfig);
            return Boolean(e2.adaptation && t2 && n2);
          }, l.prototype.enableEcoMode = function(e2) {
            this.adaptationConfig = e2.adaptation.getConfig();
            var t2 = e2.getAvailableVideoQualities()[0].codec;
            t2.includes("avc") && e2.adaptation.setConfig({ resolution: { maxSelectableVideoHeight: 720 }, limitToPlayerSize: true }), (t2.includes("hvc") || t2.includes("hev")) && e2.adaptation.setConfig({ resolution: { maxSelectableVideoHeight: 1080 }, limitToPlayerSize: true }), (t2.includes("av1") || t2.includes("av01")) && e2.adaptation.setConfig({ resolution: { maxSelectableVideoHeight: 1440 }, limitToPlayerSize: true });
          }, l.prototype.disableEcoMode = function(e2) {
            e2.adaptation.setConfig(this.adaptationConfig);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this, n2 = { text: a.i18n.getLocalizer("ecoMode"), cssClass: "ui-ecomodetogglebutton", onClass: "on", offClass: "off", ariaLabel: a.i18n.getLocalizer("ecoMode") };
            return t2.config = t2.mergeConfig(e2, n2, t2.config), t2;
          }
          n.EcoModeToggleButton = e;
        }, { "../localization/i18n": 91, "./togglebutton": 74 }], 23: [function(e, t, n) {
          "use strict";
          var o, s, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.ErrorMessageOverlay = void 0, e("./container")), a = e("./label"), l = e("./tvnoisecanvas"), c = e("../errorutils"), u = e("../mobilev3playerapi"), e = (s = r.Container, i(p, s), p.prototype.configure = function(e2, n2) {
            var t2, o2 = this, i2 = (s.prototype.configure.call(this, e2, n2), this.getConfig()), r2 = function(e3, t3) {
              e3 = function(e4, t4) {
                if (!e4) return;
                if ("function" == typeof e4) return e4(t4);
                if (e4[t4.code]) return "string" == typeof (e4 = e4[t4.code]) ? e4 : e4(t4);
              }(n2.getConfig().errorMessages || i2.messages, e3);
              o2.display(t3 = e3 ? e3 : t3);
            };
            (0, u.isMobileV3PlayerAPI)(e2) ? (e2.on(u.MobileV3PlayerEvent.PlayerError, t2 = function(e3) {
              var t3 = c.ErrorUtils.defaultMobileV3ErrorMessageTranslator(e3);
              r2(e3, t3);
            }), e2.on(u.MobileV3PlayerEvent.SourceError, t2)) : e2.on(e2.exports.PlayerEvent.Error, function(e3) {
              var t3 = c.ErrorUtils.defaultWebErrorMessageTranslator(e3);
              r2(e3, t3);
            }), e2.on(e2.exports.PlayerEvent.SourceLoaded, function(e3) {
              o2.isShown() && o2.clear();
            });
          }, p.prototype.display = function(e2) {
            this.errorLabel.setText(e2), this.tvNoiseBackground.start(), this.show();
          }, p.prototype.clear = function() {
            this.errorLabel.setText(""), this.tvNoiseBackground.stop(), this.hide();
          }, p.prototype.release = function() {
            s.prototype.release.call(this), this.clear();
          }, p);
          function p(e2) {
            var t2 = s.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.errorLabel = new a.Label({ cssClass: "ui-errormessage-label" }), t2.tvNoiseBackground = new l.TvNoiseCanvas(), t2.config = t2.mergeConfig(e2, { cssClass: "ui-errormessage-overlay", components: [t2.tvNoiseBackground, t2.errorLabel], hidden: true, role: "status" }, t2.config), t2;
          }
          n.ErrorMessageOverlay = e;
        }, { "../errorutils": 85, "../mobilev3playerapi": 97, "./container": 19, "./label": 28, "./tvnoisecanvas": 75 }], 24: [function(e, t, n) {
          "use strict";
          var o, s, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.FullscreenToggleButton = void 0, e("./togglebutton")), a = e("../localization/i18n"), e = (s = r.ToggleButton, i(l, s), l.prototype.configure = function(t2, e2) {
            function n2() {
              t2.getViewMode() === t2.exports.ViewMode.Fullscreen ? i2.on() : i2.off();
            }
            function o2() {
              r2() ? i2.show() : i2.hide();
            }
            var i2 = this, r2 = (s.prototype.configure.call(this, t2, e2), function() {
              return t2.isViewModeAvailable(t2.exports.ViewMode.Fullscreen);
            });
            t2.on(t2.exports.PlayerEvent.ViewModeChanged, n2), t2.exports.PlayerEvent.ViewModeAvailabilityChanged && t2.on(t2.exports.PlayerEvent.ViewModeAvailabilityChanged, o2), e2.getConfig().events.onUpdated.subscribe(o2), this.onClick.subscribe(function() {
              var e3;
              r2() ? (e3 = t2.getViewMode() === t2.exports.ViewMode.Fullscreen ? t2.exports.ViewMode.Inline : t2.exports.ViewMode.Fullscreen, t2.setViewMode(e3)) : console && console.log("Fullscreen unavailable");
            }), o2(), n2();
          }, l);
          function l(e2) {
            var t2 = s.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-fullscreentogglebutton", text: a.i18n.getLocalizer("fullscreen") }, t2.config), t2;
          }
          n.FullscreenToggleButton = e;
        }, { "../localization/i18n": 91, "./togglebutton": 74 }], 25: [function(e, t, n) {
          "use strict";
          var o, c, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.HugePlaybackToggleButton = void 0, e("./playbacktogglebutton")), s = e("../dom"), a = e("../localization/i18n"), e = (c = r.PlaybackToggleButton, i(l, c), l.prototype.configure = function(t2, e2) {
            function n2() {
              t2.isPlaying() || r2.isPlayInitiated ? t2.pause("ui") : t2.play("ui");
            }
            function o2() {
              t2.getViewMode() === t2.exports.ViewMode.Fullscreen ? t2.setViewMode(t2.exports.ViewMode.Inline) : t2.setViewMode(t2.exports.ViewMode.Fullscreen);
            }
            function i2() {
              r2.setTransitionAnimationsEnabled(false), r2.onToggle.subscribeOnce(function() {
                r2.setTransitionAnimationsEnabled(true);
              });
            }
            var r2 = this, s2 = (c.prototype.configure.call(this, t2, e2, false), "boolean" == typeof e2.getConfig().enterFullscreenOnInitialPlayback && (this.config.enterFullscreenOnInitialPlayback = e2.getConfig().enterFullscreenOnInitialPlayback), true), a2 = 0, l2 = 0, e2 = (this.onClick.subscribe(function() {
              var e3;
              s2 ? (n2(), r2.config.enterFullscreenOnInitialPlayback && t2.setViewMode(t2.exports.ViewMode.Fullscreen)) : (e3 = Date.now()) - a2 < 200 ? (o2(), l2 = e3) : e3 - a2 < 500 ? (o2(), n2(), l2 = e3) : (a2 = e3, setTimeout(function() {
                200 < Date.now() - l2 && n2();
              }, 200));
            }), t2.on(t2.exports.PlayerEvent.Play, function() {
              s2 = false;
            }), t2.on(t2.exports.PlayerEvent.Warning, function(e3) {
              e3.code === t2.exports.WarningCode.PLAYBACK_COULD_NOT_BE_STARTED && (s2 = true);
            }), i2(), t2.getConfig().playback && Boolean(t2.getConfig().playback.autoplay)), e2 = !t2.getSource() && e2;
            (t2.isPlaying() || e2) && (this.on(), i2(), t2.on(t2.exports.PlayerEvent.Warning, function(e3) {
              e3.code === t2.exports.WarningCode.PLAYBACK_COULD_NOT_BE_STARTED && i2();
            }));
          }, l.prototype.toDomElement = function() {
            var e2 = c.prototype.toDomElement.call(this);
            return e2.append(new s.DOM("div", { class: this.prefixCss("image") })), e2;
          }, l.prototype.setTransitionAnimationsEnabled = function(e2) {
            var t2 = this.prefixCss("no-transition-animations");
            e2 ? this.getDomElement().removeClass(t2) : this.getDomElement().hasClass(t2) || this.getDomElement().addClass(t2);
          }, l);
          function l(e2) {
            var t2 = c.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-hugeplaybacktogglebutton", text: a.i18n.getLocalizer("playPause"), role: "button" }, t2.config), t2;
          }
          n.HugePlaybackToggleButton = e;
        }, { "../dom": 84, "../localization/i18n": 91, "./playbacktogglebutton": 35 }], 26: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.HugeReplayButton = void 0, e("./button")), a = e("../dom"), l = e("../localization/i18n"), e = (i = s.Button, r(c, i), c.prototype.configure = function(e2, t2) {
            i.prototype.configure.call(this, e2, t2), this.onClick.subscribe(function() {
              e2.play("ui");
            });
          }, c.prototype.toDomElement = function() {
            var e2 = i.prototype.toDomElement.call(this);
            return e2.append(new a.DOM("div", { class: this.prefixCss("image") })), e2;
          }, c);
          function c(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-hugereplaybutton", text: l.i18n.getLocalizer("replay") }, t2.config), t2;
          }
          n.HugeReplayButton = e;
        }, { "../dom": 84, "../localization/i18n": 91, "./button": 12 }], 27: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.ItemSelectionList = void 0, e("./listselector")), l = e("../dom"), c = e("../localization/i18n");
          n.ItemSelectionList = (i = s.ListSelector, r(u, i), u.prototype.isActive = function() {
            return 1 < this.items.length;
          }, u.prototype.toDomElement = function() {
            var e2 = new l.DOM("ul", { id: this.config.id, class: this.getCssClasses() }, this);
            return this.listElement = e2, this.updateDomItems(), e2;
          }, u.prototype.updateDomItems = function(n2) {
            for (var o2 = this, i2 = (void 0 === n2 && (n2 = null), this.listElement.empty(), null), r2 = function(e3) {
              e3.addClass(o2.prefixCss(u.CLASS_SELECTED));
            }, s2 = function(e3) {
              e3.removeClass(o2.prefixCss(u.CLASS_SELECTED));
            }, a = this, e2 = 0, t2 = this.items; e2 < t2.length; e2++) !function(e3) {
              var t3 = new l.DOM("li", { type: "li", class: a.prefixCss("ui-selectionlistitem") }).append(new l.DOM("a", {}).html(c.i18n.performLocalization(e3.label)));
              i2 || null != n2 && String(n2) !== e3.key || (i2 = t3), t3.on("click", function() {
                i2 && s2(i2), r2(i2 = t3), o2.onItemSelectedEvent(e3.key, false);
              }), i2 && r2(i2), a.listElement.append(t3);
            }(t2[e2]);
          }, u.prototype.onItemAddedEvent = function(e2) {
            i.prototype.onItemAddedEvent.call(this, e2), this.updateDomItems(this.selectedItem);
          }, u.prototype.onItemRemovedEvent = function(e2) {
            i.prototype.onItemRemovedEvent.call(this, e2), this.updateDomItems(this.selectedItem);
          }, u.prototype.onItemSelectedEvent = function(e2, t2) {
            void 0 === t2 && (t2 = true), i.prototype.onItemSelectedEvent.call(this, e2), t2 && this.updateDomItems(e2);
          }, u.CLASS_SELECTED = "selected", u);
          function u(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { tag: "ul", cssClass: "ui-itemselectionlist" }, t2.config), t2;
          }
        }, { "../dom": 84, "../localization/i18n": 91, "./listselector": 30 }], 28: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.Label = void 0, e("./component")), a = e("../dom"), l = e("../eventdispatcher"), c = e("../localization/i18n"), e = (i = s.Component, r(u, i), u.prototype.toDomElement = function() {
            var e2 = this, t2 = null != this.config.for ? "label" : "span", t2 = new a.DOM(t2, { id: this.config.id, for: this.config.for, class: this.getCssClasses() }, this).html(c.i18n.performLocalization(this.text));
            return t2.on("click", function() {
              e2.onClickEvent();
            }), t2;
          }, u.prototype.setText = function(e2) {
            e2 !== this.text && (this.text = e2, e2 = c.i18n.performLocalization(e2), this.getDomElement().html(e2), this.onTextChangedEvent(e2));
          }, u.prototype.getText = function() {
            return c.i18n.performLocalization(this.text);
          }, u.prototype.clearText = function() {
            this.getDomElement().html(""), this.onTextChangedEvent(null);
          }, u.prototype.isEmpty = function() {
            return !this.text;
          }, u.prototype.onClickEvent = function() {
            this.labelEvents.onClick.dispatch(this);
          }, u.prototype.onTextChangedEvent = function(e2) {
            this.labelEvents.onTextChanged.dispatch(this, e2);
          }, Object.defineProperty(u.prototype, "onClick", { get: function() {
            return this.labelEvents.onClick.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onTextChanged", { get: function() {
            return this.labelEvents.onTextChanged.getEvent();
          }, enumerable: false, configurable: true }), u);
          function u(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.labelEvents = { onClick: new l.EventDispatcher(), onTextChanged: new l.EventDispatcher() }, t2.config = t2.mergeConfig(e2, { cssClass: "ui-label" }, t2.config), t2.text = t2.config.text, t2;
          }
          n.Label = e;
        }, { "../dom": 84, "../eventdispatcher": 86, "../localization/i18n": 91, "./component": 18 }], 29: [function(e, t, n) {
          "use strict";
          var o, r, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.ListBox = void 0, e("./togglebutton")), a = e("./listselector"), l = e("../dom"), c = e("../arrayutils"), e = (r = a.ListSelector, i(u, r), u.prototype.configure = function(e2, t2) {
            this.onItemAdded.subscribe(this.addListBoxDomItem), this.onItemRemoved.subscribe(this.removeListBoxDomItem), this.onItemSelected.subscribe(this.refreshSelectedItem), r.prototype.configure.call(this, e2, t2);
          }, u.prototype.toDomElement = function() {
            var e2 = new l.DOM("div", { id: this.config.id, class: this.getCssClasses() }, this);
            return this.listBoxElement = e2, this.createListBoxDomItems(), this.refreshSelectedItem(), e2;
          }, u.prototype.createListBoxDomItems = function() {
            this.listBoxElement.empty(), this.components = [];
            for (var e2 = 0, t2 = this.items; e2 < t2.length; e2++) {
              var n2 = t2[e2];
              this.addListBoxDomItem(this, n2.key);
            }
          }, u.prototype.buildListBoxItemButton = function(e2) {
            return new g({ key: e2.key, text: e2.label, ariaLabel: e2.ariaLabel });
          }, u.prototype.getComponentForKey = function(t2) {
            return this.components.find(function(e2) {
              return t2 === e2.key;
            });
          }, u);
          function u(e2) {
            var i2 = r.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return i2.components = [], i2.removeListBoxDomItem = function(e3, t2) {
              t2 = i2.getComponentForKey(t2);
              t2 && (t2.getDomElement().remove(), c.ArrayUtils.remove(i2.components, t2));
            }, i2.addListBoxDomItem = function(e3, t2) {
              var n2, o2 = i2.getComponentForKey(t2), t2 = i2.getItemForKey(t2);
              o2 ? o2.setText(t2.label) : ((n2 = i2.buildListBoxItemButton(t2)).onClick.subscribe(function() {
                i2.handleSelectionChange(n2);
              }), i2.components.push(n2), i2.listBoxElement.append(n2.getDomElement()));
            }, i2.refreshSelectedItem = function() {
              for (var e3 = 0, t2 = i2.items; e3 < t2.length; e3++) {
                var n2 = t2[e3], n2 = i2.getComponentForKey(n2.key);
                n2 && (String(n2.key) === String(i2.selectedItem) ? n2.on() : n2.off());
              }
            }, i2.handleSelectionChange = function(e3) {
              i2.onItemSelectedEvent(e3.key);
            }, i2.config = i2.mergeConfig(e2, { cssClass: "ui-listbox" }, i2.config), i2;
          }
          n.ListBox = e;
          p = s.ToggleButton, i(f, p), Object.defineProperty(f.prototype, "key", { get: function() {
            return this.config.key;
          }, enumerable: false, configurable: true });
          var p, g = f;
          function f(e2) {
            var t2 = p.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-listbox-button", onClass: "selected", offClass: "" }, t2.config), t2;
          }
        }, { "../arrayutils": 1, "../dom": 84, "./listselector": 30, "./togglebutton": 74 }], 30: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = this && this.__assign || function() {
            return (s = Object.assign || function(e2) {
              for (var t2, n2 = 1, o2 = arguments.length; n2 < o2; n2++) for (var i2 in t2 = arguments[n2]) Object.prototype.hasOwnProperty.call(t2, i2) && (e2[i2] = t2[i2]);
              return e2;
            }).apply(this, arguments);
          }, a = (Object.defineProperty(n, "__esModule", { value: true }), n.ListSelector = void 0, e("./component")), l = e("../eventdispatcher"), c = e("../arrayutils"), u = e("../localization/i18n"), e = (i = a.Component, r(p, i), p.prototype.getItemIndex = function(e2) {
            for (var t2 = 0; t2 < this.items.length; t2++) if (this.items[t2].key === e2) return t2;
            return -1;
          }, p.prototype.getItems = function() {
            return this.items;
          }, p.prototype.hasItem = function(e2) {
            return -1 < this.getItemIndex(e2);
          }, p.prototype.addItem = function(t2, e2, n2, o2) {
            void 0 === n2 && (n2 = false), void 0 === o2 && (o2 = "");
            e2 = s({ key: t2, label: u.i18n.performLocalization(e2) }, o2 && { ariaLabel: o2 });
            this.config.filter && !this.config.filter(e2) || (this.config.translator && (e2.label = this.config.translator(e2)), this.removeItem(t2), !n2 || (o2 = this.items.findIndex(function(e3) {
              return e3.key > t2;
            })) < 0 ? this.items.push(e2) : this.items.splice(o2, 0, e2), this.onItemAddedEvent(t2));
          }, p.prototype.removeItem = function(e2) {
            var t2 = this.getItemIndex(e2);
            return -1 < t2 && (c.ArrayUtils.remove(this.items, this.items[t2]), this.onItemRemovedEvent(e2), true);
          }, p.prototype.selectItem = function(e2) {
            return e2 === this.selectedItem || -1 < this.getItemIndex(e2) && (this.selectedItem = e2, this.onItemSelectedEvent(e2), true);
          }, p.prototype.getSelectedItem = function() {
            return this.selectedItem;
          }, p.prototype.getItemForKey = function(t2) {
            return this.items.find(function(e2) {
              return e2.key === t2;
            });
          }, p.prototype.synchronizeItems = function(e2) {
            var t2 = this;
            e2.filter(function(e3) {
              return !t2.hasItem(e3.key);
            }).forEach(function(e3) {
              return t2.addItem(e3.key, e3.label, e3.sortedInsert, e3.ariaLabel);
            }), this.items.filter(function(t3) {
              return 0 === e2.filter(function(e3) {
                return e3.key === t3.key;
              }).length;
            }).forEach(function(e3) {
              return t2.removeItem(e3.key);
            });
          }, p.prototype.clearItems = function() {
            var e2 = this.items;
            this.items = [], this.selectedItem = null;
            for (var t2 = 0, n2 = e2; t2 < n2.length; t2++) {
              var o2 = n2[t2];
              this.onItemRemovedEvent(o2.key);
            }
          }, p.prototype.itemCount = function() {
            return Object.keys(this.items).length;
          }, p.prototype.onItemAddedEvent = function(e2) {
            this.listSelectorEvents.onItemAdded.dispatch(this, e2);
          }, p.prototype.onItemRemovedEvent = function(e2) {
            this.listSelectorEvents.onItemRemoved.dispatch(this, e2);
          }, p.prototype.onItemSelectedEvent = function(e2) {
            this.listSelectorEvents.onItemSelected.dispatch(this, e2);
          }, Object.defineProperty(p.prototype, "onItemAdded", { get: function() {
            return this.listSelectorEvents.onItemAdded.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(p.prototype, "onItemRemoved", { get: function() {
            return this.listSelectorEvents.onItemRemoved.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(p.prototype, "onItemSelected", { get: function() {
            return this.listSelectorEvents.onItemSelected.getEvent();
          }, enumerable: false, configurable: true }), p);
          function p(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.listSelectorEvents = { onItemAdded: new l.EventDispatcher(), onItemRemoved: new l.EventDispatcher(), onItemSelected: new l.EventDispatcher() }, t2.config = t2.mergeConfig(e2, { items: [], cssClass: "ui-listselector" }, t2.config), t2.items = t2.config.items, t2;
          }
          n.ListSelector = e;
        }, { "../arrayutils": 1, "../eventdispatcher": 86, "../localization/i18n": 91, "./component": 18 }], 31: [function(e, t, n) {
          "use strict";
          var o, s, a, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), e = (Object.defineProperty(n, "__esModule", { value: true }), n.MetadataLabel = n.MetadataLabelContent = void 0, e("./label")), r = ((r = s = n.MetadataLabelContent || (n.MetadataLabelContent = {}))[r.Title = 0] = "Title", r[r.Description = 1] = "Description", a = e.Label, i(l, a), l.prototype.configure = function(e2, t2) {
            function n2() {
              switch (i2.content) {
                case s.Title:
                  o2.setText(r2.metadata.title);
                  break;
                case s.Description:
                  o2.setText(r2.metadata.description);
              }
            }
            var o2 = this, i2 = (a.prototype.configure.call(this, e2, t2), this.getConfig()), r2 = t2.getConfig();
            n2(), e2.on(e2.exports.PlayerEvent.SourceUnloaded, function() {
              o2.setText(null);
            }), t2.getConfig().events.onUpdated.subscribe(n2);
          }, l);
          function l(e2) {
            var t2 = a.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["label-metadata", "label-metadata-" + s[e2.content].toLowerCase()] }, t2.config), t2;
          }
          n.MetadataLabel = r;
        }, { "./label": 28 }], 32: [function(e, t, n) {
          "use strict";
          var o, s, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.PictureInPictureToggleButton = void 0, e("./togglebutton")), a = e("../localization/i18n"), e = (s = r.ToggleButton, i(l, s), l.prototype.configure = function(t2, e2) {
            function n2() {
              t2.getViewMode() === t2.exports.ViewMode.PictureInPicture ? i2.on() : i2.off();
            }
            function o2() {
              r2() ? i2.show() : i2.hide();
            }
            var i2 = this, r2 = (s.prototype.configure.call(this, t2, e2), function() {
              return t2.isViewModeAvailable(t2.exports.ViewMode.PictureInPicture);
            });
            t2.on(t2.exports.PlayerEvent.ViewModeChanged, n2), t2.exports.PlayerEvent.ViewModeAvailabilityChanged && t2.on(t2.exports.PlayerEvent.ViewModeAvailabilityChanged, o2), e2.getConfig().events.onUpdated.subscribe(o2), this.onClick.subscribe(function() {
              var e3;
              r2() ? (e3 = t2.getViewMode() === t2.exports.ViewMode.PictureInPicture ? t2.exports.ViewMode.Inline : t2.exports.ViewMode.PictureInPicture, t2.setViewMode(e3)) : console && console.log("PIP unavailable");
            }), o2(), n2();
          }, l);
          function l(e2) {
            var t2 = s.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-piptogglebutton", text: a.i18n.getLocalizer("pictureInPicture") }, t2.config), t2;
          }
          n.PictureInPictureToggleButton = e;
        }, { "../localization/i18n": 91, "./togglebutton": 74 }], 33: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.PlaybackSpeedSelectBox = void 0, e("./selectbox")), a = e("../localization/i18n"), e = (i = s.SelectBox, r(l, i), l.prototype.configure = function(n2, e2) {
            function t2() {
              var e3 = n2.getPlaybackSpeed();
              o2.setSpeed(e3);
            }
            var o2 = this;
            i.prototype.configure.call(this, n2, e2), this.addDefaultItems(), this.onItemSelected.subscribe(function(e3, t3) {
              n2.setPlaybackSpeed(parseFloat(t3)), o2.selectItem(t3);
            });
            n2.on(n2.exports.PlayerEvent.PlaybackSpeedChanged, t2), e2.getConfig().events.onUpdated.subscribe(t2);
          }, l.prototype.setSpeed = function(e2) {
            this.selectItem(String(e2)) || (this.clearItems(), this.addDefaultItems([e2]), this.selectItem(String(e2)));
          }, l.prototype.addDefaultItems = function(e2) {
            var t2 = this;
            this.defaultPlaybackSpeeds.concat(e2 = void 0 === e2 ? [] : e2).sort().forEach(function(e3) {
              1 !== e3 ? t2.addItem(String(e3), "".concat(e3, "x")) : t2.addItem(String(e3), a.i18n.getLocalizer("normal"));
            });
          }, l.prototype.clearItems = function() {
            this.items = [], this.selectedItem = null;
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.defaultPlaybackSpeeds = [0.25, 0.5, 1, 1.5, 2], t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-playbackspeedselectbox"] }, t2.config), t2;
          }
          n.PlaybackSpeedSelectBox = e;
        }, { "../localization/i18n": 91, "./selectbox": 44 }], 34: [function(e, t, n) {
          "use strict";
          var o, i, d, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.PlaybackTimeLabel = n.PlaybackTimeLabelMode = void 0, e("./label")), h = e("../playerutils"), y = e("../stringutils"), m = e("../localization/i18n"), e = ((e = i = n.PlaybackTimeLabelMode || (n.PlaybackTimeLabelMode = {}))[e.CurrentTime = 0] = "CurrentTime", e[e.TotalTime = 1] = "TotalTime", e[e.CurrentAndTotalTime = 2] = "CurrentAndTotalTime", e[e.RemainingTime = 3] = "RemainingTime", d = s.Label, r(a, d), a.prototype.configure = function(n2, e2) {
            function t2() {
              l = 0, o2.getDomElement().css({ "min-width": null }), g();
            }
            var o2 = this, i2 = (d.prototype.configure.call(this, n2, e2), this.getConfig()), r2 = false, s2 = this.prefixCss("ui-playbacktimelabel-live"), a2 = this.prefixCss("ui-playbacktimelabel-live-edge"), l = 0, c = function() {
              n2.timeShift(0);
            }, u = function() {
              var e3, t3;
              r2 && (e3 = n2.getTimeShift() < 0, t3 = n2.getMaxTimeShift() < 0, e3 || n2.isPaused() && t3 ? o2.getDomElement().removeClass(a2) : o2.getDomElement().addClass(a2));
            }, p = function() {
              r2 || n2.getDuration() === 1 / 0 || o2.setTime(h.PlayerUtils.getCurrentTimeRelativeToSeekableRange(n2), n2.getDuration());
              var e3 = o2.getDomElement().width();
              l < e3 && (l = e3, o2.getDomElement().css({ "min-width": l + "px" }));
            }, g = function() {
              o2.timeFormat = 3600 <= Math.abs(n2.isLive() ? n2.getMaxTimeShift() : n2.getDuration()) ? y.StringUtils.FORMAT_HHMMSS : y.StringUtils.FORMAT_MMSS, p();
            }, f = new h.PlayerUtils.LiveStreamDetector(n2, e2);
            f.onLiveChanged.subscribe(function(e3, t3) {
              r2 = t3.live, p(), g(), (r2 = n2.isLive()) ? (o2.getDomElement().addClass(s2), o2.setText(m.i18n.getLocalizer("live")), i2.hideInLivePlayback && o2.hide(), o2.onClick.subscribe(c), u()) : (o2.getDomElement().removeClass(s2), o2.getDomElement().removeClass(a2), o2.show(), o2.onClick.unsubscribe(c));
            }), f.detect(), n2.on(n2.exports.PlayerEvent.TimeChanged, p), n2.on(n2.exports.PlayerEvent.Ready, g), n2.on(n2.exports.PlayerEvent.Seeked, p), n2.on(n2.exports.PlayerEvent.TimeShift, u), n2.on(n2.exports.PlayerEvent.TimeShifted, u), n2.on(n2.exports.PlayerEvent.Playing, u), n2.on(n2.exports.PlayerEvent.Paused, u), n2.on(n2.exports.PlayerEvent.StallStarted, u), n2.on(n2.exports.PlayerEvent.StallEnded, u);
            e2.getConfig().events.onUpdated.subscribe(t2), t2();
          }, a.prototype.setTime = function(e2, t2) {
            var n2 = y.StringUtils.secondsToTime(e2, this.timeFormat), o2 = y.StringUtils.secondsToTime(t2, this.timeFormat);
            switch (this.config.timeLabelMode) {
              case i.CurrentTime:
                this.setText("".concat(n2));
                break;
              case i.TotalTime:
                this.setText("".concat(o2));
                break;
              case i.CurrentAndTotalTime:
                this.setText("".concat(n2, " / ").concat(o2));
                break;
              case i.RemainingTime:
                this.setText("".concat(y.StringUtils.secondsToTime(t2 - e2, this.timeFormat)));
            }
          }, a.prototype.setTimeFormat = function(e2) {
            this.timeFormat = e2;
          }, a);
          function a(e2) {
            var t2 = d.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-playbacktimelabel", timeLabelMode: i.CurrentAndTotalTime, hideInLivePlayback: false }, t2.config), t2;
          }
          n.PlaybackTimeLabel = e;
        }, { "../localization/i18n": 91, "../playerutils": 98, "../stringutils": 111, "./label": 28 }], 35: [function(e, t, n) {
          "use strict";
          var o, u, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.PlaybackToggleButton = void 0, e("./togglebutton")), p = e("../playerutils"), s = e("../localization/i18n");
          n.PlaybackToggleButton = (u = r.ToggleButton, i(g, u), g.prototype.configure = function(t2, e2, n2) {
            function o2() {
              s2 || (t2.isPlaying() || r2.isPlayInitiated ? r2.on() : r2.off());
            }
            function i2() {
              t2.isLive() && !p.PlayerUtils.isTimeShiftAvailable(t2) ? r2.getDomElement().addClass(r2.prefixCss(g.CLASS_STOPTOGGLE)) : r2.getDomElement().removeClass(r2.prefixCss(g.CLASS_STOPTOGGLE));
            }
            var r2 = this, s2 = (void 0 === n2 && (n2 = true), u.prototype.configure.call(this, t2, e2), "boolean" == typeof e2.getConfig().enterFullscreenOnInitialPlayback && (this.config.enterFullscreenOnInitialPlayback = e2.getConfig().enterFullscreenOnInitialPlayback), false), a = true, l = (t2.on(t2.exports.PlayerEvent.Play, function(e3) {
              r2.isPlayInitiated = true, a = false, o2();
            }), t2.on(t2.exports.PlayerEvent.Paused, function(e3) {
              r2.isPlayInitiated = false, o2();
            }), t2.on(t2.exports.PlayerEvent.Playing, function(e3) {
              r2.isPlayInitiated = false, o2();
            }), t2.on(t2.exports.PlayerEvent.SourceLoaded, o2), e2.getConfig().events.onUpdated.subscribe(o2), t2.on(t2.exports.PlayerEvent.SourceUnloaded, o2), t2.on(t2.exports.PlayerEvent.PlaybackFinished, o2), t2.on(t2.exports.PlayerEvent.CastStarted, o2), t2.on(t2.exports.PlayerEvent.Warning, function(e3) {
              e3.code === t2.exports.WarningCode.PLAYBACK_COULD_NOT_BE_STARTED && (r2.isPlayInitiated = false, a = true, r2.off());
            }), new p.PlayerUtils.TimeShiftAvailabilityDetector(t2)), c = new p.PlayerUtils.LiveStreamDetector(t2, e2);
            l.onTimeShiftAvailabilityChanged.subscribe(i2), c.onLiveChanged.subscribe(i2), l.detect(), c.detect(), n2 && this.onClick.subscribe(function() {
              t2.isPlaying() || r2.isPlayInitiated ? t2.pause("ui") : (t2.play("ui"), a && r2.config.enterFullscreenOnInitialPlayback && t2.setViewMode(t2.exports.ViewMode.Fullscreen));
            }), e2.onSeek.subscribe(function() {
              s2 = true;
            }), e2.onSeeked.subscribe(function() {
              s2 = false;
            }), o2();
          }, g.CLASS_STOPTOGGLE = "stoptoggle", g);
          function g(e2) {
            var t2 = u.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-playbacktogglebutton", text: s.i18n.getLocalizer("play"), onAriaLabel: s.i18n.getLocalizer("pause"), offAriaLabel: s.i18n.getLocalizer("play") }, t2.config), t2.isPlayInitiated = false, t2;
          }
        }, { "../localization/i18n": 91, "../playerutils": 98, "./togglebutton": 74 }], 36: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.PlaybackToggleOverlay = void 0, e("./container")), a = e("./hugeplaybacktogglebutton"), e = (i = s.Container, r(l, i), l);
          function l(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.playbackToggleButton = new a.HugePlaybackToggleButton({ enterFullscreenOnInitialPlayback: Boolean(e2.enterFullscreenOnInitialPlayback) }), t2.config = t2.mergeConfig(e2, { cssClass: "ui-playbacktoggle-overlay", components: [t2.playbackToggleButton] }, t2.config), t2;
          }
          n.PlaybackToggleOverlay = e;
        }, { "./container": 19, "./hugeplaybacktogglebutton": 25 }], 37: [function(e, t, n) {
          "use strict";
          var o, a, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.QuickSeekButton = void 0, e("./button")), s = e("../localization/i18n"), l = e("../playerutils"), e = (a = r.Button, i(c, a), c.prototype.configure = function(n2, e2) {
            function o2(e3, t3) {
              e3 && !t3 ? s2.hide() : s2.show();
            }
            var i2, r2, s2 = this, t2 = (a.prototype.configure.call(this, n2, e2), this.player = n2, new l.PlayerUtils.TimeShiftAvailabilityDetector(n2)), e2 = (t2.onTimeShiftAvailabilityChanged.subscribe(function(e3, t3) {
              r2 = t3.timeShiftAvailable, o2(i2, r2);
            }), new l.PlayerUtils.LiveStreamDetector(n2, e2));
            e2.onLiveChanged.subscribe(function(e3, t3) {
              i2 = t3.live, o2(i2, r2);
            }), t2.detect(), e2.detect(), this.onClick.subscribe(function() {
              var e3, t3;
              i2 && !r2 || i2 && 0 < s2.config.seekSeconds && 0 === n2.getTimeShift() || (e3 = (null !== s2.currentSeekTarget ? s2.currentSeekTarget : i2 ? n2.getTimeShift() : n2.getCurrentTime()) + s2.config.seekSeconds, i2 ? (t3 = l.PlayerUtils.clampValueToRange(e3, n2.getMaxTimeShift(), 0), n2.timeShift(t3)) : (t3 = l.PlayerUtils.clampValueToRange(e3, 0, n2.getDuration()), n2.seek(t3)));
            }), this.player.on(this.player.exports.PlayerEvent.Seek, this.onSeek), this.player.on(this.player.exports.PlayerEvent.Seeked, this.onSeekedOrTimeShifted), this.player.on(this.player.exports.PlayerEvent.TimeShift, this.onTimeShift), this.player.on(this.player.exports.PlayerEvent.TimeShifted, this.onSeekedOrTimeShifted);
          }, c.prototype.release = function() {
            this.player.off(this.player.exports.PlayerEvent.Seek, this.onSeek), this.player.off(this.player.exports.PlayerEvent.Seeked, this.onSeekedOrTimeShifted), this.player.off(this.player.exports.PlayerEvent.TimeShift, this.onTimeShift), this.player.off(this.player.exports.PlayerEvent.TimeShifted, this.onSeekedOrTimeShifted), this.currentSeekTarget = null, this.player = null;
          }, c);
          function c(e2) {
            var t2 = a.call(this, e2 = void 0 === e2 ? {} : e2) || this, e2 = (t2.onSeek = function(e3) {
              t2.currentSeekTarget = e3.seekTarget;
            }, t2.onSeekedOrTimeShifted = function() {
              t2.currentSeekTarget = null;
            }, t2.onTimeShift = function(e3) {
              t2.currentSeekTarget = t2.player.getTimeShift() + (e3.target - e3.position);
            }, t2.currentSeekTarget = null, t2.config = t2.mergeConfig(e2, { seekSeconds: -10, cssClass: "ui-quickseekbutton" }, t2.config), t2.config.seekSeconds < 0 ? "rewind" : "forward");
            return t2.config.text = t2.config.text || s.i18n.getLocalizer("quickseek.".concat(e2)), t2.config.ariaLabel = t2.config.ariaLabel || s.i18n.getLocalizer("quickseek.".concat(e2), { seekSeconds: Math.abs(t2.config.seekSeconds) }), t2.getDomElement().data(t2.prefixCss("seek-direction"), e2), t2;
          }
          n.QuickSeekButton = e;
        }, { "../localization/i18n": 91, "../playerutils": 98, "./button": 12 }], 38: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.RecommendationOverlay = void 0, e("./container")), a = e("./component"), l = e("../dom"), c = e("../stringutils"), u = e("./hugereplaybutton"), e = (i = s.Container, r(p, i), p.prototype.configure = function(e2, r2) {
            function t2() {
              a2();
              var e3 = r2.getConfig().recommendations;
              if (0 < e3.length) {
                for (var t3 = 1, n2 = 0, o2 = e3; n2 < o2.length; n2++) {
                  var i2 = o2[n2];
                  s2.addComponent(new f({ itemConfig: i2, cssClasses: ["recommendation-item-" + t3++] }));
                }
                s2.updateComponents(), s2.getDomElement().addClass(s2.prefixCss("recommendations"));
              }
            }
            var s2 = this, a2 = (i.prototype.configure.call(this, e2, r2), function() {
              for (var e3 = 0, t3 = s2.getComponents().slice(); e3 < t3.length; e3++) {
                var n2 = t3[e3];
                n2 instanceof f && s2.removeComponent(n2);
              }
              s2.updateComponents(), s2.getDomElement().removeClass(s2.prefixCss("recommendations"));
            });
            r2.getConfig().events.onUpdated.subscribe(t2), e2.on(e2.exports.PlayerEvent.SourceUnloaded, function() {
              a2(), s2.hide();
            }), e2.on(e2.exports.PlayerEvent.PlaybackFinished, function() {
              s2.show();
            }), e2.on(e2.exports.PlayerEvent.Play, function() {
              s2.hide();
            }), t2();
          }, p);
          function p(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.replayButton = new u.HugeReplayButton(), t2.config = t2.mergeConfig(e2, { cssClass: "ui-recommendation-overlay", hidden: true, components: [t2.replayButton] }, t2.config), t2;
          }
          n.RecommendationOverlay = e;
          g = a.Component, r(d, g), d.prototype.toDomElement = function() {
            var e2 = this.config.itemConfig, t2 = new l.DOM("a", { id: this.config.id, class: this.getCssClasses(), href: e2.url }, this).css({ "background-image": "url(".concat(e2.thumbnail, ")") }), n2 = new l.DOM("div", { class: this.prefixCss("background") }), n2 = (t2.append(n2), new l.DOM("span", { class: this.prefixCss("title") }).append(new l.DOM("span", { class: this.prefixCss("innertitle") }).html(e2.title))), n2 = (t2.append(n2), new l.DOM("span", { class: this.prefixCss("duration") }).append(new l.DOM("span", { class: this.prefixCss("innerduration") }).html(e2.duration ? c.StringUtils.secondsToTime(e2.duration) : "")));
            return t2.append(n2), t2;
          };
          var g, f = d;
          function d(e2) {
            var t2 = g.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-recommendation-item", itemConfig: null }, t2.config), t2;
          }
        }, { "../dom": 84, "../stringutils": 111, "./component": 18, "./container": 19, "./hugereplaybutton": 26 }], 39: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.ReplayButton = void 0, e("./button")), a = e("../localization/i18n"), l = e("../playerutils"), e = (i = s.Button, r(c, i), c.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), e2.isLive() && this.hide(), new l.PlayerUtils.LiveStreamDetector(e2, t2).onLiveChanged.subscribe(function(e3, t3) {
              t3.live ? n2.hide() : n2.show();
            }), this.onClick.subscribe(function() {
              e2.hasEnded() ? e2.play("ui") : e2.seek(0);
            });
          }, c);
          function c(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-replaybutton", text: a.i18n.getLocalizer("replay"), ariaLabel: a.i18n.getLocalizer("replay") }, t2.config), t2;
          }
          n.ReplayButton = e;
        }, { "../localization/i18n": 91, "../playerutils": 98, "./button": 12 }], 40: [function(e, t, n) {
          "use strict";
          var o, d, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), h = (Object.defineProperty(n, "__esModule", { value: true }), n.SeekBar = void 0, e("./../groupplaybackapi")), r = e("./component"), a = e("../dom"), s = e("../eventdispatcher"), l = e("../timeout"), y = e("../playerutils"), c = e("../stringutils"), m = e("./seekbarcontroller"), u = e("../localization/i18n"), p = e("../browserutils"), g = e("./timelinemarkershandler"), f = e("./seekbarbufferlevel");
          n.SeekBar = (d = r.Component, i(b, d), b.prototype.initialize = function() {
            d.prototype.initialize.call(this), this.hasLabel() && this.getLabel().initialize();
          }, b.prototype.setAriaSliderMinMax = function(e2, t2) {
            this.getDomElement().attr("aria-valuemin", e2), this.getDomElement().attr("aria-valuemax", t2);
          }, b.prototype.setAriaSliderValues = function() {
            var e2, t2;
            this.seekBarType === m.SeekBarType.Live ? (t2 = Math.ceil(this.player.getTimeShift()).toString(), e2 = "".concat(u.i18n.performLocalization(u.i18n.getLocalizer("seekBar.timeshift")), " ").concat(u.i18n.performLocalization(u.i18n.getLocalizer("seekBar.value")), ": ").concat(t2), this.getDomElement().attr("aria-valuenow", t2), this.getDomElement().attr("aria-valuetext", e2), this.config.addCurrentTimeToAriaLabel && this.getDomElement().attr("aria-label", "".concat(u.i18n.performLocalization(this.config.ariaLabel), ": ").concat(e2))) : this.seekBarType === m.SeekBarType.Vod && (t2 = "".concat(c.StringUtils.secondsToText(this.player.getCurrentTime()), " ").concat(u.i18n.performLocalization(u.i18n.getLocalizer("seekBar.durationText")), " ").concat(c.StringUtils.secondsToText(this.player.getDuration())), this.getDomElement().attr("aria-valuenow", Math.floor(this.player.getCurrentTime()).toString()), this.getDomElement().attr("aria-valuetext", t2), this.config.addCurrentTimeToAriaLabel) && this.getDomElement().attr("aria-label", "".concat(u.i18n.performLocalization(this.config.ariaLabel), ": ").concat(c.StringUtils.secondsToText(this.player.getCurrentTime())));
          }, b.prototype.getPlaybackPositionPercentage = function() {
            return this.player.isLive() ? 100 - 100 / this.player.getMaxTimeShift() * this.player.getTimeShift() : 100 / this.player.getDuration() * this.getRelativeCurrentTime();
          }, b.prototype.updateBufferLevel = function(e2) {
            e2 = this.player.isLive() ? 100 : e2 + (0, f.getMinBufferLevel)(this.player);
            this.setBufferPosition(e2);
          }, b.prototype.configure = function(o2, n2, e2) {
            var i2, r2, t2, s2, a2, l2, c2, u2, p2, g2, f2 = this;
            void 0 === e2 && (e2 = true), d.prototype.configure.call(this, o2, n2), this.player = o2, this.uiManager = n2, this.setPosition(this.seekBarBackdrop, 100), new m.SeekBarController(this.config.keyStepIncrements, o2, n2.getConfig().volumeController).setSeekBarControls(this.getDomElement(), function() {
              return f2.seekBarType;
            }), e2 ? (n2.onControlsShow.subscribe(function() {
              f2.isUiShown = true, !f2.smoothPlaybackPositionUpdater || o2.isLive() || f2.smoothPlaybackPositionUpdater.isActive() || (a2(null, true), f2.smoothPlaybackPositionUpdater.start());
            }), n2.onControlsHide.subscribe(function() {
              f2.isUiShown = false, f2.smoothPlaybackPositionUpdater && f2.smoothPlaybackPositionUpdater.isActive() && f2.smoothPlaybackPositionUpdater.clear();
            }), t2 = r2 = i2 = false, a2 = function(e3, t3) {
              var n3;
              void 0 === e3 && (e3 = null), void 0 === t3 && (t3 = false), f2.isUserSeeking || (n3 = f2.getPlaybackPositionPercentage(), f2.updateBufferLevel(n3), r2 && e3 && e3.type === o2.exports.PlayerEvent.SegmentRequestFinished && n3 !== f2.playbackPositionPercentage && (n3 = f2.playbackPositionPercentage), o2.isLive() ? 0 === o2.getMaxTimeShift() ? f2.setPlaybackPosition(100) : (f2.isSeeking() || f2.setPlaybackPosition(n3), f2.setAriaSliderMinMax(o2.getMaxTimeShift().toString(), "0")) : (e3 = f2.config.smoothPlaybackPositionUpdateIntervalMs === b.SMOOTH_PLAYBACK_POSITION_UPDATE_DISABLED || t3 || o2.isPaused(), t3 = o2.isPaused() === o2.isPlaying(), !e3 && !t3 || f2.isSeeking() || f2.setPlaybackPosition(n3), f2.setAriaSliderMinMax("0", o2.getDuration().toString())), f2.isUiShown && f2.setAriaSliderValues());
            }, o2.on(o2.exports.PlayerEvent.Ready, a2), o2.on(o2.exports.PlayerEvent.TimeChanged, a2), o2.on(o2.exports.PlayerEvent.StallEnded, a2), o2.on(o2.exports.PlayerEvent.TimeShifted, a2), o2.on(o2.exports.PlayerEvent.SegmentRequestFinished, a2), this.configureLivePausedTimeshiftUpdater(o2, n2, a2), e2 = function() {
              t2 = true, f2.setSeeking(true), r2 = false;
            }, g2 = function(e3) {
              void 0 === e3 && (e3 = null), t2 = false, f2.setSeeking(false), a2(e3, true);
            }, o2.on(o2.exports.PlayerEvent.Seek, e2), o2.on(o2.exports.PlayerEvent.Seeked, g2), o2.on(o2.exports.PlayerEvent.TimeShift, e2), o2.on(o2.exports.PlayerEvent.TimeShifted, g2), l2 = function(e3) {
              return !!e3.groupPlayback;
            }, this.onSeek.subscribe(function(e3) {
              f2.isUserSeeking = true, n2.onSeek.dispatch(e3), l2(o2) && o2.groupPlayback.hasJoined() && !s2 && (s2 = o2.groupPlayback.beginSuspension(h.GroupPlaybackSuspensionReason.UserIsScrubbing)), t2 || (i2 = o2.isPlaying()) && o2.pause("ui-seek");
            }), this.onSeekPreview.subscribe(function(e3, t3) {
              n2.onSeekPreview.dispatch(e3, t3), r2 = t3.scrubbing;
            }), "boolean" == typeof n2.getConfig().enableSeekPreview && (this.config.enableSeekPreview = n2.getConfig().enableSeekPreview), this.config.enableSeekPreview && this.onSeekPreview.subscribeRateLimited(this.seekWhileScrubbing, 200), this.onSeeked.subscribe(function(e3, t3) {
              f2.isUserSeeking = false, f2.seek(t3), n2.onSeeked.dispatch(e3), i2 && o2.play("ui-seek"), l2(o2) && o2.groupPlayback.hasJoined() && s2 && (e3 = f2.getTargetSeekPosition(t3), o2.groupPlayback.endSuspension(s2, { proposedPlaybackTime: e3 }), s2 = void 0);
            }), this.hasLabel() && this.getLabel().configure(o2, n2), c2 = false, u2 = false, p2 = function(e3, t3) {
              e3 && !t3 ? f2.hide() : f2.show(), a2(null, true), f2.refreshPlaybackPosition();
            }, (e2 = new y.PlayerUtils.LiveStreamDetector(o2, n2)).onLiveChanged.subscribe(function(e3, t3) {
              (c2 = t3.live) && null != f2.smoothPlaybackPositionUpdater ? (f2.smoothPlaybackPositionUpdater.clear(), f2.seekBarType = m.SeekBarType.Live) : f2.seekBarType = m.SeekBarType.Vod, p2(c2, u2);
            }), (g2 = new y.PlayerUtils.TimeShiftAvailabilityDetector(o2)).onTimeShiftAvailabilityChanged.subscribe(function(e3, t3) {
              u2 = t3.timeShiftAvailable, p2(c2, u2);
            }), e2.detect(), g2.detect(), o2.on(o2.exports.PlayerEvent.PlayerResized, function() {
              f2.refreshPlaybackPosition(), f2.uiBoundingRect = f2.uiManager.getUI().getDomElement().get(0).getBoundingClientRect();
            }), n2.onConfigured.subscribe(function() {
              f2.refreshPlaybackPosition();
            }), o2.on(o2.exports.PlayerEvent.SourceLoaded, function() {
              f2.refreshPlaybackPosition();
            }), n2.getConfig().events.onUpdated.subscribe(function() {
              a2();
            }), "number" == typeof n2.getConfig().seekbarSnappingRange && (this.config.snappingRange = n2.getConfig().seekbarSnappingRange), "boolean" == typeof n2.getConfig().seekbarSnappingEnabled && (this.config.snappingEnabled = n2.getConfig().seekbarSnappingEnabled), a2(), this.setBufferPosition(0), this.setSeekPosition(0), this.config.smoothPlaybackPositionUpdateIntervalMs !== b.SMOOTH_PLAYBACK_POSITION_UPDATE_DISABLED && this.configureSmoothPlaybackPositionUpdater(o2, n2), this.initializeTimelineMarkers(o2, n2)) : this.seekBarType = m.SeekBarType.Volume;
          }, b.prototype.initializeTimelineMarkers = function(e2, t2) {
            var n2 = this, o2 = { cssPrefix: this.config.cssPrefix, snappingRange: this.config.snappingRange };
            this.timelineMarkersHandler = new g.TimelineMarkersHandler(o2, function() {
              return n2.seekBar.width();
            }, this.seekBarMarkersContainer), this.timelineMarkersHandler.initialize(e2, t2);
          }, b.prototype.configureLivePausedTimeshiftUpdater = function(e2, t2, n2) {
            var o2 = this;
            this.pausedTimeshiftUpdater = new l.Timeout(1e3, n2, true), e2.on(e2.exports.PlayerEvent.Paused, function() {
              e2.isLive() && e2.getMaxTimeShift() < 0 && o2.pausedTimeshiftUpdater.start();
            }), e2.on(e2.exports.PlayerEvent.Play, function() {
              return o2.pausedTimeshiftUpdater.clear();
            });
          }, b.prototype.configureSmoothPlaybackPositionUpdater = function(t2, e2) {
            function n2() {
              t2.isLive() || (r2 = i2.getRelativeCurrentTime(), i2.smoothPlaybackPositionUpdater.start());
            }
            function o2() {
              i2.smoothPlaybackPositionUpdater.clear();
            }
            var i2 = this, r2 = 0, s2 = 0;
            this.smoothPlaybackPositionUpdater = new l.Timeout(50, function() {
              if (!i2.isSeeking()) {
                r2 += 0.05;
                try {
                  s2 = i2.getRelativeCurrentTime();
                } catch (e4) {
                  return void (e4 instanceof t2.exports.PlayerAPINotAvailableError && i2.smoothPlaybackPositionUpdater.clear());
                }
                var e3 = r2 - s2, e3 = (2 < Math.abs(e3) ? r2 = s2 : e3 <= -0.05 ? r2 += 0.05 : 0.05 <= e3 && (r2 -= 0.05), 100 / t2.getDuration() * r2);
                i2.setPlaybackPosition(e3);
              }
            }, true);
            t2.on(t2.exports.PlayerEvent.Play, n2), t2.on(t2.exports.PlayerEvent.Playing, n2), t2.on(t2.exports.PlayerEvent.Paused, o2), t2.on(t2.exports.PlayerEvent.PlaybackFinished, o2), t2.on(t2.exports.PlayerEvent.Seeked, function() {
              r2 = i2.getRelativeCurrentTime();
            }), t2.on(t2.exports.PlayerEvent.SourceUnloaded, o2), t2.isPlaying() && n2();
          }, b.prototype.getRelativeCurrentTime = function() {
            return y.PlayerUtils.getCurrentTimeRelativeToSeekableRange(this.player);
          }, b.prototype.release = function() {
            d.prototype.release.call(this), this.smoothPlaybackPositionUpdater && this.smoothPlaybackPositionUpdater.clear(), this.pausedTimeshiftUpdater && this.pausedTimeshiftUpdater.clear(), this.config.enableSeekPreview && this.onSeekPreview.unsubscribe(this.seekWhileScrubbing);
          }, b.prototype.toDomElement = function() {
            var n2 = this, e2 = (this.config.vertical && this.config.cssClasses.push("vertical"), new a.DOM("div", { id: this.config.id, class: this.getCssClasses(), role: "slider", "aria-label": u.i18n.performLocalization(this.config.ariaLabel), tabindex: this.config.tabIndex.toString() }, this)), t2 = new a.DOM("div", { class: this.prefixCss("seekbar") }), o2 = (this.seekBar = t2, new a.DOM("div", { class: this.prefixCss("seekbar-bufferlevel") })), o2 = (this.seekBarBufferPosition = o2, new a.DOM("div", { class: this.prefixCss("seekbar-playbackposition") })), o2 = (this.seekBarPlaybackPosition = o2, new a.DOM("div", { class: this.prefixCss("seekbar-playbackposition-marker") })), o2 = (this.seekBarPlaybackPositionMarker = o2, new a.DOM("div", { class: this.prefixCss("seekbar-seekposition") })), o2 = (this.seekBarSeekPosition = o2, new a.DOM("div", { class: this.prefixCss("seekbar-backdrop") })), o2 = (this.seekBarBackdrop = o2, new a.DOM("div", { class: this.prefixCss("seekbar-markers") })), i2 = (this.seekBarMarkersContainer = o2, t2.append(this.seekBarBackdrop, this.seekBarBufferPosition, this.seekBarSeekPosition, this.seekBarPlaybackPosition, this.seekBarMarkersContainer, this.seekBarPlaybackPositionMarker), false), r2 = function(e3) {
              e3.preventDefault(), null != n2.player.vr && e3.stopPropagation();
              var e3 = n2.getOffset(e3), t3 = 100 * e3, e3 = e3 * n2.seekBar.width();
              n2.setSeekPosition(t3), n2.setPlaybackPosition(t3), n2.onSeekPreviewEvent(t3, e3, true);
            }, s2 = function(e3) {
              e3.preventDefault(), new a.DOM(document).off("touchmove mousemove", r2), new a.DOM(document).off("touchend mouseup", s2);
              var t3, e3 = 100 * n2.getOffset(e3);
              n2.config.snappingEnabled && (e3 = (t3 = null == (t3 = n2.timelineMarkersHandler) ? void 0 : t3.getMarkerAtPosition(e3)) ? t3.position : e3), n2.setSeeking(false), i2 = false, n2.onSeekedEvent(e3);
            };
            return t2.on("touchstart mousedown", function(e3) {
              var t3 = p.BrowserUtils.isTouchSupported && n2.isTouchEvent(e3);
              e3.preventDefault(), null != n2.player.vr && e3.stopPropagation(), n2.setSeeking(true), i2 = true, n2.onSeekEvent(), new a.DOM(document).on(t3 ? "touchmove" : "mousemove", r2), new a.DOM(document).on(t3 ? "touchend" : "mouseup", s2);
            }), t2.on("touchmove mousemove", function(e3) {
              e3.preventDefault(), i2 && r2(e3);
              var e3 = n2.getOffset(e3), t3 = 100 * e3, e3 = e3 * n2.seekBar.width();
              n2.setSeekPosition(t3), n2.onSeekPreviewEvent(t3, e3, false), n2.hasLabel() && n2.getLabel().isHidden() && n2.getLabel().show();
            }), t2.on("touchend mouseleave", function(e3) {
              e3.preventDefault(), n2.setSeekPosition(0), n2.hasLabel() && n2.getLabel().hide();
            }), e2.append(t2), this.label && e2.append(this.label.getDomElement()), e2;
          }, b.prototype.getHorizontalOffset = function(e2) {
            var t2 = this.seekBar.offset().left, n2 = this.seekBar.width();
            return this.sanitizeOffset(1 / n2 * (e2 - t2));
          }, b.prototype.getVerticalOffset = function(e2) {
            var t2 = this.seekBar.offset().top, n2 = this.seekBar.height();
            return 1 - this.sanitizeOffset(1 / n2 * (e2 - t2));
          }, b.prototype.getOffset = function(e2) {
            return p.BrowserUtils.isTouchSupported && this.isTouchEvent(e2) ? this.config.vertical ? this.getVerticalOffset(("touchend" === e2.type ? e2.changedTouches : e2.touches)[0].pageY) : this.getHorizontalOffset(("touchend" === e2.type ? e2.changedTouches : e2.touches)[0].pageX) : e2 instanceof MouseEvent ? this.config.vertical ? this.getVerticalOffset(e2.pageY) : this.getHorizontalOffset(e2.pageX) : (console && console.warn("invalid event"), 0);
          }, b.prototype.sanitizeOffset = function(e2) {
            return e2 < 0 ? e2 = 0 : 1 < e2 && (e2 = 1), e2;
          }, b.prototype.setPlaybackPosition = function(e2) {
            this.playbackPositionPercentage = e2, this.setPosition(this.seekBarPlaybackPosition, e2);
            e2 = (this.config.vertical ? this.seekBar.height() - this.seekBarPlaybackPositionMarker.height() : this.seekBar.width()) / 100 * e2, this.config.vertical && (e2 = this.seekBar.height() - e2 - this.seekBarPlaybackPositionMarker.height()), e2 = this.config.vertical ? { transform: "translateY(" + e2 + "px)", "-ms-transform": "translateY(" + e2 + "px)", "-webkit-transform": "translateY(" + e2 + "px)" } : { transform: "translateX(" + e2 + "px)", "-ms-transform": "translateX(" + e2 + "px)", "-webkit-transform": "translateX(" + e2 + "px)" };
            this.seekBarPlaybackPositionMarker.css(e2);
          }, b.prototype.refreshPlaybackPosition = function() {
            this.setPlaybackPosition(this.playbackPositionPercentage);
          }, b.prototype.setBufferPosition = function(e2) {
            this.setPosition(this.seekBarBufferPosition, e2);
          }, b.prototype.setSeekPosition = function(e2) {
            this.setPosition(this.seekBarSeekPosition, e2);
          }, b.prototype.setPosition = function(e2, t2) {
            t2 /= 100, 0.99999 <= t2 && t2 <= 1.00001 && (t2 = 0.99999), t2 = this.config.vertical ? { transform: "scaleY(" + t2 + ")", "-ms-transform": "scaleY(" + t2 + ")", "-webkit-transform": "scaleY(" + t2 + ")" } : { transform: "scaleX(" + t2 + ")", "-ms-transform": "scaleX(" + t2 + ")", "-webkit-transform": "scaleX(" + t2 + ")" };
            e2.css(t2);
          }, b.prototype.setSeeking = function(e2) {
            e2 ? this.getDomElement().addClass(this.prefixCss(b.CLASS_SEEKING)) : this.getDomElement().removeClass(this.prefixCss(b.CLASS_SEEKING));
          }, b.prototype.isSeeking = function() {
            return this.getDomElement().hasClass(this.prefixCss(b.CLASS_SEEKING));
          }, b.prototype.hasLabel = function() {
            return null != this.label;
          }, b.prototype.getLabel = function() {
            return this.label;
          }, b.prototype.onSeekEvent = function() {
            this.seekBarEvents.onSeek.dispatch(this);
          }, b.prototype.onSeekPreviewEvent = function(e2, t2, n2) {
            var o2 = this.timelineMarkersHandler && this.timelineMarkersHandler.getMarkerAtPosition(e2), i2 = e2;
            o2 && (!(0 < o2.duration) || e2 < o2.position ? i2 = o2.position : e2 > o2.position + o2.duration && (i2 = o2.position + o2.duration)), this.label && this.updateLabelPosition(t2), this.seekBarEvents.onSeekPreview.dispatch(this, { scrubbing: n2, position: i2, marker: o2 });
          }, b.prototype.onSeekedEvent = function(e2) {
            this.seekBarEvents.onSeeked.dispatch(this, e2);
          }, Object.defineProperty(b.prototype, "onSeek", { get: function() {
            return this.seekBarEvents.onSeek.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(b.prototype, "onSeekPreview", { get: function() {
            return this.seekBarEvents.onSeekPreview.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(b.prototype, "onSeeked", { get: function() {
            return this.seekBarEvents.onSeeked.getEvent();
          }, enumerable: false, configurable: true }), b.prototype.onShowEvent = function() {
            d.prototype.onShowEvent.call(this), this.refreshPlaybackPosition();
          }, b.prototype.isTouchEvent = function(e2) {
            return window.TouchEvent && e2 instanceof TouchEvent;
          }, b.SMOOTH_PLAYBACK_POSITION_UPDATE_DISABLED = -1, b.CLASS_SEEKING = "seeking", b);
          function b(e2) {
            var n2 = d.call(this, e2 = void 0 === e2 ? {} : e2) || this, t2 = (n2.playbackPositionPercentage = 0, n2.isUserSeeking = false, n2.seekBarEvents = { onSeek: new s.EventDispatcher(), onSeekPreview: new s.EventDispatcher(), onSeeked: new s.EventDispatcher() }, n2.seekWhileScrubbing = function(e3, t3) {
              t3.scrubbing && n2.seek(t3.position);
            }, n2.getTargetSeekPosition = function(e3) {
              var t3;
              return n2.player.isLive() ? (t3 = n2.player.getMaxTimeShift()) - t3 * (e3 / 100) : (t3 = y.PlayerUtils.getSeekableRangeStart(n2.player, 0), n2.player.getDuration() * (e3 / 100) + t3);
            }, n2.seek = function(e3) {
              e3 = n2.getTargetSeekPosition(e3);
              n2.player.isLive() ? n2.player.timeShift(e3, "ui") : n2.player.seek(e3, "ui");
            }, n2.updateLabelPosition = function(e3) {
              n2.uiBoundingRect || (n2.uiBoundingRect = n2.uiManager.getUI().getDomElement().get(0).getBoundingClientRect()), n2.label.setPositionInBounds(e3, n2.uiBoundingRect);
            }, n2.config.keyStepIncrements || { leftRight: 1, upDown: 5 });
            return n2.config = n2.mergeConfig(e2, { cssClass: "ui-seekbar", vertical: false, smoothPlaybackPositionUpdateIntervalMs: 50, keyStepIncrements: t2, ariaLabel: u.i18n.getLocalizer("seekBar"), tabIndex: 0, snappingRange: 1, enableSeekPreview: true, snappingEnabled: true }, n2.config), n2.label = n2.config.label, n2;
          }
        }, { "../browserutils": 3, "../dom": 84, "../eventdispatcher": 86, "../localization/i18n": 91, "../playerutils": 98, "../stringutils": 111, "../timeout": 113, "./../groupplaybackapi": 88, "./component": 18, "./seekbarbufferlevel": 41, "./seekbarcontroller": 42, "./timelinemarkershandler": 72 }], 41: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.getMinBufferLevel = void 0, n.getMinBufferLevel = function(e2) {
            var t2 = e2.getDuration(), n2 = e2.getVideoBufferLength(), e2 = e2.getAudioBufferLength(), n2 = Math.min(null != n2 ? n2 : Number.MAX_VALUE, null != e2 ? e2 : Number.MAX_VALUE);
            return 100 / t2 * (n2 = n2 === Number.MAX_VALUE ? 0 : n2);
          };
        }, {}], 42: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.SeekBarController = n.SeekBarType = void 0;
          function r(e2, t2, n2) {
            e2 < t2.min ? n2(t2.min) : e2 > t2.max ? n2(t2.max) : n2(e2);
          }
          var o, i = e("../uiutils");
          (e = o = n.SeekBarType || (n.SeekBarType = {}))[e.Vod = 0] = "Vod", e[e.Live = 1] = "Live", e[e.Volume = 2] = "Volume";
          function s(e2, t2, n2) {
            this.keyStepIncrements = e2, this.player = t2, this.volumeController = n2;
          }
          s.prototype.arrowKeyControls = function(e2, t2, n2) {
            var o2 = this, i2 = Math.floor(e2);
            return { left: function() {
              return r(i2 - o2.keyStepIncrements.leftRight, t2, n2);
            }, right: function() {
              return r(i2 + o2.keyStepIncrements.leftRight, t2, n2);
            }, up: function() {
              return r(i2 + o2.keyStepIncrements.upDown, t2, n2);
            }, down: function() {
              return r(i2 - o2.keyStepIncrements.upDown, t2, n2);
            }, home: function() {
              return r(t2.min, t2, n2);
            }, end: function() {
              return r(t2.max, t2, n2);
            } };
          }, s.prototype.seekBarControls = function(e2) {
            return e2 === o.Live ? this.arrowKeyControls(this.player.getTimeShift(), { min: this.player.getMaxTimeShift(), max: 0 }, this.player.timeShift) : e2 === o.Vod ? this.arrowKeyControls(this.player.getCurrentTime(), { min: 0, max: this.player.getDuration() }, this.player.seek) : e2 === o.Volume && null != this.volumeController ? (e2 = this.volumeController.startTransition(), this.arrowKeyControls(this.player.getVolume(), { min: 0, max: 100 }, e2.finish.bind(e2))) : void 0;
          }, s.prototype.setSeekBarControls = function(e2, n2) {
            var o2 = this;
            e2.on("keydown", function(e3) {
              var t2 = o2.seekBarControls(n2());
              switch (e3.keyCode) {
                case i.UIUtils.KeyCode.LeftArrow:
                  t2.left(), e3.preventDefault();
                  break;
                case i.UIUtils.KeyCode.RightArrow:
                  t2.right(), e3.preventDefault();
                  break;
                case i.UIUtils.KeyCode.UpArrow:
                  t2.up(), e3.preventDefault();
                  break;
                case i.UIUtils.KeyCode.DownArrow:
                  t2.down(), e3.preventDefault();
                  break;
                case i.UIUtils.KeyCode.Home:
                  t2.home(), e3.preventDefault();
                  break;
                case i.UIUtils.KeyCode.End:
                  t2.end(), e3.preventDefault();
                  break;
                case i.UIUtils.KeyCode.Space:
                  o2.player.isPlaying() ? o2.player.pause() : o2.player.play(), e3.preventDefault();
              }
            });
          }, n.SeekBarController = s;
        }, { "../uiutils": 117 }], 43: [function(e, t, n) {
          "use strict";
          var o, r, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SeekBarLabel = void 0, e("./container")), a = e("./label"), l = e("./component"), c = e("../stringutils"), u = e("../imageloader"), p = e("../playerutils"), e = (r = s.Container, i(g, r), g.prototype.configure = function(e2, t2) {
            function n2() {
              o2.timeFormat = 3600 <= Math.abs(e2.isLive() ? e2.getMaxTimeShift() : e2.getDuration()) ? c.StringUtils.FORMAT_HHMMSS : c.StringUtils.FORMAT_MMSS, o2.setTitleText(null), o2.setThumbnail(null);
            }
            var o2 = this;
            r.prototype.configure.call(this, e2, t2), this.player = e2, (this.uiManager = t2).onSeekPreview.subscribeRateLimited(this.handleSeekPreview, 100);
            t2.getConfig().events.onUpdated.subscribe(n2), n2();
          }, g.prototype.setPositionInBounds = function(e2, t2) {
            this.getDomElement().css("left", e2 + "px");
            var n2 = this.container.getDomElement().get(0).parentElement.getBoundingClientRect(), o2 = 0;
            n2.right > t2.right ? o2 = n2.right - t2.right : n2.left < t2.left && (o2 = n2.left - t2.left), 0 !== o2 ? (this.getDomElement().css("left", e2 - o2 + "px"), this.caret.getDomElement().css("transform", "translateX(".concat(o2, "px)"))) : this.caret.getDomElement().css("transform", null);
          }, g.prototype.setText = function(e2) {
            this.timeLabel.setText(e2);
          }, g.prototype.setTime = function(e2) {
            this.setText(c.StringUtils.secondsToTime(e2, this.timeFormat));
          }, g.prototype.setTitleText = function(e2) {
            this.titleLabel.setText(e2 = void 0 === e2 ? "" : e2);
          }, g.prototype.setThumbnail = function(o2) {
            var i2 = this, r2 = (void 0 === o2 && (o2 = null), this.thumbnail.getDomElement());
            null == o2 ? r2.css({ "background-image": null, display: null, width: null, height: null }) : this.thumbnailImageLoader.load(o2.url, function(e2, t2, n2) {
              void 0 !== o2.x ? r2.css(i2.thumbnailCssSprite(o2, t2, n2)) : r2.css(i2.thumbnailCssSingleImage(o2, t2, n2));
            });
          }, g.prototype.thumbnailCssSprite = function(e2, t2, n2) {
            var t2 = 100 * (t2 / e2.width), n2 = 100 * (n2 / e2.height), o2 = 100 * (e2.x / e2.width), i2 = 100 * (e2.y / e2.height), r2 = 1 / e2.width * e2.height;
            return { display: "inherit", "background-image": "url(".concat(e2.url, ")"), "padding-bottom": "".concat(100 * r2, "%"), "background-size": "".concat(t2, "% ").concat(n2, "%"), "background-position": "-".concat(o2, "% -").concat(i2, "%") };
          }, g.prototype.thumbnailCssSingleImage = function(e2, t2, n2) {
            t2 = 1 / t2 * n2;
            return { display: "inherit", "background-image": "url(".concat(e2.url, ")"), "padding-bottom": "".concat(100 * t2, "%"), "background-size": "100% 100%", "background-position": "0 0" };
          }, g.prototype.release = function() {
            r.prototype.release.call(this), this.uiManager.onSeekPreview.unsubscribe(this.handleSeekPreview);
          }, g);
          function g(e2) {
            var i2 = r.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return i2.appliedMarkerCssClasses = [], i2.handleSeekPreview = function(e3, t2) {
              var n2, o2;
              i2.player.isLive() ? (n2 = (n2 = i2.player.getMaxTimeShift()) - n2 * (t2.position / 100), i2.setTime(n2), n2 = n2, o2 = i2.player.getTimeShift(), o2 = i2.player.getCurrentTime() - o2 + n2, i2.setThumbnail(i2.player.getThumbnail(o2))) : (n2 = i2.player.getDuration() * (t2.position / 100), i2.setTime(n2), o2 = p.PlayerUtils.getSeekableRangeStart(i2.player, 0), i2.setThumbnail(i2.player.getThumbnail(n2 + o2))), t2.marker ? i2.setTitleText(t2.marker.marker.title) : i2.setTitleText(null), 0 < i2.appliedMarkerCssClasses.length && (i2.getDomElement().removeClass(i2.appliedMarkerCssClasses.join(" ")), i2.appliedMarkerCssClasses = []), t2.marker && (t2 = (t2.marker.marker.cssClasses || []).map(function(e4) {
                return i2.prefixCss(e4);
              }), i2.getDomElement().addClass(t2.join(" ")), i2.appliedMarkerCssClasses = t2);
            }, i2.timeLabel = new a.Label({ cssClasses: ["seekbar-label-time"] }), i2.titleLabel = new a.Label({ cssClasses: ["seekbar-label-title"] }), i2.thumbnail = new l.Component({ cssClasses: ["seekbar-thumbnail"], role: "img" }), i2.thumbnailImageLoader = new u.ImageLoader(), i2.container = new s.Container({ components: [i2.thumbnail, new s.Container({ components: [i2.titleLabel, i2.timeLabel], cssClass: "seekbar-label-metadata" })], cssClass: "seekbar-label-inner" }), i2.caret = new a.Label({ cssClasses: ["seekbar-label-caret"] }), i2.config = i2.mergeConfig(e2, { cssClass: "ui-seekbar-label", components: [i2.container, i2.caret], hidden: true }, i2.config), i2;
          }
          n.SeekBarLabel = e;
        }, { "../imageloader": 90, "../playerutils": 98, "../stringutils": 111, "./component": 18, "./container": 19, "./label": 28 }], 44: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SelectBox = void 0, e("./listselector")), a = e("../dom"), l = e("../localization/i18n"), c = e("../playerutils"), u = e("./component"), p = ["mousemove", "mouseenter", "mouseleave", "touchstart", "touchmove", "touchend", "pointermove", "click", "keydown", "keypress", "keyup", "blur"], g = ["change", "keyup", "mouseup"], f = [["click", function() {
            return true;
          }], ["keydown", function(e2) {
            return [" ", "ArrowUp", "ArrowDown"].includes(e2.key);
          }], ["mousedown", function() {
            return true;
          }]], e = (i = s.ListSelector, r(d, i), d.prototype.toDomElement = function() {
            return this.selectElement = new a.DOM("select", { id: this.config.id, class: this.getCssClasses(), "aria-label": l.i18n.performLocalization(this.config.ariaLabel) }, this), this.onDisabled.subscribe(this.closeDropdown), this.onHide.subscribe(this.closeDropdown), this.addDropdownOpenedListeners(), this.updateDomItems(), this.selectElement.on("change", this.onChange), this.selectElement;
          }, d.prototype.configure = function(e2, t2) {
            i.prototype.configure.call(this, e2, t2), this.uiContainer = t2.getUI(), null != (e2 = this.uiContainer) && e2.onPlayerStateChange().subscribe(this.onPlayerStateChange);
          }, d.prototype.getSelectElement = function() {
            var e2;
            return null == (e2 = null == (e2 = this.selectElement) ? void 0 : e2.get()) ? void 0 : e2[0];
          }, d.prototype.updateDomItems = function(e2) {
            if (void 0 === e2 && (e2 = null), void 0 !== this.selectElement) {
              this.selectElement.empty();
              for (var t2 = 0, n2 = this.items; t2 < n2.length; t2++) {
                var o2 = n2[t2], i2 = new a.DOM("option", { value: String(o2.key) }).html(l.i18n.performLocalization(o2.label));
                o2.key === String(e2) && i2.attr("selected", "selected"), this.selectElement.append(i2);
              }
            }
          }, d.prototype.onItemAddedEvent = function(e2) {
            i.prototype.onItemAddedEvent.call(this, e2), this.updateDomItems(this.selectedItem);
          }, d.prototype.onItemRemovedEvent = function(e2) {
            i.prototype.onItemRemovedEvent.call(this, e2), this.updateDomItems(this.selectedItem);
          }, d.prototype.onItemSelectedEvent = function(e2, t2) {
            void 0 === t2 && (t2 = true), i.prototype.onItemSelectedEvent.call(this, e2), t2 && this.updateDomItems(e2);
          }, d.prototype.addDropdownCloseListeners = function() {
            var t2 = this;
            this.removeDropdownCloseListeners(), clearTimeout(this.dropdownCloseListenerTimeoutId), p.forEach(function(e2) {
              return document.addEventListener(e2, t2.onDropdownClosed, true);
            }), g.forEach(function(e2) {
              return t2.selectElement.on(e2, t2.onDropdownClosed, true);
            }), this.removeDropdownCloseListeners = function() {
              p.forEach(function(e2) {
                return document.removeEventListener(e2, t2.onDropdownClosed, true);
              }), g.forEach(function(e2) {
                return t2.selectElement.off(e2, t2.onDropdownClosed, true);
              });
            };
          }, d.prototype.addDropdownOpenedListeners = function() {
            for (var o2 = this, i2 = [], e2 = (this.removeDropdownOpenedListeners(), function(e3, t3) {
              function n3(e4) {
                t3(e4) && o2.onDropdownOpened();
              }
              i2.push(function() {
                return o2.selectElement.off(e3, n3, true);
              }), r2.selectElement.on(e3, n3, true);
            }), r2 = this, t2 = 0, n2 = f; t2 < n2.length; t2++) {
              var s2 = n2[t2];
              e2(s2[0], s2[1]);
            }
            this.removeDropdownOpenedListeners = function() {
              for (var e3 = 0, t3 = i2; e3 < t3.length; e3++) (0, t3[e3])();
            };
          }, d.prototype.release = function() {
            i.prototype.release.call(this), this.removeDropdownCloseListeners(), this.removeDropdownOpenedListeners(), clearTimeout(this.dropdownCloseListenerTimeoutId);
          }, d);
          function d(e2) {
            var n2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return n2.dropdownCloseListenerTimeoutId = 0, n2.removeDropdownCloseListeners = function() {
            }, n2.removeDropdownOpenedListeners = function() {
            }, n2.onChange = function() {
              var e3 = n2.selectElement.val();
              n2.onItemSelectedEvent(e3, false);
            }, n2.closeDropdown = function() {
              var e3 = n2.getSelectElement();
              void 0 !== e3 && e3.blur();
            }, n2.onPlayerStateChange = function(e3, t2) {
              [c.PlayerUtils.PlayerState.Idle, c.PlayerUtils.PlayerState.Finished].includes(t2) && n2.closeDropdown();
            }, n2.onDropdownOpened = function() {
              clearTimeout(n2.dropdownCloseListenerTimeoutId), n2.dropdownCloseListenerTimeoutId = window.setTimeout(function() {
                return n2.addDropdownCloseListeners();
              }, 100), n2.onViewModeChangedEvent(u.ViewMode.Persistent);
            }, n2.onDropdownClosed = function() {
              clearTimeout(n2.dropdownCloseListenerTimeoutId), n2.removeDropdownCloseListeners(), n2.onViewModeChangedEvent(u.ViewMode.Temporary);
            }, n2.config = n2.mergeConfig(e2, { cssClass: "ui-selectbox" }, n2.config), n2;
          }
          n.SelectBox = e;
        }, { "../dom": 84, "../localization/i18n": 91, "../playerutils": 98, "./component": 18, "./listselector": 30 }], 45: [function(e, t, n) {
          "use strict";
          var o, i, r, s = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), a = (Object.defineProperty(n, "__esModule", { value: true }), n.SettingsPanel = void 0, e("./container")), l = e("./selectbox"), c = e("../timeout"), u = e("../eventdispatcher"), p = e("./settingspanelpage");
          (e = i = i || {})[e.Forwards = 0] = "Forwards", e[e.Backwards = 1] = "Backwards", n.SettingsPanel = (r = a.Container, s(g, r), g.prototype.configure = function(e2, t2) {
            var n2 = this, o2 = (r.prototype.configure.call(this, e2, t2), this.getConfig());
            t2.onControlsHide.subscribe(function() {
              return n2.hideHoveredSelectBoxes();
            }), t2.onComponentViewModeChanged.subscribe(function(e3, t3) {
              t3 = t3.mode;
              return n2.trackComponentViewMode(t3);
            }), -1 < o2.hideDelay && (this.hideTimeout = new c.Timeout(o2.hideDelay, function() {
              n2.hide(), n2.hideHoveredSelectBoxes();
            }), this.getDomElement().on("mouseenter", function() {
              n2.hideTimeout.clear();
            }), this.getDomElement().on("mouseleave", function() {
              n2.hideTimeout.reset();
            }), this.getDomElement().on("focusin", function() {
              n2.hideTimeout.clear();
            }), this.getDomElement().on("focusout", function() {
              n2.hideTimeout.reset();
            })), this.onHide.subscribe(function() {
              -1 < o2.hideDelay && n2.hideTimeout.clear(), n2.activePage.onInactiveEvent();
            }), this.onShow.subscribe(function() {
              n2.resetNavigation(true), n2.activePage.onActiveEvent(), -1 < o2.hideDelay && n2.hideTimeout.start();
            }), this.getRootPage().onSettingsStateChanged.subscribe(function() {
              n2.onSettingsStateChangedEvent();
            }), this.updateActivePageClass();
          }, g.prototype.getActivePage = function() {
            return this.activePage;
          }, g.prototype.setActivePageIndex = function(e2) {
            this.setActivePage(this.getPages()[e2]);
          }, g.prototype.setActivePage = function(e2) {
            e2 === this.getActivePage() ? console.warn("Page is already the current one ... skipping navigation") : this.navigateToPage(e2, this.getActivePage(), i.Forwards, !this.config.pageTransitionAnimation);
          }, g.prototype.popToRootSettingsPanelPage = function() {
            this.resetNavigation(this.config.pageTransitionAnimation);
          }, g.prototype.popSettingsPanelPage = function() {
            var e2;
            0 === this.navigationStack.length ? console.warn("Already on the root page ... skipping navigation") : (e2 = (e2 = this.navigationStack[this.navigationStack.length - 2]) || this.getRootPage(), this.navigateToPage(e2, this.activePage, i.Backwards, !this.config.pageTransitionAnimation));
          }, g.prototype.rootPageHasActiveSettings = function() {
            return this.getRootPage().hasActiveSettings();
          }, g.prototype.getPages = function() {
            return this.config.components.filter(function(e2) {
              return e2 instanceof p.SettingsPanelPage;
            });
          }, Object.defineProperty(g.prototype, "onSettingsStateChanged", { get: function() {
            return this.settingsPanelEvents.onSettingsStateChanged.getEvent();
          }, enumerable: false, configurable: true }), g.prototype.release = function() {
            r.prototype.release.call(this), this.hideTimeout && this.hideTimeout.clear();
          }, g.prototype.addComponent = function(e2) {
            0 === this.getPages().length && e2 instanceof p.SettingsPanelPage && (this.activePage = e2), r.prototype.addComponent.call(this, e2);
          }, g.prototype.suspendHideTimeout = function() {
            this.hideTimeout.suspend();
          }, g.prototype.resumeHideTimeout = function() {
            this.hideTimeout.resume(true);
          }, g.prototype.updateActivePageClass = function() {
            var t2 = this;
            this.getPages().forEach(function(e2) {
              e2 === t2.activePage ? e2.getDomElement().addClass(t2.prefixCss(g.CLASS_ACTIVE_PAGE)) : e2.getDomElement().removeClass(t2.prefixCss(g.CLASS_ACTIVE_PAGE));
            });
          }, g.prototype.resetNavigation = function(e2) {
            var t2 = this.getActivePage(), n2 = this.getRootPage();
            t2 && !e2 && t2.onInactiveEvent(), this.navigationStack = [], this.animateNavigation(n2, t2, e2), this.activePage = n2, this.updateActivePageClass();
          }, g.prototype.navigateToPage = function(e2, t2, n2, o2) {
            this.activePage = e2, n2 === i.Forwards ? this.navigationStack.push(e2) : this.navigationStack.pop(), this.animateNavigation(e2, t2, o2), this.updateActivePageClass(), e2.onActiveEvent(), t2.onInactiveEvent();
          }, g.prototype.animateNavigation = function(e2, t2, n2) {
            var o2, i2, r2, s2, a2;
            this.config.pageTransitionAnimation && (o2 = this.getDomElement(), i2 = (a2 = this.getDomElement().get(0)).scrollWidth, r2 = a2.scrollHeight, t2.getDomElement().css("display", "none"), this.getDomElement().css({ width: "", height: "" }), s2 = (e2 = e2.getDomElement().get(0)).cloneNode(true), e2.parentNode.appendChild(s2), s2.style.display = "block", e2 = a2.scrollWidth, a2 = a2.scrollHeight, s2.parentElement.removeChild(s2), t2.getDomElement().css("display", ""), o2.css({ width: i2 + "px", height: r2 + "px" }), n2 || this.forceBrowserReflow(), o2.css({ width: e2 + "px", height: a2 + "px" }));
          }, g.prototype.forceBrowserReflow = function() {
            this.getDomElement().get(0).offsetLeft;
          }, g.prototype.hideHoveredSelectBoxes = function() {
            this.getComputedItems().map(function(e2) {
              return e2.setting;
            }).filter(function(e2) {
              return e2 instanceof l.SelectBox;
            }).forEach(function(e2) {
              return e2.closeDropdown();
            });
          }, g.prototype.getComputedItems = function() {
            for (var e2 = [], t2 = 0, n2 = this.getPages(); t2 < n2.length; t2++) {
              var o2 = n2[t2];
              e2.push.apply(e2, o2.getItems());
            }
            return e2;
          }, g.prototype.getRootPage = function() {
            return this.getPages()[0];
          }, g.prototype.onSettingsStateChangedEvent = function() {
            this.settingsPanelEvents.onSettingsStateChanged.dispatch(this);
          }, g.CLASS_ACTIVE_PAGE = "active", g);
          function g(e2) {
            var t2 = r.call(this, e2) || this;
            return t2.navigationStack = [], t2.settingsPanelEvents = { onSettingsStateChanged: new u.EventDispatcher() }, t2.config = t2.mergeConfig(e2, { cssClass: "ui-settings-panel", hideDelay: 3e3, pageTransitionAnimation: true }, t2.config), t2.activePage = t2.getRootPage(), t2;
          }
        }, { "../eventdispatcher": 86, "../timeout": 113, "./container": 19, "./selectbox": 44, "./settingspanelpage": 47 }], 46: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SettingsPanelItem = void 0, e("./container")), a = e("./component"), l = e("../eventdispatcher"), c = e("./label"), u = e("./selectbox"), p = e("./listbox"), g = e("./videoqualityselectbox"), f = e("./audioqualityselectbox"), d = e("./playbackspeedselectbox"), e = (i = s.Container, r(h, i), h.prototype.configure = function(e2, t2) {
            var n2, o2 = this;
            (this.setting instanceof u.SelectBox || this.setting instanceof p.ListBox) && (this.setting.onItemAdded.subscribe(n2 = function() {
              var e3;
              (o2.setting instanceof u.SelectBox || o2.setting instanceof p.ListBox) && (e3 = 2, (o2.setting instanceof g.VideoQualitySelectBox && o2.setting.hasAutoItem() || o2.setting instanceof f.AudioQualitySelectBox) && (e3 = 3), o2.setting.itemCount() < e3 || o2.setting instanceof d.PlaybackSpeedSelectBox && !t2.getConfig().playbackSpeedSelectionEnabled ? o2.hide() : o2.show(), o2.onActiveChangedEvent(), o2.getDomElement().attr("aria-haspopup", "true"));
            }), this.setting.onItemRemoved.subscribe(n2), n2());
          }, h.prototype.isActive = function() {
            return this.isShown();
          }, h.prototype.onActiveChangedEvent = function() {
            this.settingsPanelItemEvents.onActiveChanged.dispatch(this);
          }, Object.defineProperty(h.prototype, "onActiveChanged", { get: function() {
            return this.settingsPanelItemEvents.onActiveChanged.getEvent();
          }, enumerable: false, configurable: true }), h);
          function h(e2, t2, n2) {
            var o2 = i.call(this, n2 = void 0 === n2 ? {} : n2) || this;
            return o2.settingsPanelItemEvents = { onActiveChanged: new l.EventDispatcher() }, o2.setting = t2, o2.config = o2.mergeConfig(n2, { cssClass: "ui-settings-panel-item", role: "menuitem" }, o2.config), null !== e2 && (e2 instanceof a.Component ? o2.label = e2 : o2.label = new c.Label({ text: e2, for: o2.setting.getConfig().id }), o2.addComponent(o2.label)), o2.addComponent(o2.setting), o2;
          }
          n.SettingsPanelItem = e;
        }, { "../eventdispatcher": 86, "./audioqualityselectbox": 8, "./component": 18, "./container": 19, "./label": 28, "./listbox": 29, "./playbackspeedselectbox": 33, "./selectbox": 44, "./videoqualityselectbox": 77 }], 47: [function(e, t, n) {
          "use strict";
          var o, s, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.SettingsPanelPage = void 0, e("./container")), a = e("./settingspanelitem"), l = e("../eventdispatcher"), c = e("../browserutils");
          n.SettingsPanelPage = (s = r.Container, i(u, s), u.prototype.configure = function(e2, t2) {
            for (var i2 = this, n2 = (s.prototype.configure.call(this, e2, t2), function() {
              i2.onSettingsStateChangedEvent();
              for (var e3 = null, t3 = 0, n3 = i2.getItems(); t3 < n3.length; t3++) {
                var o3 = n3[t3];
                o3.getDomElement().removeClass(i2.prefixCss(u.CLASS_LAST)), o3.isShown() && (e3 = o3);
              }
              e3 && e3.getDomElement().addClass(i2.prefixCss(u.CLASS_LAST));
            }), o2 = 0, r2 = this.getItems(); o2 < r2.length; o2++) r2[o2].onActiveChanged.subscribe(n2);
          }, u.prototype.hasActiveSettings = function() {
            for (var e2 = 0, t2 = this.getItems(); e2 < t2.length; e2++) if (t2[e2].isActive()) return true;
            return false;
          }, u.prototype.getItems = function() {
            return this.config.components.filter(function(e2) {
              return e2 instanceof a.SettingsPanelItem;
            });
          }, u.prototype.onSettingsStateChangedEvent = function() {
            this.settingsPanelPageEvents.onSettingsStateChanged.dispatch(this);
          }, Object.defineProperty(u.prototype, "onSettingsStateChanged", { get: function() {
            return this.settingsPanelPageEvents.onSettingsStateChanged.getEvent();
          }, enumerable: false, configurable: true }), u.prototype.onActiveEvent = function() {
            var e2 = this.getItems().filter(function(e3) {
              return e3.isActive();
            });
            this.settingsPanelPageEvents.onActive.dispatch(this), !(0 < e2.length) || c.BrowserUtils.isIOS || c.BrowserUtils.isMacIntel && c.BrowserUtils.isTouchSupported || e2[0].getDomElement().focusToFirstInput();
          }, Object.defineProperty(u.prototype, "onActive", { get: function() {
            return this.settingsPanelPageEvents.onActive.getEvent();
          }, enumerable: false, configurable: true }), u.prototype.onInactiveEvent = function() {
            this.settingsPanelPageEvents.onInactive.dispatch(this);
          }, Object.defineProperty(u.prototype, "onInactive", { get: function() {
            return this.settingsPanelPageEvents.onInactive.getEvent();
          }, enumerable: false, configurable: true }), u.CLASS_LAST = "last", u);
          function u(e2) {
            var t2 = s.call(this, e2) || this;
            return t2.settingsPanelPageEvents = { onSettingsStateChanged: new l.EventDispatcher(), onActive: new l.EventDispatcher(), onInactive: new l.EventDispatcher() }, t2.config = t2.mergeConfig(e2, { cssClass: "ui-settings-panel-page", role: "menu" }, t2.config), t2;
          }
        }, { "../browserutils": 3, "../eventdispatcher": 86, "./container": 19, "./settingspanelitem": 46 }], 48: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), e = (Object.defineProperty(n, "__esModule", { value: true }), n.SettingsPanelPageBackButton = void 0, e("./settingspanelpagenavigatorbutton")), e = (i = e.SettingsPanelPageNavigatorButton, r(s, i), s.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.onClick.subscribe(function() {
              n2.popPage();
            });
          }, s);
          function s(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-settingspanelpagebackbutton", text: "back" }, t2.config), t2;
          }
          n.SettingsPanelPageBackButton = e;
        }, { "./settingspanelpagenavigatorbutton": 49 }], 49: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), e = (Object.defineProperty(n, "__esModule", { value: true }), n.SettingsPanelPageNavigatorButton = void 0, e("./button")), e = (i = e.Button, r(s, i), s.prototype.popPage = function() {
            this.container.popSettingsPanelPage();
          }, s.prototype.pushTargetPage = function() {
            this.container.setActivePage(this.targetPage);
          }, s);
          function s(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, {}, t2.config), t2.container = t2.config.container, t2.targetPage = t2.config.targetPage, t2;
          }
          n.SettingsPanelPageNavigatorButton = e;
        }, { "./button": 12 }], 50: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SettingsPanelPageOpenButton = void 0, e("./settingspanelpagenavigatorbutton")), a = e("../localization/i18n"), e = (i = s.SettingsPanelPageNavigatorButton, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.getDomElement().attr("aria-haspopup", "true"), this.getDomElement().attr("aria-owns", this.config.targetPage.getConfig().id), this.onClick.subscribe(function() {
              n2.pushTargetPage();
            });
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-settingspanelpageopenbutton", text: a.i18n.getLocalizer("open"), role: "menuitem" }, t2.config), t2;
          }
          n.SettingsPanelPageOpenButton = e;
        }, { "../localization/i18n": 91, "./settingspanelpagenavigatorbutton": 49 }], 51: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SettingsToggleButton = void 0, e("./togglebutton")), a = e("./settingspanel"), l = e("../arrayutils"), c = e("../localization/i18n"), e = (i = s.ToggleButton, r(u, i), u.prototype.configure = function(e2, t2) {
            var n2 = this, e2 = (i.prototype.configure.call(this, e2, t2), this.getConfig()), o2 = e2.settingsPanel;
            this.onClick.subscribe(function() {
              o2.isShown() || n2.visibleSettingsPanels.slice().forEach(function(e3) {
                return e3.hide();
              }), o2.toggleHidden();
            }), o2.onShow.subscribe(function() {
              n2.on();
            }), o2.onHide.subscribe(function() {
              n2.off();
            }), t2.onComponentShow.subscribe(function(e3) {
              e3 instanceof a.SettingsPanel && (n2.visibleSettingsPanels.push(e3), e3.onHide.subscribeOnce(function() {
                return l.ArrayUtils.remove(n2.visibleSettingsPanels, e3);
              }));
            }), e2.autoHideWhenNoActiveSettings && (o2.onSettingsStateChanged.subscribe(t2 = function() {
              o2.rootPageHasActiveSettings() ? n2.isHidden() && n2.show() : n2.isShown() && n2.hide();
            }), t2());
          }, u);
          function u(e2) {
            var t2 = i.call(this, e2) || this;
            if (t2.visibleSettingsPanels = [], e2.settingsPanel) return t2.config = t2.mergeConfig(e2, { cssClass: "ui-settingstogglebutton", text: c.i18n.getLocalizer("settings"), settingsPanel: null, autoHideWhenNoActiveSettings: true, role: "pop-up button" }, t2.config), t2.getDomElement().attr("aria-owns", e2.settingsPanel.getActivePage().getConfig().id), t2.getDomElement().attr("aria-haspopup", "true"), t2;
            throw new Error("Required SettingsPanel is missing");
          }
          n.SettingsToggleButton = e;
        }, { "../arrayutils": 1, "../localization/i18n": 91, "./settingspanel": 45, "./togglebutton": 74 }], 52: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), e = (Object.defineProperty(n, "__esModule", { value: true }), n.Spacer = void 0, e("./component")), e = (i = e.Component, r(s, i), s.prototype.onShowEvent = function() {
          }, s.prototype.onHideEvent = function() {
          }, s.prototype.onHoverChangedEvent = function(e2) {
          }, s);
          function s(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-spacer" }, t2.config), t2;
          }
          n.Spacer = e;
        }, { "./component": 18 }], 53: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SubtitleListBox = void 0, e("./listbox")), a = e("../subtitleutils"), e = (i = s.ListBox, r(l, i), l.prototype.configure = function(e2, t2) {
            i.prototype.configure.call(this, e2, t2), new a.SubtitleSwitchHandler(e2, this, t2);
          }, l);
          function l() {
            return null !== i && i.apply(this, arguments) || this;
          }
          n.SubtitleListBox = e;
        }, { "../subtitleutils": 112, "./listbox": 29 }], 54: [function(e, t, n) {
          "use strict";
          var o, s, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.SubtitleRegionContainer = n.SubtitleRegionContainerManager = n.SubtitleLabel = n.SubtitleOverlay = void 0, e("./container")), a = e("./label"), l = e("./controlbar"), c = e("../eventdispatcher"), u = e("../dom"), p = e("../localization/i18n"), g = e("../vttutils");
          n.SubtitleOverlay = (s = r.Container, i(y, s), y.prototype.configure = function(e2, o2) {
            function t2() {
              i2.hide(), i2.subtitleContainerManager.clear(), r2.clear(), i2.removeComponents(), i2.updateComponents();
            }
            function n2() {
              r2.clearInactiveCues(e2.getCurrentTime()).forEach(function(e3) {
                i2.subtitleContainerManager.removeLabel(e3.label);
              }), i2.updateComponents();
            }
            var i2 = this, r2 = (s.prototype.configure.call(this, e2, o2), new h());
            this.subtitleManager = r2, this.subtitleContainerManager = new v(this), e2.on(e2.exports.PlayerEvent.CueEnter, function(e3) {
              var t3 = i2.generateLabel(e3);
              r2.cueEnter(e3, t3), i2.preprocessLabelEventCallback.dispatch(e3, t3), i2.previewSubtitleActive && i2.subtitleContainerManager.removeLabel(i2.previewSubtitle), i2.show(), i2.subtitleContainerManager.addLabel(t3, i2.getDomElement().size()), i2.updateComponents(), o2.getConfig().forceSubtitlesIntoViewContainer && i2.handleSubtitleCropping(t3);
            }), e2.on(e2.exports.PlayerEvent.CueUpdate, function(e3) {
              var t3 = i2.generateLabel(e3), n3 = r2.cueUpdate(e3, t3);
              i2.preprocessLabelEventCallback.dispatch(e3, t3), n3 && i2.subtitleContainerManager.replaceLabel(n3, t3), o2.getConfig().forceSubtitlesIntoViewContainer && i2.handleSubtitleCropping(t3);
            }), e2.on(e2.exports.PlayerEvent.CueExit, function(e3) {
              e3 = r2.cueExit(e3);
              e3 && (i2.subtitleContainerManager.removeLabel(e3), i2.updateComponents()), r2.hasCues || (i2.previewSubtitleActive ? (i2.subtitleContainerManager.addLabel(i2.previewSubtitle), i2.updateComponents()) : i2.hide());
            });
            e2.on(e2.exports.PlayerEvent.AudioChanged, t2), e2.on(e2.exports.PlayerEvent.SubtitleDisabled, t2), e2.on(e2.exports.PlayerEvent.Seeked, n2), e2.on(e2.exports.PlayerEvent.TimeShifted, n2), e2.on(e2.exports.PlayerEvent.PlaybackFinished, t2), e2.on(e2.exports.PlayerEvent.SourceUnloaded, t2), o2.onComponentShow.subscribe(function(e3) {
              e3 instanceof l.ControlBar && i2.getDomElement().addClass(i2.prefixCss(y.CLASS_CONTROLBAR_VISIBLE));
            }), o2.onComponentHide.subscribe(function(e3) {
              e3 instanceof l.ControlBar && i2.getDomElement().removeClass(i2.prefixCss(y.CLASS_CONTROLBAR_VISIBLE));
            }), this.configureCea608Captions(e2, o2), t2();
          }, y.prototype.setFontSizeFactor = function(e2) {
            this.FONT_SIZE_FACTOR = Math.max(0.5, Math.min(2, e2)), this.recalculateCEAGrid();
          }, y.prototype.recalculateCEAGrid = function() {
            this.CEA608_NUM_ROWS = Math.floor(y.DEFAULT_CEA608_NUM_ROWS / Math.max(this.FONT_SIZE_FACTOR, 1)), this.CEA608_NUM_COLUMNS = Math.floor(y.DEFAULT_CEA608_NUM_COLUMNS / this.FONT_SIZE_FACTOR), this.CEA608_COLUMN_OFFSET = 100 / this.CEA608_NUM_COLUMNS;
          }, y.prototype.detectCroppedSubtitleLabel = function(e2) {
            var t2 = this.getDomElement().get(0), e2 = e2.getBoundingClientRect(), t2 = t2.getBoundingClientRect();
            return { top: e2.top < t2.top, right: e2.right > t2.right, bottom: e2.bottom > t2.bottom, left: e2.left < t2.left };
          }, y.prototype.handleSubtitleCropping = function(e2) {
            var e2 = e2.getDomElement(), t2 = this.detectCroppedSubtitleLabel(e2.get(0));
            t2.top && (e2.css("top", "0"), e2.removeCss("bottom")), t2.right && (e2.css("right", "0"), e2.removeCss("left")), t2.bottom && (e2.css("bottom", "0"), e2.removeCss("top")), t2.left && (e2.css("left", "0"), e2.removeCss("right"));
          }, y.prototype.resolveRowNumber = function(e2) {
            return 1 < this.FONT_SIZE_FACTOR && e2 > this.CEA608_NUM_ROWS ? e2 - (y.DEFAULT_CEA608_NUM_ROWS - this.CEA608_NUM_ROWS) : e2;
          }, y.prototype.generateLabel = function(e2) {
            var t2 = e2.region, n2 = (null == (n2 = e2.position) ? void 0 : n2.row) || 0;
            return e2.position && (e2.position.row = this.resolveRowNumber(e2.position.row) || 0, e2.position.column = e2.position.column || 0, t2 = t2 || "cea608-row-".concat(e2.position.row)), new m({ text: e2.html || h.generateImageTagText(e2.image) || e2.text, vtt: e2.vtt, region: t2, regionStyle: e2.regionStyle, originalRowPosition: n2 });
          }, y.prototype.resolveFontSizeFactor = function(e2) {
            return parseInt(e2) / 100;
          }, y.prototype.updateRegionRowPosition = function(e2) {
            var t2, n2, o2, i2 = e2.getDomElement().get()[0], e2 = e2.getComponents()[0];
            i2 && e2 && (i2 = i2.classList, e2 = null == (e2 = e2.getConfig()) ? void 0 : e2.originalRowPosition, t2 = /subtitle-position-cea608-row-(\d+)/, n2 = Array.from(i2).find(function(e3) {
              return t2.test(e3);
            })) && (o2 = (o2 = t2.exec(n2)) ? parseInt(o2[1], 10) : null, e2 = this.resolveRowNumber(null != e2 ? e2 : o2), o2 = n2.replace(t2, "subtitle-position-cea608-row-".concat(e2)), i2.replace(n2, o2));
          }, y.prototype.configureCea608Captions = function(e2, t2) {
            function n2() {
              g2.getDomElement().removeClass(g2.prefixCss(y.CLASS_CEA_608)), g2.cea608Enabled = false;
            }
            var p2, o2, g2 = this, f2 = 0, d2 = 0, i2 = true, h2 = 0.2, t2 = (this.cea608Enabled = false, t2.getSubtitleSettingsManager()), r2 = (null != t2.fontSize.value && (o2 = this.resolveFontSizeFactor(t2.fontSize.value), this.setFontSizeFactor(o2)), t2.fontSize.onChanged.subscribe(function(e3, t3) {
              t3.isSet() ? (t3 = g2.resolveFontSizeFactor(t3.value), g2.setFontSizeFactor(t3)) : g2.setFontSizeFactor(1), r2();
            }), function() {
              var e3 = new m({ text: "X" }), t3 = (e3.getDomElement().css({ "font-size": "200px", "line-height": "200px", visibility: "hidden" }), g2.addComponent(e3), g2.updateComponents(), g2.show(), e3.getDomElement().width() * g2.FONT_SIZE_FACTOR), n3 = e3.getDomElement().height() * g2.FONT_SIZE_FACTOR, o3 = t3 / n3, e3 = (g2.removeComponent(e3), g2.updateComponents(), g2.subtitleManager.hasCues || g2.hide(), g2.getDomElement()), i3 = e3.width() - 10, r3 = e3.height(), t3 = t3 * g2.CEA608_NUM_COLUMNS / (n3 * g2.CEA608_NUM_ROWS), s2 = 0;
              d2 = t3 < i3 / r3 ? (s2 = r3 / g2.CEA608_NUM_ROWS, f2 = s2 * (1 - h2), n3 = i3 / g2.CEA608_NUM_COLUMNS, t3 = f2 * o3, Math.max(n3 - t3, 0)) : (s2 = i3 / g2.CEA608_NUM_COLUMNS / o3, f2 = s2 * (1 - h2), 0), p2 = s2 * h2;
              g2.getComponents().forEach(function(e4) {
                e4 instanceof P && g2.updateRegionRowPosition(e4);
              }), e3.get().forEach(function(e4) {
                e4.style.setProperty("--cea608-row-height", "".concat(s2, "px"));
              });
              for (var a2 = function(e4) {
                var t4 = 1 < g2.FONT_SIZE_FACTOR;
                e4.getDomElement().css({ "font-size": "".concat(f2, "px"), "line-height": "".concat(f2, "px"), "letter-spacing": "".concat(t4 ? 0 : d2, "px"), "white-space": "".concat(t4 ? "nowrap" : "normal"), left: t4 && y.DEFAULT_CAPTION_LEFT_OFFSET }), e4.regionStyle = "line-height: ".concat(f2, "px; padding: ").concat(p2 / 2, "px; height: ").concat(f2, "px");
              }, l2 = 0, c2 = g2.getComponents(); l2 < c2.length; l2++) {
                var u2 = c2[l2];
                u2 instanceof P && (u2.getDomElement().css({ "line-height": "".concat(f2, "px"), padding: "".concat(p2 / 2, "px"), height: "".concat(f2, "px") }), u2.getComponents().forEach(function(e4) {
                  a2(e4);
                })), u2 instanceof m && a2(u2);
              }
            });
            e2.on(e2.exports.PlayerEvent.PlayerResized, function() {
              g2.cea608Enabled ? r2() : i2 = true;
            }), this.preprocessLabelEventCallback.subscribe(function(e3, t3) {
              var n3;
              null != e3.position && (g2.cea608Enabled || (g2.cea608Enabled = true, g2.getDomElement().addClass(g2.prefixCss(y.CLASS_CEA_608)), i2 && (r2(), i2 = false)), n3 = 1 < g2.FONT_SIZE_FACTOR, "0%" !== (e3 = e3.position.column * g2.CEA608_COLUMN_OFFSET + "%") && !n3 || (e3 = y.DEFAULT_CAPTION_LEFT_OFFSET), t3.getDomElement().css({ left: e3, "font-size": "".concat(f2, "px"), "letter-spacing": "".concat(n3 ? 0 : d2, "px"), "white-space": "".concat(n3 ? "nowrap" : "normal") }), t3.regionStyle = "line-height: ".concat(f2, "px; padding: ").concat(p2 / 2, "px; height: ").concat(f2, "px"));
            });
            e2.on(e2.exports.PlayerEvent.CueExit, function() {
              g2.subtitleManager.hasCues || n2();
            }), e2.on(e2.exports.PlayerEvent.SourceUnloaded, n2), e2.on(e2.exports.PlayerEvent.SubtitleEnable, n2), e2.on(e2.exports.PlayerEvent.SubtitleDisabled, n2);
          }, y.prototype.enablePreviewSubtitleLabel = function() {
            this.subtitleManager.hasCues || (this.previewSubtitleActive = true, this.subtitleContainerManager.addLabel(this.previewSubtitle), this.updateComponents(), this.show());
          }, y.prototype.removePreviewSubtitleLabel = function() {
            this.previewSubtitleActive && (this.previewSubtitleActive = false, this.subtitleContainerManager.removeLabel(this.previewSubtitle), this.updateComponents());
          }, y.CLASS_CONTROLBAR_VISIBLE = "controlbar-visible", y.CLASS_CEA_608 = "cea608", y.DEFAULT_CEA608_NUM_ROWS = 15, y.DEFAULT_CEA608_NUM_COLUMNS = 32, y.DEFAULT_CAPTION_LEFT_OFFSET = "0.5%", y);
          function y(e2) {
            var t2 = s.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.preprocessLabelEventCallback = new c.EventDispatcher(), t2.FONT_SIZE_FACTOR = 1, t2.CEA608_NUM_ROWS = y.DEFAULT_CEA608_NUM_ROWS, t2.CEA608_NUM_COLUMNS = y.DEFAULT_CEA608_NUM_COLUMNS, t2.CEA608_COLUMN_OFFSET = 100 / t2.CEA608_NUM_COLUMNS, t2.cea608Enabled = false, t2.filterFontSizeOptions = function(e3) {
              return !t2.cea608Enabled || null === e3.key || (e3 = parseInt(e3.key, 10), !isNaN(e3) && e3 <= 200);
            }, t2.recalculateCEAGrid(), t2.previewSubtitleActive = false, t2.previewSubtitle = new m({ text: p.i18n.getLocalizer("subtitle.example") }), t2.config = t2.mergeConfig(e2, { cssClass: "ui-subtitle-overlay" }, t2.config), t2;
          }
          f = a.Label, i(d, f), Object.defineProperty(d.prototype, "vtt", { get: function() {
            return this.config.vtt;
          }, enumerable: false, configurable: true }), Object.defineProperty(d.prototype, "region", { get: function() {
            return this.config.region;
          }, enumerable: false, configurable: true }), Object.defineProperty(d.prototype, "regionStyle", { get: function() {
            return this.config.regionStyle;
          }, set: function(e2) {
            this.config.regionStyle = e2;
          }, enumerable: false, configurable: true }), Object.defineProperty(d.prototype, "originalRowPosition", { get: function() {
            return this.config.originalRowPosition;
          }, set: function(e2) {
            this.config.originalRowPosition = e2;
          }, enumerable: false, configurable: true });
          var f, m = d;
          function d(e2) {
            var t2 = f.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-subtitle-label" }, t2.config), t2;
          }
          n.SubtitleLabel = m;
          b.calculateId = function(e2) {
            var t2 = e2.start + "-" + e2.text;
            return e2.position && (t2 += "-" + e2.position.row + "-" + e2.position.column), t2;
          }, b.prototype.cueEnter = function(e2, t2) {
            this.addCueToMap(e2, t2);
          }, b.prototype.cueUpdate = function(e2, t2) {
            var n2 = this.popCueFromMap(e2);
            if (n2) return this.addCueToMap(e2, t2), n2;
          }, b.prototype.addCueToMap = function(e2, t2) {
            var n2 = b.calculateId(e2);
            this.activeSubtitleCueMap[n2] = this.activeSubtitleCueMap[n2] || [], this.activeSubtitleCueMap[n2].push({ event: e2, label: t2 }), this.activeSubtitleCueCount++;
          }, b.prototype.popCueFromMap = function(e2) {
            var e2 = b.calculateId(e2), e2 = this.activeSubtitleCueMap[e2];
            if (e2 && 0 < e2.length) return e2 = e2.shift(), this.activeSubtitleCueCount--, e2.label;
          }, b.prototype.clearInactiveCues = function(t2) {
            var n2 = this, o2 = [];
            return Object.keys(this.activeSubtitleCueMap).forEach(function(e2) {
              n2.activeSubtitleCueMap[e2].forEach(function(e3) {
                (t2 < e3.event.start || t2 > e3.event.end) && (n2.popCueFromMap(e3.event), o2.push(e3));
              });
            }), o2;
          }, b.generateImageTagText = function(e2) {
            if (e2) return (e2 = new u.DOM("img", { src: e2 })).css("width", "100%"), e2.get(0).outerHTML;
          }, b.prototype.getCues = function(e2) {
            e2 = b.calculateId(e2), e2 = this.activeSubtitleCueMap[e2];
            if (e2 && 0 < e2.length) return e2.map(function(e3) {
              return e3.label;
            });
          }, b.prototype.cueExit = function(e2) {
            return this.popCueFromMap(e2);
          }, Object.defineProperty(b.prototype, "cueCount", { get: function() {
            return this.activeSubtitleCueCount;
          }, enumerable: false, configurable: true }), Object.defineProperty(b.prototype, "hasCues", { get: function() {
            return 0 < this.cueCount;
          }, enumerable: false, configurable: true }), b.prototype.clear = function() {
            this.activeSubtitleCueMap = {}, this.activeSubtitleCueCount = 0;
          };
          var h = b;
          function b() {
            this.activeSubtitleCueMap = {}, this.activeSubtitleCueCount = 0;
          }
          C.prototype.getRegion = function(e2) {
            return e2.vtt ? { regionContainerId: e2.vtt.region && e2.vtt.region.id ? e2.vtt.region.id : "vtt", regionName: "vtt" } : { regionContainerId: e2.region || "default", regionName: e2.region || "default" };
          }, C.prototype.addLabel = function(e2, t2) {
            var n2 = this.getRegion(e2), o2 = n2.regionContainerId, n2 = n2.regionName, n2 = ["subtitle-position-".concat(n2)];
            if (e2.vtt && e2.vtt.region && n2.push("vtt-region-".concat(e2.vtt.region.id)), !this.subtitleRegionContainers[o2]) {
              var i2, n2 = new P({ cssClasses: n2 });
              for (i2 in this.subtitleRegionContainers[o2] = n2, e2.regionStyle && n2.getDomElement().attr("style", e2.regionStyle), e2.vtt && n2.getDomElement().css("position", "static"), n2.getDomElement(), this.subtitleRegionContainers) this.subtitleOverlay.addComponent(this.subtitleRegionContainers[i2]);
            }
            this.subtitleRegionContainers[o2].addLabel(e2, t2);
          }, C.prototype.replaceLabel = function(e2, t2) {
            var n2 = this.getRegion(e2).regionContainerId;
            this.subtitleRegionContainers[n2].removeLabel(e2), this.subtitleRegionContainers[n2].addLabel(t2);
          }, C.prototype.removeLabel = function(e2) {
            var t2 = e2.vtt ? e2.vtt.region && e2.vtt.region.id ? e2.vtt.region.id : "vtt" : e2.region || "default";
            this.subtitleRegionContainers[t2].removeLabel(e2), this.subtitleRegionContainers[t2].isEmpty() && (this.subtitleOverlay.removeComponent(this.subtitleRegionContainers[t2]), delete this.subtitleRegionContainers[t2]);
          }, C.prototype.clear = function() {
            for (var e2 in this.subtitleRegionContainers) this.subtitleOverlay.removeComponent(this.subtitleRegionContainers[e2]);
            this.subtitleRegionContainers = {};
          };
          var v = C;
          function C(e2) {
            this.subtitleOverlay = e2, this.subtitleRegionContainers = {}, this.subtitleOverlay = e2;
          }
          n.SubtitleRegionContainerManager = v;
          S = r.Container, i(w, S), w.prototype.addLabel = function(e2, t2) {
            this.labelCount++, e2.vtt && (e2.vtt.region && t2 && g.VttUtils.setVttRegionStyles(this, e2.vtt.region, t2), g.VttUtils.setVttCueBoxStyles(e2, t2)), this.addComponent(e2), this.updateComponents();
          }, w.prototype.removeLabel = function(e2) {
            this.labelCount--, this.removeComponent(e2), this.updateComponents();
          }, w.prototype.isEmpty = function() {
            return 0 === this.labelCount;
          };
          var S, P = w;
          function w(e2) {
            var t2 = S.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.labelCount = 0, t2.config = t2.mergeConfig(e2, { cssClass: "subtitle-region-container" }, t2.config), t2;
          }
          n.SubtitleRegionContainer = P;
        }, { "../dom": 84, "../eventdispatcher": 86, "../localization/i18n": 91, "../vttutils": 119, "./container": 19, "./controlbar": 20, "./label": 28 }], 55: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SubtitleSelectBox = void 0, e("./selectbox")), a = e("../subtitleutils"), l = e("../localization/i18n"), e = (i = s.SelectBox, r(c, i), c.prototype.configure = function(e2, t2) {
            i.prototype.configure.call(this, e2, t2), new a.SubtitleSwitchHandler(e2, this, t2);
          }, c);
          function c(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitleselectbox"], ariaLabel: l.i18n.getLocalizer("subtitle.select") }, t2.config), t2;
          }
          n.SubtitleSelectBox = e;
        }, { "../localization/i18n": 91, "../subtitleutils": 112, "./selectbox": 44 }], 56: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.BackgroundColorSelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            function n2() {
              o2.settingsManager.backgroundColor.isSet() && o2.settingsManager.backgroundOpacity.isSet() ? o2.toggleOverlayClass("bgcolor-" + o2.settingsManager.backgroundColor.value + o2.settingsManager.backgroundOpacity.value) : o2.toggleOverlayClass(null);
            }
            var o2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("white", a.i18n.getLocalizer("colors.white")), this.addItem("black", a.i18n.getLocalizer("colors.black")), this.addItem("red", a.i18n.getLocalizer("colors.red")), this.addItem("green", a.i18n.getLocalizer("colors.green")), this.addItem("blue", a.i18n.getLocalizer("colors.blue")), this.addItem("cyan", a.i18n.getLocalizer("colors.cyan")), this.addItem("yellow", a.i18n.getLocalizer("colors.yellow")), this.addItem("magenta", a.i18n.getLocalizer("colors.magenta"));
            this.onItemSelected.subscribe(function(e3, t3) {
              o2.settingsManager.backgroundColor.value = t3;
            }), this.settingsManager.backgroundColor.onChanged.subscribe(function(e3, t3) {
              o2.settingsManager.backgroundColor.isSet() ? o2.settingsManager.backgroundOpacity.isSet() || (o2.settingsManager.backgroundOpacity.value = "100") : o2.settingsManager.backgroundOpacity.clear(), o2.selectItem(t3.value), n2();
            }), this.settingsManager.backgroundOpacity.onChanged.subscribe(function() {
              n2();
            }), this.settingsManager.backgroundColor.isSet() && this.selectItem(this.settingsManager.backgroundColor.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitlesettingsbackgroundcolorselectbox"] }, t2.config), t2;
          }
          n.BackgroundColorSelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 57: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.BackgroundOpacitySelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("100", a.i18n.getLocalizer("percent", { value: 100 })), this.addItem("75", a.i18n.getLocalizer("percent", { value: 75 })), this.addItem("50", a.i18n.getLocalizer("percent", { value: 50 })), this.addItem("25", a.i18n.getLocalizer("percent", { value: 25 })), this.addItem("0", a.i18n.getLocalizer("percent", { value: 0 })), this.onItemSelected.subscribe(function(e3, t3) {
              n2.settingsManager.backgroundOpacity.value = t3, n2.settingsManager.backgroundOpacity.isSet() ? n2.settingsManager.backgroundColor.isSet() || (n2.settingsManager.backgroundColor.value = "black") : n2.settingsManager.backgroundColor.clear();
            }), this.settingsManager.backgroundOpacity.onChanged.subscribe(function(e3, t3) {
              n2.selectItem(t3.value);
            }), this.settingsManager.backgroundOpacity.isSet() && this.selectItem(this.settingsManager.backgroundOpacity.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitlesettingsbackgroundopacityselectbox"] }, t2.config), t2;
          }
          n.BackgroundOpacitySelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 58: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.CharacterEdgeColorSelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("white", a.i18n.getLocalizer("colors.white")), this.addItem("black", a.i18n.getLocalizer("colors.black")), this.addItem("red", a.i18n.getLocalizer("colors.red")), this.addItem("green", a.i18n.getLocalizer("colors.green")), this.addItem("blue", a.i18n.getLocalizer("colors.blue")), this.addItem("cyan", a.i18n.getLocalizer("colors.cyan")), this.addItem("yellow", a.i18n.getLocalizer("colors.yellow")), this.addItem("magenta", a.i18n.getLocalizer("colors.magenta")), this.onItemSelected.subscribe(function(e3, t3) {
              n2.settingsManager.characterEdgeColor.value = t3, n2.settingsManager.characterEdgeColor.isSet() ? n2.settingsManager.characterEdge.isSet() || (n2.settingsManager.characterEdge.value = "uniform") : n2.settingsManager.characterEdge.clear();
            }), this.settingsManager.characterEdgeColor.onChanged.subscribe(function(e3, t3) {
              n2.selectItem(t3.value);
            }), this.settingsManager.characterEdgeColor.isSet() && this.selectItem(this.settingsManager.characterEdgeColor.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitle-settings-character-edge-color-select-box"] }, t2.config), t2;
          }
          n.CharacterEdgeColorSelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 59: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.CharacterEdgeSelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            function n2() {
              o2.settingsManager.characterEdge.isSet() && o2.settingsManager.characterEdgeColor.isSet() ? o2.toggleOverlayClass("characteredge-" + o2.settingsManager.characterEdge.value + "-" + o2.settingsManager.characterEdgeColor.value) : o2.toggleOverlayClass(null);
            }
            var o2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("raised", a.i18n.getLocalizer("settings.subtitles.characterEdge.raised")), this.addItem("depressed", a.i18n.getLocalizer("settings.subtitles.characterEdge.depressed")), this.addItem("uniform", a.i18n.getLocalizer("settings.subtitles.characterEdge.uniform")), this.addItem("dropshadowed", a.i18n.getLocalizer("settings.subtitles.characterEdge.dropshadowed"));
            this.onItemSelected.subscribe(function(e3, t3) {
              o2.settingsManager.characterEdge.value = t3;
            }), this.settingsManager.characterEdge.onChanged.subscribe(function(e3, t3) {
              o2.settingsManager.characterEdge.isSet() ? o2.settingsManager.characterEdgeColor.isSet() || (o2.settingsManager.characterEdgeColor.value = "black") : o2.settingsManager.characterEdgeColor.clear(), o2.selectItem(t3.value), n2();
            }), this.settingsManager.characterEdgeColor.onChanged.subscribe(function() {
              n2();
            }), this.settingsManager.characterEdge.isSet() && this.selectItem(this.settingsManager.characterEdge.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitlesettingscharacteredgeselectbox"] }, t2.config), t2;
          }
          n.CharacterEdgeSelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 60: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.FontColorSelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            function n2() {
              o2.settingsManager.fontColor.isSet() && o2.settingsManager.fontOpacity.isSet() ? o2.toggleOverlayClass("fontcolor-" + o2.settingsManager.fontColor.value + o2.settingsManager.fontOpacity.value) : o2.toggleOverlayClass(null);
            }
            var o2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("white", a.i18n.getLocalizer("colors.white")), this.addItem("black", a.i18n.getLocalizer("colors.black")), this.addItem("red", a.i18n.getLocalizer("colors.red")), this.addItem("green", a.i18n.getLocalizer("colors.green")), this.addItem("blue", a.i18n.getLocalizer("colors.blue")), this.addItem("cyan", a.i18n.getLocalizer("colors.cyan")), this.addItem("yellow", a.i18n.getLocalizer("colors.yellow")), this.addItem("magenta", a.i18n.getLocalizer("colors.magenta"));
            this.onItemSelected.subscribe(function(e3, t3) {
              o2.settingsManager.fontColor.value = t3;
            }), this.settingsManager.fontColor.onChanged.subscribe(function(e3, t3) {
              o2.settingsManager.fontColor.isSet() ? o2.settingsManager.fontOpacity.isSet() || (o2.settingsManager.fontOpacity.value = "100") : o2.settingsManager.fontOpacity.clear(), o2.selectItem(t3.value), n2();
            }), this.settingsManager.fontOpacity.onChanged.subscribe(function() {
              n2();
            }), this.settingsManager.fontColor.isSet() && this.selectItem(this.settingsManager.fontColor.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitlesettingsfontcolorselectbox"] }, t2.config), t2;
          }
          n.FontColorSelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 61: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.FontFamilySelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("monospacedserif", a.i18n.getLocalizer("settings.subtitles.font.family.monospacedserif")), this.addItem("proportionalserif", a.i18n.getLocalizer("settings.subtitles.font.family.proportionalserif")), this.addItem("monospacedsansserif", a.i18n.getLocalizer("settings.subtitles.font.family.monospacedsansserif")), this.addItem("proportionalsansserif", a.i18n.getLocalizer("settings.subtitles.font.family.proportionalsansserif")), this.addItem("casual", a.i18n.getLocalizer("settings.subtitles.font.family.casual")), this.addItem("cursive", a.i18n.getLocalizer("settings.subtitles.font.family.cursive")), this.addItem("smallcapital", a.i18n.getLocalizer("settings.subtitles.font.family.smallcapital")), this.settingsManager.fontFamily.onChanged.subscribe(function(e3, t3) {
              t3.isSet() ? n2.toggleOverlayClass("fontfamily-" + t3.value) : n2.toggleOverlayClass(null), n2.selectItem(t3.value);
            }), this.onItemSelected.subscribe(function(e3, t3) {
              n2.settingsManager.fontFamily.value = t3;
            }), this.settingsManager.fontFamily.isSet() && this.selectItem(this.settingsManager.fontFamily.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitlesettingsfontfamilyselectbox"] }, t2.config), t2;
          }
          n.FontFamilySelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 62: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.FontOpacitySelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("100", a.i18n.getLocalizer("percent", { value: 100 })), this.addItem("75", a.i18n.getLocalizer("percent", { value: 75 })), this.addItem("50", a.i18n.getLocalizer("percent", { value: 50 })), this.addItem("25", a.i18n.getLocalizer("percent", { value: 25 })), this.onItemSelected.subscribe(function(e3, t3) {
              n2.settingsManager.fontOpacity.value = t3, n2.settingsManager.fontOpacity.isSet() ? n2.settingsManager.fontColor.isSet() || (n2.settingsManager.fontColor.value = "white") : n2.settingsManager.fontColor.clear();
            }), this.settingsManager.fontOpacity.onChanged.subscribe(function(e3, t3) {
              n2.selectItem(t3.value);
            }), this.settingsManager.fontOpacity.isSet() && this.selectItem(this.settingsManager.fontOpacity.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitlesettingsfontopacityselectbox"] }, t2.config), t2;
          }
          n.FontOpacitySelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 63: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.FontSizeSelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.getFontSizeOptions = function() {
            return [{ key: null, label: a.i18n.getLocalizer("default") }, { key: "50", label: a.i18n.getLocalizer("percent", { value: 50 }) }, { key: "75", label: a.i18n.getLocalizer("percent", { value: 75 }) }, { key: "100", label: a.i18n.getLocalizer("percent", { value: 100 }) }, { key: "150", label: a.i18n.getLocalizer("percent", { value: 150 }) }, { key: "200", label: a.i18n.getLocalizer("percent", { value: 200 }) }, { key: "300", label: a.i18n.getLocalizer("percent", { value: 300 }) }, { key: "400", label: a.i18n.getLocalizer("percent", { value: 400 }) }];
          }, l.prototype.populateItemsWithFilter = function() {
            this.clearItems();
            for (var e2 = 0, t2 = this.getFontSizeOptions(); e2 < t2.length; e2++) {
              var n2 = t2[e2];
              this.config.filter && !this.config.filter(n2) || this.addItem(n2.key, n2.label);
            }
            this.settingsManager.fontSize.isSet() && this.selectItem(this.settingsManager.fontSize.value);
          }, l.prototype.reapplyFilterAndReload = function() {
            this.populateItemsWithFilter();
          }, l.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.populateItemsWithFilter(), this.onShow.subscribe(function() {
              n2.populateItemsWithFilter();
            }), this.settingsManager.fontSize.onChanged.subscribe(function(e3, t3) {
              t3.isSet() ? n2.toggleOverlayClass("fontsize-" + t3.value) : n2.toggleOverlayClass(null), n2.selectItem(t3.value);
            }), this.onItemSelected.subscribe(function(e3, t3) {
              n2.settingsManager.fontSize.value = t3;
            });
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitlesettingsfontsizeselectbox"] }, t2.config), t2;
          }
          n.FontSizeSelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 64: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.FontStyleSelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("italic", a.i18n.getLocalizer("settings.subtitles.font.style.italic")), this.addItem("bold", a.i18n.getLocalizer("settings.subtitles.font.style.bold")), null != (e2 = this.settingsManager) && e2.fontStyle.onChanged.subscribe(function(e3, t3) {
              t3.isSet() ? n2.toggleOverlayClass("fontstyle-" + t3.value) : n2.toggleOverlayClass(null), n2.selectItem(t3.value);
            }), this.onItemSelected.subscribe(function(e3, t3) {
              n2.settingsManager && (n2.settingsManager.fontStyle.value = t3);
            }), null != (t2 = this.settingsManager) && t2.fontStyle.isSet() && this.selectItem(this.settingsManager.fontStyle.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitle-settings-font-style-select-box"] }, t2.config), t2;
          }
          n.FontStyleSelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 65: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), e = (Object.defineProperty(n, "__esModule", { value: true }), n.SubtitleSettingSelectBox = void 0, e("../selectbox")), e = (i = e.SelectBox, r(s, i), s.prototype.toggleOverlayClass = function(e2) {
            this.currentCssClass && (this.overlay.getDomElement().removeClass(this.currentCssClass), this.currentCssClass = null), e2 && (this.currentCssClass = this.prefixCss(e2), this.overlay.getDomElement().addClass(this.currentCssClass));
          }, s.prototype.configure = function(e2, t2) {
            this.settingsManager = t2.getSubtitleSettingsManager();
          }, s);
          function s(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.overlay = e2.overlay, t2;
          }
          n.SubtitleSettingSelectBox = e;
        }, { "../selectbox": 44 }], 66: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SubtitleSettingsLabel = void 0, e("../container")), a = e("../../dom"), l = e("../../localization/i18n"), e = (i = s.Container, r(c, i), c.prototype.toDomElement = function() {
            return new a.DOM("label", { id: this.config.id, class: this.getCssClasses(), for: this.for }, this).append(new a.DOM("span", {}).html(l.i18n.performLocalization(this.text)), this.opener.getDomElement());
          }, c);
          function c(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.opener = e2.opener, t2.text = e2.text, t2.for = e2.for, t2.config = t2.mergeConfig(e2, { cssClass: "ui-label", components: [t2.opener] }, t2.config), t2;
          }
          n.SubtitleSettingsLabel = e;
        }, { "../../dom": 84, "../../localization/i18n": 91, "../container": 19 }], 67: [function(e, t, n) {
          "use strict";
          var o, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.SubtitleSettingsProperty = n.SubtitleSettingsManager = void 0, e("../../storageutils")), s = e("../component"), a = e("../../eventdispatcher");
          function l() {
            this._properties = { fontColor: new g(this), fontOpacity: new g(this), fontFamily: new g(this), fontSize: new g(this), fontStyle: new g(this), characterEdge: new g(this), characterEdgeColor: new g(this), backgroundColor: new g(this), backgroundOpacity: new g(this), windowColor: new g(this), windowOpacity: new g(this) }, this.userSettings = {}, this.localStorageKey = u.instance().prefixCss("subtitlesettings");
          }
          l.prototype.reset = function() {
            for (var e2 in this._properties) this._properties[e2].clear();
          }, Object.defineProperty(l.prototype, "fontColor", { get: function() {
            return this._properties.fontColor;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "fontOpacity", { get: function() {
            return this._properties.fontOpacity;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "fontFamily", { get: function() {
            return this._properties.fontFamily;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "fontSize", { get: function() {
            return this._properties.fontSize;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "fontStyle", { get: function() {
            return this._properties.fontStyle;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "characterEdge", { get: function() {
            return this._properties.characterEdge;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "characterEdgeColor", { get: function() {
            return this._properties.characterEdgeColor;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "backgroundColor", { get: function() {
            return this._properties.backgroundColor;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "backgroundOpacity", { get: function() {
            return this._properties.backgroundOpacity;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "windowColor", { get: function() {
            return this._properties.windowColor;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "windowOpacity", { get: function() {
            return this._properties.windowOpacity;
          }, enumerable: false, configurable: true }), l.prototype.initialize = function() {
            var e2, o2 = this, t2 = this;
            for (e2 in this._properties) !function(n2) {
              t2._properties[n2].onChanged.subscribe(function(e3, t3) {
                t3.isSet() ? o2.userSettings[n2] = t3.value : delete o2.userSettings[n2], o2.save();
              });
            }(e2);
            this.load();
          }, l.prototype.save = function() {
            r.StorageUtils.setObject(this.localStorageKey, this.userSettings);
          }, l.prototype.load = function() {
            for (var e2 in this.userSettings = r.StorageUtils.getObject(this.localStorageKey) || {}, this.userSettings) this._properties[e2].value = this.userSettings[e2];
          }, n.SubtitleSettingsManager = l;
          c = s.Component, i(p, c), p.instance = function() {
            return p._instance = p._instance ? p._instance : new p();
          }, p.prototype.prefixCss = function(e2) {
            return c.prototype.prefixCss.call(this, e2);
          };
          var c, u = p;
          function p() {
            return null !== c && c.apply(this, arguments) || this;
          }
          f.prototype.isSet = function() {
            return null != this._value;
          }, f.prototype.clear = function() {
            this._value = null, this.onChangedEvent(null);
          }, Object.defineProperty(f.prototype, "value", { get: function() {
            return this._value;
          }, set: function(e2) {
            this._value = e2 = "string" == typeof e2 && "null" === e2 ? null : e2, this.onChangedEvent(e2);
          }, enumerable: false, configurable: true }), f.prototype.onChangedEvent = function(e2) {
            this._onChanged.dispatch(this._manager, this);
          }, Object.defineProperty(f.prototype, "onChanged", { get: function() {
            return this._onChanged.getEvent();
          }, enumerable: false, configurable: true });
          var g = f;
          function f(e2) {
            this._manager = e2, this._onChanged = new a.EventDispatcher();
          }
          n.SubtitleSettingsProperty = g;
        }, { "../../eventdispatcher": 86, "../../storageutils": 110, "../component": 18 }], 68: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SubtitleSettingsPanelPage = void 0, e("../settingspanelpage")), a = e("./fontsizeselectbox"), l = e("./fontstyleselectbox"), c = e("./fontfamilyselectbox"), u = e("./fontcolorselectbox"), p = e("./fontopacityselectbox"), g = e("./characteredgeselectbox"), f = e("./characteredgecolorselectbox"), d = e("./backgroundcolorselectbox"), h = e("./backgroundopacityselectbox"), y = e("./windowcolorselectbox"), m = e("./windowopacityselectbox"), b = e("./subtitlesettingsresetbutton"), v = e("../settingspanelpagebackbutton"), C = e("../settingspanelitem"), S = e("../../localization/i18n"), e = (i = s.SettingsPanelPage, r(P, i), P.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.onActive.subscribe(function() {
              n2.overlay.enablePreviewSubtitleLabel();
              var e3 = n2.getComponents().find(function(e4) {
                return e4 instanceof C.SettingsPanelItem && e4.getComponents().some(function(e5) {
                  return e5 instanceof a.FontSizeSelectBox;
                });
              });
              e3 && null != (e3 = e3.getComponents().find(function(e4) {
                return e4 instanceof a.FontSizeSelectBox;
              })) && e3.reapplyFilterAndReload();
            }), this.onInactive.subscribe(function() {
              n2.overlay.removePreviewSubtitleLabel();
            });
          }, P);
          function P(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.overlay = e2.overlay, t2.settingsPanel = e2.settingsPanel, t2.config = t2.mergeConfig(e2, { components: [new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.size"), new a.FontSizeSelectBox({ overlay: t2.overlay, filter: function(e3) {
              return t2.overlay.filterFontSizeOptions(e3);
            } })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.style"), new l.FontStyleSelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.family"), new c.FontFamilySelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.color"), new u.FontColorSelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.font.opacity"), new p.FontOpacitySelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.characterEdge"), new g.CharacterEdgeSelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.characterEdge.color"), new f.CharacterEdgeColorSelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.background.color"), new d.BackgroundColorSelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.background.opacity"), new h.BackgroundOpacitySelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.window.color"), new y.WindowColorSelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(S.i18n.getLocalizer("settings.subtitles.window.opacity"), new m.WindowOpacitySelectBox({ overlay: t2.overlay })), new C.SettingsPanelItem(new v.SettingsPanelPageBackButton({ container: t2.settingsPanel, text: S.i18n.getLocalizer("back") }), new b.SubtitleSettingsResetButton({}), { role: "menubar" })] }, t2.config), t2;
          }
          n.SubtitleSettingsPanelPage = e;
        }, { "../../localization/i18n": 91, "../settingspanelitem": 46, "../settingspanelpage": 47, "../settingspanelpagebackbutton": 48, "./backgroundcolorselectbox": 56, "./backgroundopacityselectbox": 57, "./characteredgecolorselectbox": 58, "./characteredgeselectbox": 59, "./fontcolorselectbox": 60, "./fontfamilyselectbox": 61, "./fontopacityselectbox": 62, "./fontsizeselectbox": 63, "./fontstyleselectbox": 64, "./subtitlesettingsresetbutton": 69, "./windowcolorselectbox": 70, "./windowopacityselectbox": 71 }], 69: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.SubtitleSettingsResetButton = void 0, e("../button")), a = e("../../localization/i18n"), e = (i = s.Button, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.settingsManager = t2.getSubtitleSettingsManager(), this.onClick.subscribe(function() {
              n2.settingsManager.reset();
            });
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-subtitlesettingsresetbutton", text: a.i18n.getLocalizer("reset") }, t2.config), t2;
          }
          n.SubtitleSettingsResetButton = e;
        }, { "../../localization/i18n": 91, "../button": 12 }], 70: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.WindowColorSelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            function n2() {
              o2.settingsManager.windowColor.isSet() && o2.settingsManager.windowOpacity.isSet() ? o2.toggleOverlayClass("windowcolor-" + o2.settingsManager.windowColor.value + o2.settingsManager.windowOpacity.value) : o2.toggleOverlayClass(null);
            }
            var o2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("white", a.i18n.getLocalizer("colors.white")), this.addItem("black", a.i18n.getLocalizer("colors.black")), this.addItem("red", a.i18n.getLocalizer("colors.red")), this.addItem("green", a.i18n.getLocalizer("colors.green")), this.addItem("blue", a.i18n.getLocalizer("colors.blue")), this.addItem("cyan", a.i18n.getLocalizer("colors.cyan")), this.addItem("yellow", a.i18n.getLocalizer("colors.yellow")), this.addItem("magenta", a.i18n.getLocalizer("colors.magenta"));
            this.onItemSelected.subscribe(function(e3, t3) {
              o2.settingsManager.windowColor.value = t3;
            }), this.settingsManager.windowColor.onChanged.subscribe(function(e3, t3) {
              o2.settingsManager.windowColor.isSet() ? o2.settingsManager.windowOpacity.isSet() || (o2.settingsManager.windowOpacity.value = "100") : o2.settingsManager.windowOpacity.clear(), o2.selectItem(t3.value), n2();
            }), this.settingsManager.windowOpacity.onChanged.subscribe(function() {
              n2();
            }), this.settingsManager.windowColor.isSet() && this.selectItem(this.settingsManager.windowColor.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitlesettingswindowcolorselectbox"] }, t2.config), t2;
          }
          n.WindowColorSelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 71: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.WindowOpacitySelectBox = void 0, e("./subtitlesettingselectbox")), a = e("../../localization/i18n"), e = (i = s.SubtitleSettingSelectBox, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2 = this;
            i.prototype.configure.call(this, e2, t2), this.addItem(null, a.i18n.getLocalizer("default")), this.addItem("100", a.i18n.getLocalizer("percent", { value: 100 })), this.addItem("75", a.i18n.getLocalizer("percent", { value: 75 })), this.addItem("50", a.i18n.getLocalizer("percent", { value: 50 })), this.addItem("25", a.i18n.getLocalizer("percent", { value: 25 })), this.addItem("0", a.i18n.getLocalizer("percent", { value: 0 })), this.onItemSelected.subscribe(function(e3, t3) {
              n2.settingsManager.windowOpacity.value = t3, n2.settingsManager.windowOpacity.isSet() ? n2.settingsManager.windowColor.isSet() || (n2.settingsManager.windowColor.value = "black") : n2.settingsManager.windowColor.clear();
            }), this.settingsManager.windowOpacity.onChanged.subscribe(function(e3, t3) {
              n2.selectItem(t3.value);
            }), this.settingsManager.windowOpacity.isSet() && this.selectItem(this.settingsManager.windowOpacity.value);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-subtitlesettingswindowopacityselectbox"] }, t2.config), t2;
          }
          n.WindowOpacitySelectBox = e;
        }, { "../../localization/i18n": 91, "./subtitlesettingselectbox": 65 }], 72: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.TimelineMarkersHandler = void 0;
          var i = e("../dom"), s = e("../playerutils"), o = e("../timeout");
          function r(e2, t2, n2) {
            this.config = e2, this.getSeekBarWidth = t2, this.markersContainer = n2, this.timelineMarkers = [];
          }
          r.prototype.initialize = function(e2, t2) {
            this.player = e2, this.uimanager = t2, this.configureMarkers();
          }, r.prototype.configureMarkers = function() {
            var e2 = this;
            this.player.on(this.player.exports.PlayerEvent.SourceUnloaded, function() {
              return e2.clearMarkers();
            }), this.player.on(this.player.exports.PlayerEvent.AdBreakStarted, function() {
              return e2.clearMarkers();
            }), this.player.on(this.player.exports.PlayerEvent.AdBreakFinished, function() {
              return e2.updateMarkers();
            }), this.player.on(this.player.exports.PlayerEvent.PlayerResized, function() {
              return e2.updateMarkersDOM();
            }), this.player.on(this.player.exports.PlayerEvent.SourceLoaded, function() {
              e2.player.isLive() && (e2.player.on(e2.player.exports.PlayerEvent.TimeChanged, function() {
                return e2.updateMarkers();
              }), e2.configureLivePausedTimeshiftUpdater(function() {
                return e2.updateMarkers();
              }));
            }), this.uimanager.getConfig().events.onUpdated.subscribe(function() {
              return e2.updateMarkers();
            }), this.uimanager.onRelease.subscribe(function() {
              return e2.uimanager.getConfig().events.onUpdated.unsubscribe(function() {
                return e2.updateMarkers();
              });
            }), this.updateMarkers();
          }, r.prototype.getMarkerAtPosition = function(n2) {
            var o2 = this.config.snappingRange;
            return this.timelineMarkers.find(function(e2) {
              var t2 = 0 < e2.duration && n2 >= e2.position - o2 && n2 <= e2.position + e2.duration + o2, e2 = n2 >= e2.position - o2 && n2 <= e2.position + o2;
              return t2 || e2;
            }) || null;
          }, r.prototype.clearMarkers = function() {
            this.timelineMarkers = [], this.markersContainer.empty();
          }, r.prototype.removeMarkerFromConfig = function(t2) {
            this.uimanager.getConfig().metadata.markers = this.uimanager.getConfig().metadata.markers.filter(function(e2) {
              return t2 !== e2;
            });
          }, r.prototype.filterRemovedMarkers = function() {
            var n2 = this;
            this.timelineMarkers = this.timelineMarkers.filter(function(t2) {
              var e2 = n2.uimanager.getConfig().metadata.markers.find(function(e3) {
                return t2.marker === e3;
              });
              return e2 || n2.removeMarkerFromDOM(t2), e2;
            });
          }, r.prototype.removeMarkerFromDOM = function(e2) {
            e2.element && e2.element.remove();
          }, r.prototype.updateMarkers = function() {
            var r2 = this;
            !function(e2, t2) {
              e2 = e2.getDuration() !== 1 / 0 || e2.isLive(), t2 = 0 < t2.getConfig().metadata.markers.length;
              return e2 && t2;
            }(this.player, this.uimanager) ? this.clearMarkers() : (this.filterRemovedMarkers(), this.uimanager.getConfig().metadata.markers.forEach(function(t2) {
              var e2, n2, o2 = function(e3, t3) {
                var n3 = function(e4) {
                  var t4, n4;
                  return e4.isLive() ? (t4 = s.PlayerUtils.getSeekableRangeRespectingLive(e4), n4 = t4.start, t4.end - n4) : e4.getDuration();
                }(e3), e3 = 100 / n3 * function(e4, t4, n4) {
                  return t4.isLive() ? n4 - (s.PlayerUtils.getSeekableRangeRespectingLive(t4).end - e4.time) : e4.time;
                }(t3, e3, n3), n3 = 100 / n3 * t3.duration;
                e3 < 0 && !isNaN(n3) && (n3 += e3);
                100 - e3 < n3 && (n3 = 100 - e3);
                return { markerDuration: n3, markerPosition: e3 };
              }(r2.player, t2), i2 = o2.markerPosition, o2 = o2.markerDuration;
              e2 = i2, ((n2 = o2) < 0 || isNaN(n2)) && e2 < 0 ? r2.removeMarkerFromConfig(t2) : i2 <= 100 && ((n2 = r2.timelineMarkers.find(function(e3) {
                return e3.marker === t2;
              })) ? (n2.position = i2, n2.duration = o2, r2.updateMarkerDOM(n2)) : (r2.timelineMarkers.push(e2 = { marker: t2, position: i2, duration: o2 }), r2.createMarkerDOM(e2)));
            }));
          }, r.prototype.getMarkerCssProperties = function(e2) {
            var t2 = this.getSeekBarWidth(), n2 = t2 / 100 * (e2.position < 0 ? 0 : e2.position), n2 = { transform: "translateX(".concat(n2, "px)") };
            return 0 < e2.duration && (t2 = Math.round(t2 / 100 * e2.duration), n2.width = "".concat(t2, "px")), n2;
          }, r.prototype.updateMarkerDOM = function(e2) {
            e2.element.css(this.getMarkerCssProperties(e2));
          }, r.prototype.createMarkerDOM = function(e2) {
            var t2, n2 = this, o2 = ["seekbar-marker"].concat(e2.marker.cssClasses || []).map(function(e3) {
              return n2.prefixCss(e3);
            }), o2 = new i.DOM("div", { class: o2.join(" "), "data-marker-time": String(e2.marker.time), "data-marker-title": String(e2.marker.title) }).css(this.getMarkerCssProperties(e2));
            e2.marker.imageUrl && (t2 = new i.DOM("img", { class: this.prefixCss("seekbar-marker-image"), src: e2.marker.imageUrl }).on("error", function() {
              t2.remove();
            }), o2.append(t2)), e2.element = o2, this.markersContainer.append(o2);
          }, r.prototype.updateMarkersDOM = function() {
            var t2 = this;
            this.timelineMarkers.forEach(function(e2) {
              e2.element ? t2.updateMarkerDOM(e2) : t2.createMarkerDOM(e2);
            });
          }, r.prototype.configureLivePausedTimeshiftUpdater = function(e2) {
            var t2 = this;
            this.pausedTimeshiftUpdater = new o.Timeout(1e3, e2, true), this.player.on(this.player.exports.PlayerEvent.Paused, function() {
              t2.player.isLive() && t2.player.getMaxTimeShift() < 0 && t2.pausedTimeshiftUpdater.start();
            }), this.player.on(this.player.exports.PlayerEvent.Play, function() {
              return t2.pausedTimeshiftUpdater.clear();
            }), this.player.on(this.player.exports.PlayerEvent.Destroy, function() {
              return t2.pausedTimeshiftUpdater.clear();
            });
          }, r.prototype.prefixCss = function(e2) {
            return this.config.cssPrefix + "-" + e2;
          }, n.TimelineMarkersHandler = r;
        }, { "../dom": 84, "../playerutils": 98, "../timeout": 113 }], 73: [function(e, t, n) {
          "use strict";
          var o, u, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.TitleBar = void 0, e("./container")), p = e("./metadatalabel"), e = (u = r.Container, i(s, u), s.prototype.configure = function(e2, t2) {
            for (var o2 = this, i2 = (u.prototype.configure.call(this, e2, t2), this.getConfig()), r2 = !this.isHidden(), s2 = true, n2 = function() {
              s2 = false;
              for (var e3 = 0, t3 = o2.getComponents(); e3 < t3.length; e3++) {
                var n3 = t3[e3];
                if (n3 instanceof p.MetadataLabel && !n3.isEmpty()) {
                  s2 = true;
                  break;
                }
              }
              o2.isShown() ? i2.keepHiddenWithoutMetadata && !s2 && o2.hide() : r2 && o2.show();
            }, a = 0, l = this.getComponents(); a < l.length; a++) {
              var c = l[a];
              c instanceof p.MetadataLabel && c.onTextChanged.subscribe(n2);
            }
            t2.onControlsShow.subscribe(function() {
              r2 = true, i2.keepHiddenWithoutMetadata && !s2 || o2.show();
            }), t2.onControlsHide.subscribe(function() {
              r2 = false, o2.hide();
            }), n2();
          }, s);
          function s(e2) {
            var t2 = u.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-titlebar", hidden: true, components: [new p.MetadataLabel({ content: p.MetadataLabelContent.Title }), new p.MetadataLabel({ content: p.MetadataLabelContent.Description })], keepHiddenWithoutMetadata: false }, t2.config), t2;
          }
          n.TitleBar = e;
        }, { "./container": 19, "./metadatalabel": 31 }], 74: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.ToggleButton = void 0, e("./button")), a = e("../eventdispatcher"), e = (i = s.Button, r(l, i), l.prototype.configure = function(e2, t2) {
            i.prototype.configure.call(this, e2, t2);
            e2 = this.getConfig();
            this.getDomElement().addClass(this.prefixCss(e2.offClass)), this.useAriaPressedAttributeAsToggleIndicator = !this.config.onAriaLabel || !this.config.offAriaLabel, this.useAriaPressedAttributeAsToggleIndicator ? this.setAriaAttr("pressed", "false") : this.setAriaLabel(this.config.offAriaLabel);
          }, l.prototype.on = function() {
            var e2;
            this.isOff() && (e2 = this.getConfig(), this.onState = true, this.getDomElement().removeClass(this.prefixCss(e2.offClass)), this.getDomElement().addClass(this.prefixCss(e2.onClass)), this.onToggleEvent(), this.onToggleOnEvent(), this.useAriaPressedAttributeAsToggleIndicator ? this.setAriaAttr("pressed", "true") : this.setAriaLabel(this.config.onAriaLabel));
          }, l.prototype.off = function() {
            var e2;
            this.isOn() && (e2 = this.getConfig(), this.onState = false, this.getDomElement().removeClass(this.prefixCss(e2.onClass)), this.getDomElement().addClass(this.prefixCss(e2.offClass)), this.onToggleEvent(), this.onToggleOffEvent(), this.useAriaPressedAttributeAsToggleIndicator ? this.setAriaAttr("pressed", "false") : this.setAriaLabel(this.config.offAriaLabel));
          }, l.prototype.toggle = function() {
            this.isOn() ? this.off() : this.on();
          }, l.prototype.isOn = function() {
            return this.onState;
          }, l.prototype.isOff = function() {
            return !this.isOn();
          }, l.prototype.onClickEvent = function() {
            i.prototype.onClickEvent.call(this), this.onToggleEvent();
          }, l.prototype.onToggleEvent = function() {
            this.toggleButtonEvents.onToggle.dispatch(this);
          }, l.prototype.onToggleOnEvent = function() {
            this.toggleButtonEvents.onToggleOn.dispatch(this);
          }, l.prototype.onToggleOffEvent = function() {
            this.toggleButtonEvents.onToggleOff.dispatch(this);
          }, Object.defineProperty(l.prototype, "onToggle", { get: function() {
            return this.toggleButtonEvents.onToggle.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "onToggleOn", { get: function() {
            return this.toggleButtonEvents.onToggleOn.getEvent();
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "onToggleOff", { get: function() {
            return this.toggleButtonEvents.onToggleOff.getEvent();
          }, enumerable: false, configurable: true }), l);
          function l(e2) {
            var t2 = i.call(this, e2) || this;
            t2.toggleButtonEvents = { onToggle: new a.EventDispatcher(), onToggleOn: new a.EventDispatcher(), onToggleOff: new a.EventDispatcher() };
            return e2.onAriaLabel && (e2.ariaLabel = e2.onAriaLabel), t2.config = t2.mergeConfig(e2, { cssClass: "ui-togglebutton", onClass: "on", offClass: "off" }, t2.config), t2.useAriaPressedAttributeAsToggleIndicator = !t2.config.onAriaLabel || !t2.config.offAriaLabel, t2;
          }
          n.ToggleButton = e;
        }, { "../eventdispatcher": 86, "./button": 12 }], 75: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.TvNoiseCanvas = void 0, e("./component")), a = e("../dom"), e = (i = s.Component, r(l, i), l.prototype.toDomElement = function() {
            return this.canvas = new a.DOM("canvas", { class: this.getCssClasses() }, this);
          }, l.prototype.start = function() {
            this.canvasElement = this.canvas.get(0), this.canvasContext = this.canvasElement.getContext("2d"), this.noiseAnimationWindowPos = -this.canvasHeight, this.lastFrameUpdate = 0, this.canvasElement.width = this.canvasWidth, this.canvasElement.height = this.canvasHeight, this.renderFrame();
          }, l.prototype.stop = function() {
            (this.useAnimationFrame ? cancelAnimationFrame : clearTimeout)(this.frameUpdateHandlerId);
          }, l.prototype.renderFrame = function() {
            if (!(this.lastFrameUpdate + this.frameInterval > (/* @__PURE__ */ new Date()).getTime())) {
              for (var e2, t2 = this.canvasWidth, n2 = this.canvasHeight, o2 = this.canvasContext.createImageData(t2, n2), i2 = 0; i2 < n2; i2++) for (var r2 = 0; r2 < t2; r2++) o2.data[e2 = t2 * i2 * 4 + 4 * r2] = 255 * Math.random(), (i2 < this.noiseAnimationWindowPos || i2 > this.noiseAnimationWindowPos + this.interferenceHeight) && (o2.data[e2] *= 0.85), o2.data[1 + e2] = o2.data[e2], o2.data[2 + e2] = o2.data[e2], o2.data[3 + e2] = 50;
              this.canvasContext.putImageData(o2, 0, 0), this.lastFrameUpdate = (/* @__PURE__ */ new Date()).getTime(), this.noiseAnimationWindowPos += 7, this.noiseAnimationWindowPos > n2 && (this.noiseAnimationWindowPos = -n2);
            }
            this.scheduleNextRender();
          }, l.prototype.scheduleNextRender = function() {
            this.useAnimationFrame ? this.frameUpdateHandlerId = window.requestAnimationFrame(this.renderFrame.bind(this)) : this.frameUpdateHandlerId = window.setTimeout(this.renderFrame.bind(this), this.frameInterval);
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.canvasWidth = 160, t2.canvasHeight = 90, t2.interferenceHeight = 50, t2.lastFrameUpdate = 0, t2.frameInterval = 60, t2.useAnimationFrame = !!window.requestAnimationFrame, t2.config = t2.mergeConfig(e2, { cssClass: "ui-tvnoisecanvas" }, t2.config), t2;
          }
          n.TvNoiseCanvas = e;
        }, { "../dom": 84, "./component": 18 }], 76: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.UIContainer = void 0, e("./container")), u = e("../dom"), l = e("../timeout"), p = e("../playerutils"), a = e("../eventdispatcher"), c = e("../localization/i18n"), g = e("./button");
          n.UIContainer = (i = s.Container, r(f, i), f.prototype.configure = function(e2, t2) {
            var n2 = this.getConfig();
            n2.userInteractionEventSource ? this.userInteractionEventSource = new u.DOM(n2.userInteractionEventSource) : this.userInteractionEventSource = this.getDomElement(), i.prototype.configure.call(this, e2, t2), this.configureUIShowHide(e2, t2), this.configurePlayerStates(e2, t2);
          }, f.prototype.configureUIShowHide = function(n2, t2) {
            var o2, e2, i2, r2, s2 = this, a2 = this.getConfig();
            -1 === a2.hideDelay ? t2.onConfigured.subscribe(function() {
              return t2.onControlsShow.dispatch(s2);
            }) : (i2 = !(e2 = o2 = false), this.hidingPrevented = function() {
              return a2.hidePlayerStateExceptions && -1 < a2.hidePlayerStateExceptions.indexOf(r2);
            }, this.showUi = function() {
              o2 || (t2.onControlsShow.dispatch(s2), o2 = true), e2 || n2.isCasting() || s2.hidingPrevented() || s2.uiHideTimeout.start();
            }, this.hideUi = function() {
              var e3;
              o2 && !n2.isCasting() && (t2.onPreviewControlsHide.dispatch(s2, e3 = {}), e3.cancel ? s2.showUi() : (t2.onControlsHide.dispatch(s2), o2 = false));
            }, this.uiHideTimeout = new l.Timeout(a2.hideDelay, this.hideUi), this.userInteractionEvents = [{ name: "touchend", handler: function(e3) {
              function t3(e4) {
                function t4(e5) {
                  return !e5 || e5 === s2.userInteractionEventSource.get(0) || e5.component instanceof f ? null : e5.component && e5.component instanceof g.Button ? e5.component : t4(e5.parentElement);
                }
                return !((e4 = t4(e4.target)) && e4.getConfig().acceptsTouchWithUiHidden);
              }
              o2 || (i2 && !n2.isPlaying() ? i2 = false : t3(e3) && e3.preventDefault(), s2.showUi());
            } }, { name: "mouseenter", handler: function() {
              s2.showUi();
            } }, { name: "mousemove", handler: function() {
              s2.showUi();
            } }, { name: "focusin", handler: function() {
              s2.showUi();
            } }, { name: "keydown", handler: function() {
              s2.showUi();
            } }, { name: "mouseleave", handler: function() {
              e2 || s2.hidingPrevented() || (s2.config.hideImmediatelyOnMouseLeave ? s2.hideUi() : s2.uiHideTimeout.start());
            } }], this.userInteractionEvents.forEach(function(e3) {
              return s2.userInteractionEventSource.on(e3.name, e3.handler);
            }), t2.onSeek.subscribe(function() {
              s2.uiHideTimeout.clear(), e2 = true;
            }), t2.onSeeked.subscribe(function() {
              e2 = false, s2.hidingPrevented() || s2.uiHideTimeout.start();
            }), t2.onComponentViewModeChanged.subscribe(function(e3, t3) {
              t3 = t3.mode;
              return s2.trackComponentViewMode(t3);
            }), n2.on(n2.exports.PlayerEvent.CastStarted, function() {
              s2.showUi();
            }), this.playerStateChange.subscribe(function(e3, t3) {
              r2 = t3, s2.hidingPrevented() ? (s2.uiHideTimeout.clear(), s2.showUi()) : s2.uiHideTimeout.start();
            }));
          }, f.prototype.configurePlayerStates = function(e2, t2) {
            var n2, o2, i2 = this, r2 = this.getDomElement(), s2 = [];
            for (n2 in p.PlayerUtils.PlayerState) isNaN(Number(n2)) && (o2 = p.PlayerUtils.PlayerState[p.PlayerUtils.PlayerState[n2]], s2[p.PlayerUtils.PlayerState[n2]] = this.prefixCss(f.STATE_PREFIX + o2.toLowerCase()));
            function a2(e3) {
              c2(), r2.addClass(s2[e3]), i2.playerStateChange.dispatch(i2, e3);
            }
            function l2(e3, t3) {
              r2.removeClass(i2.prefixCss("layout-max-width-400")), r2.removeClass(i2.prefixCss("layout-max-width-600")), r2.removeClass(i2.prefixCss("layout-max-width-800")), r2.removeClass(i2.prefixCss("layout-max-width-1200")), e3 <= 400 ? r2.addClass(i2.prefixCss("layout-max-width-400")) : e3 <= 600 ? r2.addClass(i2.prefixCss("layout-max-width-600")) : e3 <= 800 ? r2.addClass(i2.prefixCss("layout-max-width-800")) : e3 <= 1200 && r2.addClass(i2.prefixCss("layout-max-width-1200"));
            }
            var c2 = function() {
              r2.removeClass(s2[p.PlayerUtils.PlayerState.Idle]), r2.removeClass(s2[p.PlayerUtils.PlayerState.Prepared]), r2.removeClass(s2[p.PlayerUtils.PlayerState.Playing]), r2.removeClass(s2[p.PlayerUtils.PlayerState.Paused]), r2.removeClass(s2[p.PlayerUtils.PlayerState.Finished]);
            };
            e2.on(e2.exports.PlayerEvent.SourceLoaded, function() {
              a2(p.PlayerUtils.PlayerState.Prepared);
            }), e2.on(e2.exports.PlayerEvent.Play, function() {
              a2(p.PlayerUtils.PlayerState.Playing);
            }), e2.on(e2.exports.PlayerEvent.Playing, function() {
              a2(p.PlayerUtils.PlayerState.Playing);
            }), e2.on(e2.exports.PlayerEvent.Paused, function() {
              a2(p.PlayerUtils.PlayerState.Paused);
            }), e2.on(e2.exports.PlayerEvent.PlaybackFinished, function() {
              a2(p.PlayerUtils.PlayerState.Finished);
            }), e2.on(e2.exports.PlayerEvent.SourceUnloaded, function() {
              a2(p.PlayerUtils.PlayerState.Idle);
            }), t2.getConfig().events.onUpdated.subscribe(function() {
              a2(p.PlayerUtils.getState(e2));
            }), e2.on(e2.exports.PlayerEvent.ViewModeChanged, function() {
              e2.getViewMode() === e2.exports.ViewMode.Fullscreen ? r2.addClass(i2.prefixCss(f.FULLSCREEN)) : r2.removeClass(i2.prefixCss(f.FULLSCREEN));
            }), e2.getViewMode() === e2.exports.ViewMode.Fullscreen && r2.addClass(this.prefixCss(f.FULLSCREEN)), e2.on(e2.exports.PlayerEvent.StallStarted, function() {
              r2.addClass(i2.prefixCss(f.BUFFERING));
            }), e2.on(e2.exports.PlayerEvent.StallEnded, function() {
              r2.removeClass(i2.prefixCss(f.BUFFERING));
            }), e2.isStalled() && r2.addClass(this.prefixCss(f.BUFFERING)), e2.on(e2.exports.PlayerEvent.CastStarted, function() {
              r2.addClass(i2.prefixCss(f.REMOTE_CONTROL));
            }), e2.on(e2.exports.PlayerEvent.CastStopped, function() {
              r2.removeClass(i2.prefixCss(f.REMOTE_CONTROL));
            }), e2.isCasting() && r2.addClass(this.prefixCss(f.REMOTE_CONTROL)), t2.onControlsShow.subscribe(function() {
              r2.removeClass(i2.prefixCss(f.CONTROLS_HIDDEN)), r2.addClass(i2.prefixCss(f.CONTROLS_SHOWN));
            }), t2.onControlsHide.subscribe(function() {
              r2.removeClass(i2.prefixCss(f.CONTROLS_SHOWN)), r2.addClass(i2.prefixCss(f.CONTROLS_HIDDEN));
            });
            e2.on(e2.exports.PlayerEvent.PlayerResized, function(e3) {
              var t3 = Math.round(Number(e3.width.substring(0, e3.width.length - 2)));
              Math.round(Number(e3.height.substring(0, e3.height.length - 2)));
              l2(t3);
            }), l2(new u.DOM(e2.getContainer()).width(), new u.DOM(e2.getContainer()).height());
          }, f.prototype.release = function() {
            var t2 = this;
            this.userInteractionEvents && this.userInteractionEvents.forEach(function(e2) {
              return t2.userInteractionEventSource.off(e2.name, e2.handler);
            }), i.prototype.release.call(this), this.uiHideTimeout && this.uiHideTimeout.clear();
          }, f.prototype.onPlayerStateChange = function() {
            return this.playerStateChange.getEvent();
          }, f.prototype.suspendHideTimeout = function() {
            this.uiHideTimeout.suspend();
          }, f.prototype.resumeHideTimeout = function() {
            this.uiHideTimeout.resume(!this.hidingPrevented());
          }, f.prototype.toDomElement = function() {
            var e2 = i.prototype.toDomElement.call(this);
            return document && void 0 !== document.createElement("p").style.flex ? e2.addClass(this.prefixCss("flexbox")) : e2.addClass(this.prefixCss("no-flexbox")), e2;
          }, f.STATE_PREFIX = "player-state-", f.FULLSCREEN = "fullscreen", f.BUFFERING = "buffering", f.REMOTE_CONTROL = "remote-control", f.CONTROLS_SHOWN = "controls-shown", f.CONTROLS_HIDDEN = "controls-hidden", f);
          function f(e2) {
            var t2 = i.call(this, e2) || this;
            return t2.hideUi = function() {
            }, t2.showUi = function() {
            }, t2.config = t2.mergeConfig(e2, { cssClass: "ui-uicontainer", role: "region", ariaLabel: c.i18n.getLocalizer("player"), hideDelay: 5e3, hideImmediatelyOnMouseLeave: false }, t2.config), t2.playerStateChange = new a.EventDispatcher(), t2.hidingPrevented = function() {
              return false;
            }, t2;
          }
        }, { "../dom": 84, "../eventdispatcher": 86, "../localization/i18n": 91, "../playerutils": 98, "../timeout": 113, "./button": 12, "./container": 19 }], 77: [function(e, t, n) {
          "use strict";
          var o, a, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.VideoQualitySelectBox = void 0, e("./selectbox")), l = e("../localization/i18n"), e = (a = r.SelectBox, i(s, a), s.prototype.configure = function(i2, e2) {
            function t2() {
              var e3 = i2.getAvailableVideoQualities();
              r2.clearItems(), r2.hasAuto = "progressive" !== i2.getStreamType(), r2.hasAuto && r2.addItem("auto", l.i18n.getLocalizer("auto"));
              for (var t3 = 0, n2 = e3; t3 < n2.length; t3++) {
                var o2 = n2[t3];
                r2.addItem(o2.id, o2.label);
              }
              s2();
            }
            var r2 = this, s2 = (a.prototype.configure.call(this, i2, e2), function() {
              r2.selectItem(i2.getVideoQuality().id);
            });
            this.onItemSelected.subscribe(function(e3, t3) {
              i2.setVideoQuality(t3);
            }), i2.on(i2.exports.PlayerEvent.SourceUnloaded, t2), i2.on(i2.exports.PlayerEvent.PeriodSwitched, t2), i2.on(i2.exports.PlayerEvent.VideoQualityChanged, s2), i2.exports.PlayerEvent.VideoQualityAdded && (i2.on(i2.exports.PlayerEvent.VideoQualityAdded, t2), i2.on(i2.exports.PlayerEvent.VideoQualityRemoved, t2)), e2.getConfig().events.onUpdated.subscribe(t2);
          }, s.prototype.hasAutoItem = function() {
            return this.hasAuto;
          }, s);
          function s(e2) {
            var t2 = a.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClasses: ["ui-videoqualityselectbox"] }, t2.config), t2;
          }
          n.VideoQualitySelectBox = e;
        }, { "../localization/i18n": 91, "./selectbox": 44 }], 78: [function(e, t, n) {
          "use strict";
          var o, r, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.VolumeControlButton = void 0, e("./container")), a = e("./volumeslider"), l = e("./volumetogglebutton"), c = e("../timeout"), e = (r = s.Container, i(u, r), u.prototype.configure = function(e2, t2) {
            var n2 = this, e2 = (r.prototype.configure.call(this, e2, t2), this.getVolumeToggleButton()), o2 = this.getVolumeSlider(), i2 = (this.volumeSliderHideTimeout = new c.Timeout(this.getConfig().hideDelay, function() {
              o2.hide();
            }), false);
            e2.getDomElement().on("mouseenter", function() {
              o2.isHidden() && o2.show(), n2.volumeSliderHideTimeout.clear();
            }), e2.getDomElement().on("mouseleave", function() {
              n2.volumeSliderHideTimeout.reset();
            }), o2.getDomElement().on("mouseenter", function() {
              n2.volumeSliderHideTimeout.clear(), i2 = true;
            }), o2.getDomElement().on("mouseleave", function() {
              o2.isSeeking() ? n2.volumeSliderHideTimeout.clear() : n2.volumeSliderHideTimeout.reset(), i2 = false;
            }), o2.onSeeked.subscribe(function() {
              i2 || n2.volumeSliderHideTimeout.reset();
            });
          }, u.prototype.release = function() {
            r.prototype.release.call(this), this.volumeSliderHideTimeout.clear();
          }, u.prototype.getVolumeToggleButton = function() {
            return this.volumeToggleButton;
          }, u.prototype.getVolumeSlider = function() {
            return this.volumeSlider;
          }, u);
          function u(e2) {
            var t2 = r.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.volumeToggleButton = new l.VolumeToggleButton(), t2.volumeSlider = new a.VolumeSlider({ vertical: null == e2.vertical || e2.vertical, hidden: true }), t2.config = t2.mergeConfig(e2, { cssClass: "ui-volumecontrolbutton", components: [t2.volumeToggleButton, t2.volumeSlider], hideDelay: 500 }, t2.config), t2;
          }
          n.VolumeControlButton = e;
        }, { "../timeout": 113, "./container": 19, "./volumeslider": 79, "./volumetogglebutton": 80 }], 79: [function(e, t, n) {
          "use strict";
          var o, r, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.VolumeSlider = void 0, e("./seekbar")), a = e("../localization/i18n"), e = (r = s.SeekBar, i(l, r), l.prototype.setVolumeAriaSliderValues = function(e2) {
            this.getDomElement().attr("aria-valuenow", Math.ceil(e2).toString()), this.getDomElement().attr("aria-valuetext", "".concat(a.i18n.performLocalization(a.i18n.getLocalizer("seekBar.value")), ": ").concat(Math.ceil(e2)));
          }, l.prototype.configure = function(e2, t2) {
            var n2 = this, o2 = (r.prototype.configure.call(this, e2, t2, false), this.setAriaSliderMinMax("0", "100"), this.getConfig()), i2 = t2.getConfig().volumeController;
            o2.hideIfVolumeControlProhibited && !this.detectVolumeControlAvailability() ? this.hide() : (i2.onChanged.subscribe(function(e3, t3) {
              t3.muted ? (n2.setVolumeAriaSliderValues(0), n2.setPlaybackPosition(0)) : (n2.setPlaybackPosition(t3.volume), n2.setVolumeAriaSliderValues(t3.volume));
            }), this.onSeek.subscribe(function() {
              n2.volumeTransition = i2.startTransition();
            }), this.onSeekPreview.subscribeRateLimited(this.updateVolumeWhileScrubbing, 50), this.onSeeked.subscribe(function(e3, t3) {
              n2.volumeTransition && n2.volumeTransition.finish(t3);
            }), e2.on(e2.exports.PlayerEvent.PlayerResized, function() {
              n2.refreshPlaybackPosition();
            }), t2.onConfigured.subscribe(function() {
              n2.refreshPlaybackPosition();
            }), t2.getConfig().events.onUpdated.subscribe(function() {
              n2.refreshPlaybackPosition();
            }), t2.onComponentShow.subscribe(function() {
              n2.refreshPlaybackPosition();
            }), t2.onComponentHide.subscribe(function() {
              n2.refreshPlaybackPosition();
            }), i2.onChangedEvent());
          }, l.prototype.detectVolumeControlAvailability = function() {
            var e2 = document.createElement("video");
            return e2.volume = 0.7, 1 !== e2.volume;
          }, l.prototype.release = function() {
            r.prototype.release.call(this), this.onSeekPreview.unsubscribe(this.updateVolumeWhileScrubbing);
          }, l);
          function l(e2) {
            var n2 = r.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return n2.updateVolumeWhileScrubbing = function(e3, t2) {
              t2.scrubbing && n2.volumeTransition && n2.volumeTransition.update(t2.position);
            }, n2.config = n2.mergeConfig(e2, { cssClass: "ui-volumeslider", hideIfVolumeControlProhibited: true, ariaLabel: a.i18n.getLocalizer("settings.audio.volume"), tabIndex: 0 }, n2.config), n2;
          }
          n.VolumeSlider = e;
        }, { "../localization/i18n": 91, "./seekbar": 40 }], 80: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.VolumeToggleButton = void 0, e("./togglebutton")), a = e("../localization/i18n"), e = (i = s.ToggleButton, r(l, i), l.prototype.configure = function(e2, t2) {
            var n2 = this, o2 = (i.prototype.configure.call(this, e2, t2), t2.getConfig().volumeController);
            o2.onChanged.subscribe(function(e3, t3) {
              t3.muted ? n2.on() : n2.off();
              t3 = Math.ceil(t3.volume / 10);
              n2.getDomElement().data(n2.prefixCss("volume-level-tens"), String(t3));
            }), this.onClick.subscribe(function() {
              o2.toggleMuted();
            }), o2.onChangedEvent();
          }, l);
          function l(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this, n2 = { cssClass: "ui-volumetogglebutton", text: a.i18n.getLocalizer("settings.audio.mute"), onClass: "muted", offClass: "unmuted", ariaLabel: a.i18n.getLocalizer("settings.audio.mute") };
            return t2.config = t2.mergeConfig(e2, n2, t2.config), t2;
          }
          n.VolumeToggleButton = e;
        }, { "../localization/i18n": 91, "./togglebutton": 74 }], 81: [function(e, t, n) {
          "use strict";
          var o, a, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), r = (Object.defineProperty(n, "__esModule", { value: true }), n.VRToggleButton = void 0, e("./togglebutton")), s = e("../localization/i18n"), e = (a = r.ToggleButton, i(l, a), l.prototype.configure = function(t2, e2) {
            function n2(e3) {
              e3.type === t2.exports.PlayerEvent.Warning && e3.code !== t2.exports.WarningCode.VR_RENDERING_ERROR || (r2() && s2() ? (i2.show(), t2.vr && t2.vr.getStereo() ? i2.on() : i2.off()) : i2.hide());
            }
            function o2() {
              r2() ? i2.show() : i2.hide();
            }
            var i2 = this, r2 = (a.prototype.configure.call(this, t2, e2), function() {
              var e3 = t2.getSource();
              return e3 && Boolean(e3.vr);
            }), s2 = function() {
              var e3 = t2.getSource();
              return t2.vr && Boolean(e3.vr);
            };
            t2.on(t2.exports.PlayerEvent.VRStereoChanged, n2), t2.on(t2.exports.PlayerEvent.Warning, n2), t2.on(t2.exports.PlayerEvent.SourceUnloaded, o2), e2.getConfig().events.onUpdated.subscribe(o2), this.onClick.subscribe(function() {
              s2() ? t2.vr && t2.vr.getStereo() ? t2.vr.setStereo(false) : t2.vr.setStereo(true) : console && console.log("No VR content");
            }), o2();
          }, l);
          function l(e2) {
            var t2 = a.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-vrtogglebutton", text: s.i18n.getLocalizer("vr") }, t2.config), t2;
          }
          n.VRToggleButton = e;
        }, { "../localization/i18n": 91, "./togglebutton": 74 }], 82: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = (Object.defineProperty(n, "__esModule", { value: true }), n.Watermark = void 0, e("./clickoverlay")), a = e("../localization/i18n"), e = (i = s.ClickOverlay, r(l, i), l);
          function l(e2) {
            var t2 = i.call(this, e2 = void 0 === e2 ? {} : e2) || this;
            return t2.config = t2.mergeConfig(e2, { cssClass: "ui-watermark", url: "http://bitmovin.com", role: "link", text: "logo", ariaLabel: a.i18n.getLocalizer("watermarkLink") }, t2.config), t2;
          }
          n.Watermark = e;
        }, { "../localization/i18n": 91, "./clickoverlay": 16 }], 83: [function(e, n, t) {
          "use strict";
          Object.defineProperty(t, "__esModule", { value: true }), t.DemoFactory = void 0;
          var r = e("./components/vrtogglebutton"), s = e("./components/settingstogglebutton"), a = e("./components/volumeslider"), l = e("./components/playbacktimelabel"), c = e("./components/airplaytogglebutton"), u = e("./components/errormessageoverlay"), p = e("./components/controlbar"), g = e("./components/casttogglebutton"), f = e("./components/fullscreentogglebutton"), d = e("./components/recommendationoverlay"), h = e("./components/playbackspeedselectbox"), y = e("./components/audioqualityselectbox"), m = e("./components/caststatusoverlay"), b = e("./components/uicontainer"), v = e("./components/watermark"), C = e("./components/subtitleoverlay"), S = e("./components/settingspanel"), P = e("./components/seekbarlabel"), w = e("./components/playbacktoggleoverlay"), E = e("./components/pictureinpicturetogglebutton"), _ = e("./components/spacer"), O = e("./components/container"), k = e("./components/volumetogglebutton"), x = e("./components/playbacktogglebutton"), T = e("./components/seekbar"), M = e("./components/videoqualityselectbox"), L = e("./uimanager"), A = e("./components/titlebar"), I = e("./components/bufferingoverlay"), B = e("./components/subtitlelistbox"), R = e("./components/audiotracklistbox"), j = e("./components/settingspanelitem"), D = e("./components/settingspanelpage"), U = e("./uifactory"), z = e("./main");
          (t.DemoFactory || (t.DemoFactory = {})).buildDemoWithSeparateAudioSubtitlesButtons = function(e2, t2) {
            var n2, o, i;
            return void 0 === t2 && (t2 = {}), new L.UIManager(e2, [{ ui: U.UIFactory.modernSmallScreenAdsUI(), condition: function(e3) {
              return e3.isMobile && e3.documentWidth < 600 && e3.isAd && e3.adRequiresUi;
            } }, { ui: U.UIFactory.modernAdsUI(), condition: function(e3) {
              return e3.isAd && e3.adRequiresUi;
            } }, { ui: U.UIFactory.modernSmallScreenUI(), condition: function(e3) {
              return e3.isMobile && e3.documentWidth < 600;
            } }, { ui: (e2 = new C.SubtitleOverlay(), n2 = new S.SettingsPanel({ components: [new D.SettingsPanelPage({ components: [new j.SettingsPanelItem("Video Quality", new M.VideoQualitySelectBox()), new j.SettingsPanelItem("Speed", new h.PlaybackSpeedSelectBox()), new j.SettingsPanelItem("Audio Quality", new y.AudioQualitySelectBox())] })], hidden: true }), o = new B.SubtitleListBox(), o = new S.SettingsPanel({ components: [new D.SettingsPanelPage({ components: [new j.SettingsPanelItem(null, o)] })], hidden: true }), i = new R.AudioTrackListBox(), i = new S.SettingsPanel({ components: [new D.SettingsPanelPage({ components: [new j.SettingsPanelItem(null, i)] })], hidden: true }), i = new p.ControlBar({ components: [i, o, n2, new O.Container({ components: [new l.PlaybackTimeLabel({ timeLabelMode: l.PlaybackTimeLabelMode.CurrentTime, hideInLivePlayback: true }), new T.SeekBar({ label: new P.SeekBarLabel() }), new l.PlaybackTimeLabel({ timeLabelMode: l.PlaybackTimeLabelMode.TotalTime, cssClasses: ["text-right"] })], cssClasses: ["controlbar-top"] }), new O.Container({ components: [new x.PlaybackToggleButton(), new z.QuickSeekButton({ seekSeconds: -10 }), new z.QuickSeekButton({ seekSeconds: 10 }), new k.VolumeToggleButton(), new a.VolumeSlider(), new _.Spacer(), new E.PictureInPictureToggleButton(), new c.AirPlayToggleButton(), new g.CastToggleButton(), new r.VRToggleButton(), new s.SettingsToggleButton({ settingsPanel: i, cssClass: "ui-audiotracksettingstogglebutton" }), new s.SettingsToggleButton({ settingsPanel: o, cssClass: "ui-subtitlesettingstogglebutton" }), new s.SettingsToggleButton({ settingsPanel: n2 }), new f.FullscreenToggleButton()], cssClasses: ["controlbar-bottom"] })] }), new b.UIContainer({ components: [e2, new I.BufferingOverlay(), new w.PlaybackToggleOverlay(), new m.CastStatusOverlay(), i, new A.TitleBar(), new d.RecommendationOverlay(), new v.Watermark(), new u.ErrorMessageOverlay()] })) }], t2);
          };
        }, { "./components/airplaytogglebutton": 7, "./components/audioqualityselectbox": 8, "./components/audiotracklistbox": 9, "./components/bufferingoverlay": 11, "./components/caststatusoverlay": 13, "./components/casttogglebutton": 14, "./components/container": 19, "./components/controlbar": 20, "./components/errormessageoverlay": 23, "./components/fullscreentogglebutton": 24, "./components/pictureinpicturetogglebutton": 32, "./components/playbackspeedselectbox": 33, "./components/playbacktimelabel": 34, "./components/playbacktogglebutton": 35, "./components/playbacktoggleoverlay": 36, "./components/recommendationoverlay": 38, "./components/seekbar": 40, "./components/seekbarlabel": 43, "./components/settingspanel": 45, "./components/settingspanelitem": 46, "./components/settingspanelpage": 47, "./components/settingstogglebutton": 51, "./components/spacer": 52, "./components/subtitlelistbox": 53, "./components/subtitleoverlay": 54, "./components/titlebar": 73, "./components/uicontainer": 76, "./components/videoqualityselectbox": 77, "./components/volumeslider": 79, "./components/volumetogglebutton": 80, "./components/vrtogglebutton": 81, "./components/watermark": 82, "./main": 96, "./uifactory": 115, "./uimanager": 116 }], 84: [function(e, t, n) {
          "use strict";
          function o(e2, t2, n2) {
            if (this.document = document, e2 instanceof Array) 0 < e2.length && e2[0] instanceof HTMLElement && (this.elements = e2);
            else if (e2 instanceof HTMLElement) {
              var o2 = e2;
              this.elements = [o2];
            } else if (e2 instanceof Document) this.elements = null;
            else if (t2) {
              var i, o2 = document.createElement(e2);
              for (i in t2) {
                var r = t2[i];
                null != r && o2.setAttribute(i, r);
              }
              n2 && (o2.component = n2), this.elements = [o2];
            } else this.elements = this.findChildElements(e2);
          }
          Object.defineProperty(n, "__esModule", { value: true }), n.DOM = void 0, Object.defineProperty(o.prototype, "length", { get: function() {
            return this.elements ? this.elements.length : 0;
          }, enumerable: false, configurable: true }), o.prototype.get = function(e2) {
            return void 0 === e2 ? this.elements : !this.elements || e2 >= this.elements.length || e2 < -this.elements.length ? void 0 : e2 < 0 ? this.elements[this.elements.length - e2] : this.elements[e2];
          }, o.prototype.forEach = function(t2) {
            this.elements && this.elements.forEach(function(e2) {
              t2(e2);
            });
          }, o.prototype.findChildElementsOfElement = function(e2, t2) {
            e2 = e2.querySelectorAll(t2);
            return [].slice.call(e2);
          }, o.prototype.findChildElements = function(t2) {
            var n2 = this, o2 = [];
            return this.elements ? (this.forEach(function(e2) {
              o2 = o2.concat(n2.findChildElementsOfElement(e2, t2));
            }), o2) : this.findChildElementsOfElement(document, t2);
          }, o.prototype.find = function(e2) {
            return new o(this.findChildElements(e2));
          }, o.prototype.focusToFirstInput = function() {
            var e2 = this.findChildElements('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            0 < e2.length && e2[0].focus();
          }, o.prototype.scrollTo = function(e2, t2) {
            this.elements[0].scrollTo(e2, t2);
          }, o.prototype.html = function(e2) {
            return 0 < arguments.length ? this.setHtml(e2) : this.getHtml();
          }, o.prototype.getHtml = function() {
            return this.elements[0].innerHTML;
          }, o.prototype.setHtml = function(t2) {
            return void 0 !== t2 && null != t2 || (t2 = ""), this.forEach(function(e2) {
              e2.innerHTML = t2;
            }), this;
          }, o.prototype.empty = function() {
            return this.forEach(function(e2) {
              e2.innerHTML = "";
            }), this;
          }, o.prototype.val = function() {
            var e2 = this.elements[0];
            if (e2 instanceof HTMLSelectElement || e2 instanceof HTMLInputElement) return e2.value;
            throw new Error("val() not supported for ".concat(typeof e2));
          }, o.prototype.attr = function(e2, t2) {
            return 1 < arguments.length ? this.setAttr(e2, t2) : this.getAttr(e2);
          }, o.prototype.removeAttr = function(t2) {
            this.forEach(function(e2) {
              e2.removeAttribute(t2);
            });
          }, o.prototype.getAttr = function(e2) {
            return this.elements[0].getAttribute(e2);
          }, o.prototype.setAttr = function(t2, n2) {
            return this.forEach(function(e2) {
              e2.setAttribute(t2, n2);
            }), this;
          }, o.prototype.data = function(e2, t2) {
            return 1 < arguments.length ? this.setData(e2, t2) : this.getData(e2);
          }, o.prototype.getData = function(e2) {
            return this.elements[0].getAttribute("data-" + e2);
          }, o.prototype.setData = function(t2, n2) {
            return this.forEach(function(e2) {
              e2.setAttribute("data-" + t2, n2);
            }), this;
          }, o.prototype.append = function() {
            for (var e2 = [], t2 = 0; t2 < arguments.length; t2++) e2[t2] = arguments[t2];
            return this.forEach(function(o2) {
              e2.forEach(function(n2) {
                n2.elements.forEach(function(e3, t3) {
                  o2.appendChild(n2.elements[t3]);
                });
              });
            }), this;
          }, o.prototype.remove = function() {
            this.forEach(function(e2) {
              var t2 = e2.parentNode;
              t2 && t2.removeChild(e2);
            });
          }, o.prototype.offset = function() {
            var e2 = this.elements[0].getBoundingClientRect(), t2 = document.body.parentElement.getBoundingClientRect();
            return { top: e2.top - t2.top, left: e2.left - t2.left };
          }, o.prototype.width = function() {
            return this.elements[0].offsetWidth;
          }, o.prototype.height = function() {
            return this.elements[0].offsetHeight;
          }, o.prototype.size = function() {
            return { width: this.width(), height: this.height() };
          }, o.prototype.on = function(e2, n2, o2) {
            var i = this;
            return e2.split(" ").forEach(function(t2) {
              null == i.elements ? i.document.addEventListener(t2, n2, o2) : i.forEach(function(e3) {
                e3.addEventListener(t2, n2, o2);
              });
            }), this;
          }, o.prototype.off = function(e2, n2, o2) {
            var i = this;
            return e2.split(" ").forEach(function(t2) {
              null == i.elements ? i.document.removeEventListener(t2, n2, o2) : i.forEach(function(e3) {
                e3.removeEventListener(t2, n2, o2);
              });
            }), this;
          }, o.prototype.addClass = function(o2) {
            return this.forEach(function(e2) {
              var t2, n2;
              e2.classList ? 0 < (n2 = o2.split(" ").filter(function(e3) {
                return 0 < e3.length;
              })).length && (t2 = e2.classList).add.apply(t2, n2) : e2.className += " " + o2;
            }), this;
          }, o.prototype.removeClass = function(o2) {
            return this.forEach(function(e2) {
              var t2, n2;
              e2.classList ? 0 < (n2 = o2.split(" ").filter(function(e3) {
                return 0 < e3.length;
              })).length && (t2 = e2.classList).remove.apply(t2, n2) : e2.className = e2.className.replace(new RegExp("(^|\\b)" + o2.split(" ").join("|") + "(\\b|$)", "gi"), " ");
            }), this;
          }, o.prototype.hasClass = function(t2) {
            var n2 = false;
            return this.forEach(function(e2) {
              e2.classList ? e2.classList.contains(t2) && (n2 = true) : new RegExp("(^| )" + t2 + "( |$)", "gi").test(e2.className) && (n2 = true);
            }), n2;
          }, o.prototype.css = function(e2, t2) {
            var n2;
            return "string" == typeof e2 ? (n2 = e2, 2 === arguments.length ? this.setCss(n2, t2) : this.getCss(n2)) : this.setCssCollection(e2);
          }, o.prototype.removeCss = function(e2, t2) {
            return this.elements[t2 = void 0 === t2 ? 0 : t2].style.removeProperty(e2);
          }, o.prototype.getCss = function(e2) {
            return getComputedStyle(this.elements[0])[e2];
          }, o.prototype.setCss = function(t2, n2) {
            return this.forEach(function(e2) {
              e2.style[t2] = n2;
            }), this;
          }, o.prototype.setCssCollection = function(t2) {
            return this.forEach(function(e2) {
              Object.assign(e2.style, t2);
            }), this;
          }, n.DOM = o;
        }, {}], 85: [function(e, t, n) {
          "use strict";
          var o;
          Object.defineProperty(n, "__esModule", { value: true }), n.ErrorUtils = void 0, (o = n.ErrorUtils || (n.ErrorUtils = {})).defaultErrorMessages = { 1e3: "Error is unknown", 1001: "The player API is not available after a call to PlayerAPI.destroy.", 1100: "General setup error", 1101: "There was an error when inserting the HTML video element", 1102: "No configuration was provided", 1103: "The license is not valid", 1104: "The the domain-locked player is not authorized to playback on this domain", 1105: "The domain is not allowlisted", 1106: "The license server URL is invalid", 1107: "The impression server URL is invalid", 1108: "Could not initialize a rendering engine", 1109: "The used flash version does not support playback", 1110: "Native Flash is not authorized by a valid Adobe token", 1111: "Flash doesn't have sufficient resources", 1112: "Flash container API not available", 1113: 'Protocol not supported. This site has been loaded using "file" protocol, but unfortunately this is not supported. Please load the page using a web server (using http or https)', 1200: "General source error", 1201: "No valid source was provided", 1202: "The downloaded manifest is invalid", 1203: "There was no technology detected to playback the provided source", 1204: "The stream type is not supported", 1205: "The forced technology is not supported", 1206: "No stream found for supported technologies.", 1207: "The downloaded segment is empty", 1208: "The manifest could not be loaded", 1209: "Progressive stream type not supported or the stream has an error", 1210: "HLS stream has an error", 1211: "The encryption method is not supported", 1300: "General playback error", 1301: "Video decoder or demuxer had an error with the content", 1302: "General error if Flash renderer has an error", 1303: "Flash doesn't have sufficient resources", 1304: "The transmuxer could not be initialized", 1400: "Network error while downloading", 1401: "The manifest download timed out", 1402: "The segment download timed out", 1403: "The progressive stream download timed out", 1404: "The Certificate could not be loaded", 2e3: "General DRM error", 2001: "Required DRM configuration is missing", 2002: "The licensing server URL is missing", 2003: "License request failed", 2004: "Key or KeyId is missing", 2005: "Key size is not supported", 2006: "Unable to instantiate a key system supporting the required combinations", 2007: "Unable to create or initialize key session", 2008: "The MediaKey object could not be created/initialized", 2009: "Key error", 2010: "The key system is not supported", 2011: "The certificate is not valid", 2012: "Invalid header key/value pair for PlayReady license request", 2013: "Content cannot be played back because the output is restricted on this machine", 2014: "DRM error for the Flash renderer", 2100: "General VR error", 2101: "Player technology not compatible with VR playback", 3e3: "General module error", 3001: "The definition of the module is invalid (e.g. incomplete).", 3002: "The module definition specifies dependencies but the module is not provided via a function for deferred loading.", 3003: "A module cannot be loaded because it has not been added to the player core.", 3004: "A module cannot be loaded because one or more dependencies are missing.", 3100: "An Advertising module error has occurred. Refer to the attached AdvertisingError." }, o.defaultMobileV3ErrorMessageTranslator = function(e2) {
            return e2.message;
          }, o.defaultWebErrorMessageTranslator = function(e2) {
            var t2 = o.defaultErrorMessages[e2.code];
            return t2 ? "".concat(t2, "\n(").concat(e2.name, ")") : "".concat(e2.code, " ").concat(e2.name);
          };
        }, {}], 86: [function(e, t, n) {
          "use strict";
          var o, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), l = (Object.defineProperty(n, "__esModule", { value: true }), n.EventDispatcher = void 0, e("./arrayutils")), r = e("./timeout");
          function s() {
            this.listeners = [];
          }
          s.prototype.subscribe = function(e2) {
            this.listeners.push(new a(e2));
          }, s.prototype.subscribeOnce = function(e2) {
            this.listeners.push(new a(e2, true));
          }, s.prototype.subscribeRateLimited = function(e2, t2) {
            this.listeners.push(new p(e2, t2));
          }, s.prototype.unsubscribe = function(e2) {
            for (var t2 = 0; t2 < this.listeners.length; t2++) {
              var n2 = this.listeners[t2];
              if (n2.listener === e2) return n2.clear(), l.ArrayUtils.remove(this.listeners, n2), true;
            }
            return false;
          }, s.prototype.unsubscribeAll = function() {
            for (var e2 = 0, t2 = this.listeners; e2 < t2.length; e2++) t2[e2].clear();
            this.listeners = [];
          }, s.prototype.dispatch = function(e2, t2) {
            void 0 === t2 && (t2 = null);
            for (var n2 = [], o2 = 0, i2 = this.listeners.slice(0); o2 < i2.length; o2++) {
              var r2 = i2[o2];
              r2.fire(e2, t2), r2.isOnce() && n2.push(r2);
            }
            for (var s2 = 0, a2 = n2; s2 < a2.length; s2++) l.ArrayUtils.remove(this.listeners, a2[s2]);
          }, s.prototype.getEvent = function() {
            return this;
          }, n.EventDispatcher = s;
          Object.defineProperty(c.prototype, "listener", { get: function() {
            return this.eventListener;
          }, enumerable: false, configurable: true }), c.prototype.fire = function(e2, t2) {
            this.eventListener(e2, t2);
          }, c.prototype.isOnce = function() {
            return this.once;
          }, c.prototype.clear = function() {
          };
          var a = c;
          function c(e2, t2) {
            void 0 === t2 && (t2 = false), this.eventListener = e2, this.once = t2;
          }
          i(g, u = a), g.prototype.shouldFireEvent = function() {
            return !this.rateLimitTimout.isActive();
          }, g.prototype.fireSuper = function(e2, t2) {
            u.prototype.fire.call(this, e2, t2);
          }, g.prototype.fire = function(e2, t2) {
            this.rateLimitingEventListener(e2, t2);
          }, g.prototype.clear = function() {
            u.prototype.clear.call(this), this.rateLimitTimout.clear();
          };
          var u, p = g;
          function g(e2, t2) {
            function n2() {
              o2.rateLimitTimout.start();
            }
            var o2 = u.call(this, e2) || this;
            o2.rateMs = t2;
            return o2.rateLimitTimout = new r.Timeout(o2.rateMs, function() {
              o2.lastSeenEvent && (o2.fireSuper(o2.lastSeenEvent.sender, o2.lastSeenEvent.args), n2(), o2.lastSeenEvent = null);
            }), o2.rateLimitingEventListener = function(e3, t3) {
              o2.shouldFireEvent() ? (o2.fireSuper(e3, t3), n2()) : o2.lastSeenEvent = { sender: e3, args: t3 };
            }, o2;
          }
        }, { "./arrayutils": 1, "./timeout": 113 }], 87: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.FocusVisibilityTracker = void 0;
          var o = "bmpui-focus-visible";
          function i(e2) {
            var n2 = this;
            this.bitmovinUiPrefix = e2, this.lastInteractionWasKeyboard = true, this.onKeyDown = function(e3) {
              e3.metaKey || e3.altKey || e3.ctrlKey || (n2.lastInteractionWasKeyboard = true);
            }, this.onMouseOrPointerOrTouch = function() {
              return n2.lastInteractionWasKeyboard = false;
            }, this.onFocus = function(e3) {
              var t2, e3 = e3.target;
              n2.lastInteractionWasKeyboard && r(e3) && (t2 = n2.bitmovinUiPrefix, 0 === e3.id.indexOf(t2)) && !e3.classList.contains(o) && e3.classList.add(o);
            }, this.onBlur = function(e3) {
              e3 = e3.target;
              r(e3) && e3.classList.remove(o);
            }, this.eventHandlerMap = { mousedown: this.onMouseOrPointerOrTouch, pointerdown: this.onMouseOrPointerOrTouch, touchstart: this.onMouseOrPointerOrTouch, keydown: this.onKeyDown, focus: this.onFocus, blur: this.onBlur }, this.registerEventListeners();
          }
          function r(e2) {
            return e2 instanceof HTMLElement && e2.classList instanceof DOMTokenList;
          }
          i.prototype.registerEventListeners = function() {
            for (var e2 in this.eventHandlerMap) document.addEventListener(e2, this.eventHandlerMap[e2], true);
          }, i.prototype.unregisterEventListeners = function() {
            for (var e2 in this.eventHandlerMap) document.removeEventListener(e2, this.eventHandlerMap[e2], true);
          }, i.prototype.release = function() {
            this.unregisterEventListeners();
          }, n.FocusVisibilityTracker = i;
        }, {}], 88: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.GroupPlaybackSuspensionReason = void 0, (n.GroupPlaybackSuspensionReason || (n.GroupPlaybackSuspensionReason = {})).UserIsScrubbing = "userIsScrubbing";
        }, {}], 89: [function(e, t, n) {
          "use strict";
          var o;
          Object.defineProperty(n, "__esModule", { value: true }), n.Guid = void 0, n = n.Guid || (n.Guid = {}), o = 1, n.next = function() {
            return o++;
          };
        }, {}], 90: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.ImageLoader = void 0;
          var r = e("./dom");
          function o() {
            this.state = {};
          }
          o.prototype.load = function(e2, t2) {
            var n2, o2, i = this;
            this.state[e2] ? ((n2 = this.state[e2]).loadedCallback = t2, n2.loaded && this.callLoadedCallback(n2)) : (o2 = { url: e2, image: new r.DOM("img", {}), loadedCallback: t2, loaded: false, width: 0, height: 0 }, (this.state[e2] = o2).image.on("load", function(e3) {
              o2.loaded = true, o2.width = o2.image.get(0).width, o2.height = o2.image.get(0).height, i.callLoadedCallback(o2);
            }), o2.image.attr("src", o2.url));
          }, o.prototype.callLoadedCallback = function(e2) {
            e2.loadedCallback(e2.url, e2.width, e2.height);
          }, n.ImageLoader = o;
        }, { "./dom": 84 }], 91: [function(e, t, r) {
          "use strict";
          var s = this && this.__assign || function() {
            return (s = Object.assign || function(e2) {
              for (var t2, n2 = 1, o2 = arguments.length; n2 < o2; n2++) for (var i2 in t2 = arguments[n2]) Object.prototype.hasOwnProperty.call(t2, i2) && (e2[i2] = t2[i2]);
              return e2;
            }).apply(this, arguments);
          }, n = this && this.__importDefault || function(e2) {
            return e2 && e2.__esModule ? e2 : { default: e2 };
          }, o = (Object.defineProperty(r, "__esModule", { value: true }), r.i18n = r.I18n = r.defaultVocabularies = void 0, n(e("./languages/de.json"))), i = n(e("./languages/en.json")), a = n(e("./languages/es.json")), n = n(e("./languages/nl.json")), l = (r.defaultVocabularies = { en: i.default, de: o.default, es: a.default, nl: n.default }, { language: "en", vocabularies: r.defaultVocabularies }), e = (c.prototype.setConfig = function(e2) {
            var e2 = s(s({}, l), e2), t2 = "auto" === e2.language, n2 = this.mergeVocabulariesWithDefaultVocabularies(e2.vocabularies);
            this.initializeLanguage(e2.language, t2, n2), this.initializeVocabulary(n2);
          }, c.containsLanguage = function(e2, t2) {
            return e2.hasOwnProperty(t2);
          }, c.prototype.mergeVocabulariesWithDefaultVocabularies = function(o2) {
            void 0 === o2 && (o2 = {});
            var i2 = s(s({}, r.defaultVocabularies), o2);
            return Object.keys(i2).reduce(function(e2, t2) {
              var n2 = i2[t2];
              return c.containsLanguage(r.defaultVocabularies, t2) && c.containsLanguage(o2, t2) && (n2 = s(s({}, r.defaultVocabularies[t2]), o2[t2])), s(s({}, e2), ((e2 = {})[t2] = n2, e2));
            }, {});
          }, c.prototype.initializeLanguage = function(e2, t2, n2) {
            if (t2) {
              t2 = window.navigator.language;
              if (c.containsLanguage(n2, t2)) return void (this.language = t2);
              t2 = t2.slice(0, 2);
              if (c.containsLanguage(n2, t2)) return void (this.language = t2);
            }
            this.language = e2;
          }, c.prototype.initializeVocabulary = function(n2) {
            this.vocabulary = ["en", this.language].reduce(function(e2, t2) {
              return s(s({}, e2), n2[t2] || {});
            }, {});
          }, c.prototype.replaceVariableWithPlaceholderIfExists = function(e2, o2) {
            var t2 = e2.match(new RegExp("{[a-zA-Z0-9]+}", "g"));
            return 0 === t2.length ? e2 : t2.map(function(e3) {
              return { match: e3, key: e3.slice(1, -1) };
            }).reduce(function(e3, t3) {
              var n2 = t3.key, t3 = t3.match;
              return o2.hasOwnProperty(n2) ? e3.replace(t3, o2[n2]) : e3;
            }, e2);
          }, c.prototype.getLocalizer = function(t2, n2) {
            var o2 = this;
            return function() {
              var e2;
              if (null != t2) return null == (e2 = o2.vocabulary[t2]) && (e2 = t2), null != n2 ? o2.replaceVariableWithPlaceholderIfExists(e2, n2) : e2;
            };
          }, c.prototype.performLocalization = function(e2) {
            return "function" == typeof e2 ? e2() : e2;
          }, c);
          function c(e2) {
            this.setConfig(e2);
          }
          r.I18n = e, r.i18n = new e(l);
        }, { "./languages/de.json": 92, "./languages/en.json": 93, "./languages/es.json": 94, "./languages/nl.json": 95 }], 92: [function(e, t, n) {
          t.exports = { "settings.video.quality": "Videoqualität", "settings.audio.quality": "Audioqualität", "settings.audio.track": "Audiospur", speed: "Geschwindigkeit", play: "Abspielen", pause: "Pause", playPause: "Abspielen/Pause", open: "öffnen", close: "Schließen", "settings.audio.mute": "Stummschaltung", "settings.audio.volume": "Lautstärke", pictureInPicture: "Bild im Bild", appleAirplay: "Apple AirPlay", googleCast: "Google Cast", vr: "VR", settings: "Einstellungen", fullscreen: "Vollbild", off: "aus", "settings.subtitles": "Untertitel", "settings.subtitles.font.size": "Größe", "settings.subtitles.font.style": "Schriftstil", "settings.subtitles.font.style.bold": "Fett", "settings.subtitles.font.style.italic": "Kursiv", "settings.subtitles.font.family": "Schriftart", "settings.subtitles.font.color": "Farbe", "settings.subtitles.font.opacity": "Deckkraft", "settings.subtitles.characterEdge": "Ränder", "settings.subtitles.characterEdge.color": "Buchstabenrandfarbe", "settings.subtitles.background.color": "Hintergrundfarbe", "settings.subtitles.background.opacity": "Hintergrunddeckkraft", "settings.subtitles.window.color": "Hintergrundfarbe", "settings.subtitles.window.opacity": "Hintergrunddeckkraft", "settings.time.hours": "Stunden", "settings.time.minutes": "Minuten", "settings.time.seconds": "Sekunden", back: "Zurück", reset: "Zurücksetzen", replay: "Wiederholen", "ads.remainingTime": "Diese Anzeige endet in {remainingTime} Sekunden", default: "standard", "colors.white": "weiß", "colors.black": "schwarz", "colors.red": "rot", "colors.green": "grün", "colors.blue": "blau", "colors.yellow": "gelb", "subtitle.example": "Beispiel Untertitel", "subtitle.select": "Untertitel auswählen", playingOn: "Spielt auf <strong>{castDeviceName}</strong>", connectingTo: "Verbindung mit <strong>{castDeviceName}</strong> wird hergestellt...", watermarkLink: "Link zum Homepage", controlBar: "Videoplayer Kontrollen", player: "Video player", seekBar: "Video-Timeline", "seekBar.value": "Wert", "seekBar.timeshift": "Timeshift", "seekBar.durationText": "aus", "quickseek.forward": "{seekSeconds} Sekunden Vor", "quickseek.rewind": "{seekSeconds} Sekunden Zurück", ecoMode: "ecoMode", "ecoMode.title": "Eco Mode" };
        }, {}], 93: [function(e, t, n) {
          t.exports = { "settings.video.quality": "Video Quality", "settings.audio.quality": "Audio Quality", "settings.audio.track": "Audio Track", "settings.audio.mute": "Mute", "settings.audio.volume": "Volume", "settings.subtitles.window.color": "Window color", "settings.subtitles.window.opacity": "Window opacity", "settings.subtitles": "Subtitles", "settings.subtitles.font.color": "Font color", "settings.subtitles.font.opacity": "Font opacity", "settings.subtitles.background.color": "Background color", "settings.subtitles.background.opacity": "Background opacity", "colors.white": "white", "colors.black": "black", "colors.red": "red", "colors.green": "green", "colors.blue": "blue", "colors.cyan": "cyan", "colors.yellow": "yellow", "colors.magenta": "magenta", percent: "{value}%", "settings.subtitles.font.size": "Font size", "settings.subtitles.font.style": "Font style", "settings.subtitles.font.style.bold": "bold", "settings.subtitles.font.style.italic": "italic", "settings.subtitles.characterEdge": "Character edge", "settings.subtitles.characterEdge.raised": "raised", "settings.subtitles.characterEdge.depressed": "depressed", "settings.subtitles.characterEdge.uniform": "uniform", "settings.subtitles.characterEdge.dropshadowed": "drop shadowed", "settings.subtitles.characterEdge.color": "Character edge color", "settings.subtitles.font.family": "Font family", "settings.subtitles.font.family.monospacedserif": "monospaced serif", "settings.subtitles.font.family.proportionalserif": "proportional serif", "settings.subtitles.font.family.monospacedsansserif": "monospaced sans serif", "settings.subtitles.font.family.proportionalsansserif": "proportional sans serif", "settings.subtitles.font.family.casual": "casual", "settings.subtitles.font.family.cursive": "cursive", "settings.subtitles.font.family.smallcapital": "small capital", "settings.time.hours": "Hours", "settings.time.minutes": "Minutes", "settings.time.seconds": "Seconds", "ads.remainingTime": "This ad will end in {remainingTime} seconds.", settings: "Settings", fullscreen: "Fullscreen", speed: "Speed", playPause: "Play/Pause", play: "Play", pause: "Pause", open: "open", close: "Close", pictureInPicture: "Picture-in-Picture", appleAirplay: "Apple AirPlay", googleCast: "Google Cast", vr: "VR", off: "off", auto: "auto", ecoMode: "ecoMode", "ecoMode.title": "Eco Mode", back: "Back", reset: "Reset", replay: "Replay", normal: "normal", default: "default", live: "Live", "subtitle.example": "example subtitle", "subtitle.select": "Select subtitle", playingOn: "Playing on <strong>{castDeviceName}</strong>", connectingTo: "Connecting to <strong>{castDeviceName}</strong>...", watermarkLink: "Link to Homepage", controlBar: "Video player controls", player: "Video player", seekBar: "Video timeline", "seekBar.value": "Value", "seekBar.timeshift": "Timeshift", "seekBar.durationText": "out of", "quickseek.forward": "Fast Forward {seekSeconds} seconds", "quickseek.rewind": "Rewind {seekSeconds} seconds" };
        }, {}], 94: [function(e, t, n) {
          t.exports = { "settings.video.quality": "Calidad de Video", "settings.audio.quality": "Calidad de Audio", "settings.audio.track": "Pista de Audio", "settings.audio.mute": "Silencio", "settings.audio.volume": "Volumen", "settings.subtitles.window.color": "color de Ventana", "settings.subtitles.window.opacity": "opacidad de Ventana", "settings.subtitles": "Subtítulos", "settings.subtitles.font.color": "color de Fuente", "settings.subtitles.font.opacity": "opacidad de Fuente", "settings.subtitles.background.color": "color de Fondo", "settings.subtitles.background.opacity": "opacidad de Fondo", "colors.white": "blanco", "colors.black": "negro", "colors.red": "rojo", "colors.green": "verde", "colors.blue": "azul", "colors.cyan": "cian", "colors.yellow": "amarillo", "colors.magenta": "magenta", percent: "{value}%", "settings.subtitles.font.size": "tamaño de Fuente", "settings.subtitles.font.style": "Estilo de Fuente", "settings.subtitles.font.style.bold": "negrita", "settings.subtitles.font.style.italic": "cursiva", "settings.subtitles.characterEdge": "borde del Caracter", "settings.subtitles.characterEdge.raised": "alzado", "settings.subtitles.characterEdge.depressed": "discreto", "settings.subtitles.characterEdge.uniform": "uniforme", "settings.subtitles.characterEdge.dropshadowed": "sombreado", "settings.subtitles.characterEdge.color": "color de contorno de texto", "settings.subtitles.font.family": "tipo de Fuente", "settings.subtitles.font.family.monospacedserif": "monospaced serif", "settings.subtitles.font.family.proportionalserif": "proportional serif", "settings.subtitles.font.family.monospacedsansserif": "monospaced sans serif", "settings.subtitles.font.family.proportionalsansserif": "proportional sans serif", "settings.subtitles.font.family.casual": "casual", "settings.subtitles.font.family.cursive": "cursiva", "settings.subtitles.font.family.smallcapital": "small capital", "settings.time.hours": "Horas", "settings.time.minutes": "Minutos", "settings.time.seconds": "Segundos", "ads.remainingTime": "Este anuncio acabará en {remainingTime} segundos.", settings: "Configuración", fullscreen: "Pantalla Completa", speed: "Velocidad", playPause: "Reproducir/Pausa", play: "Reproducir", pause: "Pausa", open: "Abrir", close: "Cerrar", pictureInPicture: "Imagen en Imagen", appleAirplay: "Apple AirPlay", googleCast: "Google Cast", vr: "VR", off: "off", auto: "auto", ecoMode: "ecoMode", "ecoMode.title": "Eco Mode", back: "Atrás", reset: "Reiniciar", replay: "Rebobinar", normal: "normal", default: "predeterminado", live: "Directo", "subtitle.example": "Ejemplo de Subtítulo", "subtitle.select": "Seleccionar subtítulo", playingOn: "Reproduciendo en <strong>{castDeviceName}</strong>", connectingTo: "Conectando a <strong>{castDeviceName}</strong>...", watermarkLink: "Enlace al inicio", controlBar: "Controles del Reproductor", player: "Reproductor de Video", seekBar: "Línea de Tiempo", "seekBar.value": "posición", "seekBar.timeshift": "cambio de posición", "seekBar.durationText": "de", "quickseek.forward": "Adelantar {seekSeconds} segundos", "quickseek.rewind": "Rebobinar {seekSeconds} segundos" };
        }, {}], 95: [function(e, t, n) {
          t.exports = { "settings.video.quality": "Videokwaliteit", "settings.audio.quality": "Audiokwaliteit", "settings.audio.track": "Audiospoor", "settings.audio.mute": "Dempen", "settings.audio.volume": "Volume", "settings.subtitles.window.color": "Vensterkleur", "settings.subtitles.window.opacity": "Venster doorzichtigheid", "settings.subtitles": "Ondertiteling", "settings.subtitles.font.color": "Lettertype kleur", "settings.subtitles.font.opacity": "Lettertype doorzichtigheid", "settings.subtitles.background.color": "Achtergrondkleur", "settings.subtitles.background.opacity": "Achtergrond doorzichtigheid", "colors.white": "wit", "colors.black": "zwart", "colors.red": "rood", "colors.green": "groen", "colors.blue": "blauw", "colors.cyan": "cyaan", "colors.yellow": "geel", "colors.magenta": "magenta", percent: "{value}%", "settings.subtitles.font.size": "Lettertype grootte", "settings.subtitles.characterEdge": "Lettertype rand", "settings.subtitles.characterEdge.raised": "verhoogd", "settings.subtitles.characterEdge.depressed": "verlaagd", "settings.subtitles.characterEdge.uniform": "uniform", "settings.subtitles.characterEdge.dropshadowed": "schaduw", "settings.subtitles.font.family": "Standaard lettertype", "settings.subtitles.font.family.monospacedserif": "monospace serif", "settings.subtitles.font.family.proportionalserif": "proportioneel serif", "settings.subtitles.font.family.monospacedsansserif": "monospace sans-serif", "settings.subtitles.font.family.proportionalsansserif": "proportioneel sans-serif", "settings.subtitles.font.family.casual": "casual", "settings.subtitles.font.family.cursive": "cursief", "settings.subtitles.font.family.smallcapital": "kleine hoofdletters", "settings.time.hours": "Uren", "settings.time.minutes": "Minuten", "settings.time.seconds": "Seconden", "ads.remainingTime": "Deze advertentie eindigt in {remainingTime} seconden.", settings: "Instellingen", fullscreen: "Volledig scherm", speed: "Snelheid", playPause: "Afspelen/Pauzeren", play: "Afspelen", pause: "Pauzeren", open: "Openen", close: "Sluiten", pictureInPicture: "Picture-in-Picture", appleAirplay: "Apple AirPlay", googleCast: "Google Cast", vr: "VR", off: "uit", auto: "automatisch", ecoMode: "Eco-modus", "ecoMode.title": "Eco-modus", back: "Terug", reset: "Reset", replay: "Opnieuw afspelen", normal: "normaal", default: "standaard", live: "Live", "subtitle.example": "voorbeeld ondertiteling", "subtitle.select": "Selecteer ondertiteling", playingOn: "Speelt af op <strong>{castDeviceName}</strong>", connectingTo: "Verbinden met <strong>{castDeviceName}</strong>...", watermarkLink: "Link naar homepage", controlBar: "Videospeler bediening", player: "Videospeler", seekBar: "Video tijdlijn", "seekBar.value": "Waarde", "seekBar.timeshift": "Tijdverschuiving", "seekBar.durationText": "van", "quickseek.forward": "{seekSeconds} seconden vooruitspoelen", "quickseek.rewind": "{seekSeconds} seconden terugspoelen" };
        }, {}], 96: [function(e, D, t) {
          "use strict";
          var o = this && this.__createBinding || (Object.create ? function(e2, t2, n2, o2) {
            void 0 === o2 && (o2 = n2);
            var i2 = Object.getOwnPropertyDescriptor(t2, n2);
            i2 && ("get" in i2 ? t2.__esModule : !i2.writable && !i2.configurable) || (i2 = { enumerable: true, get: function() {
              return t2[n2];
            } }), Object.defineProperty(e2, o2, i2);
          } : function(e2, t2, n2, o2) {
            e2[o2 = void 0 === o2 ? n2 : o2] = t2[n2];
          }), n = this && this.__exportStar || function(e2, t2) {
            for (var n2 in e2) "default" === n2 || Object.prototype.hasOwnProperty.call(t2, n2) || o(t2, e2, n2);
          }, i = (Object.defineProperty(t, "__esModule", { value: true }), t.ClickOverlay = t.VolumeControlButton = t.TitleBar = t.SubtitleSelectBox = t.SubtitleOverlay = t.SeekBarLabel = t.RecommendationOverlay = t.ErrorMessageOverlay = t.Component = t.CastToggleButton = t.CastStatusOverlay = t.AudioTrackSelectBox = t.AudioQualitySelectBox = t.Label = t.Container = t.UIContainer = t.Watermark = t.VRToggleButton = t.VolumeToggleButton = t.VideoQualitySelectBox = t.ToggleButton = t.SettingsToggleButton = t.SettingsPanel = t.ItemSelectionList = t.SelectBox = t.SeekBar = t.PlaybackToggleButton = t.PlaybackTimeLabelMode = t.PlaybackTimeLabel = t.HugePlaybackToggleButton = t.FullscreenToggleButton = t.ControlBar = t.Button = t.ListOrientation = t.ListNavigationGroup = t.RootNavigationGroup = t.NavigationGroup = t.SpatialNavigation = t.I18n = t.i18n = t.ErrorUtils = t.StorageUtils = t.BrowserUtils = t.UIUtils = t.PlayerUtils = t.StringUtils = t.ArrayUtils = t.DemoFactory = t.UIFactory = t.version = void 0, t.ListSelector = t.QuickSeekButton = t.ReplayButton = t.SettingsPanelItem = t.SubtitleSettingsPanelPage = t.SettingsPanelPageOpenButton = t.SettingsPanelPageBackButton = t.SettingsPanelPage = t.AudioTrackListBox = t.SubtitleListBox = t.ListBox = t.SubtitleSettingsResetButton = t.WindowOpacitySelectBox = t.WindowColorSelectBox = t.SubtitleSettingsLabel = t.SubtitleSettingSelectBox = t.FontSizeSelectBox = t.FontOpacitySelectBox = t.FontFamilySelectBox = t.FontColorSelectBox = t.CharacterEdgeSelectBox = t.BackgroundOpacitySelectBox = t.BackgroundColorSelectBox = t.Spacer = t.PictureInPictureToggleButton = t.VolumeSlider = t.AirPlayToggleButton = t.MetadataLabelContent = t.MetadataLabel = t.CloseButton = t.PlaybackToggleOverlay = t.CastUIContainer = t.BufferingOverlay = t.HugeReplayButton = t.PlaybackSpeedSelectBox = t.AdClickOverlay = t.AdMessageLabel = t.AdSkipButton = void 0, t.version = "3.98.0", n(e("./uimanager"), t), n(e("./uiconfig"), t), e("./uifactory")), r = (Object.defineProperty(t, "UIFactory", { enumerable: true, get: function() {
            return i.UIFactory;
          } }), e("./demofactory")), s = (Object.defineProperty(t, "DemoFactory", { enumerable: true, get: function() {
            return r.DemoFactory;
          } }), e("./arrayutils")), a = (Object.defineProperty(t, "ArrayUtils", { enumerable: true, get: function() {
            return s.ArrayUtils;
          } }), e("./stringutils")), l = (Object.defineProperty(t, "StringUtils", { enumerable: true, get: function() {
            return a.StringUtils;
          } }), e("./playerutils")), c = (Object.defineProperty(t, "PlayerUtils", { enumerable: true, get: function() {
            return l.PlayerUtils;
          } }), e("./uiutils")), u = (Object.defineProperty(t, "UIUtils", { enumerable: true, get: function() {
            return c.UIUtils;
          } }), e("./browserutils")), p = (Object.defineProperty(t, "BrowserUtils", { enumerable: true, get: function() {
            return u.BrowserUtils;
          } }), e("./storageutils")), g = (Object.defineProperty(t, "StorageUtils", { enumerable: true, get: function() {
            return p.StorageUtils;
          } }), e("./errorutils")), f = (Object.defineProperty(t, "ErrorUtils", { enumerable: true, get: function() {
            return g.ErrorUtils;
          } }), e("./localization/i18n")), d = (Object.defineProperty(t, "i18n", { enumerable: true, get: function() {
            return f.i18n;
          } }), Object.defineProperty(t, "I18n", { enumerable: true, get: function() {
            return f.I18n;
          } }), e("./spatialnavigation/spatialnavigation")), h = (Object.defineProperty(t, "SpatialNavigation", { enumerable: true, get: function() {
            return d.SpatialNavigation;
          } }), e("./spatialnavigation/navigationgroup")), y = (Object.defineProperty(t, "NavigationGroup", { enumerable: true, get: function() {
            return h.NavigationGroup;
          } }), e("./spatialnavigation/rootnavigationgroup")), m = (Object.defineProperty(t, "RootNavigationGroup", { enumerable: true, get: function() {
            return y.RootNavigationGroup;
          } }), e("./spatialnavigation/ListNavigationGroup")), b = (Object.defineProperty(t, "ListNavigationGroup", { enumerable: true, get: function() {
            return m.ListNavigationGroup;
          } }), Object.defineProperty(t, "ListOrientation", { enumerable: true, get: function() {
            return m.ListOrientation;
          } }), e("./components/button")), v = (Object.defineProperty(t, "Button", { enumerable: true, get: function() {
            return b.Button;
          } }), e("./components/controlbar")), C = (Object.defineProperty(t, "ControlBar", { enumerable: true, get: function() {
            return v.ControlBar;
          } }), e("./components/fullscreentogglebutton")), S = (Object.defineProperty(t, "FullscreenToggleButton", { enumerable: true, get: function() {
            return C.FullscreenToggleButton;
          } }), e("./components/hugeplaybacktogglebutton")), P = (Object.defineProperty(t, "HugePlaybackToggleButton", { enumerable: true, get: function() {
            return S.HugePlaybackToggleButton;
          } }), e("./components/playbacktimelabel")), w = (Object.defineProperty(t, "PlaybackTimeLabel", { enumerable: true, get: function() {
            return P.PlaybackTimeLabel;
          } }), Object.defineProperty(t, "PlaybackTimeLabelMode", { enumerable: true, get: function() {
            return P.PlaybackTimeLabelMode;
          } }), e("./components/playbacktogglebutton")), E = (Object.defineProperty(t, "PlaybackToggleButton", { enumerable: true, get: function() {
            return w.PlaybackToggleButton;
          } }), e("./components/seekbar")), _ = (Object.defineProperty(t, "SeekBar", { enumerable: true, get: function() {
            return E.SeekBar;
          } }), e("./components/selectbox")), O = (Object.defineProperty(t, "SelectBox", { enumerable: true, get: function() {
            return _.SelectBox;
          } }), e("./components/itemselectionlist")), k = (Object.defineProperty(t, "ItemSelectionList", { enumerable: true, get: function() {
            return O.ItemSelectionList;
          } }), e("./components/settingspanel")), x = (Object.defineProperty(t, "SettingsPanel", { enumerable: true, get: function() {
            return k.SettingsPanel;
          } }), e("./components/settingstogglebutton")), T = (Object.defineProperty(t, "SettingsToggleButton", { enumerable: true, get: function() {
            return x.SettingsToggleButton;
          } }), e("./components/togglebutton")), M = (Object.defineProperty(t, "ToggleButton", { enumerable: true, get: function() {
            return T.ToggleButton;
          } }), e("./components/videoqualityselectbox")), L = (Object.defineProperty(t, "VideoQualitySelectBox", { enumerable: true, get: function() {
            return M.VideoQualitySelectBox;
          } }), e("./components/volumetogglebutton")), A = (Object.defineProperty(t, "VolumeToggleButton", { enumerable: true, get: function() {
            return L.VolumeToggleButton;
          } }), e("./components/vrtogglebutton")), I = (Object.defineProperty(t, "VRToggleButton", { enumerable: true, get: function() {
            return A.VRToggleButton;
          } }), e("./components/watermark")), B = (Object.defineProperty(t, "Watermark", { enumerable: true, get: function() {
            return I.Watermark;
          } }), e("./components/uicontainer")), U = (Object.defineProperty(t, "UIContainer", { enumerable: true, get: function() {
            return B.UIContainer;
          } }), e("./components/container")), z = (Object.defineProperty(t, "Container", { enumerable: true, get: function() {
            return U.Container;
          } }), e("./components/label")), R = (Object.defineProperty(t, "Label", { enumerable: true, get: function() {
            return z.Label;
          } }), e("./components/audioqualityselectbox")), F = (Object.defineProperty(t, "AudioQualitySelectBox", { enumerable: true, get: function() {
            return R.AudioQualitySelectBox;
          } }), e("./components/audiotrackselectbox")), V = (Object.defineProperty(t, "AudioTrackSelectBox", { enumerable: true, get: function() {
            return F.AudioTrackSelectBox;
          } }), e("./components/caststatusoverlay")), H = (Object.defineProperty(t, "CastStatusOverlay", { enumerable: true, get: function() {
            return V.CastStatusOverlay;
          } }), e("./components/casttogglebutton")), N = (Object.defineProperty(t, "CastToggleButton", { enumerable: true, get: function() {
            return H.CastToggleButton;
          } }), e("./components/component")), W = (Object.defineProperty(t, "Component", { enumerable: true, get: function() {
            return N.Component;
          } }), e("./components/errormessageoverlay")), G = (Object.defineProperty(t, "ErrorMessageOverlay", { enumerable: true, get: function() {
            return W.ErrorMessageOverlay;
          } }), e("./components/recommendationoverlay")), q = (Object.defineProperty(t, "RecommendationOverlay", { enumerable: true, get: function() {
            return G.RecommendationOverlay;
          } }), e("./components/seekbarlabel")), K = (Object.defineProperty(t, "SeekBarLabel", { enumerable: true, get: function() {
            return q.SeekBarLabel;
          } }), e("./components/subtitleoverlay")), Q = (Object.defineProperty(t, "SubtitleOverlay", { enumerable: true, get: function() {
            return K.SubtitleOverlay;
          } }), e("./components/subtitleselectbox")), Y = (Object.defineProperty(t, "SubtitleSelectBox", { enumerable: true, get: function() {
            return Q.SubtitleSelectBox;
          } }), e("./components/titlebar")), X = (Object.defineProperty(t, "TitleBar", { enumerable: true, get: function() {
            return Y.TitleBar;
          } }), e("./components/volumecontrolbutton")), Z = (Object.defineProperty(t, "VolumeControlButton", { enumerable: true, get: function() {
            return X.VolumeControlButton;
          } }), e("./components/clickoverlay")), J = (Object.defineProperty(t, "ClickOverlay", { enumerable: true, get: function() {
            return Z.ClickOverlay;
          } }), e("./components/adskipbutton")), $ = (Object.defineProperty(t, "AdSkipButton", { enumerable: true, get: function() {
            return J.AdSkipButton;
          } }), e("./components/admessagelabel")), ee = (Object.defineProperty(t, "AdMessageLabel", { enumerable: true, get: function() {
            return $.AdMessageLabel;
          } }), e("./components/adclickoverlay")), te = (Object.defineProperty(t, "AdClickOverlay", { enumerable: true, get: function() {
            return ee.AdClickOverlay;
          } }), e("./components/playbackspeedselectbox")), ne = (Object.defineProperty(t, "PlaybackSpeedSelectBox", { enumerable: true, get: function() {
            return te.PlaybackSpeedSelectBox;
          } }), e("./components/hugereplaybutton")), oe = (Object.defineProperty(t, "HugeReplayButton", { enumerable: true, get: function() {
            return ne.HugeReplayButton;
          } }), e("./components/bufferingoverlay")), ie = (Object.defineProperty(t, "BufferingOverlay", { enumerable: true, get: function() {
            return oe.BufferingOverlay;
          } }), e("./components/castuicontainer")), re = (Object.defineProperty(t, "CastUIContainer", { enumerable: true, get: function() {
            return ie.CastUIContainer;
          } }), e("./components/playbacktoggleoverlay")), se = (Object.defineProperty(t, "PlaybackToggleOverlay", { enumerable: true, get: function() {
            return re.PlaybackToggleOverlay;
          } }), e("./components/closebutton")), j = (Object.defineProperty(t, "CloseButton", { enumerable: true, get: function() {
            return se.CloseButton;
          } }), e("./components/metadatalabel")), ae = (Object.defineProperty(t, "MetadataLabel", { enumerable: true, get: function() {
            return j.MetadataLabel;
          } }), Object.defineProperty(t, "MetadataLabelContent", { enumerable: true, get: function() {
            return j.MetadataLabelContent;
          } }), e("./components/airplaytogglebutton")), le = (Object.defineProperty(t, "AirPlayToggleButton", { enumerable: true, get: function() {
            return ae.AirPlayToggleButton;
          } }), e("./components/volumeslider")), ce = (Object.defineProperty(t, "VolumeSlider", { enumerable: true, get: function() {
            return le.VolumeSlider;
          } }), e("./components/pictureinpicturetogglebutton")), ue = (Object.defineProperty(t, "PictureInPictureToggleButton", { enumerable: true, get: function() {
            return ce.PictureInPictureToggleButton;
          } }), e("./components/spacer")), pe = (Object.defineProperty(t, "Spacer", { enumerable: true, get: function() {
            return ue.Spacer;
          } }), e("./components/subtitlesettings/backgroundcolorselectbox")), ge = (Object.defineProperty(t, "BackgroundColorSelectBox", { enumerable: true, get: function() {
            return pe.BackgroundColorSelectBox;
          } }), e("./components/subtitlesettings/backgroundopacityselectbox")), fe = (Object.defineProperty(t, "BackgroundOpacitySelectBox", { enumerable: true, get: function() {
            return ge.BackgroundOpacitySelectBox;
          } }), e("./components/subtitlesettings/characteredgeselectbox")), de = (Object.defineProperty(t, "CharacterEdgeSelectBox", { enumerable: true, get: function() {
            return fe.CharacterEdgeSelectBox;
          } }), e("./components/subtitlesettings/fontcolorselectbox")), he = (Object.defineProperty(t, "FontColorSelectBox", { enumerable: true, get: function() {
            return de.FontColorSelectBox;
          } }), e("./components/subtitlesettings/fontfamilyselectbox")), ye = (Object.defineProperty(t, "FontFamilySelectBox", { enumerable: true, get: function() {
            return he.FontFamilySelectBox;
          } }), e("./components/subtitlesettings/fontopacityselectbox")), me = (Object.defineProperty(t, "FontOpacitySelectBox", { enumerable: true, get: function() {
            return ye.FontOpacitySelectBox;
          } }), e("./components/subtitlesettings/fontsizeselectbox")), be = (Object.defineProperty(t, "FontSizeSelectBox", { enumerable: true, get: function() {
            return me.FontSizeSelectBox;
          } }), e("./components/subtitlesettings/subtitlesettingselectbox")), ve = (Object.defineProperty(t, "SubtitleSettingSelectBox", { enumerable: true, get: function() {
            return be.SubtitleSettingSelectBox;
          } }), e("./components/subtitlesettings/subtitlesettingslabel")), Ce = (Object.defineProperty(t, "SubtitleSettingsLabel", { enumerable: true, get: function() {
            return ve.SubtitleSettingsLabel;
          } }), e("./components/subtitlesettings/windowcolorselectbox")), Se = (Object.defineProperty(t, "WindowColorSelectBox", { enumerable: true, get: function() {
            return Ce.WindowColorSelectBox;
          } }), e("./components/subtitlesettings/windowopacityselectbox")), Pe = (Object.defineProperty(t, "WindowOpacitySelectBox", { enumerable: true, get: function() {
            return Se.WindowOpacitySelectBox;
          } }), e("./components/subtitlesettings/subtitlesettingsresetbutton")), we = (Object.defineProperty(t, "SubtitleSettingsResetButton", { enumerable: true, get: function() {
            return Pe.SubtitleSettingsResetButton;
          } }), e("./components/listbox")), Ee = (Object.defineProperty(t, "ListBox", { enumerable: true, get: function() {
            return we.ListBox;
          } }), e("./components/subtitlelistbox")), _e = (Object.defineProperty(t, "SubtitleListBox", { enumerable: true, get: function() {
            return Ee.SubtitleListBox;
          } }), e("./components/audiotracklistbox")), Oe = (Object.defineProperty(t, "AudioTrackListBox", { enumerable: true, get: function() {
            return _e.AudioTrackListBox;
          } }), e("./components/settingspanelpage")), ke = (Object.defineProperty(t, "SettingsPanelPage", { enumerable: true, get: function() {
            return Oe.SettingsPanelPage;
          } }), e("./components/settingspanelpagebackbutton")), xe = (Object.defineProperty(t, "SettingsPanelPageBackButton", { enumerable: true, get: function() {
            return ke.SettingsPanelPageBackButton;
          } }), e("./components/settingspanelpageopenbutton")), Te = (Object.defineProperty(t, "SettingsPanelPageOpenButton", { enumerable: true, get: function() {
            return xe.SettingsPanelPageOpenButton;
          } }), e("./components/subtitlesettings/subtitlesettingspanelpage")), Me = (Object.defineProperty(t, "SubtitleSettingsPanelPage", { enumerable: true, get: function() {
            return Te.SubtitleSettingsPanelPage;
          } }), e("./components/settingspanelitem")), Le = (Object.defineProperty(t, "SettingsPanelItem", { enumerable: true, get: function() {
            return Me.SettingsPanelItem;
          } }), e("./components/replaybutton")), Ae = (Object.defineProperty(t, "ReplayButton", { enumerable: true, get: function() {
            return Le.ReplayButton;
          } }), e("./components/quickseekbutton")), Ie = (Object.defineProperty(t, "QuickSeekButton", { enumerable: true, get: function() {
            return Ae.QuickSeekButton;
          } }), e("./components/listselector"));
          Object.defineProperty(t, "ListSelector", { enumerable: true, get: function() {
            return Ie.ListSelector;
          } }), "function" != typeof Object.assign && (Object.assign = function(e2) {
            if (null == e2) throw new TypeError("Cannot convert undefined or null to object");
            e2 = Object(e2);
            for (var t2 = 1; t2 < arguments.length; t2++) {
              var n2 = arguments[t2];
              if (null != n2) for (var o2 in n2) Object.prototype.hasOwnProperty.call(n2, o2) && (e2[o2] = n2[o2]);
            }
            return e2;
          });
        }, { "./arrayutils": 1, "./browserutils": 3, "./components/adclickoverlay": 4, "./components/admessagelabel": 5, "./components/adskipbutton": 6, "./components/airplaytogglebutton": 7, "./components/audioqualityselectbox": 8, "./components/audiotracklistbox": 9, "./components/audiotrackselectbox": 10, "./components/bufferingoverlay": 11, "./components/button": 12, "./components/caststatusoverlay": 13, "./components/casttogglebutton": 14, "./components/castuicontainer": 15, "./components/clickoverlay": 16, "./components/closebutton": 17, "./components/component": 18, "./components/container": 19, "./components/controlbar": 20, "./components/errormessageoverlay": 23, "./components/fullscreentogglebutton": 24, "./components/hugeplaybacktogglebutton": 25, "./components/hugereplaybutton": 26, "./components/itemselectionlist": 27, "./components/label": 28, "./components/listbox": 29, "./components/listselector": 30, "./components/metadatalabel": 31, "./components/pictureinpicturetogglebutton": 32, "./components/playbackspeedselectbox": 33, "./components/playbacktimelabel": 34, "./components/playbacktogglebutton": 35, "./components/playbacktoggleoverlay": 36, "./components/quickseekbutton": 37, "./components/recommendationoverlay": 38, "./components/replaybutton": 39, "./components/seekbar": 40, "./components/seekbarlabel": 43, "./components/selectbox": 44, "./components/settingspanel": 45, "./components/settingspanelitem": 46, "./components/settingspanelpage": 47, "./components/settingspanelpagebackbutton": 48, "./components/settingspanelpageopenbutton": 50, "./components/settingstogglebutton": 51, "./components/spacer": 52, "./components/subtitlelistbox": 53, "./components/subtitleoverlay": 54, "./components/subtitleselectbox": 55, "./components/subtitlesettings/backgroundcolorselectbox": 56, "./components/subtitlesettings/backgroundopacityselectbox": 57, "./components/subtitlesettings/characteredgeselectbox": 59, "./components/subtitlesettings/fontcolorselectbox": 60, "./components/subtitlesettings/fontfamilyselectbox": 61, "./components/subtitlesettings/fontopacityselectbox": 62, "./components/subtitlesettings/fontsizeselectbox": 63, "./components/subtitlesettings/subtitlesettingselectbox": 65, "./components/subtitlesettings/subtitlesettingslabel": 66, "./components/subtitlesettings/subtitlesettingspanelpage": 68, "./components/subtitlesettings/subtitlesettingsresetbutton": 69, "./components/subtitlesettings/windowcolorselectbox": 70, "./components/subtitlesettings/windowopacityselectbox": 71, "./components/titlebar": 73, "./components/togglebutton": 74, "./components/uicontainer": 76, "./components/videoqualityselectbox": 77, "./components/volumecontrolbutton": 78, "./components/volumeslider": 79, "./components/volumetogglebutton": 80, "./components/vrtogglebutton": 81, "./components/watermark": 82, "./demofactory": 83, "./errorutils": 85, "./localization/i18n": 91, "./playerutils": 98, "./spatialnavigation/ListNavigationGroup": 99, "./spatialnavigation/navigationgroup": 103, "./spatialnavigation/rootnavigationgroup": 105, "./spatialnavigation/spatialnavigation": 107, "./storageutils": 110, "./stringutils": 111, "./uiconfig": 114, "./uifactory": 115, "./uimanager": 116, "./uiutils": 117 }], 97: [function(e, t, n) {
          "use strict";
          var o, i;
          Object.defineProperty(n, "__esModule", { value: true }), n.isMobileV3PlayerAPI = n.MobileV3PlayerEvent = void 0, (i = o = n.MobileV3PlayerEvent || (n.MobileV3PlayerEvent = {})).SourceError = "sourceerror", i.PlayerError = "playererror", i.PlaylistTransition = "playlisttransition", n.isMobileV3PlayerAPI = function(e2) {
            for (var t2 in o) if (o.hasOwnProperty(t2) && !e2.exports.PlayerEvent.hasOwnProperty(t2)) return false;
            return true;
          };
        }, {}], 98: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.PlayerUtils = void 0;
          var o, i, r = e("./eventdispatcher"), s = e("./browserutils");
          function a(e2) {
            function t2() {
              n2.detect();
            }
            var n2 = this;
            this.timeShiftAvailabilityChangedEvent = new r.EventDispatcher(), this.player = e2, this.timeShiftAvailable = void 0;
            e2.on(e2.exports.PlayerEvent.SourceLoaded, t2), e2.on(e2.exports.PlayerEvent.TimeChanged, t2);
          }
          function l(e2, t2) {
            function n2() {
              o2.detect();
            }
            var o2 = this;
            this.liveChangedEvent = new r.EventDispatcher(), this.player = e2, this.uimanager = t2, this.live = void 0;
            this.uimanager.getConfig().events.onUpdated.subscribe(n2), e2.on(e2.exports.PlayerEvent.Play, n2), s.BrowserUtils.isAndroid && s.BrowserUtils.isChrome && e2.on(e2.exports.PlayerEvent.TimeChanged, n2), e2.exports.PlayerEvent.DurationChanged && e2.on(e2.exports.PlayerEvent.DurationChanged, n2), e2.on(e2.exports.PlayerEvent.AdBreakStarted, n2), e2.on(e2.exports.PlayerEvent.AdBreakFinished, n2);
          }
          o = n.PlayerUtils || (n.PlayerUtils = {}), (e = i = o.PlayerState || (o.PlayerState = {}))[e.Idle = 0] = "Idle", e[e.Prepared = 1] = "Prepared", e[e.Playing = 2] = "Playing", e[e.Paused = 3] = "Paused", e[e.Finished = 4] = "Finished", o.isTimeShiftAvailable = function(e2) {
            return e2.isLive() && 0 !== e2.getMaxTimeShift();
          }, o.getState = function(e2) {
            return e2.hasEnded() ? i.Finished : e2.isPlaying() ? i.Playing : e2.isPaused() ? i.Paused : null != e2.getSource() ? i.Prepared : i.Idle;
          }, o.getCurrentTimeRelativeToSeekableRange = function(e2) {
            var t2 = e2.getCurrentTime();
            return e2.isLive() ? t2 : t2 - o.getSeekableRangeStart(e2, 0);
          }, o.getSeekableRangeStart = function(e2, t2) {
            return void 0 === t2 && (t2 = 0), e2.getSeekableRange() && e2.getSeekableRange().start || t2;
          }, o.getSeekableRangeRespectingLive = function(e2) {
            var t2, n2, o2;
            return e2.isLive() ? (t2 = -e2.getTimeShift(), n2 = -e2.getMaxTimeShift(), { start: (o2 = e2.getCurrentTime()) - (n2 - t2), end: o2 + t2 }) : e2.getSeekableRange();
          }, a.prototype.detect = function() {
            var e2;
            this.player.isLive() && (e2 = o.isTimeShiftAvailable(this.player)) !== this.timeShiftAvailable && (this.timeShiftAvailabilityChangedEvent.dispatch(this.player, { timeShiftAvailable: e2 }), this.timeShiftAvailable = e2);
          }, Object.defineProperty(a.prototype, "onTimeShiftAvailabilityChanged", { get: function() {
            return this.timeShiftAvailabilityChangedEvent.getEvent();
          }, enumerable: false, configurable: true }), o.TimeShiftAvailabilityDetector = a, l.prototype.detect = function() {
            var e2 = this.player.isLive();
            e2 !== this.live && (this.liveChangedEvent.dispatch(this.player, { live: e2 }), this.live = e2);
          }, Object.defineProperty(l.prototype, "onLiveChanged", { get: function() {
            return this.liveChangedEvent.getEvent();
          }, enumerable: false, configurable: true }), o.LiveStreamDetector = l, o.clampValueToRange = function(e2, t2, n2) {
            var o2 = Math.min(t2, n2), t2 = Math.max(t2, n2);
            return Math.min(Math.max(e2, o2), t2);
          };
        }, { "./browserutils": 3, "./eventdispatcher": 86 }], 99: [function(e, t, n) {
          "use strict";
          var o, r, s, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), a = this && this.__spreadArray || function(e2, t2, n2) {
            if (n2 || 2 === arguments.length) for (var o2, i2 = 0, r2 = t2.length; i2 < r2; i2++) !o2 && i2 in t2 || ((o2 = o2 || Array.prototype.slice.call(t2, 0, i2))[i2] = t2[i2]);
            return e2.concat(o2 || Array.prototype.slice.call(t2));
          }, l = (Object.defineProperty(n, "__esModule", { value: true }), n.ListNavigationGroup = n.ListOrientation = void 0, e("./navigationgroup")), c = e("./types"), e = ((e = r = n.ListOrientation || (n.ListOrientation = {})).Horizontal = "horizontal", e.Vertical = "vertical", s = l.NavigationGroup, i(u, s), u.prototype.handleAction = function(e2) {
            s.prototype.handleAction.call(this, e2), e2 === c.Action.SELECT && this.handleAction(c.Action.BACK);
          }, u.prototype.handleNavigation = function(e2) {
            s.prototype.handleNavigation.call(this, e2), this.listNavigationDirections.includes(e2) || this.handleAction(c.Action.BACK);
          }, u);
          function u(e2, t2) {
            for (var n2 = [], o2 = 2; o2 < arguments.length; o2++) n2[o2 - 2] = arguments[o2];
            var i2 = s.apply(this, a([t2], n2, false)) || this;
            switch (e2) {
              case r.Vertical:
                i2.listNavigationDirections = [c.Direction.UP, c.Direction.DOWN];
                break;
              case r.Horizontal:
                i2.listNavigationDirections = [c.Direction.LEFT, c.Direction.RIGHT];
            }
            return i2;
          }
          n.ListNavigationGroup = e;
        }, { "./navigationgroup": 103, "./types": 109 }], 100: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.getHtmlElementsFromComponents = void 0;
          var o = e("../components/container"), i = e("./typeguards");
          n.getHtmlElementsFromComponents = function(e2) {
            var t2 = [];
            return e2.filter(function(e3) {
              return !e3.isHidden();
            }).forEach(function(e3) {
              (e3 instanceof o.Container ? function t3(e4) {
                var n2 = [];
                return e4.getComponents().forEach(function(e5) {
                  (0, i.isContainer)(e5) ? n2.push.apply(n2, t3(e5)) : (0, i.isComponent)(e5) && n2.push(e5);
                }), n2;
              }(e3) : [e3]).forEach(function(e4) {
                t2.push.apply(t2, (e4 = e4, (0, i.isListBox)(e4) ? [].slice.call(e4.getDomElement().get()[0].children) : e4.getDomElement().get().slice(0, 1)));
              });
            }), t2;
          };
        }, { "../components/container": 19, "./typeguards": 108 }], 101: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.getKeyMapForPlatform = void 0;
          var o = e("./types"), i = e("../browserutils"), r = { isApplicable: function() {
            return i.BrowserUtils.isTizen;
          }, keyCodes: { 38: o.Direction.UP, 40: o.Direction.DOWN, 37: o.Direction.LEFT, 39: o.Direction.RIGHT, 13: o.Action.SELECT, 10009: o.Action.BACK } }, s = { isApplicable: function() {
            return i.BrowserUtils.isWebOs;
          }, keyCodes: { 38: o.Direction.UP, 40: o.Direction.DOWN, 37: o.Direction.LEFT, 39: o.Direction.RIGHT, 13: o.Action.SELECT, 461: o.Action.BACK } }, a = { isApplicable: function() {
            return i.BrowserUtils.isPlayStation;
          }, keyCodes: { 38: o.Direction.UP, 40: o.Direction.DOWN, 37: o.Direction.LEFT, 39: o.Direction.RIGHT, 13: o.Action.SELECT, 27: o.Action.BACK } }, l = { isApplicable: function() {
            return i.BrowserUtils.isAndroid;
          }, keyCodes: { 38: o.Direction.UP, 40: o.Direction.DOWN, 37: o.Direction.LEFT, 39: o.Direction.RIGHT, 13: o.Action.SELECT, 27: o.Action.BACK } }, c = { isApplicable: function() {
            return i.BrowserUtils.isHisense;
          }, keyCodes: { 38: o.Direction.UP, 40: o.Direction.DOWN, 37: o.Direction.LEFT, 39: o.Direction.RIGHT, 13: o.Action.SELECT, 8: o.Action.BACK } }, u = { 38: o.Direction.UP, 40: o.Direction.DOWN, 37: o.Direction.LEFT, 39: o.Direction.RIGHT, 13: o.Action.SELECT, 27: o.Action.BACK };
          n.getKeyMapForPlatform = function() {
            var e2 = [s, r, a, c, l].find(function(e3) {
              return e3.isApplicable();
            });
            return e2 ? e2.keyCodes : u;
          };
        }, { "../browserutils": 3, "./types": 109 }], 102: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.getBoundingRectFromElement = n.getElementInDirection = void 0;
          var o = e("./types");
          function r(e2) {
            return Math.sqrt(Math.pow(e2.x, 2) + Math.pow(e2.y, 2));
          }
          function s(e2) {
            e2 = i(e2);
            return { x: e2.x + e2.width / 2, y: e2.y + e2.height / 2 };
          }
          function a(e2, t2, n2) {
            n2 = { x: n2 === o.Direction.LEFT ? -1 : n2 === o.Direction.RIGHT ? 1 : 0, y: n2 === o.Direction.UP ? -1 : n2 === o.Direction.DOWN ? 1 : 0 }, t2 = { x: t2.x - e2.x, y: t2.y - e2.y }, e2 = r(t2), t2 = { x: t2.x / e2, y: t2.y / e2 }, e2 = (n2.x * t2.x + n2.y * t2.y) / (r(n2) * r(t2));
            return 180 * Math.acos(e2) / Math.PI;
          }
          function i(e2) {
            e2 = e2.getBoundingClientRect();
            return "number" != typeof e2.x && "number" != typeof e2.y && (e2.x = e2.left, e2.y = e2.top), e2;
          }
          n.getElementInDirection = function(t2, e2, o2) {
            var i2;
            if (t2) return i2 = s(t2), null == (e2 = e2.filter(function(e3) {
              return e3 !== t2;
            }).map(function(e3) {
              var t3 = s(e3), n2 = r({ x: t3.x - i2.x, y: t3.y - i2.y });
              return { angle: a(i2, t3, o2), dist: n2, element: e3 };
            }).filter(function(e3) {
              return e3.angle <= 45;
            }).sort(function(e3, t3) {
              var n2 = e3.angle, e3 = e3.dist;
              return n2 - t3.angle + (e3 - t3.dist);
            }).shift()) ? void 0 : e2.element;
          }, n.getBoundingRectFromElement = i;
        }, { "./types": 109 }], 103: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.NavigationGroup = void 0;
          var o = e("./navigationalgorithm"), i = e("./gethtmlelementsfromcomponents"), r = e("./nodeeventsubscriber"), s = e("./typeguards"), a = e("./types");
          function l(e2) {
            for (var t2 = [], n2 = 1; n2 < arguments.length; n2++) t2[n2 - 1] = arguments[n2];
            this.container = e2, this.removeElementHoverEventListeners = function() {
            }, this.components = t2, this.eventSubscriber = new r.NodeEventSubscriber();
          }
          l.prototype.getActiveElement = function() {
            return this.activeElement;
          }, l.prototype.focusElement = function(e2) {
            this.blurActiveElement(), this.activeElement = e2, this.activeElement.focus();
          }, l.prototype.blurActiveElement = function() {
            var e2;
            null != (e2 = this.activeElement) && e2.blur();
          }, l.prototype.focusFirstElement = function() {
            var e2 = (0, i.getHtmlElementsFromComponents)(this.components)[0];
            e2 && this.focusElement(e2);
          }, l.prototype.defaultNavigationHandler = function(e2) {
            e2 = (0, o.getElementInDirection)(this.activeElement, (0, i.getHtmlElementsFromComponents)(this.components), e2);
            e2 && this.focusElement(e2);
          }, l.prototype.defaultActionHandler = function(e2) {
            switch (e2) {
              case a.Action.SELECT:
                this.activeElement.click();
                break;
              case a.Action.BACK:
                this.container.hide();
            }
          }, l.prototype.handleInput = function(e2, t2, n2) {
            var o2 = true;
            null != n2 && n2(e2, this.activeElement, function() {
              return o2 = false;
            }), o2 && t2.call(this, e2);
          }, l.prototype.handleNavigation = function(e2) {
            this.activeElement ? this.handleInput(e2, this.defaultNavigationHandler, this.onNavigation) : this.activeElementBeforeDisable ? this.focusElement(this.activeElementBeforeDisable) : this.focusFirstElement();
          }, l.prototype.handleAction = function(e2) {
            this.handleInput(e2, this.defaultActionHandler, this.onAction);
          }, l.prototype.disable = function() {
            this.activeElement && (this.activeElementBeforeDisable = this.activeElement, this.blurActiveElement(), this.activeElement = void 0);
          }, l.prototype.enable = function() {
            this.activeElementBeforeDisable && !(0, s.isSettingsPanel)(this.container) ? (this.focusElement(this.activeElementBeforeDisable), this.activeElementBeforeDisable = void 0) : this.focusFirstElement(), this.trackElementHover();
          }, l.prototype.trackElementHover = function() {
            var o2 = this, e2 = (this.removeElementHoverEventListeners(), (0, i.getHtmlElementsFromComponents)(this.components).map(function(e3) {
              function t2() {
                return o2.disable();
              }
              var n2 = o2.focusElement.bind(o2, e3);
              return o2.eventSubscriber.on(e3, "mouseenter", n2), o2.eventSubscriber.on(e3, "mouseleave", t2), function() {
                o2.eventSubscriber.off(e3, "mouseenter", n2), o2.eventSubscriber.off(e3, "mouseleave", t2);
              };
            }));
            this.removeElementHoverEventListeners = function() {
              return e2.forEach(function(e3) {
                return e3();
              });
            };
          }, l.prototype.release = function() {
            this.eventSubscriber.release(), this.activeElement = void 0, this.components.splice(0, this.components.length), this.removeElementHoverEventListeners();
          }, n.NavigationGroup = l;
        }, { "./gethtmlelementsfromcomponents": 100, "./navigationalgorithm": 102, "./nodeeventsubscriber": 104, "./typeguards": 108, "./types": 109 }], 104: [function(e, t, n) {
          "use strict";
          function o() {
            this.attachedListeners = /* @__PURE__ */ new Map();
          }
          Object.defineProperty(n, "__esModule", { value: true }), n.NodeEventSubscriber = void 0, o.prototype.getEventListenersOfType = function(e2) {
            return this.attachedListeners.has(e2) || this.attachedListeners.set(e2, []), this.attachedListeners.get(e2);
          }, o.prototype.on = function(e2, t2, n2, o2) {
            e2.addEventListener(t2, n2, o2), this.getEventListenersOfType(t2).push([e2, n2, o2]);
          }, o.prototype.off = function(o2, e2, i, r) {
            var t2 = this.getEventListenersOfType(e2), n2 = t2.findIndex(function(e3) {
              var t3 = e3[0], n3 = e3[1], e3 = e3[2];
              return t3 === o2 && n3 === i && e3 === r;
            });
            o2.removeEventListener(e2, i, r), -1 < n2 && t2.splice(n2, 1);
          }, o.prototype.release = function() {
            var i = this;
            this.attachedListeners.forEach(function(e2, o2) {
              e2.forEach(function(e3) {
                var t2 = e3[0], n2 = e3[1], e3 = e3[2];
                i.off(t2, o2, n2, e3);
              });
            }), this.attachedListeners.clear();
          }, n.NodeEventSubscriber = o;
        }, {}], 105: [function(e, t, n) {
          "use strict";
          var o, i, r = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), s = this && this.__spreadArray || function(e2, t2, n2) {
            if (n2 || 2 === arguments.length) for (var o2, i2 = 0, r2 = t2.length; i2 < r2; i2++) !o2 && i2 in t2 || ((o2 = o2 || Array.prototype.slice.call(t2, 0, i2))[i2] = t2[i2]);
            return e2.concat(o2 || Array.prototype.slice.call(t2));
          }, a = (Object.defineProperty(n, "__esModule", { value: true }), n.RootNavigationGroup = void 0, e("./navigationgroup")), l = e("./types"), e = (i = a.NavigationGroup, r(c, i), c.prototype.handleAction = function(e2) {
            this.container.showUi(), i.prototype.handleAction.call(this, e2);
          }, c.prototype.handleNavigation = function(e2) {
            this.container.showUi(), i.prototype.handleNavigation.call(this, e2);
          }, c.prototype.defaultActionHandler = function(e2) {
            e2 === l.Action.BACK ? this.container.hideUi() : i.prototype.defaultActionHandler.call(this, e2);
          }, c.prototype.release = function() {
            i.prototype.release.call(this);
          }, c);
          function c(e2) {
            for (var t2 = [], n2 = 1; n2 < arguments.length; n2++) t2[n2 - 1] = arguments[n2];
            var o2 = i.apply(this, s([e2], t2, false)) || this;
            return o2.container = e2, o2;
          }
          n.RootNavigationGroup = e;
        }, { "./navigationgroup": 103, "./types": 109 }], 106: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.SeekBarHandler = void 0;
          var i = e("./nodeeventsubscriber"), r = e("./types"), o = e("./navigationalgorithm");
          function s(e2) {
            var o2 = this;
            this.rootNavigationGroup = e2, this.cursorPosition = { x: 0, y: 0 }, this.isScrubbing = false, this.scrubSpeedPercentage = 5e-3, this.onNavigation = function(e3, t2, n2) {
              a(t2) && (e3 === r.Direction.UP || e3 === r.Direction.DOWN ? o2.stopSeeking(l(t2)) : (o2.initializeOrUpdateCursorPosition(t2, e3), o2.dispatchMouseMoveEvent(l(t2)), n2()));
            }, this.onAction = function(e3, t2, n2) {
              a(t2) && (t2 = l(t2), e3 === r.Action.SELECT && o2.isScrubbing ? (o2.dispatchMouseClickEvent(t2), n2()) : e3 === r.Action.BACK && (o2.stopSeeking(t2), n2()));
            }, this.rootNavigationGroup.onAction = this.onAction, this.eventSubscriber = new i.NodeEventSubscriber(), this.rootNavigationGroup.onNavigation = this.onNavigation;
          }
          function a(e2) {
            return -1 < Array.from(e2.classList).findIndex(function(e3) {
              return /-ui-seekbar$/.test(e3);
            });
          }
          function l(e2) {
            return e2.children.item(0);
          }
          s.prototype.updateScrubSpeedPercentage = function() {
            var e2 = this;
            clearTimeout(this.scrubSpeedResetTimeout), this.scrubSpeedPercentage *= 1.1, this.scrubSpeedResetTimeout = window.setTimeout(function() {
              return e2.scrubSpeedPercentage = 5e-3;
            }, 100);
          }, s.prototype.getIncrement = function(e2, t2) {
            this.updateScrubSpeedPercentage();
            t2 = t2.getBoundingClientRect().width * this.scrubSpeedPercentage;
            return e2 === r.Direction.RIGHT ? t2 : -t2;
          }, s.prototype.resetCursorPosition = function() {
            this.cursorPosition.x = 0, this.cursorPosition.y = 0;
          }, s.prototype.updateCursorPosition = function(e2, t2) {
            this.cursorPosition.x += this.getIncrement(e2, t2);
          }, s.prototype.initializeCursorPosition = function(e2) {
            var e2 = e2.querySelector('[class*="seekbar-playbackposition-marker"]'), e2 = (0, o.getBoundingRectFromElement)(e2), t2 = e2.x + e2.width / 2, e2 = e2.y;
            this.cursorPosition.x = t2, this.cursorPosition.y = e2;
          }, s.prototype.initializeOrUpdateCursorPosition = function(e2, t2) {
            this.isScrubbing ? this.updateCursorPosition(t2, e2) : this.initializeCursorPosition(e2), this.isScrubbing = true;
          }, s.prototype.getCursorPositionMouseEventInit = function() {
            return { clientX: this.cursorPosition.x, clientY: this.cursorPosition.y };
          }, s.prototype.dispatchMouseMoveEvent = function(e2) {
            e2.dispatchEvent(new MouseEvent("mousemove", this.getCursorPositionMouseEventInit()));
          }, s.prototype.dispatchMouseClickEvent = function(t2) {
            function n2() {
              var e2 = o2.getCursorPositionMouseEventInit();
              document.dispatchEvent(new MouseEvent("mouseup", e2)), o2.eventSubscriber.off(t2, "mousedown", n2), o2.stopSeeking(t2);
            }
            var o2 = this;
            this.eventSubscriber.on(t2, "mousedown", n2), t2.dispatchEvent(new MouseEvent("mousedown"));
          }, s.prototype.stopSeeking = function(e2) {
            this.resetCursorPosition(), this.isScrubbing = false, this.dispatchMouseLeaveEvent(e2);
          }, s.prototype.dispatchMouseLeaveEvent = function(e2) {
            e2.dispatchEvent(new MouseEvent("mouseleave"));
          }, s.prototype.release = function() {
            this.eventSubscriber.release(), this.rootNavigationGroup.onAction = void 0, this.rootNavigationGroup.onNavigation = void 0;
          }, n.SeekBarHandler = s;
        }, { "./navigationalgorithm": 102, "./nodeeventsubscriber": 104, "./types": 109 }], 107: [function(e, t, n) {
          "use strict";
          var i = this && this.__spreadArray || function(e2, t2, n2) {
            if (n2 || 2 === arguments.length) for (var o2, i2 = 0, r2 = t2.length; i2 < r2; i2++) !o2 && i2 in t2 || ((o2 = o2 || Array.prototype.slice.call(t2, 0, i2))[i2] = t2[i2]);
            return e2.concat(o2 || Array.prototype.slice.call(t2));
          }, r = (Object.defineProperty(n, "__esModule", { value: true }), n.SpatialNavigation = void 0, e("./nodeeventsubscriber")), s = e("./seekbarhandler"), a = e("./keymap"), l = e("./typeguards");
          function o(e2) {
            for (var t2 = [], n2 = 1; n2 < arguments.length; n2++) t2[n2 - 1] = arguments[n2];
            var o2 = this;
            this.navigationGroups = [], this.onShow = function(e3) {
              o2.activeNavigationGroups.push(e3), o2.updateEnabledNavigationGroup();
            }, this.onHide = function(t3) {
              var e3 = o2.activeNavigationGroups.findIndex(function(e4) {
                return e4 === t3;
              });
              -1 < e3 && (t3.disable(), o2.activeNavigationGroups.splice(e3, 1), o2.updateEnabledNavigationGroup());
            }, this.handleKeyEvent = function(e3) {
              var t3 = o2.keyMap[e3.keyCode], n3 = o2.getActiveNavigationGroup();
              n3 && n3.container && !n3.container.isHidden() && !n3.container.isDisabled() && ((0, l.isDirection)(t3) && (n3.handleNavigation(t3), e3.preventDefault(), e3.stopPropagation()), (0, l.isAction)(t3)) && (n3.handleAction(t3), e3.preventDefault(), e3.stopPropagation());
            }, this.seekBarHandler = new s.SeekBarHandler(e2), this.activeNavigationGroups = [], this.unsubscribeVisibilityChangesFns = [], this.eventSubscriber = new r.NodeEventSubscriber(), this.navigationGroups = i([e2], t2, true), this.keyMap = (0, a.getKeyMapForPlatform)(), this.subscribeToNavigationGroupVisibilityChanges(), this.attachKeyEventHandler(), this.enableDefaultNavigationGroup();
          }
          o.prototype.attachKeyEventHandler = function() {
            this.eventSubscriber.on(document, "keydown", this.handleKeyEvent, true);
          }, o.prototype.subscribeToNavigationGroupVisibilityChanges = function() {
            var o2 = this;
            this.navigationGroups.forEach(function(e2) {
              function t2() {
                return o2.onShow(e2);
              }
              function n2() {
                return o2.onHide(e2);
              }
              e2.container.onShow.subscribe(t2), e2.container.onHide.subscribe(n2), o2.unsubscribeVisibilityChangesFns.push(function() {
                return e2.container.onShow.unsubscribe(t2);
              }, function() {
                return e2.container.onHide.unsubscribe(n2);
              });
            });
          }, o.prototype.unsubscribeFromNavigationGroupVisibilityChanges = function() {
            this.unsubscribeVisibilityChangesFns.forEach(function(e2) {
              return e2();
            }), this.unsubscribeVisibilityChangesFns = [];
          }, o.prototype.enableDefaultNavigationGroup = function() {
            var e2 = null != (e2 = this.navigationGroups.find(function(e3) {
              return e3.container.isShown();
            })) ? e2 : this.navigationGroups[0];
            e2 && (this.activeNavigationGroups.push(e2), this.updateEnabledNavigationGroup());
          }, o.prototype.updateEnabledNavigationGroup = function() {
            var n2 = this;
            this.activeNavigationGroups.forEach(function(e2, t2) {
              t2 < n2.activeNavigationGroups.length - 1 ? e2.disable() : e2.enable();
            });
          }, o.prototype.getActiveNavigationGroup = function() {
            return this.activeNavigationGroups[this.activeNavigationGroups.length - 1];
          }, o.prototype.release = function() {
            this.unsubscribeFromNavigationGroupVisibilityChanges(), this.eventSubscriber.release(), this.navigationGroups.forEach(function(e2) {
              return e2.release();
            }), this.seekBarHandler.release();
          }, n.SpatialNavigation = o;
        }, { "./keymap": 101, "./nodeeventsubscriber": 104, "./seekbarhandler": 106, "./typeguards": 108 }], 108: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.isAction = n.isDirection = n.isListBox = n.isContainer = n.isComponent = n.isSettingsPanel = void 0;
          var o = e("../components/component"), i = e("../components/settingspanel"), r = e("../components/container"), s = e("../components/listbox"), a = e("./types");
          n.isSettingsPanel = function(e2) {
            return e2 instanceof i.SettingsPanel;
          }, n.isComponent = function(e2) {
            return null != e2 && e2 instanceof o.Component;
          }, n.isContainer = function(e2) {
            return null != e2 && e2 instanceof r.Container;
          }, n.isListBox = function(e2) {
            return e2 instanceof s.ListBox;
          }, n.isDirection = function(e2) {
            return "string" == typeof e2 && Object.values(a.Direction).includes(e2);
          }, n.isAction = function(e2) {
            return "string" == typeof e2 && Object.values(a.Action).includes(e2);
          };
        }, { "../components/component": 18, "../components/container": 19, "../components/listbox": 29, "../components/settingspanel": 45, "./types": 109 }], 109: [function(e, t, n) {
          "use strict";
          var o;
          Object.defineProperty(n, "__esModule", { value: true }), n.Action = n.Direction = void 0, (o = n.Direction || (n.Direction = {})).UP = "up", o.DOWN = "down", o.LEFT = "left", o.RIGHT = "right", (o = n.Action || (n.Action = {})).SELECT = "select", o.BACK = "back";
        }, {}], 110: [function(e, t, n) {
          "use strict";
          var o;
          function i() {
            try {
              return !o && window.localStorage && "function" == typeof localStorage.getItem && "function" == typeof localStorage.setItem;
            } catch (e2) {
            }
          }
          function r(t2, e2) {
            if (i()) try {
              window.localStorage.setItem(t2, e2);
            } catch (e3) {
              console.debug("Failed to set storage item ".concat(t2), e3);
            }
          }
          function s(t2) {
            if (i()) try {
              return window.localStorage.getItem(t2);
            } catch (e2) {
              console.debug("Failed to get storage item ".concat(t2), e2);
            }
            return null;
          }
          Object.defineProperty(n, "__esModule", { value: true }), n.StorageUtils = void 0, (n = n.StorageUtils || (n.StorageUtils = {})).setStorageApiDisabled = function(e2) {
            o = e2.disableStorageApi;
          }, n.setItem = r, n.getItem = s, n.setObject = function(e2, t2) {
            r(e2, JSON.stringify(t2));
          }, n.getObject = function(e2) {
            return (e2 = s(e2)) ? JSON.parse(e2) : null;
          };
        }, {}], 111: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.StringUtils = void 0;
          var r, i = e("./localization/i18n");
          function a(e2, t2) {
            void 0 === t2 && (t2 = r.FORMAT_HHMMSS);
            var n2 = e2 < 0, o = (n2 && (e2 = -e2), Math.floor(e2 / 3600)), i2 = Math.floor(e2 / 60) - 60 * o, e2 = Math.floor(e2) % 60;
            return (n2 ? "-" : "") + t2.replace("hh", l(o, 2)).replace("mm", l(i2, 2)).replace("ss", l(e2, 2));
          }
          function l(e2, t2) {
            e2 += "";
            return "0000000000".substr(0, t2 - e2.length) + e2;
          }
          (r = n.StringUtils || (n.StringUtils = {})).FORMAT_HHMMSS = "hh:mm:ss", r.FORMAT_MMSS = "mm:ss", r.secondsToTime = a, r.secondsToText = function(e2) {
            var t2 = e2 < 0, n2 = (t2 && (e2 = -e2), Math.floor(e2 / 3600)), o = Math.floor(e2 / 60) - 60 * n2, e2 = Math.floor(e2) % 60;
            return (t2 ? "-" : "") + (0 !== n2 ? "".concat(n2, " ").concat(i.i18n.performLocalization(i.i18n.getLocalizer("settings.time.hours")), " ") : "") + (0 != o ? "".concat(o, " ").concat(i.i18n.performLocalization(i.i18n.getLocalizer("settings.time.minutes")), " ") : "") + "".concat(e2, " ").concat(i.i18n.performLocalization(i.i18n.getLocalizer("settings.time.seconds")));
          }, r.replaceAdMessagePlaceholders = function(e2, r2, s) {
            var t2 = new RegExp("\\{(remainingTime|playedTime|adDuration|adBreakRemainingTime)(}|%((0[1-9]\\d*(\\.\\d+(d|f)|d|f)|\\.\\d+f|d|f)|hh:mm:ss|mm:ss)})", "g");
            return e2.replace(t2, function(e3) {
              var t3 = 0, n2 = (-1 < e3.indexOf("remainingTime") ? t3 = r2 ? Math.ceil(r2 - s.getCurrentTime()) : s.getDuration() - s.getCurrentTime() : -1 < e3.indexOf("playedTime") ? t3 = s.getCurrentTime() : -1 < e3.indexOf("adDuration") ? t3 = s.getDuration() : -1 < e3.indexOf("adBreakRemainingTime") && (t3 = 0, s.ads.isLinearAdActive()) && (n2 = s.ads.getActiveAdBreak().ads.findIndex(function(e4) {
                return s.ads.getActiveAd().id === e4.id;
              }), t3 = s.ads.getActiveAdBreak().ads.slice(n2).reduce(function(e4, t4) {
                return e4 + (t4.isLinear ? t4.duration : 0);
              }, 0) - s.getCurrentTime()), Math.round(t3)), t3 = e3, e3 = (/%((0[1-9]\d*(\.\d+(d|f)|d|f)|\.\d+f|d|f)|hh:mm:ss|mm:ss)/.test(t3) || (t3 = "%d"), 0), o = ((o = t3.match(/(%0[1-9]\d*)(?=(\.\d+f|f|d))/)) && (e3 = parseInt(o[0].substring(2))), null), i2 = t3.match(/\.\d*(?=f)/);
              return i2 && !isNaN(parseInt(i2[0].substring(1))) && 20 < (o = parseInt(i2[0].substring(1))) && (o = 20), -1 < t3.indexOf("f") ? (i2 = "", -1 < (i2 = null !== o ? n2.toFixed(o) : "" + n2).indexOf(".") ? l(i2, i2.length + (e3 - i2.indexOf("."))) : l(i2, e3)) : -1 < t3.indexOf(":") ? (o = Math.ceil(n2), -1 < t3.indexOf("hh") ? a(o) : (i2 = Math.floor(o / 60), t3 = o % 60, l(i2, 2) + ":" + l(t3, 2))) : l(Math.ceil(n2), e3);
            });
          };
        }, { "./localization/i18n": 91 }], 112: [function(e, t, n) {
          "use strict";
          var i = this && this.__spreadArray || function(e2, t2, n2) {
            if (n2 || 2 === arguments.length) for (var o, i2 = 0, r2 = t2.length; i2 < r2; i2++) !o && i2 in t2 || ((o = o || Array.prototype.slice.call(t2, 0, i2))[i2] = t2[i2]);
            return e2.concat(o || Array.prototype.slice.call(t2));
          }, r = (Object.defineProperty(n, "__esModule", { value: true }), n.SubtitleSwitchHandler = void 0, e("./localization/i18n"));
          n.SubtitleSwitchHandler = (s.prototype.bindSelectionEvent = function() {
            var o = this;
            this.listElement.onItemSelected.subscribe(function(e2, t2) {
              var n2;
              t2 === s.SUBTITLES_OFF_KEY ? (n2 = o.player.subtitles.list().filter(function(e3) {
                return e3.enabled;
              }).pop()) && o.player.subtitles.disable(n2.id) : o.player.subtitles.enable(t2, true);
            });
          }, s.prototype.bindPlayerEvents = function() {
            this.player.on(this.player.exports.PlayerEvent.SubtitleAdded, this.addSubtitle), this.player.on(this.player.exports.PlayerEvent.SubtitleEnabled, this.selectCurrentSubtitle), this.player.on(this.player.exports.PlayerEvent.SubtitleDisabled, this.selectCurrentSubtitle), this.player.on(this.player.exports.PlayerEvent.SubtitleRemoved, this.removeSubtitle), this.player.on(this.player.exports.PlayerEvent.SourceUnloaded, this.clearSubtitles), this.player.on(this.player.exports.PlayerEvent.PeriodSwitched, this.refreshSubtitles), this.uimanager.getConfig().events.onUpdated.subscribe(this.refreshSubtitles);
          }, s.SUBTITLES_OFF_KEY = "null", s);
          function s(e2, t2, n2) {
            var o = this;
            this.addSubtitle = function(e3) {
              e3 = e3.subtitle;
              o.listElement.hasItem(e3.id) || o.listElement.addItem(e3.id, e3.label);
            }, this.removeSubtitle = function(e3) {
              e3 = e3.subtitle;
              o.listElement.hasItem(e3.id) && o.listElement.removeItem(e3.id);
            }, this.selectCurrentSubtitle = function() {
              var e3;
              o.player.subtitles && (e3 = o.player.subtitles.list().filter(function(e4) {
                return e4.enabled;
              }).pop(), o.listElement.selectItem(e3 ? e3.id : s.SUBTITLES_OFF_KEY));
            }, this.clearSubtitles = function() {
              o.listElement.clearItems();
            }, this.refreshSubtitles = function() {
              var e3, t3;
              o.player.subtitles && (e3 = { key: s.SUBTITLES_OFF_KEY, label: r.i18n.getLocalizer("off") }, t3 = o.player.subtitles.list(), o.listElement.synchronizeItems(i([e3], t3.map(function(e4) {
                return { key: e4.id, label: e4.label };
              }), true)), o.selectCurrentSubtitle());
            }, this.player = e2, this.listElement = t2, this.uimanager = n2, this.bindSelectionEvent(), this.bindPlayerEvents(), this.refreshSubtitles();
          }
        }, { "./localization/i18n": 91 }], 113: [function(e, t, n) {
          "use strict";
          function o(e2, t2, n2) {
            void 0 === n2 && (n2 = false), this.delay = e2, this.callback = t2, this.repeat = n2, this.timeoutOrIntervalId = 0, this.active = false, this.suspended = false;
          }
          Object.defineProperty(n, "__esModule", { value: true }), n.Timeout = void 0, o.prototype.start = function() {
            return this.reset(), this;
          }, o.prototype.clear = function() {
            this.clearInternal();
          }, o.prototype.suspend = function() {
            return this.suspended = true, this.clearInternal(), this;
          }, o.prototype.resume = function(e2) {
            return this.suspended = false, e2 && this.reset(), this;
          }, o.prototype.reset = function() {
            var e2 = this;
            this.clearInternal(), this.suspended || (this.repeat ? this.timeoutOrIntervalId = setInterval(this.callback, this.delay) : this.timeoutOrIntervalId = setTimeout(function() {
              e2.active = false, e2.callback();
            }, this.delay), this.active = true);
          }, o.prototype.isActive = function() {
            return this.active;
          }, o.prototype.clearInternal = function() {
            (this.repeat ? clearInterval : clearTimeout)(this.timeoutOrIntervalId), this.active = false;
          }, n.Timeout = o;
        }, {}], 114: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true });
        }, {}], 115: [function(e, D, t) {
          "use strict";
          var n, o = this && this.__assign || function() {
            return (o = Object.assign || function(e2) {
              for (var t2, n2 = 1, o2 = arguments.length; n2 < o2; n2++) for (var i2 in t2 = arguments[n2]) Object.prototype.hasOwnProperty.call(t2, i2) && (e2[i2] = t2[i2]);
              return e2;
            }).apply(this, arguments);
          }, c = (Object.defineProperty(t, "__esModule", { value: true }), t.UIFactory = void 0, e("./components/subtitleoverlay")), u = e("./components/settingspanelpage"), p = e("./components/settingspanelitem"), s = e("./components/videoqualityselectbox"), a = e("./components/playbackspeedselectbox"), U = e("./components/audiotrackselectbox"), z = e("./components/audioqualityselectbox"), g = e("./components/settingspanel"), R = e("./components/subtitlesettings/subtitlesettingspanelpage"), F = e("./components/settingspanelpageopenbutton"), V = e("./components/subtitlesettings/subtitlesettingslabel"), H = e("./components/subtitleselectbox"), f = e("./components/controlbar"), d = e("./components/container"), h = e("./components/playbacktimelabel"), y = e("./components/seekbar"), m = e("./components/seekbarlabel"), l = e("./components/playbacktogglebutton"), b = e("./components/volumetogglebutton"), N = e("./components/volumeslider"), W = e("./components/spacer"), G = e("./components/pictureinpicturetogglebutton"), q = e("./components/airplaytogglebutton"), K = e("./components/casttogglebutton"), Q = e("./components/vrtogglebutton"), v = e("./components/settingstogglebutton"), C = e("./components/fullscreentogglebutton"), S = e("./components/uicontainer"), P = e("./components/bufferingoverlay"), w = e("./components/playbacktoggleoverlay"), Y = e("./components/caststatusoverlay"), E = e("./components/titlebar"), _ = e("./components/recommendationoverlay"), O = e("./components/watermark"), k = e("./components/errormessageoverlay"), i = e("./components/adclickoverlay"), r = e("./components/admessagelabel"), x = e("./components/adskipbutton"), X = e("./components/closebutton"), T = e("./components/metadatalabel"), M = e("./playerutils"), Z = e("./components/label"), J = e("./components/castuicontainer"), L = e("./uimanager"), A = e("./localization/i18n"), $ = e("./components/subtitlelistbox"), ee = e("./components/audiotracklistbox"), te = e("./spatialnavigation/spatialnavigation"), ne = e("./spatialnavigation/rootnavigationgroup"), I = e("./spatialnavigation/ListNavigationGroup"), oe = e("./components/ecomodecontainer");
          function ie(e2) {
            var t2 = new c.SubtitleOverlay(), n2 = [new p.SettingsPanelItem(A.i18n.getLocalizer("settings.video.quality"), new s.VideoQualitySelectBox()), new p.SettingsPanelItem(A.i18n.getLocalizer("speed"), new a.PlaybackSpeedSelectBox()), new p.SettingsPanelItem(A.i18n.getLocalizer("settings.audio.track"), new U.AudioTrackSelectBox()), new p.SettingsPanelItem(A.i18n.getLocalizer("settings.audio.quality"), new z.AudioQualitySelectBox())], o2 = (e2.ecoMode && ((e2 = new oe.EcoModeContainer()).setOnToggleCallback(function() {
              o2.getDomElement().css({ width: "", height: "" });
            }), n2.unshift(e2)), e2 = new u.SettingsPanelPage({ components: n2 }), new g.SettingsPanel({ components: [e2], hidden: true })), n2 = new R.SubtitleSettingsPanelPage({ settingsPanel: o2, overlay: t2 }), i2 = new H.SubtitleSelectBox(), r2 = new F.SettingsPanelPageOpenButton({ targetPage: n2, container: o2, ariaLabel: A.i18n.getLocalizer("settings.subtitles"), text: A.i18n.getLocalizer("open") }), e2 = (e2.addComponent(new p.SettingsPanelItem(new V.SubtitleSettingsLabel({ text: A.i18n.getLocalizer("settings.subtitles"), opener: r2 }), i2, { role: "menubar" })), o2.addComponent(n2), new f.ControlBar({ components: [o2, new d.Container({ components: [new h.PlaybackTimeLabel({ timeLabelMode: h.PlaybackTimeLabelMode.CurrentTime, hideInLivePlayback: true }), new y.SeekBar({ label: new m.SeekBarLabel() }), new h.PlaybackTimeLabel({ timeLabelMode: h.PlaybackTimeLabelMode.TotalTime, cssClasses: ["text-right"] })], cssClasses: ["controlbar-top"] }), new d.Container({ components: [new l.PlaybackToggleButton(), new b.VolumeToggleButton(), new N.VolumeSlider(), new W.Spacer(), new G.PictureInPictureToggleButton(), new q.AirPlayToggleButton(), new K.CastToggleButton(), new Q.VRToggleButton(), new v.SettingsToggleButton({ settingsPanel: o2 }), new C.FullscreenToggleButton()], cssClasses: ["controlbar-bottom"] })] }));
            return new S.UIContainer({ components: [t2, new P.BufferingOverlay(), new w.PlaybackToggleOverlay(), new Y.CastStatusOverlay(), e2, new E.TitleBar(), new _.RecommendationOverlay(), new O.Watermark(), new k.ErrorMessageOverlay()], hideDelay: 2e3, hidePlayerStateExceptions: [M.PlayerUtils.PlayerState.Prepared, M.PlayerUtils.PlayerState.Paused, M.PlayerUtils.PlayerState.Finished] });
          }
          function re() {
            return new S.UIContainer({ components: [new P.BufferingOverlay(), new i.AdClickOverlay(), new w.PlaybackToggleOverlay(), new d.Container({ components: [new r.AdMessageLabel({ text: A.i18n.getLocalizer("ads.remainingTime") }), new x.AdSkipButton()], cssClass: "ui-ads-status" }), new f.ControlBar({ components: [new d.Container({ components: [new l.PlaybackToggleButton(), new b.VolumeToggleButton(), new N.VolumeSlider(), new W.Spacer(), new C.FullscreenToggleButton()], cssClasses: ["controlbar-bottom"] })] })], cssClasses: ["ui-skin-ads"], hideDelay: 2e3, hidePlayerStateExceptions: [M.PlayerUtils.PlayerState.Prepared, M.PlayerUtils.PlayerState.Paused, M.PlayerUtils.PlayerState.Finished] });
          }
          function B() {
            var e2 = new c.SubtitleOverlay(), t2 = new u.SettingsPanelPage({ components: [new p.SettingsPanelItem(A.i18n.getLocalizer("settings.video.quality"), new s.VideoQualitySelectBox()), new p.SettingsPanelItem(A.i18n.getLocalizer("speed"), new a.PlaybackSpeedSelectBox()), new p.SettingsPanelItem(A.i18n.getLocalizer("settings.audio.track"), new U.AudioTrackSelectBox()), new p.SettingsPanelItem(A.i18n.getLocalizer("settings.audio.quality"), new z.AudioQualitySelectBox())] }), n2 = new g.SettingsPanel({ components: [t2], hidden: true, pageTransitionAnimation: false, hideDelay: -1 }), o2 = new R.SubtitleSettingsPanelPage({ settingsPanel: n2, overlay: e2 }), i2 = new F.SettingsPanelPageOpenButton({ targetPage: o2, container: n2, ariaLabel: A.i18n.getLocalizer("settings.subtitles"), text: A.i18n.getLocalizer("open") }), r2 = new H.SubtitleSelectBox(), t2 = (t2.addComponent(new p.SettingsPanelItem(new V.SubtitleSettingsLabel({ text: A.i18n.getLocalizer("settings.subtitles"), opener: i2 }), r2, { role: "menubar" })), n2.addComponent(o2), n2.addComponent(new X.CloseButton({ target: n2 })), o2.addComponent(new X.CloseButton({ target: n2 })), new f.ControlBar({ components: [new d.Container({ components: [new h.PlaybackTimeLabel({ timeLabelMode: h.PlaybackTimeLabelMode.CurrentTime, hideInLivePlayback: true }), new y.SeekBar({ label: new m.SeekBarLabel() }), new h.PlaybackTimeLabel({ timeLabelMode: h.PlaybackTimeLabelMode.TotalTime, cssClasses: ["text-right"] })], cssClasses: ["controlbar-top"] })] }));
            return new S.UIContainer({ components: [e2, new P.BufferingOverlay(), new Y.CastStatusOverlay(), new w.PlaybackToggleOverlay(), new _.RecommendationOverlay(), t2, new E.TitleBar({ components: [new T.MetadataLabel({ content: T.MetadataLabelContent.Title }), new K.CastToggleButton(), new Q.VRToggleButton(), new G.PictureInPictureToggleButton(), new q.AirPlayToggleButton(), new b.VolumeToggleButton(), new v.SettingsToggleButton({ settingsPanel: n2 }), new C.FullscreenToggleButton()] }), n2, new O.Watermark(), new k.ErrorMessageOverlay()], cssClasses: ["ui-skin-smallscreen"], hideDelay: 2e3, hidePlayerStateExceptions: [M.PlayerUtils.PlayerState.Prepared, M.PlayerUtils.PlayerState.Paused, M.PlayerUtils.PlayerState.Finished] });
          }
          function j() {
            return new S.UIContainer({ components: [new P.BufferingOverlay(), new i.AdClickOverlay(), new w.PlaybackToggleOverlay(), new E.TitleBar({ components: [new Z.Label({ cssClass: "label-metadata-title" }), new C.FullscreenToggleButton()] }), new d.Container({ components: [new r.AdMessageLabel({ text: "Ad: {remainingTime} secs" }), new x.AdSkipButton()], cssClass: "ui-ads-status" })], cssClasses: ["ui-skin-ads", "ui-skin-smallscreen"], hideDelay: 2e3, hidePlayerStateExceptions: [M.PlayerUtils.PlayerState.Prepared, M.PlayerUtils.PlayerState.Paused, M.PlayerUtils.PlayerState.Finished] });
          }
          function se() {
            var e2 = new f.ControlBar({ components: [new d.Container({ components: [new h.PlaybackTimeLabel({ timeLabelMode: h.PlaybackTimeLabelMode.CurrentTime, hideInLivePlayback: true }), new y.SeekBar({ smoothPlaybackPositionUpdateIntervalMs: -1 }), new h.PlaybackTimeLabel({ timeLabelMode: h.PlaybackTimeLabelMode.TotalTime, cssClasses: ["text-right"] })], cssClasses: ["controlbar-top"] })] });
            return new J.CastUIContainer({ components: [new c.SubtitleOverlay(), new P.BufferingOverlay(), new w.PlaybackToggleOverlay(), new O.Watermark(), e2, new E.TitleBar({ keepHiddenWithoutMetadata: true }), new k.ErrorMessageOverlay()], cssClasses: ["ui-skin-cast-receiver"], hideDelay: 2e3, hidePlayerStateExceptions: [M.PlayerUtils.PlayerState.Prepared, M.PlayerUtils.PlayerState.Paused, M.PlayerUtils.PlayerState.Finished] });
          }
          function ae() {
            var e2 = new $.SubtitleListBox(), t2 = new g.SettingsPanel({ components: [new u.SettingsPanelPage({ components: [new p.SettingsPanelItem(null, e2)] })], hidden: true }), n2 = new ee.AudioTrackListBox(), o2 = new g.SettingsPanel({ components: [new u.SettingsPanelPage({ components: [new p.SettingsPanelItem(null, n2)] })], hidden: true }), i2 = new y.SeekBar({ label: new m.SeekBarLabel() }), r2 = new w.PlaybackToggleOverlay(), s2 = new v.SettingsToggleButton({ settingsPanel: t2, autoHideWhenNoActiveSettings: true, cssClass: "ui-subtitlesettingstogglebutton", text: A.i18n.getLocalizer("settings.subtitles") }), a2 = new v.SettingsToggleButton({ settingsPanel: o2, autoHideWhenNoActiveSettings: true, cssClass: "ui-audiotracksettingstogglebutton", ariaLabel: A.i18n.getLocalizer("settings.audio.track"), text: A.i18n.getLocalizer("settings.audio.track") }), l2 = new S.UIContainer({ components: [new c.SubtitleOverlay(), new P.BufferingOverlay(), r2, new f.ControlBar({ components: [new d.Container({ components: [new h.PlaybackTimeLabel({ timeLabelMode: h.PlaybackTimeLabelMode.CurrentTime, hideInLivePlayback: true }), i2, new h.PlaybackTimeLabel({ timeLabelMode: h.PlaybackTimeLabelMode.RemainingTime, cssClasses: ["text-right"] })], cssClasses: ["controlbar-top"] })] }), new E.TitleBar({ components: [new d.Container({ components: [new T.MetadataLabel({ content: T.MetadataLabelContent.Title }), s2, a2], cssClasses: ["ui-titlebar-top"] }), new d.Container({ components: [new T.MetadataLabel({ content: T.MetadataLabelContent.Description }), t2, o2], cssClasses: ["ui-titlebar-bottom"] })] }), new _.RecommendationOverlay(), new k.ErrorMessageOverlay()], cssClasses: ["ui-skin-tv"], hideDelay: 2e3, hidePlayerStateExceptions: [M.PlayerUtils.PlayerState.Prepared, M.PlayerUtils.PlayerState.Paused, M.PlayerUtils.PlayerState.Finished] });
            return { ui: l2, spatialNavigation: new te.SpatialNavigation(new ne.RootNavigationGroup(l2, r2, i2, a2, s2), new I.ListNavigationGroup(I.ListOrientation.Vertical, t2, e2), new I.ListNavigationGroup(I.ListOrientation.Vertical, o2, n2)) };
          }
          function le() {
            var e2 = new x.AdSkipButton(), t2 = new l.PlaybackToggleButton(), n2 = new S.UIContainer({ components: [new P.BufferingOverlay(), new i.AdClickOverlay(), new w.PlaybackToggleOverlay(), new d.Container({ components: [new r.AdMessageLabel({ text: A.i18n.getLocalizer("ads.remainingTime") }), e2], cssClass: "ui-ads-status" }), new f.ControlBar({ components: [new d.Container({ components: [t2], cssClasses: ["controlbar-bottom"] })] })], cssClasses: ["ui-skin-tv", "ui-skin-ads"], hideDelay: 2e3, hidePlayerStateExceptions: [M.PlayerUtils.PlayerState.Prepared, M.PlayerUtils.PlayerState.Paused, M.PlayerUtils.PlayerState.Finished] });
            return { ui: n2, spatialNavigation: new te.SpatialNavigation(new ne.RootNavigationGroup(n2, t2, e2)) };
          }
          (n = t.UIFactory || (t.UIFactory = {})).buildDefaultUI = function(e2, t2) {
            return n.buildModernUI(e2, t2 = void 0 === t2 ? {} : t2);
          }, n.buildDefaultSmallScreenUI = function(e2, t2) {
            return n.buildModernSmallScreenUI(e2, t2 = void 0 === t2 ? {} : t2);
          }, n.buildDefaultCastReceiverUI = function(e2, t2) {
            return n.buildModernCastReceiverUI(e2, t2 = void 0 === t2 ? {} : t2);
          }, n.buildDefaultTvUI = function(e2, t2) {
            return n.buildModernTvUI(e2, t2 = void 0 === t2 ? {} : t2);
          }, n.modernUI = ie, n.modernAdsUI = re, n.modernSmallScreenUI = B, n.modernSmallScreenAdsUI = j, n.modernCastReceiverUI = se, n.buildModernUI = function(e2, t2) {
            return void 0 === t2 && (t2 = {}), new L.UIManager(e2, [{ ui: j(), condition: function(e3) {
              return e3.isMobile && e3.documentWidth < 600 && e3.isAd && e3.adRequiresUi;
            } }, { ui: re(), condition: function(e3) {
              return e3.isAd && e3.adRequiresUi;
            } }, { ui: B(), condition: function(e3) {
              return !e3.isAd && !e3.adRequiresUi && e3.isMobile && e3.documentWidth < 600;
            } }, { ui: ie(t2), condition: function(e3) {
              return !e3.isAd && !e3.adRequiresUi;
            } }], t2);
          }, n.buildModernSmallScreenUI = function(e2, t2) {
            return void 0 === t2 && (t2 = {}), new L.UIManager(e2, [{ ui: j(), condition: function(e3) {
              return e3.isAd && e3.adRequiresUi;
            } }, { ui: B(), condition: function(e3) {
              return !e3.isAd && !e3.adRequiresUi;
            } }], t2);
          }, n.buildModernCastReceiverUI = function(e2, t2) {
            return void 0 === t2 && (t2 = {}), new L.UIManager(e2, se(), t2);
          }, n.buildModernTvUI = function(e2, t2) {
            return void 0 === t2 && (t2 = {}), new L.UIManager(e2, [o(o({}, le()), { condition: function(e3) {
              return e3.isAd && e3.adRequiresUi;
            } }), o(o({}, ae()), { condition: function(e3) {
              return !e3.isAd && !e3.adRequiresUi;
            } })], t2);
          }, n.modernTvUI = ae, n.modernTvAdsUI = le;
        }, { "./components/adclickoverlay": 4, "./components/admessagelabel": 5, "./components/adskipbutton": 6, "./components/airplaytogglebutton": 7, "./components/audioqualityselectbox": 8, "./components/audiotracklistbox": 9, "./components/audiotrackselectbox": 10, "./components/bufferingoverlay": 11, "./components/caststatusoverlay": 13, "./components/casttogglebutton": 14, "./components/castuicontainer": 15, "./components/closebutton": 17, "./components/container": 19, "./components/controlbar": 20, "./components/ecomodecontainer": 21, "./components/errormessageoverlay": 23, "./components/fullscreentogglebutton": 24, "./components/label": 28, "./components/metadatalabel": 31, "./components/pictureinpicturetogglebutton": 32, "./components/playbackspeedselectbox": 33, "./components/playbacktimelabel": 34, "./components/playbacktogglebutton": 35, "./components/playbacktoggleoverlay": 36, "./components/recommendationoverlay": 38, "./components/seekbar": 40, "./components/seekbarlabel": 43, "./components/settingspanel": 45, "./components/settingspanelitem": 46, "./components/settingspanelpage": 47, "./components/settingspanelpageopenbutton": 50, "./components/settingstogglebutton": 51, "./components/spacer": 52, "./components/subtitlelistbox": 53, "./components/subtitleoverlay": 54, "./components/subtitleselectbox": 55, "./components/subtitlesettings/subtitlesettingslabel": 66, "./components/subtitlesettings/subtitlesettingspanelpage": 68, "./components/titlebar": 73, "./components/uicontainer": 76, "./components/videoqualityselectbox": 77, "./components/volumeslider": 79, "./components/volumetogglebutton": 80, "./components/vrtogglebutton": 81, "./components/watermark": 82, "./localization/i18n": 91, "./playerutils": 98, "./spatialnavigation/ListNavigationGroup": 99, "./spatialnavigation/rootnavigationgroup": 105, "./spatialnavigation/spatialnavigation": 107, "./uimanager": 116 }], 116: [function(e, t, n) {
          "use strict";
          var o, i = this && this.__extends || (o = function(e2, t2) {
            return (o = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? function(e3, t3) {
              e3.__proto__ = t3;
            } : function(e3, t3) {
              for (var n2 in t3) Object.prototype.hasOwnProperty.call(t3, n2) && (e3[n2] = t3[n2]);
            }))(e2, t2);
          }, function(e2, t2) {
            if ("function" != typeof t2 && null !== t2) throw new TypeError("Class extends value " + String(t2) + " is not a constructor or null");
            function n2() {
              this.constructor = e2;
            }
            o(e2, t2), e2.prototype = null === t2 ? Object.create(t2) : (n2.prototype = t2.prototype, new n2());
          }), g = this && this.__assign || function() {
            return (g = Object.assign || function(e2) {
              for (var t2, n2 = 1, o2 = arguments.length; n2 < o2; n2++) for (var i2 in t2 = arguments[n2]) Object.prototype.hasOwnProperty.call(t2, i2) && (e2[i2] = t2[i2]);
              return e2;
            }).apply(this, arguments);
          }, d = this && this.__spreadArray || function(e2, t2, n2) {
            if (n2 || 2 === arguments.length) for (var o2, i2 = 0, r2 = t2.length; i2 < r2; i2++) !o2 && i2 in t2 || ((o2 = o2 || Array.prototype.slice.call(t2, 0, i2))[i2] = t2[i2]);
            return e2.concat(o2 || Array.prototype.slice.call(t2));
          }, f = (Object.defineProperty(n, "__esModule", { value: true }), n.PlayerWrapper = n.UIInstanceManager = n.UIManager = void 0, e("./components/uicontainer")), h = e("./dom"), r = e("./components/container"), y = e("./eventdispatcher"), s = e("./uiutils"), m = e("./arrayutils"), c = e("./browserutils"), b = e("./volumecontroller"), a = e("./localization/i18n"), v = e("./focusvisibilitytracker"), C = e("./mobilev3playerapi"), S = e("./components/subtitlesettings/subtitlesettingsmanager"), P = e("./storageutils");
          function l(i2, e2, t2) {
            void 0 === t2 && (t2 = {});
            for (var r2 = this, n2 = (this.events = { onUiVariantResolve: new y.EventDispatcher(), onActiveUiChanged: new y.EventDispatcher() }, e2 instanceof f.UIContainer ? ((o2 = []).push({ ui: e2 }), this.uiVariants = o2) : this.uiVariants = e2, this.subtitleSettingsManager = new S.SubtitleSettingsManager(), this.player = i2, this.managerPlayerWrapper = new _(i2), t2.metadata = t2.metadata || {}, this.config = g(g({ playbackSpeedSelectionEnabled: true, autoUiVariantResolve: true, disableAutoHideWhenHovered: false, enableSeekPreview: true }, t2), { events: { onUpdated: new y.EventDispatcher() }, volumeController: new b.VolumeController(this.managerPlayerWrapper.getPlayer()) }), function() {
              var e3 = i2.getSource() || {}, e3 = (r2.config.metadata = JSON.parse(JSON.stringify(t2.metadata || {})), { metadata: { title: e3.title, description: e3.description, markers: e3.markers }, recommendations: e3.recommendations });
              r2.config.metadata.title = e3.metadata.title || t2.metadata.title, r2.config.metadata.description = e3.metadata.description || t2.metadata.description, r2.config.metadata.markers = e3.metadata.markers || t2.metadata.markers || [], r2.config.recommendations = e3.recommendations || t2.recommendations || [], P.StorageUtils.setStorageApiDisabled(t2);
            }), o2 = (n2(), this.subtitleSettingsManager.initialize(), function() {
              n2(), r2.config.events.onUpdated.dispatch(r2);
            }), e2 = this.managerPlayerWrapper.getPlayer(), s2 = (e2.on(this.player.exports.PlayerEvent.SourceLoaded, o2), (0, C.isMobileV3PlayerAPI)(e2) && e2.on(C.MobileV3PlayerEvent.PlaylistTransition, o2), t2.container ? this.uiContainerElement = (t2.container instanceof HTMLElement, new h.DOM(t2.container)) : this.uiContainerElement = new h.DOM(i2.getContainer()), this.uiInstanceManagers = [], []), a2 = 0, l2 = this.uiVariants; a2 < l2.length; a2++) {
              var c2 = l2[a2];
              null == c2.condition && s2.push(c2), this.uiInstanceManagers.push(new w(i2, c2.ui, this.config, this.subtitleSettingsManager, c2.spatialNavigation));
            }
            if (1 < s2.length) throw Error("Too many UIs without a condition: You cannot have more than one default UI");
            if (0 < s2.length && s2[0] !== this.uiVariants[this.uiVariants.length - 1]) throw Error("Invalid UI variant order: the default UI (without condition) must be at the end of the list");
            function u2(e3) {
              if (null != e3) switch (e3.type) {
                case i2.exports.PlayerEvent.AdStarted:
                  p2 = e3;
                  break;
                case i2.exports.PlayerEvent.AdBreakFinished:
                  p2 = null, r2.config.events.onUpdated.dispatch(r2);
                  break;
                case i2.exports.PlayerEvent.SourceLoaded:
                case i2.exports.PlayerEvent.SourceUnloaded:
                  p2 = null;
              }
              var t3, n3 = null != p2, o3 = false;
              (o3 = n3 && (t3 = p2.ad).isLinear ? t3.uiConfig && t3.uiConfig.requestsUi || false : o3) && r2.config.events.onUpdated.dispatch(r2), r2.resolveUiVariant({ isAd: n3, adRequiresUi: o3 }, function(e4) {
                e4.isAd && r2.currentUi.getWrappedPlayer().fireEventInUI(r2.player.exports.PlayerEvent.AdStarted, p2);
              });
            }
            var p2 = null;
            this.config.autoUiVariantResolve && (this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.SourceLoaded, u2), this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.SourceUnloaded, u2), this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.Play, u2), this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.Paused, u2), this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.AdStarted, u2), this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.AdBreakFinished, u2), this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.PlayerResized, u2), this.managerPlayerWrapper.getPlayer().on(this.player.exports.PlayerEvent.ViewModeChanged, u2)), this.focusVisibilityTracker = new v.FocusVisibilityTracker("bmpui"), u2(null);
          }
          l.localize = function(e2) {
            return a.i18n.getLocalizer(e2);
          }, l.setLocalizationConfig = function(e2) {
            a.i18n.setConfig(e2);
          }, l.prototype.getSubtitleSettingsManager = function() {
            return this.subtitleSettingsManager;
          }, l.prototype.getConfig = function() {
            return this.config;
          }, l.prototype.getUiVariants = function() {
            return this.uiVariants;
          }, l.prototype.switchToUiVariant = function(e2, t2) {
            var e2 = this.uiVariants.indexOf(e2), n2 = this.currentUi, e2 = this.uiInstanceManagers[e2];
            e2 !== this.currentUi && (this.currentUi && this.currentUi.getUI().hide(), this.currentUi = e2, null != this.currentUi) && (this.currentUi.isConfigured() || (this.addUi(this.currentUi), this.currentUi.getUI().isHidden()) || this.currentUi.getUI().hide(), t2 && t2(), this.currentUi.getUI().show(), this.events.onActiveUiChanged.dispatch(this, { previousUi: n2, currentUi: e2 }));
          }, l.prototype.resolveUiVariant = function(e2, t2) {
            void 0 === e2 && (e2 = {});
            for (var n2 = { isAd: false, adRequiresUi: false, isFullscreen: this.player.getViewMode() === this.player.exports.ViewMode.Fullscreen, isMobile: c.BrowserUtils.isMobile, isPlaying: this.player.isPlaying(), width: this.uiContainerElement.width(), documentWidth: document.body.clientWidth }, o2 = g(g({}, n2), e2), i2 = (this.events.onUiVariantResolve.dispatch(this, o2), null), r2 = 0, s2 = this.uiVariants; r2 < s2.length; r2++) {
              var a2 = s2[r2], l2 = null == a2.condition || true === a2.condition(o2);
              null == i2 && l2 ? i2 = a2 : a2.ui.hide();
            }
            this.switchToUiVariant(i2, function() {
              t2 && t2(o2);
            });
          }, l.prototype.addUi = function(e2) {
            var t2 = e2.getUI().getDomElement(), n2 = e2.getWrappedPlayer();
            e2.configureControls(), this.uiContainerElement.append(t2), n2.getSource() && this.config.events.onUpdated.dispatch(this), window.requestAnimationFrame ? requestAnimationFrame(function() {
              e2.onConfigured.dispatch(e2.getUI());
            }) : setTimeout(function() {
              e2.onConfigured.dispatch(e2.getUI());
            }, 0);
          }, l.prototype.releaseUi = function(e2) {
            e2.releaseControls();
            var t2 = e2.getUI();
            t2.hasDomElement() && t2.getDomElement().remove(), e2.clearEventHandlers();
          }, l.prototype.release = function() {
            for (var e2 = 0, t2 = this.uiInstanceManagers; e2 < t2.length; e2++) {
              var n2 = t2[e2];
              this.releaseUi(n2);
            }
            this.managerPlayerWrapper.clearEventHandlers(), this.focusVisibilityTracker.release();
          }, Object.defineProperty(l.prototype, "onUiVariantResolve", { get: function() {
            return this.events.onUiVariantResolve;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "onActiveUiChanged", { get: function() {
            return this.events.onActiveUiChanged;
          }, enumerable: false, configurable: true }), Object.defineProperty(l.prototype, "activeUi", { get: function() {
            return this.currentUi;
          }, enumerable: false, configurable: true }), l.prototype.getTimelineMarkers = function() {
            return this.config.metadata.markers;
          }, l.prototype.addTimelineMarker = function(e2) {
            this.config.metadata.markers.push(e2), this.config.events.onUpdated.dispatch(this);
          }, l.prototype.removeTimelineMarker = function(e2) {
            return m.ArrayUtils.remove(this.config.metadata.markers, e2) === e2 && (this.config.events.onUpdated.dispatch(this), true);
          }, n.UIManager = l;
          u.prototype.getSubtitleSettingsManager = function() {
            return this.subtitleSettingsManager;
          }, u.prototype.getConfig = function() {
            return this.config;
          }, u.prototype.getUI = function() {
            return this.ui;
          }, u.prototype.getPlayer = function() {
            return this.playerWrapper.getPlayer();
          }, Object.defineProperty(u.prototype, "onConfigured", { get: function() {
            return this.events.onConfigured;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onSeek", { get: function() {
            return this.events.onSeek;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onSeekPreview", { get: function() {
            return this.events.onSeekPreview;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onSeeked", { get: function() {
            return this.events.onSeeked;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onComponentShow", { get: function() {
            return this.events.onComponentShow;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onComponentHide", { get: function() {
            return this.events.onComponentHide;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onControlsShow", { get: function() {
            return this.events.onControlsShow;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onPreviewControlsHide", { get: function() {
            return this.events.onPreviewControlsHide;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onControlsHide", { get: function() {
            return this.events.onControlsHide;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onRelease", { get: function() {
            return this.events.onRelease;
          }, enumerable: false, configurable: true }), Object.defineProperty(u.prototype, "onComponentViewModeChanged", { get: function() {
            return this.events.onComponentViewModeChanged;
          }, enumerable: false, configurable: true }), u.prototype.clearEventHandlers = function() {
            this.playerWrapper.clearEventHandlers();
            var e2, t2 = this.events;
            for (e2 in t2) t2[e2].unsubscribeAll();
          };
          e = u;
          function u(e2, t2, n2, o2, i2) {
            this.events = { onConfigured: new y.EventDispatcher(), onSeek: new y.EventDispatcher(), onSeekPreview: new y.EventDispatcher(), onSeeked: new y.EventDispatcher(), onComponentShow: new y.EventDispatcher(), onComponentHide: new y.EventDispatcher(), onComponentViewModeChanged: new y.EventDispatcher(), onControlsShow: new y.EventDispatcher(), onPreviewControlsHide: new y.EventDispatcher(), onControlsHide: new y.EventDispatcher(), onRelease: new y.EventDispatcher() }, this.playerWrapper = new _(e2), this.ui = t2, this.config = n2, this.subtitleSettingsManager = o2, this.spatialNavigation = i2;
          }
          n.UIInstanceManager = e;
          i(E, p = e), E.prototype.getWrappedPlayer = function() {
            return this.getPlayer();
          }, E.prototype.configureControls = function() {
            this.configureControlsTree(this.getUI()), this.configured = true;
          }, E.prototype.isConfigured = function() {
            return this.configured;
          }, E.prototype.configureControlsTree = function(e2) {
            var o2 = this, i2 = [];
            s.UIUtils.traverseTree(e2, function(e3) {
              for (var t2 = 0, n2 = i2; t2 < n2.length; t2++) if (n2[t2] === e3) throw console && console.error("Circular reference in UI tree", e3), Error("Circular reference in UI tree: " + e3.constructor.name);
              e3.initialize(), e3.configure(o2.getPlayer(), o2), i2.push(e3);
            });
          }, E.prototype.releaseControls = function() {
            var e2;
            this.configured && (this.onRelease.dispatch(this.getUI()), this.releaseControlsTree(this.getUI()), this.configured = false), null != (e2 = this.spatialNavigation) && e2.release(), this.released = true;
          }, E.prototype.isReleased = function() {
            return this.released;
          }, E.prototype.releaseControlsTree = function(e2) {
            if (e2.release(), e2 instanceof r.Container) for (var t2 = 0, n2 = e2.getComponents(); t2 < n2.length; t2++) {
              var o2 = n2[t2];
              this.releaseControlsTree(o2);
            }
          }, E.prototype.clearEventHandlers = function() {
            p.prototype.clearEventHandlers.call(this);
          };
          var p, w = E;
          function E() {
            return null !== p && p.apply(this, arguments) || this;
          }
          O.prototype.getPlayer = function() {
            return this.wrapper;
          }, O.prototype.clearEventHandlers = function() {
            try {
              this.player.getSource();
            } catch (e3) {
              e3 instanceof this.player.exports.PlayerAPINotAvailableError && (this.eventHandlers = {});
            }
            for (var e2 in this.eventHandlers) for (var t2 = 0, n2 = this.eventHandlers[e2]; t2 < n2.length; t2++) {
              var o2 = n2[t2];
              this.player.off(e2, o2);
            }
          };
          var _ = O;
          function O(o2) {
            for (var r2 = this, e2 = (this.eventHandlers = {}, this.player = o2, Object.getOwnPropertyNames(Object.getPrototypeOf({}))), t2 = d(["constructor"], e2, true), n2 = [], i2 = [], s2 = 0, a2 = function(e3) {
              var t3 = [];
              for (; e3; ) {
                var n3 = Object.getOwnPropertyNames(e3).filter(function(e4) {
                  return -1 === t3.indexOf(e4);
                });
                t3 = t3.concat(n3), e3 = Object.getPrototypeOf(e3);
              }
              return t3;
            }(o2).filter(function(e3) {
              return -1 === t2.indexOf(e3);
            }); s2 < a2.length; s2++) {
              var l2 = a2[s2];
              ("function" == typeof o2[l2] ? n2 : i2).push(l2);
            }
            for (var c2 = {}, u2 = 0, p2 = n2; u2 < p2.length; u2++) !function(e3) {
              c2[e3] = function() {
                return o2[e3].apply(o2, arguments);
              };
            }(p2[u2]);
            for (var g2 = 0, f2 = i2; g2 < f2.length; g2++) !function(n3) {
              var t3 = function(e3) {
                for (; e3; ) {
                  var t4 = Object.getOwnPropertyDescriptor(e3, n3);
                  if (t4) return t4;
                  e3 = Object.getPrototypeOf(e3);
                }
              }(o2);
              t3 && (t3.get || t3.set) ? Object.defineProperty(c2, n3, { get: function() {
                return t3.get.call(o2);
              }, set: function(e3) {
                return t3.set.call(o2, e3);
              } }) : c2[n3] = o2[n3];
            }(f2[g2]);
            c2.on = function(e3, t3) {
              return o2.on(e3, t3), r2.eventHandlers[e3] || (r2.eventHandlers[e3] = []), r2.eventHandlers[e3].push(t3), c2;
            }, c2.off = function(e3, t3) {
              return o2.off(e3, t3), r2.eventHandlers[e3] && m.ArrayUtils.remove(r2.eventHandlers[e3], t3), c2;
            }, c2.fireEventInUI = function(e3, t3) {
              if (r2.eventHandlers[e3]) for (var n3 = Object.assign({}, { timestamp: Date.now(), type: e3, uiSourced: true }, t3), o3 = 0, i3 = r2.eventHandlers[e3]; o3 < i3.length; o3++) (0, i3[o3])(n3);
            }, this.wrapper = c2;
          }
          n.PlayerWrapper = _;
        }, { "./arrayutils": 1, "./browserutils": 3, "./components/container": 19, "./components/subtitlesettings/subtitlesettingsmanager": 67, "./components/uicontainer": 76, "./dom": 84, "./eventdispatcher": 86, "./focusvisibilitytracker": 87, "./localization/i18n": 91, "./mobilev3playerapi": 97, "./storageutils": 110, "./uiutils": 117, "./volumecontroller": 118 }], 117: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.UIUtils = void 0;
          var a = e("./components/container");
          (e = n.UIUtils || (n.UIUtils = {})).traverseTree = function(e2, r) {
            function s(e3, t2) {
              if (r(e3, t2), e3 instanceof a.Container) for (var n2 = 0, o = e3.getComponents(); n2 < o.length; n2++) {
                var i = o[n2];
                s(i, e3);
              }
            }
            s(e2);
          }, (e = e.KeyCode || (e.KeyCode = {}))[e.LeftArrow = 37] = "LeftArrow", e[e.UpArrow = 38] = "UpArrow", e[e.RightArrow = 39] = "RightArrow", e[e.DownArrow = 40] = "DownArrow", e[e.Space = 32] = "Space", e[e.End = 35] = "End", e[e.Home = 36] = "Home";
        }, { "./components/container": 19 }], 118: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.VolumeTransition = n.VolumeController = void 0;
          var o = e("./eventdispatcher");
          n.VolumeController = (i.prototype.setVolume = function(e2) {
            this.player.setVolume(e2, i.issuerName);
          }, i.prototype.getVolume = function() {
            return this.player.getVolume();
          }, i.prototype.setMuted = function(e2) {
            e2 ? this.player.mute(i.issuerName) : this.player.unmute(i.issuerName);
          }, i.prototype.toggleMuted = function() {
            this.isMuted() || 0 === this.getVolume() ? this.recallVolume() : this.setMuted(true);
          }, i.prototype.isMuted = function() {
            return this.player.isMuted();
          }, i.prototype.storeVolume = function() {
            this.storedVolume = this.getVolume();
          }, i.prototype.recallVolume = function() {
            this.setMuted(0 === this.storedVolume), this.setVolume(this.storedVolume);
          }, i.prototype.startTransition = function() {
            return new r(this);
          }, i.prototype.onChangedEvent = function() {
            var e2 = this.isMuted(), t2 = this.getVolume(), n2 = e2 || 0 === t2, e2 = e2 ? 0 : t2;
            this.storeVolume(), this.events.onChanged.dispatch(this, { volume: e2, muted: n2 });
          }, Object.defineProperty(i.prototype, "onChanged", { get: function() {
            return this.events.onChanged.getEvent();
          }, enumerable: false, configurable: true }), i.issuerName = "ui-volumecontroller", i);
          function i(e2) {
            function t2() {
              n2.onChangedEvent();
            }
            var n2 = this;
            this.player = e2, this.events = { onChanged: new o.EventDispatcher() }, this.storeVolume();
            e2.on(e2.exports.PlayerEvent.SourceLoaded, t2), e2.on(e2.exports.PlayerEvent.VolumeChanged, t2), e2.on(e2.exports.PlayerEvent.Muted, t2), e2.on(e2.exports.PlayerEvent.Unmuted, t2);
          }
          s.prototype.update = function(e2) {
            this.controller.setMuted(false), this.controller.setVolume(e2);
          }, s.prototype.finish = function(e2) {
            0 === e2 ? (this.controller.recallVolume(), this.controller.setMuted(true)) : (this.controller.setMuted(false), this.controller.setVolume(e2), this.controller.storeVolume());
          };
          var r = s;
          function s(e2) {
            (this.controller = e2).storeVolume();
          }
          n.VolumeTransition = r;
        }, { "./eventdispatcher": 86 }], 119: [function(e, t, n) {
          "use strict";
          Object.defineProperty(n, "__esModule", { value: true }), n.VttUtils = void 0;
          function l(e2, t2, n2, o2) {
            var i = t2 === u.Right ? "vertical-lr" : "vertical-rl";
            e2.css("writing-mode", i), e2.css(u.Top, "0"), d(e2, n2, t2, o2);
          }
          function c(e2, t2, n2) {
            if ("auto" === t2.position) e2.css(n2, "0");
            else switch (t2.positionAlign) {
              case "line-left":
                e2.css(n2, "".concat(t2.position, "%")), e2.css(g.get(n2), "auto"), e2.css("justify-content", "flex-start");
                break;
              case "center":
                e2.css(n2, "".concat(t2.position - t2.size / 2, "%")), e2.css(g.get(n2), "auto"), e2.css("justify-content", "center");
                break;
              case "line-right":
                e2.css(n2, "auto"), e2.css(g.get(n2), "".concat(100 - t2.position, "%")), e2.css("justify-content", "flex-end");
                break;
              default:
                e2.css(n2, "".concat(t2.position, "%")), e2.css("justify-content", "flex-start");
            }
          }
          var u, p, o, a = 21, g = ((o = u = u || {}).Top = "top", o.Bottom = "bottom", o.Left = "left", o.Right = "right", (o = p = p || {}).GrowingRight = "lr", o.GrowingLeft = "rl", /* @__PURE__ */ new Map([[u.Top, u.Bottom], [u.Bottom, u.Top], [u.Left, u.Right], [u.Right, u.Left]])), f = function(e2, t2, n2, o2) {
            switch (t2.lineAlign) {
              case "center":
                var i = e2;
                switch (n2) {
                  case u.Bottom:
                    i.css("transform", "translateY(-50%)");
                    break;
                  case u.Left:
                    i.css("transform", "translateX(50%)");
                    break;
                  case u.Right:
                    i.css("transform", "translateX(-50%)");
                    break;
                }
                break;
              case "end":
                e2.css(n2, "".concat(100 - o2, "%"));
            }
          }, d = function(e2, t2, n2, o2) {
            var i, r, s = g.get(n2);
            "auto" === t2.line && t2.vertical ? e2.css(s, "0") : "auto" === t2.line && !t2.vertical || (r = parseFloat(t2.line), t2.snapToLines && (i = Number(t2.line), r = 100 * (o2.height / a * (i = i < 0 ? a + i : i)) / o2.height), "end" !== t2.lineAlign && e2.css(s, "".concat(r, "%")), f(e2, t2, n2, r));
          };
          (o = n.VttUtils || (n.VttUtils = {})).setVttCueBoxStyles = function(e2, t2) {
            var n2 = e2.vtt, o2 = e2.getDomElement(), i = (a2 = o2, n2.region ? (a2.css("position", "relative"), a2.css("unicode-bidi", "plaintext")) : (a2.css("position", "absolute"), a2.css("overflow-wrap", "break-word"), a2.css("overflow", "hidden"), a2.css("flex-flow", "column")), a2.css("display", "inline-flex"), e2.getText().split("<br />").length, o2), r = n2, s = t2;
            switch (r.vertical) {
              case "":
                i.css("writing-mode", "horizontal-tb"), i.css(u.Bottom, "0"), d(i, r, u.Bottom, s);
                break;
              case p.GrowingRight:
                l(i, u.Right, r, s);
                break;
              case p.GrowingLeft:
                l(i, u.Left, r, s);
            }
            var a2 = "middle" === n2.align ? "center" : n2.align, e2 = (o2.css("text-align", a2), n2.size);
            "" === n2.vertical ? (o2.css("width", "".concat(e2, "%")), c(o2, n2, u.Left)) : (o2.css("height", "".concat(e2, "%")), c(o2, n2, u.Top));
          }, o.setVttRegionStyles = function(e2, t2, n2) {
            var e2 = e2.getDomElement(), o2 = n2.width * t2.viewportAnchorX / 100 - n2.width * t2.width / 100 * t2.regionAnchorX / 100, n2 = n2.height * t2.viewportAnchorY / 100 - 28 * t2.lines * t2.regionAnchorY / 100;
            e2.css("position", "absolute"), e2.css("overflow", "hidden"), e2.css("width", "".concat(t2.width, "%")), e2.css(u.Left, "".concat(o2, "px")), e2.css(u.Right, "unset"), e2.css(u.Top, "".concat(n2, "px")), e2.css(u.Bottom, "unset"), e2.css("height", "".concat(28 * t2.lines, "px"));
          };
        }, {}] }, {}, [96])(96);
      });
    })();
  }
});
export default require_bitmovinplayer_ui();
//# sourceMappingURL=bitmovin-player_bitmovinplayer-ui.js.map
