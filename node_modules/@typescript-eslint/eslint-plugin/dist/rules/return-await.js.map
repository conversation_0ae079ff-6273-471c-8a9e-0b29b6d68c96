{"version": 3, "file": "return-await.js", "sourceRoot": "", "sources": ["../../src/rules/return-await.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAOiB;AACjB,yEAAsE;AAYtE,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,gDAAgD;YAC7D,oBAAoB,EAAE,IAAI;YAC1B,eAAe,EAAE,iBAAiB;SACnC;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE;YACR,eAAe,EACb,kEAAkE;YACpE,sBAAsB,EACpB,8DAA8D;YAChE,oBAAoB,EAClB,2DAA2D;SAC9D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,cAAc,EAAE,QAAQ,EAAE,OAAO,CAAC;aAC1C;SACF;KACF;IACD,cAAc,EAAE,CAAC,cAAc,CAAC;IAEhC,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,MAAM,cAAc,GAAgB,EAAE,CAAC;QAEvC,SAAS,aAAa,CAAC,IAAkB;YACvC,cAAc,CAAC,IAAI,CAAC;gBAClB,QAAQ,EAAE,IAAI,CAAC,KAAK;gBACpB,UAAU,EAAE,IAAI;aACjB,CAAC,CAAC;QACL,CAAC;QAED,SAAS,YAAY;YACnB,cAAc,CAAC,GAAG,EAAE,CAAC;QACvB,CAAC;QAED,SAAS,KAAK,CAAC,IAAa;YAC1B,IAAI,QAAQ,GAAG,IAAI,CAAC,MAA6B,CAAC;YAElD,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,IAAI,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAChC,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,OAAO,CAAC,IAAa;YAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,MAA6B,CAAC;YAElD,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,IAAI,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/B,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,wBAAwB,CAAC,IAAa;YAC7C,IAAI,QAAQ,GAAG,IAAI,CAAC,MAA6B,CAAC;YAElD,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,IACE,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAClC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;oBACpB,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,EACpC,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe,CAAC,IAAa;YACpC,IAAI,QAAQ,GAAG,IAAI,CAAC,MAA6B,CAAC;YAElD,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,IAAI,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAChC,OAAO,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC;gBACjC,CAAC;gBACD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,gCAAgC;QAEhC,SAAS,WAAW,CAClB,KAAyB,EACzB,IAAyB;YAEzB,qDAAqD;YACrD,wBAAwB,CAAC,IAAI,CAAC,IAAA,wBAAiB,EAAC,IAAI,CAAC,EAAE,CAAC;gBACtD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,qBAAc,CAAC,CAAC;YAC1E,gDAAgD;YAChD,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACzC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChC,wEAAwE;YACxE,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,EAAE;gBAC7D,eAAe,EAAE,IAAI;aACtB,CAAC,CAAC;YACH,IAAI,SAAS,EAAE,CAAC;gBACd,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC;YAED,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,SAAS,WAAW,CAClB,KAAyB,EACzB,IAAyB,EACzB,iBAA0B;YAE1B,IAAI,iBAAiB,EAAE,CAAC;gBACtB,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAChD,CAAC;YACD,OAAO;gBACL,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC;gBACvC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC;aACjC,CAAC;QACJ,CAAC;QAED,SAAS,2BAA2B,CAAC,IAAa;YAChD,MAAM,QAAQ,GAAG,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC1C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI;gBACzB,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;YAC1B,MAAM,cAAc,GAAG,IAAA,6CAAqB,EAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAClE,MAAM,eAAe,GAAG,IAAA,6CAAqB,EAC3C,EAAE,CAAC,UAAU,CAAC,eAAe,EAC7B,EAAE,CAAC,UAAU,CAAC,OAAO,CACtB,CAAC;YACF,OAAO,cAAc,GAAG,eAAe,CAAC;QAC1C,CAAC;QAED,SAAS,IAAI,CAAC,IAAyB,EAAE,UAAmB;YAC1D,IAAI,KAAc,CAAC;YAEnB,MAAM,OAAO,GAAG,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEjD,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,UAAU,CAAC;YACrB,CAAC;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;YAErE,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC5B,OAAO;YACT,CAAC;YAED,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC3B,iDAAiD;gBACjD,MAAM,UAAU,GAAG,CAAC,CAAC,IAAA,oBAAa,EAAC,IAAI,CAAC,IAAI,IAAA,wBAAiB,EAAC,IAAI,CAAC,CAAC,CAAC;gBACrE,MAAM,GAAG,GAAG,CAAC,KAAyB,EAA2B,EAAE,CACjE,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAE3B,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EAAE,iBAAiB;oBAC5B,IAAI;oBACJ,GAAG,CAAC,UAAU;wBACZ,CAAC,CAAC,EAAE,GAAG,EAAE;wBACT,CAAC,CAAC;4BACE,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,iBAAiB;oCAC5B,GAAG;iCACJ;6BACF;yBACF,CAAC;iBACP,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACxB,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE,CAAC;oBAC3B,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,sBAAsB;wBACjC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,2BAA2B,CAAC,UAAU,CAAC,CAAC;qBACpE,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO;YACT,CAAC;YAED,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;gBACvB,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,wBAAwB;wBACnC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;qBACvC,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO;YACT,CAAC;YAED,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;gBAC9B,MAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC9D,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;oBAC7B,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,wBAAwB;wBACnC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;qBACvC,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,CAAC,OAAO,IAAI,YAAY,EAAE,CAAC;oBACpC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;wBACxD,OAAO;oBACT,CAAC;oBAED,IAAI,wBAAwB,CAAC,UAAU,CAAC,EAAE,CAAC;wBACzC,OAAO;oBACT,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,sBAAsB;wBACjC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,2BAA2B,CAAC,UAAU,CAAC,CAAC;qBACpE,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO;YACT,CAAC;QACH,CAAC;QAED,SAAS,yBAAyB,CAChC,IAAyB;YAEzB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB,EAAE,CAAC;gBACvD,OAAO;oBACL,GAAG,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC;oBAC5C,GAAG,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC9C,CAAC;YACJ,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,uBAAuB,EAAE,aAAa;YAEtC,0BAA0B,EAAE,YAAY;YACxC,yBAAyB,EAAE,YAAY;YACvC,8BAA8B,EAAE,YAAY;YAE5C,kEAAkE;YAClE,4CAA4C,CAC1C,IAAsC;gBAEtC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;oBACrD,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBAClD,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBACxD,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,MAAM,SAAS,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAC3C,OAAO;gBACT,CAAC;gBACD,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACtD,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBACxD,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}