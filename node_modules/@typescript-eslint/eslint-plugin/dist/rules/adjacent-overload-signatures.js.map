{"version": 3, "file": "adjacent-overload-signatures.js", "sourceRoot": "", "sources": ["../../src/rules/adjacent-overload-signatures.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAwE;AAcxE,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,8BAA8B;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,0DAA0D;YACvE,WAAW,EAAE,WAAW;SACzB;QACD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE;YACR,iBAAiB,EAAE,6CAA6C;SACjE;KACF;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QAQZ;;;;WAIG;QACH,SAAS,eAAe,CAAC,MAAqB;YAC5C,MAAM,QAAQ,GAAG,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;YAEvD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,sBAAc,CAAC,wBAAwB,CAAC;gBAC7C,KAAK,sBAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC;oBAC3C,yCAAyC;oBACzC,uCAAuC;oBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;wBACxB,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,OAAO,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAC7C,CAAC;gBACD,KAAK,sBAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,sBAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBACxC,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,IAAI,IAAI,IAAI,CAAC;oBACrC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;wBACjB,OAAO,IAAI,CAAC;oBACd,CAAC;oBACD,OAAO;wBACL,IAAI;wBACJ,MAAM,EAAE,QAAQ;wBAChB,aAAa,EAAE,KAAK;wBACpB,IAAI,EAAE,qBAAc,CAAC,MAAM;qBAC5B,CAAC;gBACJ,CAAC;gBACD,KAAK,sBAAc,CAAC,iBAAiB;oBACnC,OAAO;wBACL,GAAG,IAAA,wBAAiB,EAAC,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC;wBAChD,MAAM,EAAE,QAAQ;wBAChB,aAAa,EAAE,KAAK;qBACrB,CAAC;gBACJ,KAAK,sBAAc,CAAC,0BAA0B;oBAC5C,OAAO;wBACL,IAAI,EAAE,MAAM;wBACZ,MAAM,EAAE,QAAQ;wBAChB,aAAa,EAAE,IAAI;wBACnB,IAAI,EAAE,qBAAc,CAAC,MAAM;qBAC5B,CAAC;gBACJ,KAAK,sBAAc,CAAC,+BAA+B;oBACjD,OAAO;wBACL,IAAI,EAAE,KAAK;wBACX,MAAM,EAAE,QAAQ;wBAChB,aAAa,EAAE,KAAK;wBACpB,IAAI,EAAE,qBAAc,CAAC,MAAM;qBAC5B,CAAC;gBACJ,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,OAAO;wBACL,GAAG,IAAA,wBAAiB,EAAC,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC;wBAChD,MAAM,EAAE,QAAQ;wBAChB,aAAa,EAAE,KAAK;qBACrB,CAAC;YACN,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,YAAY,CAAC,OAAe,EAAE,OAAsB;YAC3D,OAAO,CACL,CAAC,CAAC,OAAO;gBACT,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;gBAC7B,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;gBACjC,OAAO,CAAC,aAAa,KAAK,OAAO,CAAC,aAAa;gBAC/C,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAC9B,CAAC;QACJ,CAAC;QAED,SAAS,UAAU,CAAC,IAAc;YAChC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,sBAAc,CAAC,SAAS,CAAC;gBAC9B,KAAK,sBAAc,CAAC,OAAO,CAAC;gBAC5B,KAAK,sBAAc,CAAC,aAAa,CAAC;gBAClC,KAAK,sBAAc,CAAC,eAAe,CAAC;gBACpC,KAAK,sBAAc,CAAC,cAAc;oBAChC,OAAO,IAAI,CAAC,IAAI,CAAC;gBAEnB,KAAK,sBAAc,CAAC,aAAa;oBAC/B,OAAO,IAAI,CAAC,OAAO,CAAC;YACxB,CAAC;QACH,CAAC;QAED,SAAS,2BAA2B,CAAC,IAAc;YACjD,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;YAEjC,IAAI,UAAU,GAAkB,IAAI,CAAC;YACrC,MAAM,WAAW,GAAa,EAAE,CAAC;YAEjC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACvB,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;gBACvC,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;oBACnB,UAAU,GAAG,IAAI,CAAC;oBAClB,OAAO;gBACT,CAAC;gBAED,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAC/C,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CACjC,CAAC;gBACF,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC;oBACpD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,MAAM;wBACZ,SAAS,EAAE,mBAAmB;wBAC9B,IAAI,EAAE;4BACJ,IAAI,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;yBACxD;qBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACxB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,CAAC;gBAED,UAAU,GAAG,MAAM,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,SAAS,EAAE,2BAA2B;YACtC,OAAO,EAAE,2BAA2B;YACpC,aAAa,EAAE,2BAA2B;YAC1C,aAAa,EAAE,2BAA2B;YAC1C,eAAe,EAAE,2BAA2B;YAC5C,cAAc,EAAE,2BAA2B;SAC5C,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}