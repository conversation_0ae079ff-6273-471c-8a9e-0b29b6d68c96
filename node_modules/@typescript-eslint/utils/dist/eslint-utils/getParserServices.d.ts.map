{"version": 3, "file": "getParserServices.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/getParserServices.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,QAAQ,MAAM,cAAc,CAAC;AAC9C,OAAO,KAAK,EACV,cAAc,EACd,iCAAiC,EAClC,MAAM,cAAc,CAAC;AAUtB;;;GAGG;AACH,iBAAS,iBAAiB,CACxB,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,EAElC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,GAC3D,iCAAiC,CAAC;AACrC;;;GAGG;AACH,iBAAS,iBAAiB,CACxB,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,EAElC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,EAC5D,+BAA+B,EAAE,KAAK,GACrC,iCAAiC,CAAC;AACrC;;;GAGG;AACH,iBAAS,iBAAiB,CACxB,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,EAElC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,EAC5D,+BAA+B,EAAE,IAAI,GACpC,cAAc,CAAC;AAClB;;;GAGG;AACH,iBAAS,iBAAiB,CACxB,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,EAElC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,EAC5D,+BAA+B,EAAE,OAAO,GACvC,cAAc,CAAC;AA+ClB,OAAO,EAAE,iBAAiB,EAAE,CAAC"}