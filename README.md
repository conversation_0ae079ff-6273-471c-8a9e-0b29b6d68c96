# Bitmovin DRM Player

一个基于Vite + React + TypeScript构建的DRM流媒体播放器，集成了Bitmovin Player来支持各种DRM保护的内容。

## 功能特性

- 🎥 支持多种DRM技术：Widevine、PlayReady、FairPlay
- 📱 响应式设计，支持移动端和桌面端
- 🚀 基于Vite的快速开发和构建
- 💪 TypeScript支持，提供类型安全
- 🎨 现代化UI设计
- 🔧 可配置的播放器设置

## 支持的DRM技术

| DRM技术 | 支持的浏览器 | 说明 |
|---------|-------------|------|
| Widevine | Chrome, Firefox, Edge | Google开发的DRM技术 |
| PlayReady | Edge, IE | Microsoft开发的DRM技术 |
| FairPlay | Safari | Apple开发的DRM技术 |

## 快速开始

### 前置要求

- Node.js 16+ 
- npm 或 yarn 或 pnpm

### 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 配置Bitmovin Player License

1. 注册Bitmovin账户：https://bitmovin.com/
2. 获取Player License Key
3. 在 `src/components/BitmovinPlayer.tsx` 中替换 `YOUR_BITMOVIN_PLAYER_KEY`

```typescript
const playerConfig = {
  key: 'YOUR_ACTUAL_LICENSE_KEY', // 替换为实际的license key
  // ... 其他配置
}
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 项目结构

```
ms-vite-player/
├── src/
│   ├── components/
│   │   ├── BitmovinPlayer.tsx    # 主播放器组件
│   │   └── BitmovinPlayer.css    # 播放器样式
│   ├── App.tsx                   # 主应用组件
│   ├── App.css                   # 应用样式
│   ├── main.tsx                  # 应用入口
│   └── index.css                 # 全局样式
├── index.html                    # HTML模板
├── vite.config.ts               # Vite配置
├── tsconfig.json                # TypeScript配置
└── package.json                 # 项目依赖
```

## 使用说明

### 基本用法

1. 启动应用后，选择一个DRM流媒体源
2. 播放器会自动检测浏览器的DRM支持情况
3. 点击播放按钮开始播放受DRM保护的内容

### 添加自定义DRM源

在 `BitmovinPlayer.tsx` 中的 `drmSources` 数组中添加新的源：

```typescript
const customSource: SourceConfig = {
  title: '自定义DRM源',
  description: '描述信息',
  dash: 'https://your-dash-url.mpd',
  hls: 'https://your-hls-url.m3u8',
  drm: {
    widevine: {
      LA_URL: 'https://your-widevine-license-server'
    },
    playready: {
      LA_URL: 'https://your-playready-license-server'
    }
  }
}
```

### DRM配置说明

```typescript
interface DRMConfig {
  widevine?: {
    LA_URL: string              // Widevine许可证服务器URL
    headers?: Record<string, string>  // 自定义请求头
  }
  playready?: {
    LA_URL: string              // PlayReady许可证服务器URL
    headers?: Record<string, string>  // 自定义请求头
  }
  fairplay?: {
    LA_URL: string              // FairPlay许可证服务器URL
    certificateURL: string      // FairPlay证书URL
    headers?: Record<string, string>  // 自定义请求头
  }
}
```

## 常见问题

### Q: 播放器无法加载？
A: 请确保已配置有效的Bitmovin Player License Key。

### Q: DRM内容无法播放？
A: 检查浏览器是否支持相应的DRM技术，确保许可证服务器配置正确。

### Q: 移动端播放问题？
A: 确保移动浏览器支持相应的DRM技术，iOS Safari需要FairPlay DRM。

## 技术栈

- **前端框架**: React 18
- **构建工具**: Vite 5
- **语言**: TypeScript
- **播放器**: Bitmovin Player 8
- **样式**: CSS3 + CSS Modules

## 许可证

本项目仅用于演示目的。Bitmovin Player需要商业许可证才能在生产环境中使用。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题，请创建Issue或联系项目维护者。
