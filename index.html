<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bitmovin DRM Player</title>
    
    <!-- Bitmovin Player CSS -->
    <link rel="stylesheet" href="https://cdn.bitmovin.com/player/web/8/bitmovinplayer-ui.css">
    
    <!-- 预加载Bitmovin Player脚本 -->
    <link rel="preload" href="https://cdn.bitmovin.com/player/web/8/bitmovinplayer.js" as="script">
    
    <style>
      /* 防止页面闪烁 */
      body {
        margin: 0;
        background-color: #1a1a1a;
      }
      
      /* 加载动画 */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        margin-left: 1rem;
        font-family: 'Inter', sans-serif;
        font-size: 1.2rem;
      }
    </style>
  </head>
  <body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>
    
    <div id="root"></div>
    
    <!-- Bitmovin Player脚本 -->
    <script src="https://cdn.bitmovin.com/player/web/8/bitmovinplayer.js"></script>
    
    <script>
      // 隐藏加载屏幕
      window.addEventListener('load', function() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          setTimeout(() => {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
              loadingScreen.style.display = 'none';
            }, 500);
          }, 1000);
        }
      });
      
      // 检查DRM支持
      function checkDRMSupport() {
        const support = {
          widevine: false,
          playready: false,
          fairplay: false
        };
        
        if (navigator.requestMediaKeySystemAccess) {
          // 检查Widevine支持
          navigator.requestMediaKeySystemAccess('com.widevine.alpha', [{
            initDataTypes: ['cenc'],
            videoCapabilities: [{contentType: 'video/mp4; codecs="avc1.42E01E"'}]
          }]).then(() => {
            support.widevine = true;
            console.log('Widevine DRM supported');
          }).catch(() => {
            console.log('Widevine DRM not supported');
          });
          
          // 检查PlayReady支持
          navigator.requestMediaKeySystemAccess('com.microsoft.playready', [{
            initDataTypes: ['cenc'],
            videoCapabilities: [{contentType: 'video/mp4; codecs="avc1.42E01E"'}]
          }]).then(() => {
            support.playready = true;
            console.log('PlayReady DRM supported');
          }).catch(() => {
            console.log('PlayReady DRM not supported');
          });
        }
        
        // 检查FairPlay支持 (Safari)
        if (window.WebKitMediaKeys) {
          support.fairplay = true;
          console.log('FairPlay DRM supported');
        }
        
        // 将支持信息存储到全局变量
        window.drmSupport = support;
        
        return support;
      }
      
      // 页面加载完成后检查DRM支持
      document.addEventListener('DOMContentLoaded', checkDRMSupport);
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
